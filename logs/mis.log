2024-12-18 00:07:14,414 [WARN] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool [HikariPool.java : 787] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=12m26s219ms).
2024-12-18 00:23:40,205 [WARN] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool [HikariPool.java : 787] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=15m55s727ms).
2024-12-18 00:39:36,817 [WARN] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool [HikariPool.java : 787] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=15m56s613ms).
2024-12-18 00:56:02,570 [WARN] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool [HikariPool.java : 787] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=15m55s748ms).
2024-12-18 01:12:55,042 [WARN] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool [HikariPool.java : 787] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=15m52s443ms).
2024-12-18 01:28:50,804 [WARN] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool [HikariPool.java : 787] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=15m55s763ms).
2024-12-18 01:45:13,817 [WARN] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool [HikariPool.java : 787] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=15m52s989ms).
2024-12-18 02:01:09,591 [WARN] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool [HikariPool.java : 787] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=15m55s775ms).
2024-12-18 02:14:07,903 [WARN] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool [HikariPool.java : 787] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=12m28s308ms).
2024-12-18 02:30:32,580 [WARN] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool [HikariPool.java : 787] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=15m54s663ms).
2024-12-18 02:46:28,234 [WARN] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool [HikariPool.java : 787] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=15m55s654ms).
2024-12-18 03:02:54,072 [WARN] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool [HikariPool.java : 787] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=15m55s806ms).
2024-12-18 03:18:59,450 [WARN] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool [HikariPool.java : 787] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=15m35s363ms).
2024-12-18 03:34:55,302 [WARN] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool [HikariPool.java : 787] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=15m55s852ms).
2024-12-18 03:51:20,173 [WARN] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool [HikariPool.java : 787] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=15m54s900ms).
2024-12-18 04:01:08,704 [WARN] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool [HikariPool.java : 787] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=9m48s531ms).
2024-12-18 04:17:34,474 [WARN] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool [HikariPool.java : 787] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=15m55s759ms).
2024-12-18 04:33:59,080 [WARN] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool [HikariPool.java : 787] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=15m54s583ms).
2024-12-18 04:50:24,069 [WARN] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool [HikariPool.java : 787] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=15m54s972ms).
2024-12-18 05:06:19,836 [WARN] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool [HikariPool.java : 787] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=15m55s767ms).
2024-12-18 05:22:15,653 [WARN] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool [HikariPool.java : 787] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=15m55s817ms).
2024-12-18 05:38:41,342 [WARN] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool [HikariPool.java : 787] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=15m55s695ms).
2024-12-18 05:54:36,277 [WARN] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool [HikariPool.java : 787] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=15m54s934ms).
2024-12-18 06:02:32,124 [WARN] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool [HikariPool.java : 787] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=7m25s818ms).
2024-12-18 06:18:27,794 [WARN] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool [HikariPool.java : 787] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=15m55s669ms).
2024-12-18 06:34:48,126 [WARN] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool [HikariPool.java : 787] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=15m50s335ms).
2024-12-18 06:50:43,957 [WARN] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool [HikariPool.java : 787] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=15m55s832ms).
2024-12-18 07:03:21,873 [WARN] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool [HikariPool.java : 787] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=12m7s893ms).
2024-12-18 07:19:17,587 [WARN] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool [HikariPool.java : 787] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=15m55s715ms).
2024-12-18 07:35:13,479 [WARN] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool [HikariPool.java : 787] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=15m55s892ms).
2024-12-18 07:51:39,300 [WARN] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool [HikariPool.java : 787] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=15m55s807ms).
2024-12-18 08:04:17,689 [WARN] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool [HikariPool.java : 787] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=12m38s388ms).
2024-12-18 08:20:47,987 [WARN] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool [HikariPool.java : 787] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=16m285ms).
2024-12-18 08:36:43,699 [WARN] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool [HikariPool.java : 787] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=15m55s712ms).
2024-12-18 08:51:06,148 [WARN] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool [HikariPool.java : 787] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=13m52s452ms).
2024-12-18 09:05:34,899 [WARN] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool [HikariPool.java : 787] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=14m28s751ms).
2024-12-18 09:09:56,951 [WARN] [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool [HikariPool.java : 787] HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=4m22s51ms).
2024-12-18 09:18:05,212 [INFO] [SessionValidationThread-1] o.apache.shiro.session.mgt.AbstractValidatingSessionManager [AbstractValidatingSessionManager.java : 275] Validating all active sessions...
2024-12-18 09:18:05,317 [INFO] [SessionValidationThread-1] o.apache.shiro.session.mgt.AbstractValidatingSessionManager [AbstractValidatingSessionManager.java : 308] Finished session validation.  No sessions were stopped.
2024-12-18 09:29:06,957 [INFO] [schedule-pool-6] com.yzm.property.materials.service.impl.IssueTempDtlImpl [IssueTempDtlImpl.java : 323] *********************************************************************************************************************************************************************************************@
2024-12-18 09:29:07,277 [INFO] [schedule-pool-6] com.yzm.property.materials.service.impl.IssueTempDtlImpl [IssueTempDtlImpl.java : 328] Connection established successfully.
2024-12-18 09:29:07,321 [ERROR] [schedule-pool-6] com.yzm.property.materials.service.impl.IssueTempDtlImpl [IssueTempDtlImpl.java : 370] Error while executing query: FUNCTION storage_management.DATEADD does not exist
2024-12-18 09:29:07,327 [INFO] [schedule-pool-6] com.yzm.property.materials.service.impl.IssueTempDtlImpl [IssueTempDtlImpl.java : 375] Connection closed successfully.
2024-12-18 09:29:07,328 [INFO] [schedule-pool-6] com.yzm.property.materials.service.impl.IssueTempDtlImpl [IssueTempDtlImpl.java : 494] ==========查询到单据==================0
2024-12-18 09:29:07,440 [INFO] [schedule-pool-6] com.yzm.property.materials.service.impl.IssueTempDtlImpl [IssueTempDtlImpl.java : 270] getIssueTempDtlTube==============*********************************************************************************************************************************************************************************************@
2024-12-18 09:29:07,745 [INFO] [schedule-pool-6] com.yzm.property.materials.service.impl.IssueTempDtlImpl [IssueTempDtlImpl.java : 275] Connection established successfully.
2024-12-18 09:29:07,786 [ERROR] [schedule-pool-6] com.yzm.property.materials.service.impl.IssueTempDtlImpl [IssueTempDtlImpl.java : 305] Error while executing query: Table 'storage_management.b3e_bom_temp_tube' doesn't exist
2024-12-18 09:29:07,790 [INFO] [schedule-pool-6] com.yzm.property.materials.service.impl.IssueTempDtlImpl [IssueTempDtlImpl.java : 310] Connection closed successfully.
2024-12-18 09:29:07,790 [INFO] [schedule-pool-6] com.yzm.property.materials.service.impl.IssueTempDtlImpl [IssueTempDtlImpl.java : 404] ==========查询到单据胶管数据==================0
2024-12-18 09:29:07,833 [INFO] [schedule-pool-6] com.yzm.property.materials.service.impl.IssueTempDtlImpl [IssueTempDtlImpl.java : 747] ---------------开始运行发票----------------
2024-12-18 09:29:07,833 [INFO] [schedule-pool-6] com.yzm.property.materials.service.impl.IssueTempDtlImpl [IssueTempDtlImpl.java : 647] ***********************************************************************************************************************
2024-12-18 09:30:08,727 [ERROR] [schedule-pool-6] com.yzm.property.materials.service.impl.IssueTempDtlImpl [IssueTempDtlImpl.java : 682] Error while executing query: 无法连接到命名实例 MSSQLSERVER2019 上的主机 172.19.60.188。错误:“java.net.SocketTimeoutException: Receive timed out”。请验证服务器和实例名称是否正确无误，并检查是否没有防火墙在阻止流向端口 1434 的 UDP 流量。对于 SQL Server 2005 或更高版本，请验证 SQL Server Browser 服务是否正在主机上运行。
2024-12-18 09:39:22,680 [INFO] [main] com.yzm.StorageManagementApplication [StartupInfoLogger.java : 55] Starting StorageManagementApplication using Java 1.8.0_111 on yizengmingdeMacBook-Pro.local with PID 89816 (/Users/<USER>/工作/yizengming/storageManagement/target/classes started by yizengming in /Users/<USER>/工作/yizengming/storageManagement)
2024-12-18 09:39:22,683 [INFO] [main] com.yzm.StorageManagementApplication [SpringApplication.java : 679] The following profiles are active: dev
2024-12-18 09:39:24,187 [INFO] [main] o.s.data.repository.config.RepositoryConfigurationDelegate [RepositoryConfigurationDelegate.java : 250] Multiple Spring Data modules found, entering strict repository configuration mode!
2024-12-18 09:39:24,189 [INFO] [main] o.s.data.repository.config.RepositoryConfigurationDelegate [RepositoryConfigurationDelegate.java : 128] Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2024-12-18 09:39:24,568 [INFO] [main] o.s.data.repository.config.RepositoryConfigurationDelegate [RepositoryConfigurationDelegate.java : 188] Finished Spring Data repository scanning in 367 ms. Found 66 JPA repository interfaces.
2024-12-18 09:39:24,615 [INFO] [main] o.s.data.repository.config.RepositoryConfigurationDelegate [RepositoryConfigurationDelegate.java : 250] Multiple Spring Data modules found, entering strict repository configuration mode!
2024-12-18 09:39:24,616 [INFO] [main] o.s.data.repository.config.RepositoryConfigurationDelegate [RepositoryConfigurationDelegate.java : 128] Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2024-12-18 09:39:24,654 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.basis.repository.AreaRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,655 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.basis.repository.AssetsInfoRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,655 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.basis.repository.AssetsLogRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,656 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.basis.repository.BrandRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,656 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.basis.repository.DepartmentRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,656 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.basis.repository.DepartmentRoleRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,658 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.basis.repository.InventoryAssetsInfoRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,659 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.basis.repository.InventoryInfoRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,660 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.basis.repository.InventorySimpleItemRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,660 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.basis.repository.InventorySimpleRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,661 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.basis.repository.PersonnelRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,662 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.basis.repository.SupplierComparisonRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,662 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.basis.repository.TypeRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,664 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.basis.repository.UnitRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,664 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.basis.repository.WarehouseRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,665 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.business.repository.AssetsRepairsMiddleRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,665 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.business.repository.AssetsScrapMiddleRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,665 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.business.repository.BorrowReturnItemRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,666 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.business.repository.BorrowReturnRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,666 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.business.repository.BusinessRepairsItemRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,666 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.business.repository.BusinessRepairsRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,666 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.business.repository.BusinessScrapRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,667 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.business.repository.ReceiptNoteItemRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,667 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.business.repository.ReceiptNoteRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,669 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.consumable.repository.ConsumableInfoRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,669 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.consumable.repository.ConsumableListRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,670 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.consumable.repository.ConsumableRepertoryRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,670 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.consumable.repository.ConsumableStandingBookRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,670 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.consumable.repository.InStorageItemRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,670 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.consumable.repository.InStorageRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,671 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.consumable.repository.OutWarehouseItemRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,671 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.consumable.repository.OutWarehouseRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,672 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.consumable.repository.TransferItemRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,672 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.consumable.repository.TransferRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,672 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.BindingCarRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,673 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.DelinquentMaterialRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,673 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.InStorageItemMaterialsRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,674 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.InStorageMaterialsRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,674 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.IssueTempDtlItemRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,675 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.IssueTempDtlRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,675 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.IssueTempDtlTubeItemRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,675 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.IssueTempDtlTubeRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,675 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.MaterialTransitRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,675 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.MaterialsInfoBomRepeatRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,676 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.MaterialsInfoBomRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,676 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.MaterialsInfoRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,677 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.MaterialsListRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,677 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.MaterialsRepertoryFifoRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,678 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.MaterialsRepertoryRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,678 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.MaterialsStandingBookRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,679 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.OutWarehouseItemMaterialsRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,679 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.OutWarehouseMaterialsRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,679 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.TransferItemMaterialsRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,679 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.TransferMaterialsRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,680 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.statistics.repository.AssetListRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,680 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.system.repository.SysConfigRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,680 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.system.repository.SysDictDataRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,680 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.system.repository.SysDictTypeRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,680 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.system.repository.SysLoginInfoRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,681 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.system.repository.SysMneuRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,681 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.system.repository.SysOperLogRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,681 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.system.repository.SysOrganRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,684 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.system.repository.SysRoleMenuRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,687 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.system.repository.SysRoleRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,688 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.system.repository.SysUserRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,689 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.system.repository.SysUserRoleRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:39:24,689 [INFO] [main] o.s.data.repository.config.RepositoryConfigurationDelegate [RepositoryConfigurationDelegate.java : 188] Finished Spring Data repository scanning in 65 ms. Found 0 Redis repository interfaces.
2024-12-18 09:39:25,077 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'shiroConfig' of type [com.yzm.common.config.ShiroConfig$$EnhancerBySpringCGLIB$$bca82508] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:25,577 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:25,592 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:25,896 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:26,100 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:26,103 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'redisConfig' of type [com.yzm.common.config.RedisConfig$$EnhancerBySpringCGLIB$$776ac52] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:26,185 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'redisTemplate' of type [org.springframework.data.redis.core.RedisTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:26,199 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:26,202 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'stringRedisTemplate' of type [org.springframework.data.redis.core.StringRedisTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:26,203 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'redisUtil' of type [com.yzm.common.utils.RedisUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:26,203 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'getcacheManager' of type [com.yzm.framework.shiro.cache.RedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:26,257 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#508ad266' of type [org.springframework.beans.factory.config.PropertiesFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:26,258 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#508ad266' of type [java.util.Properties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:26,261 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#538a2f0e' of type [org.springframework.data.repository.core.support.PropertiesBasedNamedQueries] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:26,266 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#6cbb7a7d' of type [org.springframework.data.repository.core.support.RepositoryFragmentsFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:26,267 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#6cbb7a7d' of type [org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:26,270 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'org.springframework.boot.autoconfigure.cache.RedisCacheConfiguration' of type [org.springframework.boot.autoconfigure.cache.RedisCacheConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:26,276 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'spring.cache-org.springframework.boot.autoconfigure.cache.CacheProperties' of type [org.springframework.boot.autoconfigure.cache.CacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:26,277 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration' of type [org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:26,279 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'cacheManagerCustomizers' of type [org.springframework.boot.autoconfigure.cache.CacheManagerCustomizers] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:26,291 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'cacheManager' of type [org.springframework.data.redis.cache.RedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:26,293 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceConfiguration$Hikari' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceConfiguration$Hikari] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:26,306 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:26,392 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'dataSource' of type [com.zaxxer.hikari.HikariDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:26,404 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'spring.jpa-org.springframework.boot.autoconfigure.orm.jpa.JpaProperties' of type [org.springframework.boot.autoconfigure.orm.jpa.JpaProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:26,407 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'spring.jpa.hibernate-org.springframework.boot.autoconfigure.orm.jpa.HibernateProperties' of type [org.springframework.boot.autoconfigure.orm.jpa.HibernateProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:26,411 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration$HikariPoolDataSourceMetadataProviderConfiguration' of type [org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration$HikariPoolDataSourceMetadataProviderConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:26,424 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'hikariPoolDataSourceMetadataProvider' of type [org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration$HikariPoolDataSourceMetadataProviderConfiguration$$Lambda$583/293669143] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:26,427 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaConfiguration' of type [org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:26,465 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'jpaVendorAdapter' of type [org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:26,470 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'entityManagerFactoryBuilder' of type [org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:26,527 [INFO] [main] org.hibernate.jpa.internal.util.LogHelper [LogHelper.java : 31] HHH000204: Processing PersistenceUnitInfo [name: default]
2024-12-18 09:39:26,566 [INFO] [main] org.hibernate.Version [Version.java : 44] HHH000412: Hibernate ORM core version 5.4.30.Final
2024-12-18 09:39:26,669 [INFO] [main] org.hibernate.annotations.common.Version [JavaReflectionManager.java : 56] HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2024-12-18 09:39:26,749 [INFO] [main] com.zaxxer.hikari.HikariDataSource [HikariDataSource.java : 110] HikariPool-1 - Starting...
2024-12-18 09:39:26,923 [INFO] [main] com.zaxxer.hikari.HikariDataSource [HikariDataSource.java : 123] HikariPool-1 - Start completed.
2024-12-18 09:39:26,936 [INFO] [main] org.hibernate.dialect.Dialect [Dialect.java : 175] HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2024-12-18 09:39:27,439 [WARN] [main] org.hibernate.orm.deprecation [SessionFactoryOptionsBuilder.java : 547] HHH90000021: Encountered deprecated setting [hibernate.ejb.interceptor], use [hibernate.session_factory.interceptor] instead
2024-12-18 09:39:28,791 [INFO] [main] o.h.e.transaction.jta.platform.internal.JtaPlatformInitiator [JtaPlatformInitiator.java : 52] HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2024-12-18 09:39:28,809 [INFO] [main] o.s.orm.jpa.LocalContainerEntityManagerFactoryBean [AbstractEntityManagerFactoryBean.java : 437] Initialized JPA EntityManagerFactory for persistence unit 'default'
2024-12-18 09:39:28,852 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'entityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:28,855 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'entityManagerFactory' of type [com.sun.proxy.$Proxy89] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:28,901 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#34279b8a' of type [com.sun.proxy.$Proxy90] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:28,949 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'jpaMappingContext' of type [org.springframework.data.jpa.repository.config.JpaMetamodelMappingContextFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:28,950 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'jpaMappingContext' of type [org.springframework.data.jpa.mapping.JpaMetamodelMappingContext] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:29,638 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'sysUserRepository' of type [org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:29,641 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'sysUserRepository' of type [com.sun.proxy.$Proxy97] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:29,662 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#398f97ae' of type [org.springframework.beans.factory.config.PropertiesFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:29,662 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#398f97ae' of type [java.util.Properties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:29,663 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#67b920c9' of type [org.springframework.data.repository.core.support.PropertiesBasedNamedQueries] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:29,664 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#7a986079' of type [org.springframework.data.repository.core.support.RepositoryFragmentsFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:29,665 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#7a986079' of type [org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:29,667 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#65354578' of type [com.sun.proxy.$Proxy90] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:29,728 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'sysMneuRepository' of type [org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:29,728 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'sysMneuRepository' of type [com.sun.proxy.$Proxy98] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:29,732 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#20c3be4c' of type [org.springframework.beans.factory.config.PropertiesFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:29,732 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#20c3be4c' of type [java.util.Properties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:29,733 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#7bc342f6' of type [org.springframework.data.repository.core.support.PropertiesBasedNamedQueries] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:29,733 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#7dae9ff4' of type [org.springframework.data.repository.core.support.RepositoryFragmentsFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:29,733 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#7dae9ff4' of type [org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:29,734 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#1fb029e' of type [com.sun.proxy.$Proxy90] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:29,768 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'sysUserRoleRepository' of type [org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:29,768 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'sysUserRoleRepository' of type [com.sun.proxy.$Proxy99] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:29,771 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'sysUserRoleServiceImpl' of type [com.yzm.property.system.service.impl.SysUserRoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:29,774 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#3fc736c4' of type [org.springframework.beans.factory.config.PropertiesFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:29,775 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#3fc736c4' of type [java.util.Properties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:29,775 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#2e1a8c1a' of type [org.springframework.data.repository.core.support.PropertiesBasedNamedQueries] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:29,775 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#38db8089' of type [org.springframework.data.repository.core.support.RepositoryFragmentsFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:29,775 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#38db8089' of type [org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:29,778 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#5fe9851f' of type [com.sun.proxy.$Proxy90] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:29,809 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'sysOrganRepository' of type [org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:29,810 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'sysOrganRepository' of type [com.sun.proxy.$Proxy102] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:29,810 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'sysOrganServiceImpl' of type [com.yzm.property.system.service.impl.SysOrganServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:29,811 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'sysUserServiceImpl' of type [com.yzm.property.system.service.impl.SysUserServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:29,811 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'loginService' of type [com.yzm.framework.shiro.service.LoginService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:29,811 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'userRealm' of type [com.yzm.framework.shiro.realm.UserRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:29,816 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'sessionListener' of type [com.yzm.framework.shiro.session.ShiroSessionListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:29,819 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'sessionDAO' of type [com.yzm.framework.shiro.session.RedisSessionDAO] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:29,823 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'cookieDAO' of type [org.apache.shiro.web.servlet.SimpleCookie] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:29,826 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'sessionManager' of type [com.yzm.framework.shiro.session.ShiroSessionManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:29,829 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:29,852 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:30,008 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'redisCacheCconfig' of type [com.yzm.common.config.RedisCacheCconfig$$EnhancerBySpringCGLIB$$e561ad73] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:39:30,404 [INFO] [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer [TomcatWebServer.java : 108] Tomcat initialized with port(s): 18034 (https)
2024-12-18 09:39:30,413 [INFO] [main] org.apache.catalina.core.StandardService [DirectJDKLog.java : 173] Starting service [Tomcat]
2024-12-18 09:39:30,414 [INFO] [main] org.apache.catalina.core.StandardEngine [DirectJDKLog.java : 173] Starting Servlet engine: [Apache Tomcat/9.0.45]
2024-12-18 09:39:30,523 [INFO] [main] o.a.catalina.core.ContainerBase.[Tomcat].[localhost].[/wms] [DirectJDKLog.java : 173] Initializing Spring embedded WebApplicationContext
2024-12-18 09:39:30,524 [INFO] [main] o.s.b.web.servlet.context.ServletWebServerApplicationContext [ServletWebServerApplicationContext.java : 289] Root WebApplicationContext: initialization completed in 7743 ms
2024-12-18 09:39:36,344 [INFO] [main] o.s.b.f.annotation.AutowiredAnnotationBeanPostProcessor [AutowiredAnnotationBeanPostProcessor.java : 502] Autowired annotation should only be used on methods with parameters: public com.yzm.framework.base.BaseRepository com.yzm.property.materials.service.impl.OutWarehouseMaterialsImpl.getRepository()
2024-12-18 09:39:37,056 [INFO] [main] o.s.scheduling.concurrent.ThreadPoolTaskExecutor [ExecutorConfigurationSupport.java : 181] Initializing ExecutorService 'threadPoolTaskExecutor'
2024-12-18 09:39:37,649 [INFO] [main] o.s.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping [WelcomePageHandlerMapping.java : 57] Adding welcome page template: index
2024-12-18 09:39:38,350 [INFO] [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer [TomcatWebServer.java : 220] Tomcat started on port(s): 18034 (https) with context path '/wms'
2024-12-18 09:39:38,379 [INFO] [main] com.yzm.StorageManagementApplication [StartupInfoLogger.java : 61] Started StorageManagementApplication in 16.311 seconds (JVM running for 17.225)
2024-12-18 09:39:43,074 [INFO] [https-jsse-nio-18034-exec-8] o.a.catalina.core.ContainerBase.[Tomcat].[localhost].[/wms] [DirectJDKLog.java : 173] Initializing Spring DispatcherServlet 'dispatcherServlet'
2024-12-18 09:39:43,077 [INFO] [https-jsse-nio-18034-exec-8] org.springframework.web.servlet.DispatcherServlet [FrameworkServlet.java : 525] Initializing Servlet 'dispatcherServlet'
2024-12-18 09:39:43,094 [INFO] [https-jsse-nio-18034-exec-8] org.springframework.web.servlet.DispatcherServlet [FrameworkServlet.java : 547] Completed initialization in 17 ms
2024-12-18 09:39:43,113 [INFO] [https-jsse-nio-18034-exec-8] o.apache.shiro.session.mgt.AbstractValidatingSessionManager [AbstractValidatingSessionManager.java : 233] Enabling session validation scheduler...
2024-12-18 09:39:43,405 [WARN] [https-jsse-nio-18034-exec-8] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:44,284 [WARN] [https-jsse-nio-18034-exec-9] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:44,366 [WARN] [https-jsse-nio-18034-exec-10] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:44,374 [WARN] [https-jsse-nio-18034-exec-6] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:44,374 [WARN] [https-jsse-nio-18034-exec-1] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:44,374 [WARN] [https-jsse-nio-18034-exec-11] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:44,374 [WARN] [https-jsse-nio-18034-exec-2] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:44,420 [WARN] [https-jsse-nio-18034-exec-2] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:44,421 [WARN] [https-jsse-nio-18034-exec-1] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:44,423 [WARN] [https-jsse-nio-18034-exec-6] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:44,426 [WARN] [https-jsse-nio-18034-exec-9] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:44,434 [WARN] [https-jsse-nio-18034-exec-5] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:44,435 [WARN] [https-jsse-nio-18034-exec-7] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:44,462 [WARN] [https-jsse-nio-18034-exec-11] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:44,462 [WARN] [https-jsse-nio-18034-exec-1] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:44,466 [WARN] [https-jsse-nio-18034-exec-10] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:44,468 [WARN] [https-jsse-nio-18034-exec-8] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:44,466 [WARN] [https-jsse-nio-18034-exec-4] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:44,478 [WARN] [https-jsse-nio-18034-exec-3] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:44,490 [WARN] [https-jsse-nio-18034-exec-4] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:44,493 [WARN] [https-jsse-nio-18034-exec-2] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:44,493 [WARN] [https-jsse-nio-18034-exec-10] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:44,495 [WARN] [https-jsse-nio-18034-exec-11] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:44,496 [WARN] [https-jsse-nio-18034-exec-1] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:44,498 [WARN] [https-jsse-nio-18034-exec-5] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:44,534 [WARN] [https-jsse-nio-18034-exec-5] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:44,545 [WARN] [https-jsse-nio-18034-exec-2] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:44,546 [WARN] [https-jsse-nio-18034-exec-4] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:44,553 [WARN] [https-jsse-nio-18034-exec-10] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:44,561 [WARN] [https-jsse-nio-18034-exec-7] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:44,563 [WARN] [https-jsse-nio-18034-exec-1] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:44,564 [WARN] [https-jsse-nio-18034-exec-11] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:44,569 [WARN] [https-jsse-nio-18034-exec-4] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:44,595 [WARN] [https-jsse-nio-18034-exec-6] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:44,642 [WARN] [https-jsse-nio-18034-exec-9] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:44,647 [WARN] [https-jsse-nio-18034-exec-8] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:44,684 [WARN] [https-jsse-nio-18034-exec-3] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:44,698 [WARN] [https-jsse-nio-18034-exec-5] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:44,727 [WARN] [https-jsse-nio-18034-exec-1] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:44,981 [WARN] [https-jsse-nio-18034-exec-4] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:44,981 [WARN] [https-jsse-nio-18034-exec-2] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:44,981 [WARN] [https-jsse-nio-18034-exec-11] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:44,984 [WARN] [https-jsse-nio-18034-exec-7] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:44,984 [WARN] [https-jsse-nio-18034-exec-10] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:44,984 [WARN] [https-jsse-nio-18034-exec-6] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:45,044 [WARN] [https-jsse-nio-18034-exec-9] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:45,044 [WARN] [https-jsse-nio-18034-exec-7] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:45,044 [WARN] [https-jsse-nio-18034-exec-8] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:45,045 [WARN] [https-jsse-nio-18034-exec-2] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:45,051 [WARN] [https-jsse-nio-18034-exec-3] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:45,051 [WARN] [https-jsse-nio-18034-exec-5] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:45,100 [WARN] [https-jsse-nio-18034-exec-5] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:45,100 [WARN] [https-jsse-nio-18034-exec-7] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:45,100 [WARN] [https-jsse-nio-18034-exec-8] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:45,100 [WARN] [https-jsse-nio-18034-exec-9] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:45,111 [WARN] [https-jsse-nio-18034-exec-2] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:45,114 [WARN] [https-jsse-nio-18034-exec-3] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:45,131 [WARN] [https-jsse-nio-18034-exec-11] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:45,131 [WARN] [https-jsse-nio-18034-exec-1] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:45,131 [WARN] [https-jsse-nio-18034-exec-6] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:45,131 [WARN] [https-jsse-nio-18034-exec-4] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:45,132 [WARN] [https-jsse-nio-18034-exec-3] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:45,142 [WARN] [https-jsse-nio-18034-exec-10] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:45,146 [WARN] [https-jsse-nio-18034-exec-9] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:45,146 [WARN] [https-jsse-nio-18034-exec-1] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:45,147 [WARN] [https-jsse-nio-18034-exec-11] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:45,160 [WARN] [https-jsse-nio-18034-exec-3] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:45,161 [WARN] [https-jsse-nio-18034-exec-8] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:45,161 [WARN] [https-jsse-nio-18034-exec-10] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:45,186 [WARN] [https-jsse-nio-18034-exec-5] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:45,210 [WARN] [https-jsse-nio-18034-exec-7] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:39:45,213 [WARN] [https-jsse-nio-18034-exec-2] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:43:00,857 [WARN] [https-jsse-nio-18034-exec-1] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:43:01,099 [WARN] [https-jsse-nio-18034-exec-8] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:43:01,136 [WARN] [https-jsse-nio-18034-exec-11] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:43:26,755 [WARN] [https-jsse-nio-18034-exec-1] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:43:27,051 [WARN] [https-jsse-nio-18034-exec-8] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:43:27,088 [WARN] [https-jsse-nio-18034-exec-11] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:43:27,943 [WARN] [https-jsse-nio-18034-exec-6] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:43:28,418 [WARN] [https-jsse-nio-18034-exec-3] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:43:28,462 [WARN] [https-jsse-nio-18034-exec-1] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:43:28,462 [WARN] [https-jsse-nio-18034-exec-4] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:43:28,462 [WARN] [https-jsse-nio-18034-exec-11] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:43:28,463 [WARN] [https-jsse-nio-18034-exec-9] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:43:28,514 [WARN] [https-jsse-nio-18034-exec-5] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:43:34,411 [WARN] [https-jsse-nio-18034-exec-7] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:43:34,575 [WARN] [https-jsse-nio-18034-exec-2] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:43:34,602 [WARN] [https-jsse-nio-18034-exec-11] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:43:41,738 [WARN] [https-jsse-nio-18034-exec-7] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:44:14,008 [WARN] [https-jsse-nio-18034-exec-5] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:44:14,726 [WARN] [https-jsse-nio-18034-exec-8] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:44:14,735 [WARN] [https-jsse-nio-18034-exec-1] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:44:14,771 [WARN] [https-jsse-nio-18034-exec-4] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:44:17,287 [WARN] [https-jsse-nio-18034-exec-5] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:44:17,805 [WARN] [https-jsse-nio-18034-exec-7] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:44:19,639 [WARN] [https-jsse-nio-18034-exec-2] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:44:22,596 [WARN] [https-jsse-nio-18034-exec-6] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:44:46,151 [WARN] [https-jsse-nio-18034-exec-11] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:45:49,598 [WARN] [https-jsse-nio-18034-exec-5] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:45:49,923 [WARN] [https-jsse-nio-18034-exec-7] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:45:49,953 [WARN] [https-jsse-nio-18034-exec-11] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:45:49,974 [WARN] [https-jsse-nio-18034-exec-6] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:45:50,026 [WARN] [https-jsse-nio-18034-exec-1] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:45:50,718 [WARN] [https-jsse-nio-18034-exec-4] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:45:50,911 [WARN] [https-jsse-nio-18034-exec-5] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:45:50,928 [WARN] [https-jsse-nio-18034-exec-2] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:46:01,219 [WARN] [https-jsse-nio-18034-exec-11] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:46:01,219 [WARN] [https-jsse-nio-18034-exec-2] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:46:01,498 [WARN] [https-jsse-nio-18034-exec-5] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:46:01,522 [WARN] [https-jsse-nio-18034-exec-4] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:46:16,504 [WARN] [https-jsse-nio-18034-exec-5] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:46:16,692 [WARN] [https-jsse-nio-18034-exec-4] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:46:16,711 [WARN] [https-jsse-nio-18034-exec-1] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:51:58,867 [WARN] [https-jsse-nio-18034-exec-5] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:52:00,467 [WARN] [https-jsse-nio-18034-exec-9] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:52:26,724 [WARN] [https-jsse-nio-18034-exec-3] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:52:26,964 [WARN] [https-jsse-nio-18034-exec-2] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:52:27,144 [WARN] [https-jsse-nio-18034-exec-3] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:52:27,151 [WARN] [https-jsse-nio-18034-exec-6] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:52:27,151 [WARN] [https-jsse-nio-18034-exec-8] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:52:27,151 [WARN] [https-jsse-nio-18034-exec-11] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:52:27,184 [WARN] [https-jsse-nio-18034-exec-5] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:52:27,216 [WARN] [https-jsse-nio-18034-exec-9] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:53:21,592 [WARN] [https-jsse-nio-18034-exec-7] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:53:21,842 [WARN] [https-jsse-nio-18034-exec-1] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:53:21,869 [WARN] [https-jsse-nio-18034-exec-2] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:54:07,289 [INFO] [main] com.yzm.StorageManagementApplication [StartupInfoLogger.java : 55] Starting StorageManagementApplication using Java 1.8.0_111 on yizengmingdeMacBook-Pro.local with PID 90903 (/Users/<USER>/工作/yizengming/storageManagement/target/classes started by yizengming in /Users/<USER>/工作/yizengming/storageManagement)
2024-12-18 09:54:07,292 [INFO] [main] com.yzm.StorageManagementApplication [SpringApplication.java : 679] The following profiles are active: dev
2024-12-18 09:54:08,699 [INFO] [main] o.s.data.repository.config.RepositoryConfigurationDelegate [RepositoryConfigurationDelegate.java : 250] Multiple Spring Data modules found, entering strict repository configuration mode!
2024-12-18 09:54:08,700 [INFO] [main] o.s.data.repository.config.RepositoryConfigurationDelegate [RepositoryConfigurationDelegate.java : 128] Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2024-12-18 09:54:09,027 [INFO] [main] o.s.data.repository.config.RepositoryConfigurationDelegate [RepositoryConfigurationDelegate.java : 188] Finished Spring Data repository scanning in 317 ms. Found 66 JPA repository interfaces.
2024-12-18 09:54:09,072 [INFO] [main] o.s.data.repository.config.RepositoryConfigurationDelegate [RepositoryConfigurationDelegate.java : 250] Multiple Spring Data modules found, entering strict repository configuration mode!
2024-12-18 09:54:09,073 [INFO] [main] o.s.data.repository.config.RepositoryConfigurationDelegate [RepositoryConfigurationDelegate.java : 128] Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2024-12-18 09:54:09,109 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.basis.repository.AreaRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,109 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.basis.repository.AssetsInfoRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,110 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.basis.repository.AssetsLogRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,110 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.basis.repository.BrandRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,110 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.basis.repository.DepartmentRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,111 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.basis.repository.DepartmentRoleRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,112 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.basis.repository.InventoryAssetsInfoRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,113 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.basis.repository.InventoryInfoRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,114 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.basis.repository.InventorySimpleItemRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,114 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.basis.repository.InventorySimpleRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,115 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.basis.repository.PersonnelRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,116 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.basis.repository.SupplierComparisonRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,117 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.basis.repository.TypeRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,118 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.basis.repository.UnitRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,118 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.basis.repository.WarehouseRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,119 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.business.repository.AssetsRepairsMiddleRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,119 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.business.repository.AssetsScrapMiddleRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,119 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.business.repository.BorrowReturnItemRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,119 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.business.repository.BorrowReturnRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,120 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.business.repository.BusinessRepairsItemRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,120 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.business.repository.BusinessRepairsRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,120 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.business.repository.BusinessScrapRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,120 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.business.repository.ReceiptNoteItemRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,121 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.business.repository.ReceiptNoteRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,122 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.consumable.repository.ConsumableInfoRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,123 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.consumable.repository.ConsumableListRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,123 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.consumable.repository.ConsumableRepertoryRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,123 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.consumable.repository.ConsumableStandingBookRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,123 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.consumable.repository.InStorageItemRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,123 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.consumable.repository.InStorageRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,124 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.consumable.repository.OutWarehouseItemRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,124 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.consumable.repository.OutWarehouseRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,125 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.consumable.repository.TransferItemRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,125 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.consumable.repository.TransferRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,126 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.BindingCarRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,126 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.DelinquentMaterialRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,126 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.InStorageItemMaterialsRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,127 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.InStorageMaterialsRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,128 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.IssueTempDtlItemRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,128 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.IssueTempDtlRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,128 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.IssueTempDtlTubeItemRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,129 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.IssueTempDtlTubeRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,129 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.MaterialTransitRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,129 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.MaterialsInfoBomRepeatRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,129 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.MaterialsInfoBomRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,129 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.MaterialsInfoRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,130 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.MaterialsListRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,130 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.MaterialsRepertoryFifoRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,131 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.MaterialsRepertoryRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,131 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.MaterialsStandingBookRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,131 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.OutWarehouseItemMaterialsRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,131 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.OutWarehouseMaterialsRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,131 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.TransferItemMaterialsRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,132 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.TransferMaterialsRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,132 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.statistics.repository.AssetListRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,132 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.system.repository.SysConfigRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,132 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.system.repository.SysDictDataRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,132 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.system.repository.SysDictTypeRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,133 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.system.repository.SysLoginInfoRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,133 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.system.repository.SysMneuRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,133 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.system.repository.SysOperLogRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,133 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.system.repository.SysOrganRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,133 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.system.repository.SysRoleMenuRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,134 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.system.repository.SysRoleRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,138 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.system.repository.SysUserRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,138 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.system.repository.SysUserRoleRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 09:54:09,139 [INFO] [main] o.s.data.repository.config.RepositoryConfigurationDelegate [RepositoryConfigurationDelegate.java : 188] Finished Spring Data repository scanning in 57 ms. Found 0 Redis repository interfaces.
2024-12-18 09:54:09,415 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'shiroConfig' of type [com.yzm.common.config.ShiroConfig$$EnhancerBySpringCGLIB$$1aa04cc3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:09,688 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:09,691 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:09,775 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:09,952 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:09,955 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'redisConfig' of type [com.yzm.common.config.RedisConfig$$EnhancerBySpringCGLIB$$656ed40d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:10,027 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'redisTemplate' of type [org.springframework.data.redis.core.RedisTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:10,039 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:10,042 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'stringRedisTemplate' of type [org.springframework.data.redis.core.StringRedisTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:10,042 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'redisUtil' of type [com.yzm.common.utils.RedisUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:10,043 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'getcacheManager' of type [com.yzm.framework.shiro.cache.RedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:10,091 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#66f16742' of type [org.springframework.beans.factory.config.PropertiesFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:10,092 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#66f16742' of type [java.util.Properties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:10,094 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#57186526' of type [org.springframework.data.repository.core.support.PropertiesBasedNamedQueries] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:10,097 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#67acfde9' of type [org.springframework.data.repository.core.support.RepositoryFragmentsFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:10,099 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#67acfde9' of type [org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:10,102 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'org.springframework.boot.autoconfigure.cache.RedisCacheConfiguration' of type [org.springframework.boot.autoconfigure.cache.RedisCacheConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:10,109 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'spring.cache-org.springframework.boot.autoconfigure.cache.CacheProperties' of type [org.springframework.boot.autoconfigure.cache.CacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:10,110 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration' of type [org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:10,111 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'cacheManagerCustomizers' of type [org.springframework.boot.autoconfigure.cache.CacheManagerCustomizers] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:10,121 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'cacheManager' of type [org.springframework.data.redis.cache.RedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:10,123 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceConfiguration$Hikari' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceConfiguration$Hikari] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:10,134 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:10,203 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'dataSource' of type [com.zaxxer.hikari.HikariDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:10,212 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'spring.jpa-org.springframework.boot.autoconfigure.orm.jpa.JpaProperties' of type [org.springframework.boot.autoconfigure.orm.jpa.JpaProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:10,215 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'spring.jpa.hibernate-org.springframework.boot.autoconfigure.orm.jpa.HibernateProperties' of type [org.springframework.boot.autoconfigure.orm.jpa.HibernateProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:10,218 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration$HikariPoolDataSourceMetadataProviderConfiguration' of type [org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration$HikariPoolDataSourceMetadataProviderConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:10,230 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'hikariPoolDataSourceMetadataProvider' of type [org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration$HikariPoolDataSourceMetadataProviderConfiguration$$Lambda$583/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:10,233 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaConfiguration' of type [org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:10,264 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'jpaVendorAdapter' of type [org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:10,267 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'entityManagerFactoryBuilder' of type [org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:10,314 [INFO] [main] org.hibernate.jpa.internal.util.LogHelper [LogHelper.java : 31] HHH000204: Processing PersistenceUnitInfo [name: default]
2024-12-18 09:54:10,350 [INFO] [main] org.hibernate.Version [Version.java : 44] HHH000412: Hibernate ORM core version 5.4.30.Final
2024-12-18 09:54:10,455 [INFO] [main] org.hibernate.annotations.common.Version [JavaReflectionManager.java : 56] HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2024-12-18 09:54:10,547 [INFO] [main] com.zaxxer.hikari.HikariDataSource [HikariDataSource.java : 110] HikariPool-1 - Starting...
2024-12-18 09:54:10,742 [INFO] [main] com.zaxxer.hikari.HikariDataSource [HikariDataSource.java : 123] HikariPool-1 - Start completed.
2024-12-18 09:54:10,755 [INFO] [main] org.hibernate.dialect.Dialect [Dialect.java : 175] HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2024-12-18 09:54:11,212 [WARN] [main] org.hibernate.orm.deprecation [SessionFactoryOptionsBuilder.java : 547] HHH90000021: Encountered deprecated setting [hibernate.ejb.interceptor], use [hibernate.session_factory.interceptor] instead
2024-12-18 09:54:12,428 [INFO] [main] o.h.e.transaction.jta.platform.internal.JtaPlatformInitiator [JtaPlatformInitiator.java : 52] HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2024-12-18 09:54:12,446 [INFO] [main] o.s.orm.jpa.LocalContainerEntityManagerFactoryBean [AbstractEntityManagerFactoryBean.java : 437] Initialized JPA EntityManagerFactory for persistence unit 'default'
2024-12-18 09:54:12,455 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'entityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:12,457 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'entityManagerFactory' of type [com.sun.proxy.$Proxy89] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:12,489 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#7c588adc' of type [com.sun.proxy.$Proxy90] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:12,498 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'jpaMappingContext' of type [org.springframework.data.jpa.repository.config.JpaMetamodelMappingContextFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:12,498 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'jpaMappingContext' of type [org.springframework.data.jpa.mapping.JpaMetamodelMappingContext] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:12,984 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'sysUserRepository' of type [org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:12,988 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'sysUserRepository' of type [com.sun.proxy.$Proxy97] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:13,003 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#7a08da83' of type [org.springframework.beans.factory.config.PropertiesFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:13,004 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#7a08da83' of type [java.util.Properties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:13,004 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#3d376f28' of type [org.springframework.data.repository.core.support.PropertiesBasedNamedQueries] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:13,005 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#721fbae2' of type [org.springframework.data.repository.core.support.RepositoryFragmentsFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:13,005 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#721fbae2' of type [org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:13,006 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#56c278bf' of type [com.sun.proxy.$Proxy90] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:13,052 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'sysMneuRepository' of type [org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:13,052 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'sysMneuRepository' of type [com.sun.proxy.$Proxy98] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:13,056 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#49924fc6' of type [org.springframework.beans.factory.config.PropertiesFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:13,056 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#49924fc6' of type [java.util.Properties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:13,056 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#29cbbff7' of type [org.springframework.data.repository.core.support.PropertiesBasedNamedQueries] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:13,057 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#d9ef223' of type [org.springframework.data.repository.core.support.RepositoryFragmentsFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:13,057 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#d9ef223' of type [org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:13,058 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#a9c923f' of type [com.sun.proxy.$Proxy90] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:13,090 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'sysUserRoleRepository' of type [org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:13,091 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'sysUserRoleRepository' of type [com.sun.proxy.$Proxy99] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:13,093 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'sysUserRoleServiceImpl' of type [com.yzm.property.system.service.impl.SysUserRoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:13,097 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#60d1b2a7' of type [org.springframework.beans.factory.config.PropertiesFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:13,097 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#60d1b2a7' of type [java.util.Properties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:13,097 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#27035f4f' of type [org.springframework.data.repository.core.support.PropertiesBasedNamedQueries] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:13,098 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#4a2f4bfa' of type [org.springframework.data.repository.core.support.RepositoryFragmentsFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:13,098 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#4a2f4bfa' of type [org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:13,101 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#c4e440b' of type [com.sun.proxy.$Proxy90] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:13,126 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'sysOrganRepository' of type [org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:13,126 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'sysOrganRepository' of type [com.sun.proxy.$Proxy102] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:13,127 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'sysOrganServiceImpl' of type [com.yzm.property.system.service.impl.SysOrganServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:13,127 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'sysUserServiceImpl' of type [com.yzm.property.system.service.impl.SysUserServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:13,127 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'loginService' of type [com.yzm.framework.shiro.service.LoginService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:13,128 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'userRealm' of type [com.yzm.framework.shiro.realm.UserRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:13,131 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'sessionListener' of type [com.yzm.framework.shiro.session.ShiroSessionListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:13,133 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'sessionDAO' of type [com.yzm.framework.shiro.session.RedisSessionDAO] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:13,134 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'cookieDAO' of type [org.apache.shiro.web.servlet.SimpleCookie] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:13,136 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'sessionManager' of type [com.yzm.framework.shiro.session.ShiroSessionManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:13,137 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:13,152 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:13,283 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'redisCacheCconfig' of type [com.yzm.common.config.RedisCacheCconfig$$EnhancerBySpringCGLIB$$4359d52e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 09:54:13,615 [INFO] [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer [TomcatWebServer.java : 108] Tomcat initialized with port(s): 18034 (https)
2024-12-18 09:54:13,623 [INFO] [main] org.apache.catalina.core.StandardService [DirectJDKLog.java : 173] Starting service [Tomcat]
2024-12-18 09:54:13,624 [INFO] [main] org.apache.catalina.core.StandardEngine [DirectJDKLog.java : 173] Starting Servlet engine: [Apache Tomcat/9.0.45]
2024-12-18 09:54:13,703 [INFO] [main] o.a.catalina.core.ContainerBase.[Tomcat].[localhost].[/wms] [DirectJDKLog.java : 173] Initializing Spring embedded WebApplicationContext
2024-12-18 09:54:13,704 [INFO] [main] o.s.b.web.servlet.context.ServletWebServerApplicationContext [ServletWebServerApplicationContext.java : 289] Root WebApplicationContext: initialization completed in 6311 ms
2024-12-18 09:54:19,459 [INFO] [main] o.s.b.f.annotation.AutowiredAnnotationBeanPostProcessor [AutowiredAnnotationBeanPostProcessor.java : 502] Autowired annotation should only be used on methods with parameters: public com.yzm.framework.base.BaseRepository com.yzm.property.materials.service.impl.OutWarehouseMaterialsImpl.getRepository()
2024-12-18 09:54:20,861 [INFO] [main] o.s.scheduling.concurrent.ThreadPoolTaskExecutor [ExecutorConfigurationSupport.java : 181] Initializing ExecutorService 'threadPoolTaskExecutor'
2024-12-18 09:54:21,624 [INFO] [main] o.s.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping [WelcomePageHandlerMapping.java : 57] Adding welcome page template: index
2024-12-18 09:54:22,252 [INFO] [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer [TomcatWebServer.java : 220] Tomcat started on port(s): 18034 (https) with context path '/wms'
2024-12-18 09:54:26,786 [INFO] [main] com.yzm.StorageManagementApplication [StartupInfoLogger.java : 61] Started StorageManagementApplication in 20.055 seconds (JVM running for 20.79)
2024-12-18 09:59:06,829 [INFO] [https-jsse-nio-18034-exec-6] o.a.catalina.core.ContainerBase.[Tomcat].[localhost].[/wms] [DirectJDKLog.java : 173] Initializing Spring DispatcherServlet 'dispatcherServlet'
2024-12-18 09:59:06,832 [INFO] [https-jsse-nio-18034-exec-6] org.springframework.web.servlet.DispatcherServlet [FrameworkServlet.java : 525] Initializing Servlet 'dispatcherServlet'
2024-12-18 09:59:06,840 [INFO] [https-jsse-nio-18034-exec-6] org.springframework.web.servlet.DispatcherServlet [FrameworkServlet.java : 547] Completed initialization in 8 ms
2024-12-18 09:59:06,856 [INFO] [https-jsse-nio-18034-exec-6] o.apache.shiro.session.mgt.AbstractValidatingSessionManager [AbstractValidatingSessionManager.java : 233] Enabling session validation scheduler...
2024-12-18 09:59:07,159 [WARN] [https-jsse-nio-18034-exec-6] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:59:08,090 [WARN] [https-jsse-nio-18034-exec-7] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:59:08,198 [WARN] [https-jsse-nio-18034-exec-8] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:59:50,158 [WARN] [https-jsse-nio-18034-exec-6] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:59:50,435 [WARN] [https-jsse-nio-18034-exec-7] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:59:50,463 [WARN] [https-jsse-nio-18034-exec-8] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:59:50,489 [WARN] [https-jsse-nio-18034-exec-8] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:59:50,506 [WARN] [https-jsse-nio-18034-exec-6] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:59:52,911 [WARN] [https-jsse-nio-18034-exec-6] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 09:59:59,091 [WARN] [https-jsse-nio-18034-exec-7] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 10:00:14,045 [INFO] [schedule-pool-1] com.yzm.property.materials.service.impl.IssueTempDtlImpl [IssueTempDtlImpl.java : 323] *********************************************************************************************************************************************************************************************@
2024-12-18 10:00:14,339 [INFO] [schedule-pool-1] com.yzm.property.materials.service.impl.IssueTempDtlImpl [IssueTempDtlImpl.java : 328] Connection established successfully.
2024-12-18 10:00:14,400 [ERROR] [schedule-pool-1] com.yzm.property.materials.service.impl.IssueTempDtlImpl [IssueTempDtlImpl.java : 370] Error while executing query: FUNCTION storage_management.DATEADD does not exist
2024-12-18 10:00:14,401 [INFO] [schedule-pool-1] com.yzm.property.materials.service.impl.IssueTempDtlImpl [IssueTempDtlImpl.java : 375] Connection closed successfully.
2024-12-18 10:00:14,401 [INFO] [schedule-pool-1] com.yzm.property.materials.service.impl.IssueTempDtlImpl [IssueTempDtlImpl.java : 494] ==========查询到单据==================0
2024-12-18 10:00:14,484 [INFO] [schedule-pool-1] com.yzm.property.materials.service.impl.IssueTempDtlImpl [IssueTempDtlImpl.java : 270] getIssueTempDtlTube==============*********************************************************************************************************************************************************************************************@
2024-12-18 10:00:14,780 [INFO] [schedule-pool-1] com.yzm.property.materials.service.impl.IssueTempDtlImpl [IssueTempDtlImpl.java : 275] Connection established successfully.
2024-12-18 10:00:14,820 [ERROR] [schedule-pool-1] com.yzm.property.materials.service.impl.IssueTempDtlImpl [IssueTempDtlImpl.java : 305] Error while executing query: Table 'storage_management.b3e_bom_temp_tube' doesn't exist
2024-12-18 10:00:14,821 [INFO] [schedule-pool-1] com.yzm.property.materials.service.impl.IssueTempDtlImpl [IssueTempDtlImpl.java : 310] Connection closed successfully.
2024-12-18 10:00:14,821 [INFO] [schedule-pool-1] com.yzm.property.materials.service.impl.IssueTempDtlImpl [IssueTempDtlImpl.java : 404] ==========查询到单据胶管数据==================0
2024-12-18 10:00:14,841 [INFO] [schedule-pool-1] com.yzm.property.materials.service.impl.IssueTempDtlImpl [IssueTempDtlImpl.java : 747] ---------------开始运行发票----------------
2024-12-18 10:00:14,841 [INFO] [schedule-pool-1] com.yzm.property.materials.service.impl.IssueTempDtlImpl [IssueTempDtlImpl.java : 647] ***********************************************************************************************************************
2024-12-18 10:01:15,748 [ERROR] [schedule-pool-1] com.yzm.property.materials.service.impl.IssueTempDtlImpl [IssueTempDtlImpl.java : 682] Error while executing query: 无法连接到命名实例 MSSQLSERVER2019 上的主机 172.19.60.188。错误:“java.net.SocketTimeoutException: Receive timed out”。请验证服务器和实例名称是否正确无误，并检查是否没有防火墙在阻止流向端口 1434 的 UDP 流量。对于 SQL Server 2005 或更高版本，请验证 SQL Server Browser 服务是否正在主机上运行。
2024-12-18 10:02:39,255 [WARN] [https-jsse-nio-18034-exec-2] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 10:02:40,710 [WARN] [https-jsse-nio-18034-exec-5] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 10:02:42,024 [WARN] [https-jsse-nio-18034-exec-10] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 10:02:42,254 [WARN] [https-jsse-nio-18034-exec-2] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 10:02:42,288 [WARN] [https-jsse-nio-18034-exec-5] com.yzm.framework.shiro.session.RedisSessionDAO [RedisSessionDAO.java : 125] Redis session expire time: -59900000 is less than Session timeout: -60000 . It may cause some problems.
2024-12-18 10:08:18,021 [INFO] [main] com.yzm.StorageManagementApplication [StartupInfoLogger.java : 55] Starting StorageManagementApplication using Java 1.8.0_111 on yizengmingdeMacBook-Pro.local with PID 91170 (/Users/<USER>/工作/yizengming/storageManagement/target/classes started by yizengming in /Users/<USER>/工作/yizengming/storageManagement)
2024-12-18 10:08:18,024 [INFO] [main] com.yzm.StorageManagementApplication [SpringApplication.java : 679] The following profiles are active: dev
2024-12-18 10:08:19,502 [INFO] [main] o.s.data.repository.config.RepositoryConfigurationDelegate [RepositoryConfigurationDelegate.java : 250] Multiple Spring Data modules found, entering strict repository configuration mode!
2024-12-18 10:08:19,503 [INFO] [main] o.s.data.repository.config.RepositoryConfigurationDelegate [RepositoryConfigurationDelegate.java : 128] Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2024-12-18 10:08:19,864 [INFO] [main] o.s.data.repository.config.RepositoryConfigurationDelegate [RepositoryConfigurationDelegate.java : 188] Finished Spring Data repository scanning in 352 ms. Found 66 JPA repository interfaces.
2024-12-18 10:08:19,920 [INFO] [main] o.s.data.repository.config.RepositoryConfigurationDelegate [RepositoryConfigurationDelegate.java : 250] Multiple Spring Data modules found, entering strict repository configuration mode!
2024-12-18 10:08:19,921 [INFO] [main] o.s.data.repository.config.RepositoryConfigurationDelegate [RepositoryConfigurationDelegate.java : 128] Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2024-12-18 10:08:19,965 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.basis.repository.AreaRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,966 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.basis.repository.AssetsInfoRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,966 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.basis.repository.AssetsLogRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,967 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.basis.repository.BrandRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,967 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.basis.repository.DepartmentRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,967 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.basis.repository.DepartmentRoleRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,968 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.basis.repository.InventoryAssetsInfoRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,970 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.basis.repository.InventoryInfoRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,971 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.basis.repository.InventorySimpleItemRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,972 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.basis.repository.InventorySimpleRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,972 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.basis.repository.PersonnelRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,974 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.basis.repository.SupplierComparisonRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,977 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.basis.repository.TypeRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,977 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.basis.repository.UnitRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,977 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.basis.repository.WarehouseRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,978 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.business.repository.AssetsRepairsMiddleRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,978 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.business.repository.AssetsScrapMiddleRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,979 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.business.repository.BorrowReturnItemRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,979 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.business.repository.BorrowReturnRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,979 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.business.repository.BusinessRepairsItemRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,980 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.business.repository.BusinessRepairsRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,980 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.business.repository.BusinessScrapRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,980 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.business.repository.ReceiptNoteItemRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,981 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.business.repository.ReceiptNoteRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,982 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.consumable.repository.ConsumableInfoRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,982 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.consumable.repository.ConsumableListRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,982 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.consumable.repository.ConsumableRepertoryRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,983 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.consumable.repository.ConsumableStandingBookRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,983 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.consumable.repository.InStorageItemRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,983 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.consumable.repository.InStorageRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,984 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.consumable.repository.OutWarehouseItemRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,984 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.consumable.repository.OutWarehouseRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,985 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.consumable.repository.TransferItemRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,985 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.consumable.repository.TransferRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,985 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.BindingCarRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,986 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.DelinquentMaterialRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,986 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.InStorageItemMaterialsRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,987 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.InStorageMaterialsRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,987 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.IssueTempDtlItemRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,988 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.IssueTempDtlRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,988 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.IssueTempDtlTubeItemRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,988 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.IssueTempDtlTubeRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,988 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.MaterialTransitRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,989 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.MaterialsInfoBomRepeatRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,989 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.MaterialsInfoBomRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,989 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.MaterialsInfoRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,990 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.MaterialsListRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,990 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.MaterialsRepertoryFifoRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,991 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.MaterialsRepertoryRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,991 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.MaterialsStandingBookRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,991 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.OutWarehouseItemMaterialsRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,991 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.OutWarehouseMaterialsRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,991 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.TransferItemMaterialsRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,992 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.materials.repository.TransferMaterialsRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,992 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.statistics.repository.AssetListRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,992 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.system.repository.SysConfigRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,992 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.system.repository.SysDictDataRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,993 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.system.repository.SysDictTypeRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,993 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.system.repository.SysLoginInfoRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,993 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.system.repository.SysMneuRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,993 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.system.repository.SysOperLogRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,994 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.system.repository.SysOrganRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,997 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.system.repository.SysRoleMenuRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,998 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.system.repository.SysRoleRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,998 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.system.repository.SysUserRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,999 [INFO] [main] o.s.d.r.config.RepositoryConfigurationExtensionSupport [RepositoryConfigurationExtensionSupport.java : 349] Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.yzm.property.system.repository.SysUserRoleRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2024-12-18 10:08:19,999 [INFO] [main] o.s.data.repository.config.RepositoryConfigurationDelegate [RepositoryConfigurationDelegate.java : 188] Finished Spring Data repository scanning in 69 ms. Found 0 Redis repository interfaces.
2024-12-18 10:08:20,308 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'shiroConfig' of type [com.yzm.common.config.ShiroConfig$$EnhancerBySpringCGLIB$$dc5d93c1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:20,606 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:20,611 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:20,704 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:20,880 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:20,883 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'redisConfig' of type [com.yzm.common.config.RedisConfig$$EnhancerBySpringCGLIB$$272c1b0b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:20,957 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'redisTemplate' of type [org.springframework.data.redis.core.RedisTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:20,972 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:20,974 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'stringRedisTemplate' of type [org.springframework.data.redis.core.StringRedisTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:20,975 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'redisUtil' of type [com.yzm.common.utils.RedisUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:20,976 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'getcacheManager' of type [com.yzm.framework.shiro.cache.RedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:21,029 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#703fa45' of type [org.springframework.beans.factory.config.PropertiesFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:21,030 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#703fa45' of type [java.util.Properties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:21,033 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#62a6674f' of type [org.springframework.data.repository.core.support.PropertiesBasedNamedQueries] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:21,037 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#4a2bf50f' of type [org.springframework.data.repository.core.support.RepositoryFragmentsFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:21,037 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#4a2bf50f' of type [org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:21,040 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'org.springframework.boot.autoconfigure.cache.RedisCacheConfiguration' of type [org.springframework.boot.autoconfigure.cache.RedisCacheConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:21,047 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'spring.cache-org.springframework.boot.autoconfigure.cache.CacheProperties' of type [org.springframework.boot.autoconfigure.cache.CacheProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:21,048 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration' of type [org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:21,050 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'cacheManagerCustomizers' of type [org.springframework.boot.autoconfigure.cache.CacheManagerCustomizers] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:21,061 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'cacheManager' of type [org.springframework.data.redis.cache.RedisCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:21,062 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceConfiguration$Hikari' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceConfiguration$Hikari] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:21,082 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:21,205 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'dataSource' of type [com.zaxxer.hikari.HikariDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:21,224 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'spring.jpa-org.springframework.boot.autoconfigure.orm.jpa.JpaProperties' of type [org.springframework.boot.autoconfigure.orm.jpa.JpaProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:21,227 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'spring.jpa.hibernate-org.springframework.boot.autoconfigure.orm.jpa.HibernateProperties' of type [org.springframework.boot.autoconfigure.orm.jpa.HibernateProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:21,232 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration$HikariPoolDataSourceMetadataProviderConfiguration' of type [org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration$HikariPoolDataSourceMetadataProviderConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:21,246 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'hikariPoolDataSourceMetadataProvider' of type [org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration$HikariPoolDataSourceMetadataProviderConfiguration$$Lambda$583/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:21,252 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaConfiguration' of type [org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:21,289 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'jpaVendorAdapter' of type [org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:21,292 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'entityManagerFactoryBuilder' of type [org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:21,346 [INFO] [main] org.hibernate.jpa.internal.util.LogHelper [LogHelper.java : 31] HHH000204: Processing PersistenceUnitInfo [name: default]
2024-12-18 10:08:21,386 [INFO] [main] org.hibernate.Version [Version.java : 44] HHH000412: Hibernate ORM core version 5.4.30.Final
2024-12-18 10:08:21,479 [INFO] [main] org.hibernate.annotations.common.Version [JavaReflectionManager.java : 56] HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2024-12-18 10:08:21,562 [INFO] [main] com.zaxxer.hikari.HikariDataSource [HikariDataSource.java : 110] HikariPool-1 - Starting...
2024-12-18 10:08:21,746 [INFO] [main] com.zaxxer.hikari.HikariDataSource [HikariDataSource.java : 123] HikariPool-1 - Start completed.
2024-12-18 10:08:21,764 [INFO] [main] org.hibernate.dialect.Dialect [Dialect.java : 175] HHH000400: Using dialect: org.hibernate.dialect.MySQL5InnoDBDialect
2024-12-18 10:08:22,340 [WARN] [main] org.hibernate.orm.deprecation [SessionFactoryOptionsBuilder.java : 547] HHH90000021: Encountered deprecated setting [hibernate.ejb.interceptor], use [hibernate.session_factory.interceptor] instead
2024-12-18 10:08:23,870 [INFO] [main] o.h.e.transaction.jta.platform.internal.JtaPlatformInitiator [JtaPlatformInitiator.java : 52] HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2024-12-18 10:08:23,883 [INFO] [main] o.s.orm.jpa.LocalContainerEntityManagerFactoryBean [AbstractEntityManagerFactoryBean.java : 437] Initialized JPA EntityManagerFactory for persistence unit 'default'
2024-12-18 10:08:23,893 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'entityManagerFactory' of type [org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:23,894 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'entityManagerFactory' of type [com.sun.proxy.$Proxy89] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:23,933 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#6b70d1fb' of type [com.sun.proxy.$Proxy90] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:23,943 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'jpaMappingContext' of type [org.springframework.data.jpa.repository.config.JpaMetamodelMappingContextFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:23,944 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'jpaMappingContext' of type [org.springframework.data.jpa.mapping.JpaMetamodelMappingContext] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:24,553 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'sysUserRepository' of type [org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:24,555 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'sysUserRepository' of type [com.sun.proxy.$Proxy97] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:24,573 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#105c3bea' of type [org.springframework.beans.factory.config.PropertiesFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:24,573 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#105c3bea' of type [java.util.Properties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:24,574 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#75fa16cc' of type [org.springframework.data.repository.core.support.PropertiesBasedNamedQueries] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:24,575 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#1fd18007' of type [org.springframework.data.repository.core.support.RepositoryFragmentsFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:24,575 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#1fd18007' of type [org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:24,576 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#b78dcdf' of type [com.sun.proxy.$Proxy90] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:24,636 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'sysMneuRepository' of type [org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:24,637 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'sysMneuRepository' of type [com.sun.proxy.$Proxy98] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:24,642 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#5fe9851f' of type [org.springframework.beans.factory.config.PropertiesFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:24,642 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#5fe9851f' of type [java.util.Properties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:24,642 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#6183dd2' of type [org.springframework.data.repository.core.support.PropertiesBasedNamedQueries] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:24,643 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#6d5ef5ee' of type [org.springframework.data.repository.core.support.RepositoryFragmentsFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:24,643 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#6d5ef5ee' of type [org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:24,644 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#ff03384' of type [com.sun.proxy.$Proxy90] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:24,679 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'sysUserRoleRepository' of type [org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:24,679 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'sysUserRoleRepository' of type [com.sun.proxy.$Proxy99] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:24,682 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'sysUserRoleServiceImpl' of type [com.yzm.property.system.service.impl.SysUserRoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:24,686 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#2463a9ec' of type [org.springframework.beans.factory.config.PropertiesFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:24,687 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#2463a9ec' of type [java.util.Properties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:24,687 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#6b951ee5' of type [org.springframework.data.repository.core.support.PropertiesBasedNamedQueries] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:24,687 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#57d9b564' of type [org.springframework.data.repository.core.support.RepositoryFragmentsFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:24,688 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#57d9b564' of type [org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:24,690 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean '(inner bean)#2149532d' of type [com.sun.proxy.$Proxy90] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:24,720 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'sysOrganRepository' of type [org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:24,721 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'sysOrganRepository' of type [com.sun.proxy.$Proxy102] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:24,722 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'sysOrganServiceImpl' of type [com.yzm.property.system.service.impl.SysOrganServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:24,722 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'sysUserServiceImpl' of type [com.yzm.property.system.service.impl.SysUserServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:24,723 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'loginService' of type [com.yzm.framework.shiro.service.LoginService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:24,723 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'userRealm' of type [com.yzm.framework.shiro.realm.UserRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:24,727 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'sessionListener' of type [com.yzm.framework.shiro.session.ShiroSessionListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:24,731 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'sessionDAO' of type [com.yzm.framework.shiro.session.RedisSessionDAO] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:24,733 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'cookieDAO' of type [org.apache.shiro.web.servlet.SimpleCookie] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:24,735 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'sessionManager' of type [com.yzm.framework.shiro.session.ShiroSessionManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:24,737 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:24,755 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:24,922 [INFO] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 376] Bean 'redisCacheCconfig' of type [com.yzm.common.config.RedisCacheCconfig$$EnhancerBySpringCGLIB$$5171c2c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2024-12-18 10:08:25,335 [INFO] [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer [TomcatWebServer.java : 108] Tomcat initialized with port(s): 18034 (https)
2024-12-18 10:08:25,346 [INFO] [main] org.apache.catalina.core.StandardService [DirectJDKLog.java : 173] Starting service [Tomcat]
2024-12-18 10:08:25,346 [INFO] [main] org.apache.catalina.core.StandardEngine [DirectJDKLog.java : 173] Starting Servlet engine: [Apache Tomcat/9.0.45]
2024-12-18 10:08:25,433 [INFO] [main] o.a.catalina.core.ContainerBase.[Tomcat].[localhost].[/wms] [DirectJDKLog.java : 173] Initializing Spring embedded WebApplicationContext
2024-12-18 10:08:25,433 [INFO] [main] o.s.b.web.servlet.context.ServletWebServerApplicationContext [ServletWebServerApplicationContext.java : 289] Root WebApplicationContext: initialization completed in 7295 ms
2024-12-18 10:08:31,522 [INFO] [main] o.s.b.f.annotation.AutowiredAnnotationBeanPostProcessor [AutowiredAnnotationBeanPostProcessor.java : 502] Autowired annotation should only be used on methods with parameters: public com.yzm.framework.base.BaseRepository com.yzm.property.materials.service.impl.OutWarehouseMaterialsImpl.getRepository()
2024-12-18 10:08:32,280 [INFO] [main] o.s.scheduling.concurrent.ThreadPoolTaskExecutor [ExecutorConfigurationSupport.java : 181] Initializing ExecutorService 'threadPoolTaskExecutor'
2024-12-18 10:08:33,288 [INFO] [main] o.s.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping [WelcomePageHandlerMapping.java : 57] Adding welcome page template: index
2024-12-18 10:08:34,073 [INFO] [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer [TomcatWebServer.java : 220] Tomcat started on port(s): 18034 (https) with context path '/wms'
2024-12-18 10:08:34,102 [INFO] [main] com.yzm.StorageManagementApplication [StartupInfoLogger.java : 61] Started StorageManagementApplication in 16.676 seconds (JVM running for 17.476)
