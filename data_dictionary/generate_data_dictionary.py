#!/usr/bin/env python3
import os
import re
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side, Color
from openpyxl.styles.differential import DifferentialStyle
from openpyxl.formatting.rule import Rule

# Path to entity files
ENTITY_DIR = "/Users/<USER>/工作/yizengming/storageManagement/src/main/java/com/yzm/property/materials/entity"
OUTPUT_FILE = "/Users/<USER>/工作/yizengming/storageManagement/data_dictionary/实体数据字典.xlsx"

def extract_entity_info(file_path, file_name):
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Extract class comment (description)
    class_comment = ""
    class_comment_match = re.search(r'/\*\*(.*?)\*/', content, re.DOTALL)
    if class_comment_match:
        comment_lines = class_comment_match.group(1).strip().split('\n')
        for line in comment_lines:
            line = line.strip().lstrip('*').strip()
            if line and not line.startswith('@'):
                class_comment = line
                break
    
    # Extract table name
    table_name = ""
    table_match = re.search(r'@Table\(name\s*=\s*"([^"]+)"', content)
    if table_match:
        table_name = table_match.group(1)
    
    # Extract fields
    fields = []
    
    # 从类定义开始的内容
    class_match = re.search(r'public\s+class\s+\w+.*?\{(.*?)\}', content, re.DOTALL)
    if class_match:
        class_body = class_match.group(1)
    else:
        class_body = content  # 如果找不到类定义，就使用整个文件内容
    
    # 找出所有的字段声明
    # 这个正则表达式匹配字段声明模式：
    # 1. 可能有注解(@开头的行)
    # 2. 最终以"private 类型 字段名;"结尾
    # 3. 可能有注释(//后面的内容)
    field_blocks = []
    
    # 首先分割类体为单独的行
    lines = class_body.split('\n')
    current_block = []
    in_field_block = False
    
    for line in lines:
        line = line.strip()
        if not line or line.startswith('//'): 
            # 跳过空行或纯注释行
            if in_field_block:
                current_block.append(line)
            continue
            
        if line.startswith('@') or (not in_field_block and 'private' in line):
            # 新的注解或私有字段开始
            if current_block and 'private' in ''.join(current_block):
                # 如果之前有一个处理中的字段块并且它包含private
                field_blocks.append('\n'.join(current_block))
            if 'private' in line and ';' in line:
                # 单行字段声明
                field_blocks.append(line)
                current_block = []
                in_field_block = False
            else:
                # 多行字段声明开始
                current_block = [line]
                in_field_block = True
        elif in_field_block:
            current_block.append(line)
            if 'private' in line and ';' in line:
                # 找到了字段声明结束
                field_blocks.append('\n'.join(current_block))
                current_block = []
                in_field_block = False
                
    # 加上最后一个块
    if current_block and 'private' in ''.join(current_block):
        field_blocks.append('\n'.join(current_block))
    
    # 处理每个字段块
    for block in field_blocks:
        # 跳过注释掉的字段
        if '//    private' in block:
            continue
            
        # 提取字段类型和名称
        private_match = re.search(r'private\s+([a-zA-Z0-9_.<>]+)\s+([a-zA-Z0-9_]+)\s*;', block)
        if private_match:
            field_type = private_match.group(1)
            field_name = private_match.group(2)
            
            # 提取行内注释
            inline_comment = ""
            inline_comment_match = re.search(r';\s*//\s*(.+)$', block, re.MULTILINE)
            if inline_comment_match:
                inline_comment = inline_comment_match.group(1).strip()
            
            # 检查是否有@Column注解和columnDefinition
            column_def = ""
            column_def_match = re.search(r'@Column\(.*?columnDefinition\s*=\s*"([^"]+)"', block)
            if column_def_match:
                column_def = column_def_match.group(1)
            
            # 从columnDefinition中提取注释
            column_comment = ""
            if column_def:
                comment_match = re.search(r"COMMENT\s+'([^']+)'", column_def)
                if comment_match:
                    column_comment = comment_match.group(1)
            
            # 优先使用columnDefinition中的注释，其次是行内注释
            description = column_comment if column_comment else inline_comment
            
            # 确定字段存储类型
            storage_type = "实体字段"
            if "@Column" in block:
                storage_type = "数据库字段"
            if "@Id" in block:
                storage_type = "主键字段"
            if "@Transient" in block:
                storage_type = "临时字段"
            
            # 尝试获取数据库列名
            db_column_name = ""
            column_name_match = re.search(r'@Column\(.*?name\s*=\s*"([^"]+)"', block)
            if column_name_match:
                db_column_name = column_name_match.group(1)
            
            # 确定数据库类型
            db_type = "未指定"
            if column_def:
                db_type_match = re.search(r'^(\w+(\([^)]+\))?)', column_def)
                if db_type_match:
                    db_type = db_type_match.group(1)
            if "@Transient" in block:
                db_type = "TRANSIENT"
            
            # 添加字段信息
            fields.append({
                "字段名称": field_name,
                "数据库列名": db_column_name,
                "字段类型": field_type,
                "数据库类型": db_type,
                "字段描述": description,
                "存储类型": storage_type
            })
    
    # 按字段名称排序
    fields.sort(key=lambda x: x["字段名称"])
    
    return {
        "实体名称": file_name.replace(".java", ""),
        "表名": table_name,
        "描述": class_comment,
        "字段列表": fields
    }

def create_excel(entities_info):
    # Create a new workbook
    wb = Workbook()
    
    # Create styles
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="4F81BD", end_color="4F81BD", fill_type="solid")
    centered_alignment = Alignment(horizontal='center', vertical='center')
    border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    # Create a summary sheet
    summary_sheet = wb.active
    summary_sheet.title = "实体汇总"
    
    # Add headers to summary sheet
    summary_sheet["A1"] = "序号"
    summary_sheet["B1"] = "实体名称"
    summary_sheet["C1"] = "表名"
    summary_sheet["D1"] = "描述"
    summary_sheet["E1"] = "字段数量"
    
    # Style the headers
    for cell in summary_sheet["1:1"]:
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = centered_alignment
        cell.border = border
    
    # Adjust column widths
    summary_sheet.column_dimensions["A"].width = 8
    summary_sheet.column_dimensions["B"].width = 35
    summary_sheet.column_dimensions["C"].width = 35
    summary_sheet.column_dimensions["D"].width = 60
    summary_sheet.column_dimensions["E"].width = 15
    
    # Add entity data to summary sheet
    for i, entity_info in enumerate(entities_info, 1):
        row = i + 1
        summary_sheet[f"A{row}"] = i
        summary_sheet[f"B{row}"] = entity_info["实体名称"]
        summary_sheet[f"C{row}"] = entity_info["表名"]
        summary_sheet[f"D{row}"] = entity_info["描述"]
        summary_sheet[f"E{row}"] = len(entity_info["字段列表"])
        
        # Style the data rows
        for cell in summary_sheet[f"{row}:{row}"]:
            cell.border = border
            cell.alignment = Alignment(vertical='center')
    
    # Create individual sheets for each entity
    for entity_info in entities_info:
        entity_name = entity_info["实体名称"]
        # Limit sheet name to 31 characters (Excel limit)
        sheet_name = entity_name[:31]
        entity_sheet = wb.create_sheet(title=sheet_name)
        
        # Add entity information
        entity_sheet["A1"] = "实体名称:"
        entity_sheet["B1"] = entity_name
        entity_sheet["A2"] = "表名:"
        entity_sheet["B2"] = entity_info["表名"]
        entity_sheet["A3"] = "描述:"
        entity_sheet["B3"] = entity_info["描述"]
        
        # Style entity information
        for cell in [entity_sheet["A1"], entity_sheet["A2"], entity_sheet["A3"]]:
            cell.font = Font(bold=True)
        
        # Extend cell B3 across multiple columns to allow for long descriptions
        entity_sheet.merge_cells('B3:D3')
        entity_sheet["B3"].alignment = Alignment(wrap_text=True, vertical='center')
        
        # Add field headers
        entity_sheet["A5"] = "序号"
        entity_sheet["B5"] = "字段名称"
        entity_sheet["C5"] = "字段类型"
        entity_sheet["D5"] = "字段描述"
        
        # Style the field headers
        for cell in entity_sheet["5:5"]:
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = centered_alignment
            cell.border = border
        
        # Adjust column widths
        entity_sheet.column_dimensions["A"].width = 8
        entity_sheet.column_dimensions["B"].width = 30
        entity_sheet.column_dimensions["C"].width = 30
        entity_sheet.column_dimensions["D"].width = 70
        
        # Add field data
        for i, field in enumerate(entity_info["字段列表"], 1):
            row = i + 5
            entity_sheet[f"A{row}"] = i
            entity_sheet[f"B{row}"] = field["字段名称"]
            entity_sheet[f"C{row}"] = field["字段类型"]
            entity_sheet[f"D{row}"] = field["字段描述"]
            
            # Style the data rows
            for cell in entity_sheet[f"{row}:{row}"]:
                cell.border = border
                cell.alignment = Alignment(vertical='center')
    
    # Save the workbook
    wb.save(OUTPUT_FILE)
    return OUTPUT_FILE

def main():
    entities_info = []
    
    # Process each Java file in the entity directory
    for file_name in os.listdir(ENTITY_DIR):
        if file_name.endswith(".java"):
            file_path = os.path.join(ENTITY_DIR, file_name)
            try:
                entity_info = extract_entity_info(file_path, file_name)
                entities_info.append(entity_info)
                print(f"处理文件: {file_name} - 找到 {len(entity_info['字段列表'])} 个字段")
            except Exception as e:
                print(f"处理文件 {file_name} 时出错: {e}")
                import traceback
                traceback.print_exc()
    
    # Sort entities by name
    entities_info.sort(key=lambda x: x["实体名称"])
    
    # Create Excel file
    output_file = create_excel(entities_info)
    print(f"\n数据字典生成成功: {output_file}")

if __name__ == "__main__":
    main() 