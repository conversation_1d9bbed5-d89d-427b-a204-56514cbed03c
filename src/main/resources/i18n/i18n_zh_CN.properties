#########################登陆相关#####################
sys.login.failure=账号或密码错误,请重试.
sys.login.noauth=\u7528\u6237\u5DF2\u88AB\u7981\u7528
sys.login.sm4=账号或密码加密错误,请重试.
sys.login.accountDisabled=账号已被锁定,请联系管理员!
sys.login.failedNumLock=账户被锁定,{0} 分钟后解锁!
sys.login.password.retry.limit.count=账号或密码不正确,输入错误{0}次,账号将锁定!
sys.login.code.error=验证码不正确!
sys.login.code.invalid=验证码已失效!
sys.login.ex.info=登陆地点:{0}#登陆IP:{1}
sys.login.out.error=您与服务器断开连接了,请重新登陆!
sys.login.out.info=账号在别处登陆!如不是本人授权登陆,请尽快修改登陆密码!
sys.login.accountFreezed=该帐号已冻结
sys.login.userout=您被管理员踢出系统!
# =========== 用户密码安全策略 ===========
sys.user.oldPasswordError=旧密码错误，请重新输入
sys.user.newpassw.tips=新密码不能为空!
sys.user.name.tips=用户姓名不能为空!
sys.user.phone.tips=手机号码不能为空!
sys.user.email.tips=电子邮件不能为空!
sys.user.phone.improper.format = 手机号码格式不正确！
sys.user.email.improper.format = 电子邮件格式不正确！
sys.error=错误
sys.error.info=对不起,页面没找到.
sys.error.page=错误页面
#================================================

#####################系統服務器公共####################
sys.update.error=修改异常!
sys.msg.permissions=无权限操作
sys.file.null=文件为空!
#################################################

##################后台配置
#sys.os.signout=退出
#sys.os.out=注销
#sys.os.updatePassword=修改密码
#sys.os.online =在线
#sys.os.mainMenu=主导航
#sys.os.new=新
#sys.os.all=全屏显示
#sys.os.lock=锁屏
#sys.os.windows=系统窗口

######################################菜单##############

########################################################

# =========== 用户密码安全策略 ===========

#================================================

####################################系统返回信息######################
sys.select.comp=请选择公司
sys.select.dept=请选择地区
sys.select.one=请至少选择一条记录!

sys.msg.user.already=用户已经存在
sys.msg.role.already=角色名称已经存在
sys.msg.permission.already=权限字符已经存在
sys.msg.mobile.already=手机号码已经存在
sys.msg.remote=请修正此栏位
sys.msg.email=请修正此栏位
sys.msg.url=请输入有效的网址
sys.msg.date=请输入有效的日期
sys.msg.dateISO=请输入有效的日期 (YYYY-MM-DD)
sys.msg.number=请输入正确的数字
sys.msg.digits=只能输入数字
sys.msg.creditcard=请输入有效的信用卡号码
sys.msg.equalTo=你的输入不相同
sys.msg.extension=请输入有效的后缀
sys.msg.maxlength=最多 {0} 个字
sys.msg.minlength=最少 {0} 个字
sys.msg.rangelength=请输入长度为 {0} 至 {1} 之间的字串
sys.msg.range=请输入 {0} 至 {1} 之间的数值
sys.msg.max=请输入不大于 {0} 的数值
sys.msg.min=请输入不小于 {0} 的数值
sys.msg.isUserName=请输入数字或者字母,不包含特殊字符
sys.opneLock=开锁
sys.lockInfo =开锁日志
sys.return=返回
sys.alert.info=提示
sys.mach.info=终端信息
sys.box.info=胆箱信息
sys.atm.keyinfo=终端密钥
sys.atm.resetKey=重置密钥
sys.atm.exportKey=导出密钥
sys.poi.info=报表信息
