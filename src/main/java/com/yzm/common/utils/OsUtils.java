package com.yzm.common.utils;

import cn.hutool.core.net.NetUtil;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * 
 * @类名称 OsUtils.java
 * @类描述 <pre>操作系统工具</pre>
 * @作者  xingsfdz
 * @创建时间 2020年5月18日 上午10:25:33
 * @版本 5.0.0
 *
 * @修改记录
 * <pre>
 *     版本                       修改人 		修改日期 		 修改内容描述
 *     ----------------------------------------------
 *     5.0.0 	 	2020年5月18日             
 *     ----------------------------------------------
 * </pre>
 */
public class OsUtils {
 
	public static String getMACAddress() {
        String address = "";
        String os = System.getProperty("os.name");
        if (os != null && os.contains("Windows")) {
            try {
                String command = "cmd.exe /c ipconfig /all";
                Process p = Runtime.getRuntime().exec(command);
                BufferedReader br =
                        new BufferedReader(
                                new InputStreamReader(p.getInputStream()));
                String line;
                while ((line = br.readLine()) != null) {
                    if (line.indexOf("Physical Address") > 0) {
                        int index = line.indexOf(":");
                        index += 2;
                        address = line.substring(index);
                        break;
                    }
                }
                br.close();
                return address.trim();
            } catch (IOException e) {}
        }
        return address;
    }
	
	public static void main(String args[]) throws UnknownHostException {
        String macAddress = NetUtil.getMacAddress(InetAddress.getLocalHost());
        System.out.println(macAddress);
//		System.out.println(getMACAddress());
	}
 
}