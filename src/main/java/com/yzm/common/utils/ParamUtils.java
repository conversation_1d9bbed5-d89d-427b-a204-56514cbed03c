package com.yzm.common.utils;

import com.yzm.property.system.entity.SysConfig;
import com.yzm.property.system.repository.SysConfigRepository;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;

public class ParamUtils {

	public static String getParameterNo(String key, SysConfigRepository sysConfigRepository) {
		DecimalFormat df = new DecimalFormat("0000");
		SimpleDateFormat sdfMonth = new SimpleDateFormat("yyyyMM");
		SysConfig p = sysConfigRepository.findByParamKey(key);
		String lastNumberNo = p.getParamValue();
		String strLastMonth = lastNumberNo.substring(2, 8);
		String strLastNo = lastNumberNo.substring(8);
		String strNowNo = df.format(new Integer(strLastNo).intValue() + 1);
		Calendar cal = Calendar.getInstance(); // 使用默认时区和语言环境获得一个日历。
		String strNowMonth = sdfMonth.format(cal.getTime());
		if (!strNowMonth.equals(strLastMonth)) {
			strNowNo = "0001";
		}
		String no = lastNumberNo.substring(0, 2) + sdfMonth.format(cal.getTime()) + strNowNo;
		p.setParamValue(no);
		sysConfigRepository.save(p);
		return no;
	}
	public static boolean isNumberWithLength(String str, int length) {
		// 检查字符串是否为空或长度不匹配
		if (str == null || str.length() != length) {
			return false;
		}

		// 检查每个字符是否是数字
		for (int i = 0; i < str.length(); i++) {
			if (!Character.isDigit(str.charAt(i))) {
				return false; // 如果有任何一个字符不是数字，返回false
			}
		}
		return true; // 如果字符串所有字符都是数字，并且长度正确，返回true
	}

}
