package com.yzm.common.utils;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.NetworkInterface;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Enumeration;
import java.util.Scanner;

public class Machine {

	public static String getMachieCode() {
		String key;
		try {
			// mac address
			key = getMac();
			System.out.printf(key);
			// serial number
			key += "|" + "QWERTYUIOP";
		} catch (Exception e) {
			e.printStackTrace();
			key = "NullKey";
		}
		
		return getMD5(key, 16);
	}

	public static String getMac() throws Exception {
		String strreturn = "";
    	Enumeration<NetworkInterface> nets = NetworkInterface.getNetworkInterfaces();
    	ArrayList<String> macs = new ArrayList<String>();
    	while(nets.hasMoreElements()) {
    		NetworkInterface ni = nets.nextElement();
    		byte[] mac = ni.getHardwareAddress();
    		if(mac == null) {
    			continue;
    		}
    		macs.add(BytesToHex(mac));
    	}
    	String[] strsmac = macs.toArray(new String[macs.size()]);
    	Arrays.sort(strsmac);
    	for(int i = 0; i < strsmac.length; i ++) {
    		strreturn += strsmac[i];
    		if(i < strsmac.length - 1) {
    			strreturn += "_";
    		}
    	}
//    	for(String mac : strsmac) {
//    		strreturn += mac;
//    	}
    	//System.out.println(strreturn);
		return strreturn;
	}
	/**
	 * 获取当前系统CPU序列，可区分linux系统和windows系统
	 */
	public static String getCpuId() throws Exception {
		String cpuId;
		// 获取当前操作系统名称
		String os = System.getProperty("os.name");
		os = os.toUpperCase();
		System.out.println(os);

		// linux系统用Runtime.getRuntime().exec()执行 dmidecode -t processor 查询cpu序列
		// windows系统用 wmic cpu get ProcessorId 查看cpu序列
		if ("LINUX".equals(os)) {
			cpuId = getLinuxCpuId("dmidecode -t processor | grep 'ID'", "ID", ":");
		} else {
			cpuId = getWindowsCpuId();
		}
		return cpuId.toUpperCase().replace(" ", "");
	}

	/**
	 * 获取linux系统CPU序列
	 */
	public static String getLinuxCpuId(String cmd, String record, String symbol) throws Exception {
		String execResult = executeLinuxCmd(cmd);
		String[] infos = execResult.split("\n");
		for (String info : infos) {
			info = info.trim();
			if (info.indexOf(record) != -1) {
				info.replace(" ", "");
				String[] sn = info.split(symbol);
				return sn[1];
			}
		}
		return null;
	}

	public static String executeLinuxCmd(String cmd) throws Exception {
		Runtime run = Runtime.getRuntime();
		Process process;
		process = run.exec(cmd);
		InputStream in = process.getInputStream();
		BufferedReader bs = new BufferedReader(new InputStreamReader(in));
		StringBuffer out = new StringBuffer();
		byte[] b = new byte[8192];
		for (int n; (n = in.read(b)) != -1; ) {
			out.append(new String(b, 0, n));
		}
		in.close();
		process.destroy();
		return out.toString();
	}

	/**
	 * 获取windows系统CPU序列
	 */
	public static String getWindowsCpuId() throws Exception {
		Process process = Runtime.getRuntime().exec(
				new String[]{"wmic", "cpu", "get", "ProcessorId"});
		process.getOutputStream().close();
		Scanner sc = new Scanner(process.getInputStream());
		sc.next();
		String serial = sc.next();
		return serial;
	}

	public static String getMD5(String message, int bit) {  
        String md5str = "";  
        try {  
            // 1 创建一个提供信息摘要算法的对象，初始化为md5算法对象  
            MessageDigest md = MessageDigest.getInstance("MD5");  

            // 2 将消息变成byte数组  
            byte[] input = message.getBytes();  
      
            // 3 计算后获得字节数组,这就是那128位了  
            byte[] buff = md.digest(input);  
      
            // 4 把数组每一字节（一个字节占八位）换成16进制连成md5字符串
            md5str = BytesToHex(buff);
           // System.out.println(md5str);
            if(bit == 16) {
            	md5str = md5str.substring(8, 8 + bit);
               // System.out.println(md5str);
            }
      
        } catch (Exception e) {  
            e.printStackTrace();  
        }  
        return md5str;  
    }
	
	public static String BytesToHex(byte[] bytes) {
		String outBytes = "";
		for(byte b : bytes) {
			String outByte = String.format("%02x", b);
			outBytes += outByte;
		}
		outBytes = outBytes.toUpperCase();
		return outBytes;
	}
	
	public static void main(String[] args) {
		try {
			System.out.println(getCpuId());
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
