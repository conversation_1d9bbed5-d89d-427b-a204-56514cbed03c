package com.yzm.common.utils;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;

public class OwnerParamUtils {

	public static String getParameterNo(String code,String value) {
		DecimalFormat df = new DecimalFormat("0000");
		SimpleDateFormat sdfMonth = new SimpleDateFormat("yyyyMM");
		Calendar cal = Calendar.getInstance(); // 使用默认时区和语言环境获得一个日历。
		String strNowMonth = sdfMonth.format(cal.getTime());/*当前年月*/
		if(StringUtils.isNotEmpty(value)){
			String strLastMonth = value.substring(3, 9);
			String strLastNo = value.substring(9);
			String strNowNo = df.format(new Integer(strLastNo).intValue() + 1);
			if (!strNowMonth.equals(strLastMonth)) {
				strNowNo = "0001";
			}
			return value.substring(0, 3) + sdfMonth.format(cal.getTime()) + strNowNo;
		}else{
			return code+ sdfMonth.format(cal.getTime())+"0001";
		}

	}


}
