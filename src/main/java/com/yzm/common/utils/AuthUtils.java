package com.yzm.common.utils;

public class AuthUtils {
	
	public static boolean canDataChange() {
		String strKey = PropertyConfig.getKeyValue("machinekey");
		String strPublicKey = PropertyConfig.getKeyValue("publickey");
		if(strKey != null && strPublicKey != null) {
			byte[] decodedData;
			try {
				decodedData = RSAUtils.decryptByPublicKey(Base64Utils.decode(strKey), strPublicKey);
		        String target = new String(decodedData);
		        if(target.startsWith(Machine.getMachieCode())) {
		    		return true;
		        }
			} catch (Exception e) {
				e.printStackTrace();
			}  
		}
		return false;
	}
}
