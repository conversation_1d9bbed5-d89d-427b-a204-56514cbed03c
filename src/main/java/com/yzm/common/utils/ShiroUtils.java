package com.yzm.common.utils;

import com.yzm.framework.shiro.LoginUser;
import com.yzm.framework.shiro.realm.UserRealm;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.crypto.hash.SimpleHash;
import org.apache.shiro.mgt.RealmSecurityManager;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.subject.SimplePrincipalCollection;
import org.apache.shiro.subject.Subject;

import java.util.List;

public class ShiroUtils {
	
	/** 加密算法 */
	public final static String hashAlgorithmName = "SHA-256";
	/** 循环次数 */
	public final static int hashIterations = 16;

	public static String sha256(String password, String salt) {
		return new SimpleHash(hashAlgorithmName, password, salt, hashIterations).toString();
	}

	public static List<Long> getCurrentUserDataScope() {
		return null;
	}

	public static boolean isLogin() {
		return SecurityUtils.getSubject().getPrincipal() != null;
	}

	public static void logout() {
		SecurityUtils.getSubject().logout();
	}

	/**
	 * 获取用户信息
	 * 
	 * @return
	 */
	public static LoginUser getUserInfo() {
		return (LoginUser) SecurityUtils.getSubject().getPrincipal();
	}
	
	public static Subject getSubject() {
		return SecurityUtils.getSubject();
	}

	public static Session getSession() {
		return SecurityUtils.getSubject().getSession();
	}

	public static void clearCachedAuthorizationInfo() {
		RealmSecurityManager rsm = (RealmSecurityManager) SecurityUtils.getSecurityManager();
		UserRealm realm = (UserRealm) rsm.getRealms().iterator().next();
		realm.clearCachedAuthorizationInfo();
	}

	/**
	 * 生成随机盐
	 */
	public static String randomSalt() {
		return RandomStringUtils.randomAlphanumeric(20);
	}

	public static Object getUserId() {
		return getUserInfo().getId();
	}

	public static void reloadUser(LoginUser loginUser) {
		Subject subject = getSubject();
		PrincipalCollection principalCollection = subject.getPrincipals();
		String realmName = principalCollection.getRealmNames().iterator().next();
		PrincipalCollection newPrincipalCollection = new SimplePrincipalCollection(loginUser, realmName);
		// 重新加载Principal
		subject.runAs(newPrincipalCollection);
	}
	
	public static void setSessionAttribute(Object key, Object value) {
		getSession().setAttribute(key, value);
	}

	/**
	 * 获取登陆IP
	 * 
	 * @return
	 */
	public static String getIp() {
		return getSubject().getSession().getHost();
	}

	public static String getKaptcha(String kaptchaSessionKey) {
		return (String) getSession().getAttribute(kaptchaSessionKey);
	}

}
