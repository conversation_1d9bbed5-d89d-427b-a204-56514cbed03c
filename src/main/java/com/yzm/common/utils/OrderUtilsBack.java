//package com.yzm.common.utils;
//
//import java.text.DateFormat;
//import java.text.SimpleDateFormat;
//import java.util.Collections;
//import java.util.Date;
//import java.util.HashSet;
//import java.util.Set;
//import java.util.concurrent.CountDownLatch;
//import java.util.concurrent.ExecutorService;
//import java.util.concurrent.Executors;
//
///**
// * 订单规则生成工具类
// *
// * <AUTHOR> 2024年3月16日
// */
//public class OrderUtilsBack {
//    private static final Object lock = new Object();
//
//    /*
//     * 资产
//     */
//    private static final String ASSETS_CODE = "ZC";
//    /*
//     * 领用
//     */
//    private static final String LingYong_CODE = "LY";
//    /*
//     * 退库
//     */
//    private static final String TuiKu_CODE = "TK";
//
//    /*
//     * 借用
//     */
//    private static final String JieYong_CODE = "JY";
//    /*
//     * 归还
//     */
//    private static final String GuiHuan_CODE = "GH";
//    /*
//     * 维修
//     */
//    private static final String REPAIR_CODE = "WX";
//    /*
//     * 维修归还
//     */
//    private static final String REPAIR_BACK_CODE = "WXGH";
//    /*
//     * 报废
//     */
//    private static final String SCRAP_CODE = "BF";
//
//    /*
//     * 盘点
//     */
//    private static final String INVENTORY_CODE = "PD";
//
//    /*
//     * 耗材
//     */
//    private static final String HaoCai_CODE = "HC";
//    /*
//     * 入库
//     */
//    private static final String RuKu_CODE = "RK";
//    /*
//    出库
//    */
//    private static final String CuKu_CODE = "CK";
//    /*
//    出库
//    */
//    private static final String QTCuKu_CODE = "QTCK";
//    /*
//    出库
//    */
//    private static final String FiFo_CODE = "IO";
//    /*
//    出库
//    */
//    private static final String Jiao_Guan_CuKu_CODE = "CKJG";
//    /*
//    出库
//    */
//    private static final String DiaoBo_CODE = "DB";
//    /**
//     * 耗材批次
//     */
//    private static final String HaoCaiPiCi_CODE = "HCPC";
//
//    /**
//     * 生成时间戳
//     */
//    private static String getDateTime() {
//        DateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
//        return sdf.format(new Date()) ;
//    }
//    //===========资产===============
//
//    public static String getAssetsCode() {
//        return getCode(ASSETS_CODE);
//    }
//
//    //===========领用===============
//
//    public static String getLingYongCode() {
//        return getCode(LingYong_CODE);
//    }
//    //===========退库===============
//
//    public static String getTuiKuCode() {
//        return getCode(TuiKu_CODE);
//    }
//
//
//    //===========借用==============
//
//    public static String getJieYongCode() {
//        return getCode(JieYong_CODE);
//    }
//    //===========调拨==============
//
//    public static String getDiaoBoCode() {
//        return getCode(DiaoBo_CODE);
//    }
//    //===========归还===============
//
//    public static String getGuiHuanCode() {
//        return getCode(GuiHuan_CODE);
//    }
//
//    //===========维修==============
//
//    public static String getRepairCode() {
//        return getCode(REPAIR_CODE);
//    }  //===========维修归还==============
//
//    public static String getRepairBackCode() {
//        return getCode(REPAIR_BACK_CODE);
//    }
//    //===========报废===============
//
//    public static String getScrapCode() {
//        return getCode(SCRAP_CODE);
//    }
//
//    //===========耗材==============
//
//    public static String getHaoCaiCode() {
//        return getCode(HaoCai_CODE);
//    }
//
//    public static String getHaoCaiPiCiCode() {
//        return getCode(HaoCaiPiCi_CODE);
//    }
//
//    //===========入库===============
//
//    public static String getRuKuCode() {
//        return getCode(RuKu_CODE);
//    }
//
//    //===========出库===============
//    public static String getCuKuCode() {
//        return getCode(CuKu_CODE);
//    }
//
//    //===========出库===============
//    public static String getQTCuKuCode() {
//        return getCode(QTCuKu_CODE);
//    }
//
//    //===========先进先出审核===============
//    public static String getFifoCode() {
//        return getCode(FiFo_CODE);
//    }
//
//    //===========胶管出库===============
//    public static String getJiaoGuanCuKuCode() {
//        return getCode(Jiao_Guan_CuKu_CODE);
//    }
//
//    private static final SimpleDateFormat sdf = new SimpleDateFormat("yyMMddHHmmssSSS");
//    private static long lastTimestamp = 0;
//    private static short sequence = 0;
//
//    public static String getCode(String header) {
//        synchronized (lock) {
//            long current = System.currentTimeMillis();
//            if (current == lastTimestamp) {
//                if (++sequence > 99) { // 两位序列号
//                    while (current <= lastTimestamp) {
//                        current = System.currentTimeMillis();
//                    }
//                    sequence = 0;
//                }
//            } else {
//                sequence = 0;
//            }
//            lastTimestamp = current;
//
//            // 使用固定SimpleDateFormat实例
//            return header + sdf.format(new Date(current)) + String.format("%02d", sequence);
//        }
//    }
//
//    public static void main(String[] args) throws InterruptedException {
//        final int THREADS = 100;
//        ExecutorService exec = Executors.newFixedThreadPool(THREADS);
//        Set<String> ids = Collections.synchronizedSet(new HashSet<>());
//
//        CountDownLatch latch = new CountDownLatch(THREADS);
//        for (int i = 0; i < THREADS; i++) {
//            exec.execute(() -> {
//                String id = OrderUtilsBack.getCuKuCode();
//                if (!ids.add(id)) {
//                    System.out.println("发现重复ID: " + id);
//                }
//                latch.countDown();
//            });
//        }
//
//        latch.await();
//        exec.shutdown();
//        System.out.println("生成数量: " + ids.size() + "/" + THREADS);
//    }
//
//    public static String getInventoryCode() {
//        return getCode(INVENTORY_CODE);
//    }
//}
