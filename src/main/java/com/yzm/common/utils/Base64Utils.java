package com.yzm.common.utils;

//import it.sauronsoftware.base64.Base64;  

import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;  
//若报错，编译路径移除jdk，再添加jdk  
  
/** */

/**
 * <p> 
 * BASE64编码解码工具包 
 * </p> 
 * <p> 
 * 依赖javabase64-1.3.1.jar 或 common-codec 
 * </p> 
 *  
 * <AUTHOR> 
 * @date 2012-5-19 
 * @version 1.0 
 */  
public class Base64Utils {  
  
    /** *//** 
     * <p> 
     * BASE64字符串解码为二进制数据 
     * </p> 
     *  
     * @param base64 
     * @return 
     * @throws Exception 
     */  
    public static byte[] decode(String base64) throws Exception {  
        //return Base64.decode(base64.getBytes());  
        return new BASE64Decoder().decodeBuffer(base64);  
    }  
      
    /** *//** 
     * <p> 
     * 二进制数据编码为BASE64字符串 
     * </p> 
     *  
     * @param bytes 
     * @return 
     * @throws Exception 
     */  
    public static String encode(byte[] bytes) throws Exception {  
        //return new String(Base64.encode(bytes));  
        return new BASE64Encoder().encode(bytes);  
    }  
}  