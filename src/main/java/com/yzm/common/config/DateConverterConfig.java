package com.yzm.common.config;

import cn.hutool.core.date.DateUtil;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <p>全局handler前日期统一处理</p>
 *
 */
@Component
public class DateConverterConfig implements Converter<String, Date> {

    @Override
    public Date convert(String source) {
        String value = source.trim();
        if ("".equals(value)) {
            return null;
        }
        return DateUtil.parse(source);
    }

}
