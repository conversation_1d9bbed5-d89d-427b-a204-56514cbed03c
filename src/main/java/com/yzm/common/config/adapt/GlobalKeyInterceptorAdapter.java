package com.yzm.common.config.adapt;

import com.yzm.common.config.ConfigConstant;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 全局变量配置
 * <AUTHOR>
 *2024年1月12日
 */
@Component
public class GlobalKeyInterceptorAdapter extends HandlerInterceptorAdapter {

	@Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
			throws Exception {
		
		String scheme = request.getScheme();
		String serverName = request.getServerName();
		int port = request.getServerPort();
		String path = request.getContextPath();
		String basePath = scheme + "://" + serverName + ":" + port + path;
		// 修改绝对地址
		request.setAttribute(ConfigConstant.CTX_STATIC, path);
		request.setAttribute(ConfigConstant.BASE_PATH, basePath);
		return super.preHandle(request, response, handler);
	}
}
