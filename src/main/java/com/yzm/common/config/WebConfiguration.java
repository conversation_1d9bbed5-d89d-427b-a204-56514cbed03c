package com.yzm.common.config;

import com.yzm.app.AuthInterceptor;
import com.yzm.common.adapt.SecurityKeyInterceptorAdapter;
import com.yzm.common.config.adapt.GlobalKeyInterceptorAdapter;
import com.yzm.common.utils.CookieUtil;
import com.yzm.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Locale;

@Configuration
public class WebConfiguration implements WebMvcConfigurer {
	
	@Value("${spring.messages.defaultLocale: zh_CN}")
	private String defaultLocale;
	@Autowired
	private GlobalKeyInterceptorAdapter globalKeyInterceptorAdapter;
	@Autowired
	private SecurityKeyInterceptorAdapter securityKeyInterceptorAdapter;
	@Autowired
	private AuthInterceptor authInterceptor;
	
	@Override
	public void addResourceHandlers(ResourceHandlerRegistry registry) {
		registry.addResourceHandler("/i18n/**").addResourceLocations("classpath:/i18n/");
		registry.addResourceHandler("/static/**").addResourceLocations("classpath:/static/");
	}
	
	@Override
	public void addInterceptors(InterceptorRegistry registry) {
		registry.addInterceptor(globalKeyInterceptorAdapter).addPathPatterns("/**");
		registry.addInterceptor(securityKeyInterceptorAdapter).addPathPatterns("/**");
		registry.addInterceptor(authInterceptor).addPathPatterns("/app/**").excludePathPatterns("/app/login");
	}
	
	/**
	 * 配置国际化参数
	 */
	@Bean
	public LocaleResolver localeResolver() {
		return new NativeLocaleResolver(defaultLocale);
	}
	
	protected static class NativeLocaleResolver implements LocaleResolver {
		private String defaultLocale;

		public NativeLocaleResolver(String defaultLocale) {
			this.defaultLocale = defaultLocale;
		}

		@Override
		public Locale resolveLocale(HttpServletRequest request) {
			String language = request.getParameter(ConfigConstant.LANGUAGE);
			Locale locale = null;
			if (StringUtils.isNotEmpty(language)) {
				String[] split = language.split("_");
				locale = new Locale(split[0], split[1]);
			} else {
				language = CookieUtil.getCookie(request, ConfigConstant.LANGUAGE);
				if (StringUtils.isNotEmpty(language)) {
					String[] split = language.split("_");
					locale = new Locale(split[0], split[1]);
				} else {
					String[] split = defaultLocale.split("_");
					locale = new Locale(split[0], split[1]);
				}
			}
			return locale;
		}

		@Override
		public void setLocale(HttpServletRequest request, HttpServletResponse response, Locale locale) {
			// TODO Auto-generated method stub
			
		}

	}
}
