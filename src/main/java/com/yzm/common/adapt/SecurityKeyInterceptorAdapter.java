package com.yzm.common.adapt;

import cn.hutool.core.util.IdUtil;
import com.yzm.app.ConfigConstant;
import com.yzm.common.utils.CookieUtil;
import com.yzm.common.utils.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 安全key,多语言 拦截器
 */
@Component
public class SecurityKeyInterceptorAdapter extends Hand<PERSON>InterceptorAdapter {

	@Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
			throws Exception {

		// 切换语言
		String language = request.getParameter(ConfigConstant.LANGUAGE);

		// 设置语言 Cookie
		if (StringUtils.isNotEmpty(language)) {
			CookieUtil.setReadCookie(response, ConfigConstant.LANGUAGE, language, 60 * 60 * 24 * 7);
		}
		String _secretKey = "";
		// 获取登录安全Key
		if (request.getCookies() == null) {
			_secretKey = IdUtil.simpleUUID().toLowerCase();
			CookieUtil.setReadCookie(response, ConfigConstant.SECRETKEY, _secretKey, 60 * 60 * 24 * 7);

		} else {
			_secretKey = CookieUtil.getCookie(request, ConfigConstant.SECRETKEY);
			if (StringUtils.isEmpty(_secretKey)) {
				_secretKey = IdUtil.simpleUUID().toLowerCase();
				CookieUtil.setReadCookie(response, ConfigConstant.SECRETKEY, _secretKey, 60 * 60 * 24 * 7);
			}
		}
		request.setAttribute(ConfigConstant.SECRETKEY, _secretKey);
		String scheme = request.getScheme();
		String serverName = request.getServerName();
		int port = request.getServerPort();
		String path = request.getContextPath();
		String basePath = scheme + "://" + serverName + ":" + port + path;
		// 修改绝对地址
		request.setAttribute(ConfigConstant.CTX_STATIC, path);
		request.setAttribute(ConfigConstant.BASE_PATH, basePath);
		return super.preHandle(request, response, handler);
	}

}
