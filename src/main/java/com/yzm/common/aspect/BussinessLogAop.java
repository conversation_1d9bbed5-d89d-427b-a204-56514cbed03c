package com.yzm.common.aspect;

import com.yzm.property.system.entity.SysOperLog;
import com.yzm.common.annotation.BussinessLog;
import com.yzm.common.enums.BusinessStatus;
import com.yzm.common.utils.MapperUtils;
import com.yzm.common.utils.ServletUtil;
import com.yzm.common.utils.ShiroUtils;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.manager.AsyncManager;
import com.yzm.framework.manager.factory.AsyncFactory;
import com.yzm.framework.shiro.LoginUser;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Date;
import java.util.Map;

/**
 * 系统日志，切面处理类AOP实现日志统一处理
 */
@Aspect
@Component
public class BussinessLogAop {

	private static final Logger LOG = LoggerFactory.getLogger(BussinessLogAop.class);

	/**
	 * 配置织入点
	 * 
	 */
	@Pointcut("@annotation(com.yzm.common.annotation.BussinessLog)")
	public void logCut() {

	}

	/**
	 * 处理完请求后执行
	 *
	 * @param joinPoint 切点
	 */
	@AfterReturning(pointcut = "logCut()", returning = "jsonResult")
	public void doAfterReturning(JoinPoint joinPoint, Object jsonResult) {
		handleLog(joinPoint, null, jsonResult);
	}

	protected void handleLog(final JoinPoint joinPoint, final Exception e, Object jsonResult) {
		try {
			long beginTime = System.currentTimeMillis();
			// 获得注解
			BussinessLog controllerLog = getAnnotationLog(joinPoint);
			if (controllerLog == null) {
				return;
			}
			// 获取当前的用户
			LoginUser currentUser = ShiroUtils.getUserInfo();
			// *========数据库日志=========*//
			SysOperLog operLog = new SysOperLog();
			operLog.setStatus(BusinessStatus.SUCCESS.ordinal());
			// 请求的地址
			String ip = ShiroUtils.getIp();

			operLog.setOperIp(ip);
			// 返回参数
			operLog.setJsonResult(MapperUtils.obj2json(jsonResult));

			operLog.setOperUrl(ServletUtil.getRequest().getRequestURI());
			if (currentUser != null) {
				// 当操作用户存在 保存用户相关信息
				operLog.setOperName(currentUser.getName());
				operLog.setWhId(currentUser.getWarehouseId());
			}
			if (StringUtils.isNotEmpty(e)) {
				operLog.setStatus(BusinessStatus.FAIL.ordinal());
				operLog.setErrorMsg(StringUtils.substring(e.getMessage(), 0, 2000));
			}
			// 设置方法名称
			String className = joinPoint.getTarget().getClass().getName();
			String methodName = joinPoint.getSignature().getName();
			operLog.setMethod(className + "." + methodName + "()");
			// 设置请求方式
			operLog.setRequestMethod(ServletUtil.getRequest().getMethod());
			// 处理设置注解上的参数
			getControllerMethodDescription(controllerLog, operLog);
			operLog.setOperTime(new Date());
			// 执行时长(毫秒)
			long time = System.currentTimeMillis() - beginTime;
			operLog.setTime(time);
			// 保存数据库
			AsyncManager.me().execute(AsyncFactory.recordOper(operLog));
		} catch (Exception ex) {
			// 记录本地异常日志
			LOG.error("==前置通知异常==");
			LOG.error("异常信息:{}", ex.getMessage());
		}
	}

	/**
	 * 获取注解中对方法的描述信息 用于Controller层注解
	 */
	public void getControllerMethodDescription(BussinessLog log, SysOperLog operLog) throws Exception {
		// 设置action动作
		operLog.setBusinessType(log.businessType().ordinal());
		// 设置标题
		operLog.setTitle(log.title());
		// 设置操作人类别
		operLog.setOperatorType(log.operatorType().ordinal());
		// 是否需要保存request，参数和值
		if (log.isSaveRequestData()) {
			// 获取参数的信息，传入到数据库中。
			setRequestValue(operLog);
		}
	}

	/**
	 * 获取请求的参数，放到log中
	 */
	private void setRequestValue(SysOperLog operLog) throws Exception {
		Map<String, String[]> map = ServletUtil.getRequest().getParameterMap();
		String params = MapperUtils.obj2json(map);
		operLog.setOperParam(StringUtils.substring(params, 0, 2000));
	}

	/**
	 * 是否存在注解，如果存在就获取
	 */
	private BussinessLog getAnnotationLog(JoinPoint joinPoint) throws Exception {
		Signature signature = joinPoint.getSignature();
		MethodSignature methodSignature = (MethodSignature) signature;
		Method method = methodSignature.getMethod();

		if (method != null) {
			return method.getAnnotation(BussinessLog.class);
		}
		return null;
	}

}
