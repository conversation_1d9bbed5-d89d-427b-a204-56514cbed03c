package com.yzm.bean;

import java.math.BigDecimal;
import java.util.Date;

public class BalanceBean {
	
	private String port;   //电子秤的端口
	
	private BigDecimal number;
	
	private String unit;   //单位
	
	private Date updateTime;

	public BigDecimal getNumber() {
		return number;
	}

	public void setNumber(BigDecimal number) {
		this.number = number;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public String getPort() {
		return port;
	}

	public void setPort(String port) {
		this.port = port;
	}

	public String getUnit() {
		return unit;
	}

	public void setUnit(String unit) {
		this.unit = unit;
	}

	@Override
	public String toString() {
		return "Balance [port=" + port + ", number=" + number + ", unit=" + unit + ", updateTime=" + updateTime + "]";
	}
	
}
