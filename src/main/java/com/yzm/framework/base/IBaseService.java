package com.yzm.framework.base;

import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

public interface IBaseService<T,ID> {

	Map<String, Object> findAllByPage(CriteriaBean bean, Pageable page);

	Map<String, Object> findAllByPage(CriteriaBean bean, Pageable page, Boolean auth);
	
	List<T> findAll();

	List<T> findAll(CriteriaBean bean);
	
	T getById(ID id);
	
	T save(T t);
	
	T update(T t);
	
	boolean delete(T t);
	
	boolean deleteById(ID id);
	
	boolean deleteBatchByIds(ID[] ids);
	
	List<T> findAllBy(String pName, Object pValue);
	
}
