package com.yzm.framework.base;

import cn.hutool.core.util.StrUtil;
import com.yzm.common.utils.HttpContextUtil;
import com.yzm.framework.bean.ResponseData;

public abstract class BaseController {

	protected final String REDIRECT = "redirect:";
	protected final String FORWARD = "forward:";
	
	
	protected String getPara(String name) {
		return StrUtil.nullToDefault(HttpContextUtil.getRequest().getParameter(name), "");
	}
	protected String getPara(String name, String default1) {
		return StrUtil.nullToDefault(HttpContextUtil.getRequest().getParameter(name), default1);
	}
	
	/**
	 * 返回页面错误
	 * 
	 * @param msg
	 * @return
	 */
	protected ResponseData error(String msg) {
		return ResponseData.error(msg);
	}

	protected ResponseData error(String code, String msg) {
		return ResponseData.error(code, msg);
	}

	/**
	 * 返回成功消息
	 */
	protected ResponseData success(String msg) {
		return ResponseData.success(msg);
	}

	protected ResponseData success() {
		return ResponseData.success();
	}

	protected ResponseData success(Object data) {
		return ResponseData.success(data);
	}

	protected ResponseData success(String msg, Object data) {
		return ResponseData.success(msg, data);
	}

}
