package com.yzm.framework.base;

import com.yzm.common.utils.PageUtil;
import com.yzm.common.utils.QueryHelp;
import com.yzm.common.utils.ShiroUtils;
import com.yzm.framework.shiro.LoginUser;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Transactional(rollbackFor=Exception.class)
public abstract class BaseService<T, ID> implements IBaseService<T,ID> {
	
	@SuppressWarnings("serial")
	@Override
	public Map<String, Object> findAllByPage(CriteriaBean bean, Pageable page) {
//		LoginUser userInfo = ShiroUtils.getUserInfo();
//		if(!userInfo.getId().equals(1L)) {
//			//非超管，查询自己租户数据
//			bean.setWhId(userInfo.getWarehouseId());
//		}
		Page<T> datas = getRepository().findAll(new Specification<T>() {
			@Override
			public Predicate toPredicate(Root<T> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
				return QueryHelp.getPredicate(root, bean, criteriaBuilder);
			}
		}, page);
		return PageUtil.toPage(datas);
	}
	
	@SuppressWarnings("serial")
	@Override
	public Map<String, Object> findAllByPage(CriteriaBean bean, Pageable page, Boolean auth) {
		if(auth) {
			LoginUser userInfo = ShiroUtils.getUserInfo();
			if(!userInfo.getId().equals(1L)) {
				//非超管，查询自己租户数据
				bean.setWhId(userInfo.getWarehouseId());
			}
		}
		Page<T> datas = getRepository().findAll(new Specification<T>() {
			@Override
			public Predicate toPredicate(Root<T> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
				return QueryHelp.getPredicate(root, bean, criteriaBuilder);
			}
		}, page);
		return PageUtil.toPage(datas);
	}
	
	@SuppressWarnings("serial")
	@Override
	public List<T> findAllBy(String pName, Object pValue) {
		List<T> list = getRepository().findAll(new Specification<T>() {
			@Override
			public Predicate toPredicate(Root<T> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
				Predicate predicate = criteriaBuilder.equal(root.get(pName), pValue);
				return predicate;
			}
		});
		return list;
	}
	
	
	public abstract BaseRepository<T, ID> getRepository();


	@Override
	public T save(T t) {
		return getRepository().save(t);
	}


	@Override
	public T update(T t) {
		return getRepository().save(t);
	}


	@Override
	public boolean delete(T t) {
		getRepository().delete(t);
		return true;
	}


	@Override
	public boolean deleteById(ID id) {
		getRepository().deleteById(id);
		return true;
	}

	@Override
	public List<T> findAll() {
		return getRepository().findAll();
	}

	@Override
	public List<T> findAll(CriteriaBean bean) {
//		LoginUser userInfo = ShiroUtils.getUserInfo();
//		if(!userInfo.getId().equals(1L)) {
//			//非超管，查询自己租户数据
//			bean.setWhId(userInfo.getWarehouseId());
//		}
		return getRepository().findAll(new Specification<T>() {
			@Override
			public Predicate toPredicate(Root<T> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
				return QueryHelp.getPredicate(root, bean, criteriaBuilder);
			}
		});
	}


	@Override
	public T getById(ID id) {
		Optional<T> opt = getRepository().findById(id);
		if(opt.isPresent()) {
			return opt.get();
		}
		return null;
	}


	@Override
	public boolean deleteBatchByIds(ID[] ids) {
		for (ID id : ids) {
			getRepository().deleteById(id);
		}
		return true;
	}
}
