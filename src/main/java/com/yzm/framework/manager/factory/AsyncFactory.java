package com.yzm.framework.manager.factory;

import cn.hutool.core.date.DateUtil;
import cn.hutool.http.useragent.UserAgent;
import cn.hutool.http.useragent.UserAgentUtil;
import com.yzm.property.system.entity.SysLoginInfo;
import com.yzm.property.system.entity.SysOperLog;
import com.yzm.property.system.service.ISysLoginInfoService;
import com.yzm.property.system.service.ISysOperLogService;
import com.yzm.common.utils.AddressUtil;
import com.yzm.common.utils.HttpContextUtil;
import com.yzm.common.utils.ShiroUtils;
import com.yzm.common.utils.SpringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.TimerTask;

/**
 * 异步工厂
 * 
 */
public class AsyncFactory {

	private static final Logger LOG = LoggerFactory.getLogger(AsyncFactory.class);

	/**
	 * 操作日志记录
	 *
	 * @param operLog 操作日志信息
	 * @return 任务task
	 */
	public static TimerTask recordOper(final SysOperLog operLog) {

		return new TimerTask() {
			@Override
			public void run() {
				// 远程查询操作地点
				operLog.setOperLocation(AddressUtil.getRealAddressByIP(operLog.getOperIp()));
				SpringUtil.getBean(ISysOperLogService.class).save(operLog);
			}
		};
	}

	/**
	 * 记录登陆信息
	 * 
	 * @param username 用户名
	 * @param status   状态
	 * @param message  消息
	 * @param args     列表
	 * @return 任务task
	 */
	public static TimerTask recordLogininfor(final String username, final String organName,
			final String status, final String message, final Date loginDate, final Integer loginType) {
		final UserAgent userAgent = UserAgentUtil.parse(HttpContextUtil.getRequest().getHeader("User-Agent"));
		final String ip = HttpContextUtil.getIp();
		return new TimerTask() {
			@Override
			public void run() {
				// 获取客户端操作系统
				String os = userAgent.getOs().toString();
				// 获取客户端浏览器
				String browser = userAgent.getBrowser().toString();
				// 封装对象
				SysLoginInfo logininfor = new SysLoginInfo();
				logininfor.setUsername(username);
				logininfor.setIpaddr(ip);
				logininfor.setLoginLocation(AddressUtil.getRealAddressByIP(ip));
				logininfor.setBrowser(browser);
				logininfor.setOs(os);
				logininfor.setMsg(message);
				if (userAgent.isMobile()) {
					logininfor.setMobile(0);
				} else {
					logininfor.setMobile(1);
				}
				// 日志状态
				logininfor.setStatus(status);
				logininfor.setLoginTime(loginDate);
				logininfor.setWhId(ShiroUtils.getUserInfo().getWarehouseId());
				// 插入数据
				SpringUtil.getBean(ISysLoginInfoService.class).save(logininfor);
				LOG.info("记录登陆信息!");
			}
		};
	}

	/**
	 * 记录登陆信息
	 *
	 * @param username 用户名
	 * @param status   状态
	 * @param message  消息
	 * @param args     列表
	 * @return 任务task
	 */
	public static TimerTask recordLogininfor(final String username, final Long whId,
			final String status, final String message) {
		final UserAgent userAgent = UserAgentUtil.parse(HttpContextUtil.getRequest().getHeader("User-Agent"));
		final String ip = HttpContextUtil.getIp();
		return new TimerTask() {
			@Override
			public void run() {
				// 获取客户端操作系统
				String os = userAgent.getOs().toString();
				// 获取客户端浏览器
				String browser = userAgent.getBrowser().toString();
				// 封装对象
				SysLoginInfo logininfor = new SysLoginInfo();
				logininfor.setUsername(username);
				logininfor.setIpaddr(ip);
				logininfor.setLoginLocation(AddressUtil.getRealAddressByIP(ip));
				logininfor.setBrowser(browser);
				logininfor.setOs(os);
				logininfor.setMsg(message);
				if (userAgent.isMobile()) {
					logininfor.setMobile(0);
				} else {
					logininfor.setMobile(1);
				}
				// 日志状态
				logininfor.setStatus(status);
				logininfor.setLoginTime(DateUtil.date());
				logininfor.setWhId(whId);
				// 插入数据
				SpringUtil.getBean(ISysLoginInfoService.class).save(logininfor);
				LOG.info("记录登陆信息!");
			}
		};
	}
}
