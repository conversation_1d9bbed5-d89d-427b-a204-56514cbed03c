package com.yzm.framework.bean;

import com.yzm.common.utils.ServletUtil;
import com.yzm.common.utils.StringUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;

public class PageBean{

	private Integer limit;
	private Integer page;
	private String sidx;
	private String order;

	public PageBean() {
		this.limit = Integer.parseInt(ServletUtil.getParameter("__limit","20"));
		this.page = Integer.parseInt(ServletUtil.getParameter("__page","0"));
		this.sidx = ServletUtil.getParameter("__sidx");
		this.order =ServletUtil.getParameter("__order");
	}

	public Pageable getPagable() {
		if(StringUtils.isNotEmpty(getSidx())) {
			Sort sort = Sort.by(Direction.fromString(getOrder()), getSidx());
			return PageRequest.of(getPage()-1, getLimit(),sort);
		}else {
			return PageRequest.of(getPage()-1, getLimit());
		}
	}

	public Pageable getPagable(Sort sort) {
		sort.and(Sort.by(Direction.fromString(getOrder()), getSidx()));
		PageRequest page = PageRequest.of(getPage()-1, getLimit(),sort);
		return page;
	}

	public Integer getPage() {
		return page;
	}

	public Integer getLimit() {
		return limit;
	}

	public void setLimit(Integer __limit) {
		this.limit = __limit;
	}

	public String getSidx() {
		return sidx;
	}

	public void setSidx(String sidx) {
		this.sidx = sidx;
	}

	public String getOrder() {
		return order;
	}

	public void setOrder(String order) {
		this.order = order;
	}

	public void setPage(Integer __page) {
		this.page = __page;
	}
}
