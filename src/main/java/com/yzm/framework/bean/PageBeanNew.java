package com.yzm.framework.bean;

import com.yzm.common.utils.ServletUtil;
import com.yzm.common.utils.StringUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import java.util.ArrayList;
import java.util.List;

public class PageBeanNew {

    private Integer limit; // 每页条数
    private Integer page; // 当前页数
    private List<String> sidx; // 排序字段
    private List<String> order; // 排序方向

    public PageBeanNew() {
        this.limit = Integer.parseInt(ServletUtil.getParameter("__limit", "20"));
        this.page = Integer.parseInt(ServletUtil.getParameter("__page", "1"));
        this.sidx = parseList(ServletUtil.getParameter("__sidx"));
        this.order = parseList(ServletUtil.getParameter("__order"));
    }

    /**
     * 构造分页对象，支持多字段排序
     */
    public Pageable getPagable() {
        Sort sort = getSort();
        return PageRequest.of(getPage() - 1, getLimit(), sort);
    }

    /**
     * 获取排序对象，支持多字段排序
     */
    public Sort getSort() {
        if (sidx != null && !sidx.isEmpty() && order != null && !order.isEmpty()) {
            List<Sort.Order> orders = new ArrayList<>();
            for (int i = 0; i < sidx.size(); i++) {
                String field = sidx.get(i);
                String direction = i < order.size() ? order.get(i) : "asc"; // 默认升序
                orders.add(new Sort.Order(Sort.Direction.fromString(direction), field));
            }
            return Sort.by(orders);
        }
        return Sort.unsorted(); // 无排序字段时返回无序对象
    }

    /**
     * 将逗号分隔的字符串解析为列表
     */
    private List<String> parseList(String input) {
        if (StringUtils.isNotEmpty(input)) {
            String[] parts = input.split(",");
            List<String> result = new ArrayList<>();
            for (String part : parts) {
                result.add(part.trim());
            }
            return result;
        }
        return new ArrayList<>();
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public List<String> getSidx() {
        return sidx;
    }

    public void setSidx(List<String> sidx) {
        this.sidx = sidx;
    }

    public List<String> getOrder() {
        return order;
    }

    public void setOrder(List<String> order) {
        this.order = order;
    }
}