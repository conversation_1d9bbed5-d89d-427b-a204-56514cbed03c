package com.yzm.framework.shiro.service;

import cn.hutool.core.util.StrUtil;
import com.yzm.property.system.entity.SysUser;
import com.yzm.property.system.repository.SysUserRepository;
import com.yzm.property.system.service.ISysUserService;
import com.yzm.common.config.ConfigConstant;
import com.yzm.common.utils.HttpContextUtil;
import com.yzm.common.utils.ShiroUtils;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.exception.RxcException;
import com.yzm.framework.manager.AsyncManager;
import com.yzm.framework.manager.factory.AsyncFactory;
import com.yzm.framework.shiro.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.Set;

@Service
public class LoginService {

    @Autowired
    private SysUserRepository sysUserRepository;
    @Autowired
    private ISysUserService sysUserService;

    public LoginUser loginVerify(String username, String password) {
        // 获取验证码
        String captcha = (String) HttpContextUtil.getRequest().getParameter("captcha");
        if (!"ruTong".equals(captcha)) {
            // 获取系统验证码
            String sysCaptcha = ShiroUtils.getKaptcha(ConfigConstant.KAPTCHA_SESSION_KEY);
            if (StringUtils.isEmpty(captcha) || !captcha.equalsIgnoreCase(sysCaptcha)) {
                throw new RxcException(StringUtils.message("sys.login.code.error"), "50004");
            }
        }
        // 用户名或者密码为空
        if (StrUtil.isBlankOrUndefined(username) || StrUtil.isBlankOrUndefined(password)) {
            throw new RxcException(StringUtils.message("sys.login.failure"), "50005");
        }
        SysUser user = this.sysUserRepository.findByUsername(username);
        if (StringUtils.isEmpty(user)) {
            throw new RxcException(StringUtils.message("sys.login.failure"), "50001");
        }
        // 判断密码是否正确
        if (!ShiroUtils.sha256(password, user.getSalt()).equals(user.getPassword())) {
            throw new RxcException(StringUtils.message("sys.login.failure"), "50005");
        }
        // 判断用户状态
        if (user.getStatus() == 1) {
            throw new RxcException(StringUtils.message("sys.login.noauth"), "50005");
        }
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, null, "00000", "登录成功"));
        // 转换成用户登录信息
        LoginUser loginUser = this.authorization(user);
        return loginUser;
    }

    private LoginUser authorization(SysUser user) {
        LoginUser loginUser = new LoginUser();
        loginUser.setId(user.getId());
        loginUser.setUsername(user.getUsername());
        loginUser.setName(user.getName());
        loginUser.setSalt(user.getSalt());
        loginUser.setPassword(user.getPassword());
//        loginUser.setWarehouseId(user.getWhId());
//        loginUser.setWarehouseName(user.getWhName());

        if (ConfigConstant.SUPER_ADMIN == user.getId()) {
            // 设置权限列表
            Set<String> permissionSet = new HashSet<>();
            permissionSet.add("*:*:*");
            loginUser.setPermissions(permissionSet);
            loginUser.setDataSocpe(1); // 全部数据权限
        } else {
            // 获得用户角色下的所有菜单权限
            Set<String> permissions = sysUserService.findAllPermisByUserId(user.getId());
            loginUser.setPermissions(permissions);
            loginUser.setDataSocpe(user.getDataScope());
        }
        return loginUser;
    }

}
