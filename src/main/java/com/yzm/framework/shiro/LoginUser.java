package com.yzm.framework.shiro;

import lombok.Data;

import java.io.Serializable;
import java.util.Set;

@Data
public class LoginUser implements Serializable{
	private static final long serialVersionUID = 5823267492641254593L;
	
	public LoginUser() {
	}
	public LoginUser(String username, String password) {
		this.username = username;
		this.password = password;
	}
	private Long id;
	private String username;
	private String password;
	private String salt;
	private String name;
	
	private Integer pwdSecurityLevel;
	
	private Integer dataSocpe;
	
	private Long warehouseId;
	private String warehouseName;
	/**
	 * 登陆状态
	 */
	private Integer loginStatus;
	/**
	 * 拥有菜单的权限
	 */
	private Set<String> permissions;
}
