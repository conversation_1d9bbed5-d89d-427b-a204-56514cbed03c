package com.yzm.framework.hibernate;

import com.yzm.common.constant.Constant;
import com.yzm.common.utils.ShiroUtils;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.shiro.LoginUser;
import org.apache.shiro.UnavailableSecurityManagerException;
import org.hibernate.EmptyInterceptor;
import org.hibernate.type.Type;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.Date;

@Component
public class FieldSetInterceptor extends EmptyInterceptor {
    private static final long serialVersionUID = -5248367455579990663L;

    /**
     * 添加数据
     */
    @Override
    public boolean onSave(Object entity, Serializable id, Object[] state, String[] propertyNames, Type[] types) {
        LoginUser userInfo = null;
        try {
            userInfo = ShiroUtils.getUserInfo();
        } catch (UnavailableSecurityManagerException E) {

        }
        if (!StringUtils.isEmpty(userInfo)) {
            // 添加数据
            for (int index = 0; index < propertyNames.length; index++) {
                if (Constant.FIELD_CREATE_TIME.equals(propertyNames[index])) {
                    if (StringUtils.isEmpty(state[index])) {
                        state[index] = new Date();
                    }
                    continue;
                }
                if (Constant.FIELD_CREATE_BY.equals(propertyNames[index])) {
                    if (StringUtils.isEmpty(state[index])) {
                        state[index] = userInfo.getUsername();
                    }
                    continue;
                }
                if (Constant.FIELD_WH_ID.equals(propertyNames[index])) {
                    if (StringUtils.isEmpty(state[index])) {
                        state[index] = userInfo.getWarehouseId();
                    }
                    continue;
                }
                if (Constant.FIELD_WH_NAME.equals(propertyNames[index])) {
                    if (StringUtils.isEmpty(state[index])) {
                        state[index] = userInfo.getWarehouseName();
                    }
                    continue;
                }
            }
        }
        return true;
    }

    /**
     * 更新数据
     */
    @Override
    public boolean onFlushDirty(Object entity, Serializable id, Object[] state, Object[] previousState,
                                String[] propertyNames, Type[] types) {
        LoginUser userInfo = null;
        try {
            userInfo = ShiroUtils.getUserInfo();
        } catch (UnavailableSecurityManagerException E) {

        }
        if (!StringUtils.isEmpty(userInfo)) {

            for (int index = 0; index < propertyNames.length; index++) {
                if (Constant.FIELD_UPDATE_TIME.equals(propertyNames[index])) {
                    if (StringUtils.isEmpty(state[index])) {
                        state[index] = new Date();
                    }
                    continue;
                }
                if (Constant.FIELD_UPDATE_BY.equals(propertyNames[index])) {
                    if (StringUtils.isEmpty(state[index])) {
                        state[index] = userInfo == null ? "" : userInfo.getUsername();
                    }
                    continue;
                }
                /*
                 * 不更新NULL 值 代替BeanUtil.copyProperties(source, target,
                 * CopyOptions.create().ignoreNullValue());
                 */
                if (StringUtils.isNotEmpty(previousState[index]) && state[index] == null) {
                    state[index] = previousState[index];
                }
            }
        }
        return true;
    }
}
