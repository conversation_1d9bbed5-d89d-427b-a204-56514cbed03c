package com.yzm.framework.freemark;

import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;

@Component
public class ConfigUtil {

	public String getVersion() {
		return Global.getVersion();
	}
	
	public String getDvVersion() {
		return Global.getVersion()+"-"+ Global.getDvVersion();
	}
	
	public String getSysLang() {
		return LocaleContextHolder.getLocale().toString();
	}

	public String getKey(String key) {
		return Global.getDbKey(key);
	}

	public String getKey(String key,String default0) {
		return Global.getDbKey(key,default0);
	}
	
	public boolean optimize(){
		return Global.optimize();
	}
}
