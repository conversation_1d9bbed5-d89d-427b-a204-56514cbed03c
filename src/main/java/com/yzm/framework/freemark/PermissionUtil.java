package com.yzm.framework.freemark;

import cn.hutool.core.util.StrUtil;
import org.apache.shiro.SecurityUtils;
import org.springframework.stereotype.Component;

/**
 * 前端权限校验
 * <AUTHOR>
 *2024年1月14日
 */
@Component
public class PermissionUtil {

	/** 没有权限，hidden用于前端隐藏按钮 */
	public static final String NOACCESS = "hidden";
	
	/**
	 * 验证用户是否具备某权限，无权限返回hidden用于前端隐藏（如需返回Boolean使用isPermitted）
	 *
	 * @param permission 权限字符串
	 * @return 用户是否具备某权限
	 */
	public String hasPermi(String permission) {
		return isPermitted(permission) ? StrUtil.EMPTY : NOACCESS;
	}

	/**
	 * 判断用户是否拥有某个权限
	 *
	 * @param permission 权限字符串
	 * @return 用户是否具备某权限
	 */
	public boolean isPermitted(String permission) {
		return SecurityUtils.getSubject().isPermitted(permission);
	}
	
	/**
	 * 判断用户是否拥有多个权限
	 * 
	 * @param permission
	 * @return
	 */
	public boolean isPermitteds(String permission) {
		boolean isFlag = false;
		String[] permissions = permission.split(",");
		for (String p : permissions) {
			if (SecurityUtils.getSubject().isPermitted(p)) {
				isFlag = true;
				break;
			}
		}
		return isFlag;
	}
}
