package com.yzm.framework.freemark;

import cn.hutool.core.util.StrUtil;
import com.yzm.property.system.service.ISysConfigService;
import com.yzm.common.config.ConfigConstant;
import com.yzm.common.utils.SpringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 获取系统配置参数
 */
public class Global {

	private static final Logger log = LoggerFactory.getLogger(Global.class);

	/**
	 * 获取配置
	 */
	public static String getConfig(String key) {
		try {
			return SpringUtil.getBean(ISysConfigService.class).getConfigObject(key, String.class);
		} catch (Exception e) {
			log.error("获取配置错误!", e);
			return null;
		}

	}
	
	/**
	 * 压缩前端脚本
	 * @return
	 */
	public static boolean optimize() {
		return StrUtil.blankToDefault(getConfig(ConfigConstant.SYS_COMPRESS), "false").equals("true") ? true : false;
	}

	/**
	 * 获取项目版本
	 */
	public static String getVersion() {
		return StrUtil.blankToDefault(getConfig(ConfigConstant.SYS_CONFIG_VERSION), "1.0.0");
	}

	/**
	 * @Description:样式JS 版本
	 * @<NAME_EMAIL>
	 * @time 2019-04-28 22:17
	 * @return
	 *
	 */
	public static String getDvVersion() {
		return StrUtil.blankToDefault(getConfig(ConfigConstant.SYS_CONFIG_DV_VERSION), "20190425");
	}

	/**
	 * 获取后台系统标题
	 */
	public static String getTitle() {
		return StrUtil.blankToDefault(getConfig(ConfigConstant.SYS_CONFIG_TITLE), "管理系统");
	}

	public static String getDbKey(String key) {
		return StrUtil.blankToDefault(getConfig(key), "默认值[获取数据库值失败]");
	}

	public static String getDbKey(String key, String default0) {
		return StrUtil.blankToDefault(getConfig(key), default0);
	}

	/**
	 * @Title: getRootPath
	 * @Description:项目文件根路径
	 * <AUTHOR>
	 * @Date: 2020年8月13日
	 */
	public static String getRootPath() {

		return StrUtil.blankToDefault(getConfig(ConfigConstant.SYS_CONFIG_ROOT_PATH), "c:/sunadmin/");
	}

	/**
	 * temp 临时根目录
	 */
	public static String getTempPath() {
		return StrUtil.blankToDefault(getConfig(ConfigConstant.SYS_CONFIG_TEMP_PATH), getRootPath() + "temp");
	}

	/**
	 * attach 上传组件根目录
	 */
	public static String getAttachPath() {
		return StrUtil.blankToDefault(getConfig(ConfigConstant.SYS_CONFIG_ATTACH_PATH), getRootPath() + "attach");
	}

}
