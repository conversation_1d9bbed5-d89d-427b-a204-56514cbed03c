package com.yzm.framework.freemark;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.yzm.property.system.entity.SysDictData;
import com.yzm.property.system.service.ISysDictDataService;
import com.yzm.common.utils.SpringUtil;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class DictUtil {

	/**
	 * 根据字典类型查询字典数据信息
	 *
	 * @param dictType 字典类型
	 * @return 参数键值
	 */
	public List<SysDictData> getType(String dictType) {
		return SpringUtil.getBean(ISysDictDataService.class).selectDictDataByType(dictType);
	}
	
	public String getDictListJson(String dictType) {
		List<SysDictData> list = SpringUtil.getBean(ISysDictDataService.class).selectDictDataByType(dictType);
		JSONArray jsonArray = JSONUtil.parseArray(list, false);
		return jsonArray.toString();
	}
	
	/**
	 * 根据字典类型和字典键值查询字典数据信息
	 *
	 * @param dictType  字典类型
	 * @param dictValue 字典键值
	 * @return 字典标签
	 */
	public String getLabel(String dictType, String dictValue) {
		return SpringUtil.getBean(ISysDictDataService.class).selectDictLabel(dictType, dictValue);
	}
}
