package com.yzm.app;

import com.yzm.common.utils.ShiroUtils;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.ResponseData;
import com.yzm.framework.exception.RxcException;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.apache.shiro.subject.Subject;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/app")
public class APPLoginController extends BaseController {

	@PostMapping("/login")
	public ResponseData login(String username, String password) {

		Subject subject = null;
		try {
			// 账号密码登录
			UsernamePasswordToken token = new UsernamePasswordToken(username, password);
			subject = ShiroUtils.getSubject();
			subject.login(token);
			return success("登录成功",JwtUtil.buildJWT(token.getUsername()));
		} catch (Exception e) {
			e.printStackTrace();
			e.printStackTrace();
			RxcException ex = (RxcException) e.getCause();
			String msg = StringUtils.message("sys.login.failure");
			if (!StringUtils.isEmpty(e.getMessage())) {
				msg = e.getMessage();
			}
			if ("50004".equals(ex.getCode())) {
				return error(ex.getCode(), ex.getMessage());
			}
			return error(msg);
		}
	}
}
