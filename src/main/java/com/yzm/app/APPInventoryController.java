package com.yzm.app;

import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.basis.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/9 9:57
 */
@Controller
@RequestMapping("/app/inventory")
public class APPInventoryController extends BaseController {

    private String urlPrefix = "basis/inventory";

    @Autowired
    private InventorySimpleService inventorySimpleService;

    @Autowired
    private InventoryInfoService inventoryInfoService;

    /**
     * 根据关键字和分页查询盘点
     *
     */
    @PostMapping("/list")
    @ResponseBody
    public ResponseData showInventoryByKeywordAndPage(
            PageBean pageBean
    ) {
        return success(inventorySimpleService.findAllByPage(pageBean.getPagable()));
    }

    /**
     * 返回盘点详细信息
     */
    @RequestMapping("/showInfos/{id}")
    @ResponseBody
    public ResponseData showInfos(@PathVariable("id") Long id, ModelMap mmap,PageBean pageBean) {
        return success(inventoryInfoService.findAllByInventSimpleIdAndStatus(id,pageBean.getPagable()));
    }

    /**
     * 查找info
     */
    @RequestMapping("/assetsByTable/{id}")
    @ResponseBody
    public ResponseData assetsByTable(@PathVariable("id")Long id) {
        return success(inventorySimpleService.findAssetsByTableAndType(id));
    }


    /**
     * 盘点
     */
    @GetMapping("/inventoryAssets")
    @ResponseBody
    public ResponseData inventory(
            @RequestParam("id")Long id,
            @RequestParam("barcode")String barcode
    ) {
        String inventory = inventorySimpleService.inventory(id, barcode);
        return  inventory == null?success("资产盘点成功"):error(inventory);
    }

}
