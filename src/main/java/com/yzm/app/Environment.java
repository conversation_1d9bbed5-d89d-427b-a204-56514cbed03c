package com.yzm.app;

import com.yzm.property.system.entity.SysUser;

public class Environment {
	
	private static final ThreadLocal<SysUser> USER_LOCAL = new ThreadLocal<>();
	
	/**
	 * 获取当前用户
	 * 
	 * @return 如果没有登录，返回null
	 */
	public static SysUser getUser() {
		return USER_LOCAL.get();
	}

	/**
	 * 设置用户上下文
	 * 
	 * @param user
	 *            用户
	 */
	public static void setUser(SysUser user) {
		USER_LOCAL.set(user);
	}
	
	

}
