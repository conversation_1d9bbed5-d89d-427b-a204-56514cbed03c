package com.yzm.app;

import com.alibaba.fastjson.JSON;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.system.entity.SysUser;
import com.yzm.property.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Component
public class AuthInterceptor extends HandlerInterceptorAdapter {
	
	@Autowired
	private ISysUserService iSysUserService;

	@Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
			throws Exception {
		boolean flag = false;
		// 获取 HTTP HEAD 中的 TOKEN
		String authorization = request.getHeader("token");
		if (authorization == null) {
			authorization = request.getParameter("token");
		}
		// 校验 TOKEN
		flag = authorization != null ? JwtUtil.checkJWT(authorization) : false;
		// 如果校验未通过，返回500 状态
		if (!flag) {
			// 重新登录
			response.getWriter().write(JSON.toJSONString(new ResponseData("50000", "no found token", null)));
			return false;
		}
		// 获得用户信息
		String username = JwtUtil.generateContent(authorization);
		SysUser user = iSysUserService.findUserByUserName(username);
		if (user == null) {
			// 重新登录
			response.getWriter().write(JSON.toJSONString(new ResponseData("50000", "token exception", null)));
			return false;
		}
		Environment.setUser(user);
		return true;
	}
}
