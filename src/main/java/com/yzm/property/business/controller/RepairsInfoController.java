package com.yzm.property.business.controller;

import com.yzm.common.annotation.BussinessLog;
import com.yzm.common.annotation.RepeatSubmit;
import com.yzm.common.enums.BusinessType;
import com.yzm.common.utils.PDFTemplateUtil;
import com.yzm.common.utils.ShiroUtils;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.basis.service.PersonnelService;
import com.yzm.property.basis.service.WarehouseService;
import com.yzm.property.business.criteria.BorrowReturnItemCriteria;
import com.yzm.property.business.entity.BorrowReturn;
import com.yzm.property.business.entity.BorrowReturnItem;
import com.yzm.property.business.service.BorrowReturnItemService;
import com.yzm.property.business.service.BorrowReturnService;
import com.yzm.property.consumable.service.ConsumableRepertoryService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 维修归还
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 11:33
 */
@Controller
@RequestMapping(value = "business/repairsInfo")
public class RepairsInfoController extends BaseController {
    private String urlPrefix = "business/repairsInfo";

    @Autowired
    private BorrowReturnService borrowReturnService;
    @Autowired
    private BorrowReturnItemService borrowReturnItemService;
    @Autowired
    private PersonnelService personnelService;
    @Autowired
    private WarehouseService warehouseService;
    @Autowired
    private ConsumableRepertoryService consumableRepertoryService;


    @RequiresPermissions("business:borrowList:view")
    @RequestMapping("/borrowList")
    public String borrowList(ModelMap mmap) {
        return urlPrefix + "/borrowList";
    }

    @RequiresPermissions("business:returnList:view")
    @RequestMapping("/returnList")
    public String returnList(ModelMap mmap) {
        return urlPrefix + "/returnList";
    }


    /**
     * 借用或归还页面
     */
    @GetMapping("add")
    public String add(String id, ModelMap mmap) {
        mmap.addAttribute("personnelList", personnelService.findByDeleteFlag(0));
        mmap.put("orId", id);
        //借用
        if (id.equals("0")) {
            return urlPrefix + "/addBorrow";
        } else {
            return urlPrefix + "/addReturns";
        }
    }

    /**
     * 借用或归还页面
     */
    @GetMapping("returnInfo")
    public String returnInfo(String id, ModelMap mmap) {
        mmap.addAttribute("personnelList", personnelService.findByDeleteFlag(0));
        mmap.put("orId", id);
        mmap.put("borrowReturn", borrowReturnService.getById(Long.parseLong(id)));

        mmap.put("warehouseList",warehouseService.findAll());
        mmap.put("areaList",new ArrayList<>());
        return urlPrefix + "/addReturns";

    }

    /**
     * 根据id查询详细
     *
     * @param id
     * @return
     */
    @GetMapping("/findDetailById")
    @ResponseBody
    public ResponseData findDetailById(String id) {
        List<BorrowReturnItem> borrowReturnItemData = borrowReturnItemService.findByBorrowReturnIdAndBiReturnStandby(id,0);

        return success().put("borrowReturnItemList", borrowReturnItemData);
    }
    /**
     * 借用(选择资产信息)页面
     */
    @GetMapping("/addBorrowItem")
    public String addBorrowItem() {
        return urlPrefix + "/addBorrowItem";
    }

    /**
     * 归还(选择资产信息)页面
     */
    @GetMapping("/addReturnsItem")
    public String addReturnsItem() {
        return urlPrefix + "/addReturnsItem";
    }

    /**
     * 修改跳转页面
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        mmap.addAttribute("personnelList", personnelService.findByDeleteFlag(0));
        mmap.put("borrowReturn", borrowReturnService.getById(id));
        return urlPrefix + "/edit";
    }


    /**
     * 归还跳转页面
     */
    @GetMapping("/returns/{id}")
    public String returns(@PathVariable("id") Long id, ModelMap map) {
        BorrowReturn borrowReturn = borrowReturnService.getById(id);
        map.put("borrowReturn", borrowReturn);
        return urlPrefix + "/returns";
    }

    /**
     * 详细显示跳转页面
     *
     * @param id
     * @param mmap
     * @return
     */
    @GetMapping("/view/{id}")
    public String viewPage(@PathVariable("id") Long id, ModelMap mmap) {
        BorrowReturn borrowReturn = borrowReturnService.getById(id);
        List<BorrowReturnItem> list = borrowReturnItemService.findByBiNumber(borrowReturn.getBrNumber());
        mmap.put("borrowReturn", borrowReturn);
        mmap.put("borrowReturnList", list);
        if (borrowReturn.getBrBusinessStandby().equals("借用")) {
            return urlPrefix + "/borrowView";
        } else if (borrowReturn.getBrBusinessStandby().equals("维修登记")) {
            return urlPrefix + "/borrowView";
        }else {
            return urlPrefix + "/returnView";
        }
    }


   /* @ResponseBody
    @PostMapping(value = "/list")*/
   /* public ResponseData page(PageBean pageBean, BorrowReturnCriteria area) {
        Map<String, Object> datas = borrowReturnService.findAllByPage(area, pageBean.getPagable());
        return success(datas);
    }*/

    /**
     * 借用查询列表
     *
     * @param keyword
     * @param type
     * @param pageBean
     * @return
     */
    //@PostMapping("/list")
    @RequestMapping(value = "/listBorrow", method = RequestMethod.POST)
    @ResponseBody
    public ResponseData showAllBorrowInfo(
            @RequestParam(value = "keyword", required = false)
                    String keyword,
            @RequestParam(value = "type", required = false)
                    String type,
            PageBean pageBean
    ) {

        return success(borrowReturnService.showAllRepairsInfo(keyword, type, pageBean));

    }

    /**
     * 归还查询列表
     *
     * @param keyword
     * @param type
     * @param pageBean
     * @return
     */
    @RequestMapping(value = "/listReturn", method = RequestMethod.POST)
    @ResponseBody
    public ResponseData showAllReturnInfo(
            @RequestParam(value = "keyword", required = false)
                    String keyword,
            @RequestParam(value = "type", required = false)
                    String type,
            PageBean pageBean
    ) {

        return success(borrowReturnService.showAllRepairsInfoBack(keyword, type, pageBean));

    }

    /**
     * 待归还材料查询
     *
     * @return
     */
    @RequestMapping(value = "/listReturnItem")
    @ResponseBody
    public ResponseData listReturnItem(PageBean pageBean) {
        BorrowReturnItemCriteria borrowReturnItemCriteria = new BorrowReturnItemCriteria();
        borrowReturnItemCriteria.setBiReturnStandby(0);

        PageRequest page1 = PageRequest.of(pageBean.getPage() - 1, pageBean.getLimit(), Sort.Direction.DESC, "createTime");
        return success(borrowReturnItemService.findAllByPage(borrowReturnItemCriteria, page1));
    }


    /**
     * 保存
     */
    @RequestMapping("/add")
    @RequiresPermissions("business:area:add")
    @ResponseBody
//    @BussinessLog(businessType = BusinessType.INSERT, title = "新增维修归还信息")
    public ResponseData save(BorrowReturn borrowReturn, String orId) {
        if (orId.equals("0")) {
            return success(borrowReturnService.saveBorrowReturn(borrowReturn,"维修登记"));
        } else {
            return success(borrowReturnService.returns(borrowReturn,"维修归还"));
        }
    }

    /**
     * 修改
     */
    @RequestMapping(value = "/edit", method = RequestMethod.POST)
    @RequiresPermissions("business:borrowReturn:edit")
    @ResponseBody
    @BussinessLog(businessType = BusinessType.UPDATE, title = "修改维修归还信息")
    public ResponseData update(BorrowReturn borrowReturn) {
        return StringUtils.isNotEmpty(borrowReturnService.updateBorrowReturn(borrowReturn)) ? success() : error("修改失败!");
    }

    /**
     * 删除
     */
    @RequestMapping(value = "/del", method = RequestMethod.POST)
    @RequiresPermissions("business:area:del")
    @ResponseBody
    @BussinessLog(businessType = BusinessType.DELETE, title = "删除维修归还信息")
    public ResponseData delete(Long[] ids) {
        return borrowReturnService.deleteBatchByIds(ids) ? success() : error("删除失败!");
    }


    /**
     * 提交审核
     */
    @RepeatSubmit
    @BussinessLog(title = "借用", businessType = BusinessType.RUN)
    @RequestMapping(value = "/submit", method = RequestMethod.POST)
    @ResponseBody
    public ResponseData submit(Long[] ids) {
        return borrowReturnService.submitBorrowReturnByIds(ids) ? success() : error("提交失败!");
    }

    /**
     * 送审、同意、拒绝、取消(借用)
     */
    @RequestMapping("/audit/{id}/{status}")
    @RequiresPermissions("sys:repairs:review")
    @BussinessLog(businessType = BusinessType.UPDATE, title = "修改状态")
    public String audit(@PathVariable("id") Long id, @PathVariable("status") Integer status, ModelMap mmap) {
        //如果状态为送审
        if (status == 2) {
            borrowReturnService.review(id, status);
        } else {
            borrowReturnService.audit(id, status);
        }
        return urlPrefix + "/borrowList";
    }

    /**
     * 根据单据号查询详细
     *
     * @param biNumber
     * @return
     */
    @GetMapping("/findDetailByBrNumber")
    @ResponseBody
    public ResponseData findDetailByBrNumber(@RequestParam String biNumber) {
        List<BorrowReturnItem> borrowReturnItemData = borrowReturnItemService.findByBiNumberUpAssetsInfo(biNumber);
        return success().put("borrowReturnItemList", borrowReturnItemData);
    }

    @GetMapping("exportInformation")
    public void downloadFile(HttpServletResponse response, Long id) throws Exception {
        ByteArrayOutputStream baos = null;
        OutputStream out = null;
        try {
            BorrowReturn borrowReturn = borrowReturnService.getById(id);
            Map<String, Object> data = new HashMap<>();
            data.put("title", "预览PDF");
            //申领编号
            data.put("recordNumber",borrowReturn.getBrNumber());
            data.put("brPeople",borrowReturn.getBrPeople());
            data.put("brTime",new SimpleDateFormat("yyyy-MM-dd").format(borrowReturn.getBrTime()));
            data.put("brOrganization",borrowReturn.getBrOrganization());
            data.put("brName",borrowReturn.getBrName());
            data.put("brRemark",borrowReturn.getBrRemark());
            data.put("custom3",borrowReturn.getCustom3());
            data.put("handlerName",borrowReturn.getHandlerName());

            data.put("user", ShiroUtils.getUserInfo().getName());
            data.put("now", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy.MM.dd HH:mm:ss")));
            List<BorrowReturnItem> overList = borrowReturnItemService.findByBorrowReturnId(String.valueOf(borrowReturn.getId()));
//1. 根据 number
            Map<String, List<BorrowReturnItem>> map = overList.stream().collect(Collectors.groupingBy(user -> user.getAssetsNumber()));
// 2. 对于每个分组内的用户，将其 rfid 字段进行拼接
            List<BorrowReturnItem> result = new ArrayList<>();
            for (Map.Entry<String, List<BorrowReturnItem>> entry : map.entrySet()) {
                List<BorrowReturnItem> userList = entry.getValue();
                BorrowReturnItem user1 = userList.get(0);
                StringBuilder builder = new StringBuilder();
                int count = 0;
                for (BorrowReturnItem user : userList) {
                    builder.append(user.getAssetsRfid()).append(",");
                    count++;
                }
                user1.setNowRepertory(count);
                user1.setConsumableNum(count);
                user1.setAssetsRfid(builder.toString());
                result.add(user1);
            }
            data.put("detailList", result);
            data.put("count", borrowReturn.getBrStorageTotal());
            baos = PDFTemplateUtil.createPDF(data, "维修单.ftl");


            // 设置响应消息头，告诉浏览器当前响应是一个下载文件
            response.setContentType("application/pdf;charset=UTF-8");
            // 告诉浏览器，当前响应数据要求用户干预保存到文件中，以及文件名是什么 如果文件名有中文，必须URL编码
            String fileName = URLEncoder.encode(borrowReturn.getBrNumber()+ ".pdf", "UTF-8");
            response.setHeader("Content-Disposition", "inline;filename=" + fileName);
            out = response.getOutputStream();
            baos.writeTo(out);
            baos.close();
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("导出失败：" + e.getMessage());
        } finally {
            if (baos != null) {
                baos.close();
            }
            if (out != null) {
                out.close();
            }
        }
    }

    @GetMapping("exportInformationGH")
    public void exportInformationGH(HttpServletResponse response, Long id) throws Exception {
        ByteArrayOutputStream baos = null;
        OutputStream out = null;
        try {
            BorrowReturn borrowReturn = borrowReturnService.getById(id);
            Map<String, Object> data = new HashMap<>();
            data.put("title", "预览PDF");
            //申领编号
            data.put("recordNumber",borrowReturn.getBrNumber());
            data.put("brPeople",borrowReturn.getBrPeople());
            data.put("brActualReturnTime",new SimpleDateFormat("yyyy-MM-dd").format(borrowReturn.getBrActualReturnTime()));
            data.put("brOrganization",borrowReturn.getBrOrganization());
            data.put("brName",borrowReturn.getBrName());
            data.put("brRemark",borrowReturn.getBrReturnRemark());

            data.put("user", ShiroUtils.getUserInfo().getName());
            data.put("now", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy.MM.dd HH:mm:ss")));
            List<BorrowReturnItem> overList = borrowReturnItemService.findByBorrowReturnId(String.valueOf(borrowReturn.getId()));
//1. 根据 number
            Map<String, List<BorrowReturnItem>> map = overList.stream().collect(Collectors.groupingBy(user -> user.getAssetsNumber()));

// 2. 对于每个分组内的用户，将其 rfid 字段进行拼接
            List<BorrowReturnItem> result = new ArrayList<>();
            for (Map.Entry<String, List<BorrowReturnItem>> entry : map.entrySet()) {
                List<BorrowReturnItem> userList = entry.getValue();
                BorrowReturnItem user1 = userList.get(0);
                StringBuilder builder = new StringBuilder();
                int count = 0;
                for (BorrowReturnItem user : userList) {
                    builder.append(user.getAssetsRfid()).append(",");
                    count++;
                }
                user1.setNowRepertory(count);
                user1.setConsumableNum(count);
                user1.setAssetsRfid(builder.toString());
                result.add(user1);
            }
            data.put("detailList", result);
            data.put("count", borrowReturn.getBrStorageTotal());
            baos = PDFTemplateUtil.createPDF(data, "维修归还.ftl");


            // 设置响应消息头，告诉浏览器当前响应是一个下载文件
            response.setContentType("application/pdf;charset=UTF-8");
            // 告诉浏览器，当前响应数据要求用户干预保存到文件中，以及文件名是什么 如果文件名有中文，必须URL编码
            String fileName = URLEncoder.encode(borrowReturn.getBrNumber()+ ".pdf", "UTF-8");
            response.setHeader("Content-Disposition", "inline;filename=" + fileName);
            out = response.getOutputStream();
            baos.writeTo(out);
            baos.close();
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("导出失败：" + e.getMessage());
        } finally {
            if (baos != null) {
                baos.close();
            }
            if (out != null) {
                out.close();
            }
        }
    }
}
