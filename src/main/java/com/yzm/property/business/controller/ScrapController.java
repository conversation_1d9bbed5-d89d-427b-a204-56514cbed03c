package com.yzm.property.business.controller;

import com.yzm.common.annotation.BussinessLog;
import com.yzm.common.annotation.RepeatSubmit;
import com.yzm.common.enums.BusinessType;
import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.framework.shiro.LoginUser;
import com.yzm.property.business.entity.BusinessScrap;
import com.yzm.property.business.entity.HandleStatus;
import com.yzm.property.business.entity.ScrapEntity;
import com.yzm.property.business.service.BusinessScrapService;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/28 14:41
 */
@Controller
@RequestMapping("bus/scrap")
public class ScrapController extends BaseController {

    SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Autowired
    private BusinessScrapService businessScrapService;


    private String urlPrefix = "business/scrap";

    @RequiresPermissions("sys:scrap:view")
    @RequestMapping
    public String scrap(ModelMap mmap) {
        return urlPrefix + "/list";
    }

    /**
     * 根据关键字和分页查询报修信息
     *
     * @param keyword  关键字
     * @param pageBean 分页
     * @return
     */
    @PostMapping("/list")
    @ResponseBody
    public ResponseData showAllScrapInfo(
            @RequestParam(value = "keyword", required = false)
                    String keyword,
            PageBean pageBean
    ) {
        return success(businessScrapService.showAllScraps(keyword, pageBean));
    }

    /**
     * 跳转并携带状态和资产列表至添加报废信息页面
     *
     * @param mmap
     * @return
     */
    @GetMapping("/add")
    public String addAssetsInfo(ModelMap mmap) {
        return urlPrefix + "/add";
    }

    /**
     * 新增报废信息
     *
     * @param scrapEntity
     * @return
     */
    @PostMapping("/add")
    @ResponseBody
    @RequiresPermissions("sys:scrap:add")
    @BussinessLog(businessType = BusinessType.INSERT, title = "新增报废信息")
    public ResponseData addScrap(
            ScrapEntity scrapEntity
    ) {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        BusinessScrap businessScrap = scrapEntity.getBusinessScrap();
        businessScrap.setUserId(user.getId());
        businessScrap.setUserName(user.getUsername());
        businessScrap.setScrapDate(format.format(new Date()));
        return businessScrapService.save(scrapEntity.getIds(), businessScrap) ? success() : error("新增失败");
    }

    /**
     * 修改报废
     */
    @GetMapping("/update/{id}")
    @RequiresPermissions("sys:scrap:update")
    @BussinessLog(businessType = BusinessType.UPDATE, title = "修改报废状态")
    public String updateScrap(@PathVariable("id") Long id, ModelMap mmap) {
        BusinessScrap businessScrap = businessScrapService.findById(id);
        Map map = getScrapResult(businessScrap);
        mmap.put("statusList", map.get("statusList"));
        mmap.put("scrap", businessScrap);
        Integer status = businessScrap.getScrapStatus();
        if (status == 2 || status == 3 || status == 6) {
            return urlPrefix + "/show";
        } else {
            return urlPrefix + "/edit";
        }
    }

    /**
     * 展示报废信息
     */
    @GetMapping("/view/{id}")
    @RequiresPermissions("sys:scrap:view")
    public String showScrap(@PathVariable("id") Long id, ModelMap mmap) {
        BusinessScrap businessScrap = businessScrapService.findById(id);
        Map map = getScrapResult(businessScrap);
        mmap.put("statusList", map.get("statusList"));
        mmap.put("scrap", businessScrap);
        return urlPrefix + "/show";
    }

    private Map getScrapResult(BusinessScrap businessScrap) {
        HashMap<String, Object> map = new HashMap<>();
        List<HandleStatus> attrsToUpdate = new ArrayList<>();
        switch (businessScrap.getScrapStatus()) {
            case 1:
                attrsToUpdate.add(new HandleStatus(1, "未送审"));
                break;
            case 2:
                attrsToUpdate.add(new HandleStatus(2, "审核中"));
                break;
            case 3:
                attrsToUpdate.add(new HandleStatus(3, "审核通过"));
                break;
            case 4:
                attrsToUpdate.add(new HandleStatus(4, "审核不通过"));
                break;
            case 5:
                attrsToUpdate.add(new HandleStatus(5, "已取消"));
                break;
            case 6:
                attrsToUpdate.add(new HandleStatus(6, "完成"));
                break;
        }
        map.put("statusList", attrsToUpdate);
        return map;
    }

    /**
     * 修改报废信息
     */
    @PostMapping("/update")
    @ResponseBody
    @RequiresPermissions("sys:scrap:update")
    @BussinessLog(businessType = BusinessType.UPDATE, title = "编辑报废信息")
    public ResponseData updateScrap(ScrapEntity scrapEntity) {
        try{
            return businessScrapService.update(scrapEntity) ? success() : error("新增失败");
        }catch (Exception e){
            return error(e.getMessage());
        }

    }

    /**
     * 修改报废状态
     *
     * @param id
     * @param status
     * @param mmap
     * @return
     */
    @RequestMapping("/review/{id}/{status}")
    @RequiresPermissions("sys:scrap:review")
    @BussinessLog(businessType = BusinessType.UPDATE, title = "修改报废状态")
    public String reviewScrap(@PathVariable("id") Long id, @PathVariable("status") Integer status, ModelMap mmap) {
        businessScrapService.changeStatus(id, status);
        return urlPrefix + "/list";
    }

    /**
     * 删除报废信息
     *
     * @param ids
     * @return
     */
    @PostMapping("/delete")
    @ResponseBody
    @RequiresPermissions("sys:repairs:delete")
    @BussinessLog(businessType = BusinessType.DELETE, title = "删除报废信息")
    public ResponseData deleteScrap(Long[] ids) {
        businessScrapService.delete(ids);
        return success();
    }

    /**
     * 新增详细页面
     */
    @GetMapping("/addItem")
    public String addItem() {
        return urlPrefix + "/addItem";
    }

    /**
     * 提交审核
     */
    @RepeatSubmit
    @BussinessLog(title = "报废", businessType = BusinessType.RUN)
    @RequestMapping(value = "/audit", method = RequestMethod.POST)
    @ResponseBody
    public ResponseData submit(Long[] ids) {
        return businessScrapService.auditByIds(ids) ? success() : error("提交失败!");
    }
}
