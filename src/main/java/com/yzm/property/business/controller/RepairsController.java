package com.yzm.property.business.controller;

import com.yzm.common.annotation.BussinessLog;
import com.yzm.common.annotation.RepeatSubmit;
import com.yzm.common.enums.BusinessType;
import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.framework.shiro.LoginUser;
import com.yzm.property.basis.entity.AssetsInfo;
import com.yzm.property.basis.entity.BasisPersonnel;
import com.yzm.property.basis.service.AssetsInfoService;
import com.yzm.property.basis.service.PersonnelService;
import com.yzm.property.business.entity.*;
import com.yzm.property.business.repository.BusinessRepairsItemRepository;
import com.yzm.property.business.service.BusinessRepairsService;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/28 14:41
 */
@Controller
@RequestMapping("bus/repairs")
public class RepairsController extends BaseController {

    private String urlPrefix = "business/repairs";

    @Autowired
    private BusinessRepairsService businessRepairsService;

    @Autowired
    private AssetsInfoService assetsInfoService;

    @Autowired
    private PersonnelService personnelService;
    @Autowired
    private BusinessRepairsItemRepository businessRepairsItemRepository;

    /**
     * 每天凌晨判断资产的报修状态
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public void TuoBao() {
        List<AssetsInfo> list = assetsInfoService.findAll();
        Date today = new Date();
        for (AssetsInfo assetsInfo : list) {
            if (today.after(assetsInfo.getTuobaoDate())) {
                assetsInfo.setMaintenanceStatus("已脱保");
                assetsInfoService.save(assetsInfo);
            }
        }
    }

    /**
     * 跳转至报修列表页
     *
     * @param mmap
     * @return
     */
    @RequiresPermissions("sys:repairs:view")
    @RequestMapping
    public String repairs(ModelMap mmap) {
        return urlPrefix + "/list";
    }

    /**
     * 跳转至报修归还列表页
     *
     * @param mmap
     * @return
     */
    @RequiresPermissions("sys:repairs:view")
    @RequestMapping("/returnView")
    public String repairsReturn(ModelMap mmap) {
        return urlPrefix + "/listToReturn";
    }

    /**
     * 根据关键字和分页查找报修信息
     *
     * @param keyword
     * @param pageBean
     * @return
     */
    /*@PostMapping("/list")
    @ResponseBody
    public ResponseData showAllRepairs(
            @RequestParam(value = "keyword", required = false)
                    String keyword,
            @RequestParam(value = "type", required = false)
                    String type,
            PageBean pageBean
    ) {
        if (!type.isEmpty()){
            return success(businessRepairsService.showAllRepairs(keyword, type,pageBean));
        }else {
            return success(businessRepairsService.showAllRepairs(keyword, pageBean));
        }
    }*/

    /**
     * 根据关键字和分页查找报修信息
     *
     * @param keyword
     * @param pageBean
     * @return
     */
    @PostMapping("/listToRepair")
    @ResponseBody
    public ResponseData showAllToRepairs(
            @RequestParam(value = "keyword", required = false)
                    String keyword,
            PageBean pageBean
    ) {
            return success(businessRepairsService.showAllRepairs(keyword, "送修",pageBean));
    }

    @PostMapping("/listToReturn")
    @ResponseBody
    public ResponseData showAllToReturn(
            @RequestParam(value = "keyword", required = false)
                    String keyword,
            PageBean pageBean
    ) {
        return success(businessRepairsService.showAllRepairs(keyword, "归还",pageBean));
    }

    /**
     * 跳转至添加页面并携带状态信息以及资产列表
     *
     * @param mmap
     * @return
     */
    @GetMapping("/add")
    @RequiresPermissions("sys:assets:add")
    public String addRepairs(String id, ModelMap mmap) {
        mmap.addAttribute("personnelList", personnelService.findByDeleteFlag(0));
        if ("0".equals(id)) {
            return urlPrefix + "/add";
        } else {
            return urlPrefix + "/return";
        }
    }

    /**
     * 新增报修
     *
     * @param repairsEntity 封装的报修信息实体
     * @return
     */
    @PostMapping("/add")
    @ResponseBody
    @RequiresPermissions("sys:repairs:add")
    @BussinessLog(businessType = BusinessType.INSERT, title = "新增维修信息")
    public ResponseData addRepairs(
            RepairsEntity repairsEntity
    ) {
        return businessRepairsService.saveInfo(repairsEntity) ? success() : error("新增失败");
    }

    /**
     * 归还报修
     *
     * @param repairsEntity 封装的报修信息实体
     * @return
     */
    @PostMapping("/return")
    @ResponseBody
    @RequiresPermissions("sys:repairs:add")
    @BussinessLog(businessType = BusinessType.INSERT, title = "归还维修信息")
    public ResponseData returnRepairs(
            RepairsEntity repairsEntity
    ) {
        return businessRepairsService.returnRepairs(setEntityHandler(repairsEntity)) ? success() : error("新增失败");
    }

    /**
     * 修改维修状态
     */
    @GetMapping("/finish/{id}")
    @RequiresPermissions("sys:repairs:update")
    public String finishRepairs(@PathVariable("id") Long id, ModelMap mmap) {
        BusinessRepairs byId = businessRepairsService.findById(id);
        if (byId.getRepairsStatus() == 4) {
            byId.setRepairsStatus(2);
            byId.setFinishDate(new Date());
            businessRepairsService.save(setHandler(byId));
        }
        return urlPrefix + "/list";
    }

    /**
     * 修改维修
     */
    @GetMapping("/update/{id}")
    @RequiresPermissions("sys:repairs:update")
    @BussinessLog(businessType = BusinessType.UPDATE, title = "修改维修状态")
    public String updateRepairs(@PathVariable("id") Long id, ModelMap mmap) {
        BusinessRepairs businessRepairs = businessRepairsService.findById(id);
        Map map = getRepairsResult(businessRepairs);
        mmap.put("repairs", businessRepairs);
        mmap.put("attr", map.get("attr"));
        Integer status = businessRepairs.getRepairsStatus();
        if (status == 2 || status == 3 || status == 6) {
            List<BasisPersonnel> list = new ArrayList<>();
            list.add(personnelService.findById(businessRepairs.getMaintainer()));
            mmap.put("personnelList", list);
            return urlPrefix + "/show";
        } else {
            mmap.put("personnelList", personnelService.findByDeleteFlag(0));
            return urlPrefix + "/edit";
        }

    }

    private Map getRepairsResult(BusinessRepairs businessRepairs){
        Map<String, Object> map = new HashMap<>();

        List<HandleStatus> attrsToUpdate = new ArrayList<>();
        switch (businessRepairs.getRepairsStatus()) {
            case 1:
                attrsToUpdate.add(new HandleStatus(1, "未送审"));
                break;
            case 2:
                attrsToUpdate.add(new HandleStatus(2, "审核中"));
                break;
            case 3:
                attrsToUpdate.add(new HandleStatus(3, "审核通过"));
                break;
            case 4:
                attrsToUpdate.add(new HandleStatus(4, "审核不通过"));
                break;
            case 5:
                attrsToUpdate.add(new HandleStatus(5, "已取消"));
                break;
            case 6:
                attrsToUpdate.add(new HandleStatus(6, "完成"));
                break;
            case 7:
                attrsToUpdate.add(new HandleStatus(7, "部分归还"));
                break;
        }
        map.put("attr", attrsToUpdate);
        return map;
    }

    /**
     * 修改维修
     */
    @GetMapping("/view/{id}")
    @RequiresPermissions("sys:repairs:view")
    public String showRepairs(@PathVariable("id") Long id, ModelMap mmap) {
        BusinessRepairs businessRepairs = businessRepairsService.findById(id);
        Map map = getRepairsResult(businessRepairs);
        mmap.put("repairs", businessRepairs);
        mmap.put("attr", map.get("attr"));
        List<BasisPersonnel> list = new ArrayList<>();
        list.add(personnelService.findById(businessRepairs.getMaintainer()));
        mmap.put("personnelList", list);
        return urlPrefix + "/show";
    }

    /**
     * 修改维修信息
     */
    @PostMapping("/update")
    @ResponseBody
    @RequiresPermissions("sys:repairs:update")
    @BussinessLog(businessType = BusinessType.UPDATE, title = "修改维修信息")
    public ResponseData updateRepairs(RepairsEntity repairsEntity) {
        try{
            return businessRepairsService.update(setEntityHandler(repairsEntity)) ? success() : error("新增失败");
        }catch (Exception e){
            return error(e.getMessage());
        }


    }

    /**
     * 变更状态
     *
     * @param id     报修id
     * @param status 报修状态
     * @param mmap
     * @return
     */
    @RequestMapping("/review/{id}/{status}")
    @RequiresPermissions("sys:repairs:review")
    @BussinessLog(businessType = BusinessType.UPDATE, title = "修改维修状态")
    public String reviewRepairs(@PathVariable("id") Long id, @PathVariable("status") Integer status, ModelMap mmap) {
        businessRepairsService.changeStatus(id, status);
        return urlPrefix + "/list";
    }

    /**
     * 删除报修信息
     *
     * @param ids 要删除的报修单的id集合
     * @return
     */
    @PostMapping("/delete")
    @ResponseBody
    @RequiresPermissions("sys:repairs:delete")
    @BussinessLog(businessType = BusinessType.DELETE, title = "删除维修信息")
    public ResponseData deleteAssetsInfo(Long[] ids) {
        businessRepairsService.delete(ids);
        return success();
    }

    /**
     * 新增详细页面
     */
    @GetMapping("/addItem")
    public String addItem() {
        return urlPrefix + "/addItem";
    }

    /**
     * 新增归还详细页面
     */
    @GetMapping("/addItemToReturn")
    public String returnItem() {
        return urlPrefix + "/addItemToReturn";
    }

    /**
     * 提交审核
     */
    @RepeatSubmit
    @BussinessLog(title = "报修", businessType = BusinessType.RUN)
    @RequestMapping(value = "/audit", method = RequestMethod.POST)
    @ResponseBody
    public ResponseData submit(Long[] ids) {
        return businessRepairsService.auditByIds(ids) ? success() : error("提交失败!");
    }

    private BusinessRepairs setHandler(BusinessRepairs businessRepairs) {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        businessRepairs.setHandlerId(user.getId());
        businessRepairs.setHandlerName(user.getUsername());
        return businessRepairs;
    }

    private RepairsEntity setEntityHandler(RepairsEntity repairsEntity) {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        repairsEntity.setHandlerId(user.getId());
        repairsEntity.setHandlerName(user.getUsername());
        return repairsEntity;
    }


    /**
     * 根据单据号查询详细
     *
     * @param biNumber
     * @return
     */
    @GetMapping("/findDetailByBrNumber")
    @ResponseBody
    public ResponseData findDetailByBrNumber(@RequestParam String biNumber) {
        List<BusinessRepairsItem> businessRepairsItem = businessRepairsItemRepository.findByRecordNumber(biNumber);
        return success().put("businessRepairsItem", businessRepairsItem);
    }
}
