package com.yzm.property.business.controller;

import com.alibaba.fastjson.JSONObject;
import com.yzm.common.annotation.BussinessLog;
import com.yzm.common.annotation.RepeatSubmit;
import com.yzm.common.enums.BusinessType;
import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.basis.entity.AssetsInfo;
import com.yzm.property.basis.service.AreaService;
import com.yzm.property.basis.service.AssetsInfoService;
import com.yzm.property.basis.service.PersonnelService;
import com.yzm.property.business.criteria.ReceiptNoteCriteria;
import com.yzm.property.business.entity.ReceiptNote;
import com.yzm.property.business.entity.ReceiptNoteItem;
import com.yzm.property.business.service.IReceiptNoteItemService;
import com.yzm.property.business.service.IReceiptNoteService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 10:30
 */
@Controller
@RequestMapping("/bus/receiptNote")
public class ReceiptNoteController extends BaseController {
    private String urlPrefixBase = "business/receiptNote";

    @Autowired
    private IReceiptNoteService iReceiptNoteService;
    @Autowired
    private IReceiptNoteItemService iReceiptNoteItemService;
    @Autowired
    private AssetsInfoService assetsInfoService;
    @Autowired
    private PersonnelService personnelService;
    @Autowired
    private AreaService areaService;

    /**
     * 领用list页面
     */
    @GetMapping
    @RequiresPermissions("bus:receiptNote:view")
    public String receiveList(ModelMap mmap) {
        mmap.addAttribute("type", "领用");
        return urlPrefixBase + "/receiptNote";
    }

    /**
     * 退库list页面
     */
    @GetMapping("cancelling")
    @RequiresPermissions("bus:cancelling:view")
    public String cancellingList(ModelMap mmap) {
        mmap.addAttribute("type", "退库");
        return urlPrefixBase + "/cancelling";
    }

    /**
     * 领用Or退库页面
     */
    @GetMapping("add")
    @RequiresPermissions("bus:receiptNote:receive")
    public String receive(String id, ModelMap mmap) {
        mmap.addAttribute("personnelList", personnelService.findByDeleteFlag(0));
        mmap.addAttribute("areaList", areaService.findAll());
        mmap.put("orId", id);
        if (id.equals("0")) {
            return urlPrefixBase + "/receive";
        } else {
            return urlPrefixBase + "/cancellingStocks";
        }
    }

    /**
     * 领用页面
     */
    @GetMapping("addItemCancelling")
    @RequiresPermissions("bus:receiptNote:addItem")
    public String addItemCancelling() {
        return urlPrefixBase + "/addItemCancelling";
    }

    /**
     * 退库页面
     */
    @GetMapping("addItem")
    @RequiresPermissions("bus:receiptNote:addItem")
    public String addItem() {
        return urlPrefixBase + "/addItem";
    }

    /**
     * 修改领用Or退库页面
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        mmap.addAttribute("personnelList", personnelService.findByDeleteFlag(0));
        mmap.addAttribute("areaList", areaService.findAll());
        ReceiptNote byId = iReceiptNoteService.getById(id);
        List<ReceiptNoteItem> list = iReceiptNoteItemService.findByReceiptNo(byId.getReceiptNo());
        mmap.put("receiptNote", byId);
        mmap.put("receiptNoteList", list);
        return urlPrefixBase + "/edit";
    }

    /**
     * 修改保存待入库保存
     */
    @RepeatSubmit
    @RequiresPermissions("wms:stockPending:edit")
    @BussinessLog(title = "领用/退库编辑", businessType = BusinessType.UPDATE)
    @RequestMapping(value = "/edit", method = RequestMethod.POST)
    @ResponseBody
    public ResponseData editWmsInInventory(ReceiptNote receiptNote) {
        return success(iReceiptNoteService.updateReceiptNote(receiptNote));
//        return StringUtils.isNotEmpty(wmsStockPendingService.update(wmsInInventory)) ? success() : error("修改失败!");
    }

    /**
     * 详细显示页面
     *
     * @param id
     * @param mmap
     * @return
     */
    @GetMapping("/view/{id}")
    public String view(@PathVariable("id") Long id, ModelMap mmap) {
        ReceiptNote byId = iReceiptNoteService.getById(id);
        List<ReceiptNoteItem> list = iReceiptNoteItemService.findByReceiptNo(byId.getReceiptNo());
        mmap.put("receiptNote", byId);
        mmap.put("receiptNoteList", list);
        if (byId.getReceiptType().equals("领用")) {
            return urlPrefixBase + "/receiveView";
        } else {
            return urlPrefixBase + "/view";
        }
    }

    //
//    /**
//     * lis页面
//     *
//     * @param __page
//     * @param __limit
//     * @param criteria
//     * @return
//     */
//    @RequestMapping(value = "/list", method = RequestMethod.POST)
//    @RequiresPermissions("bus:receiptNote:list")
//    @ResponseBody
//    public ResponseData list(Integer __page, Integer __limit, ReceiptNoteCriteria criteria) {
//        Map<String, Object> page = iReceiptNoteService.findAllByPage(criteria, PageRequest.of(__page - 1, __limit, Sort.by(Sort.Direction.DESC, "id")));
//        return success(page);
//    }

    /**
     * 领用列表
     *
     * @param criteria
     * @param pageBean
     * @return
     */
    @RequestMapping("/receiveList")
    @ResponseBody
    public ResponseData receiveList(ReceiptNoteCriteria criteria, PageBean pageBean) {
        criteria.setReceiptType("领用");
        return success(iReceiptNoteService.showAllReceiptNote(criteria.getReceiptNo(), criteria.getReceiptType(), pageBean));
    }

    /**
     * 退库列表
     *
     * @param criteria
     * @param pageBean
     * @return
     */
    @RequestMapping("/cancellingList")
    @ResponseBody
    public ResponseData cancellingList(ReceiptNoteCriteria criteria, PageBean pageBean) {
        criteria.setReceiptType("退库");
        return success(iReceiptNoteService.showAllReceiptNote(criteria.getReceiptNo(), criteria.getReceiptType(), pageBean));
    }

    /**
     * 增加
     *
     * @param receiptNote
     * @return
     */
    @PostMapping("/add")
    @ResponseBody
    public ResponseData addReceiptNote(ReceiptNote receiptNote, String orId) {
        if (orId.equals("0")) {
            return success(iReceiptNoteService.addReceiptNote(receiptNote));
        } else {
            return success(iReceiptNoteService.addCancellingStocks(receiptNote));
        }
    }

    /**
     * 根据单据号查询详细
     *
     * @param receiptNo
     * @return
     */
    @GetMapping("/findDetailByReceiptNo/{receiptNo}")
    @ResponseBody
    public ResponseData findDetailByReceiptNo(@PathVariable String receiptNo) {
        List<ReceiptNoteItem> byReceiptNo = iReceiptNoteService.findByReceiptNoUpAssetsInfo(receiptNo);
        return success().put("receiptNoteItemList", byReceiptNo);
    }

    /**
     * 根据单据号查询详细资产
     *
     * @param receiptNo
     * @return
     */
    @GetMapping("/findDetailByReceiptNoAssetsInfo/{receiptNo}")
    @ResponseBody
    public ResponseData findDetailByReceiptNoAssetsInfo(@PathVariable String receiptNo) {
        List<AssetsInfo> byReceiptNo = iReceiptNoteService.findDetailByReceiptNoAssetsInfo(receiptNo);
        return success().put("assetsInfoItemList", byReceiptNo);
    }

    /**
     * 送审功能多个
     *
     * @return
     */
    @RequestMapping("/review/{id}")
//    @RequiresPermissions("bus:receiptNote:review")
    public ResponseData reviewRepairsList(@PathVariable("id") Long[] id) {
        return success(iReceiptNoteService.reviewList(id));
    }

    /**
     * 送审功能单个
     *
     * @return
     */
    @RequestMapping("/review/{id}/{status}")
    public String reviewRepairs(@PathVariable("id") Long id, @PathVariable("status") Integer status) {
        if (status == 2) {
            iReceiptNoteService.review(id, status);
        } else {
            iReceiptNoteService.audit(id, status);
        }
        return urlPrefixBase + "/receiptNote";
    }

    /**
     * 提交审核
     */
    @RepeatSubmit
    @BussinessLog(title = "领用", businessType = BusinessType.RUN)
    @RequestMapping(value = "/submit", method = RequestMethod.POST)
    @ResponseBody
    public ResponseData submit(Long[] ids) {
        return iReceiptNoteService.submitBorrowReturnByIds(ids) ? success() : error("提交失败!");
    }

    /**
     * 审核跳转页面
     */
    @GetMapping("/audit/{id}")
    public String comment(@PathVariable("id") Long id, ModelMap map) {
        ReceiptNote receiptNote = iReceiptNoteService.getById(id);
        map.put("receiptNote", receiptNote);
        return urlPrefixBase + "/audit";
    }

    /**
     * 审核
     */
    @RepeatSubmit
    @BussinessLog(title = "借用", businessType = BusinessType.APPROVAL)
    @RequestMapping(value = "/audit", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public ResponseData audit(@Validated @RequestParam String jsonStr) {
        JSONObject jsonObject = JSONObject.parseObject(jsonStr);
        Long id = Long.parseLong(jsonObject.getString("id"));
        String audit = jsonObject.getString("audit");
        return iReceiptNoteService.audit(id, Integer.parseInt(audit)) ? success() : error("提交失败 !");
    }
}
