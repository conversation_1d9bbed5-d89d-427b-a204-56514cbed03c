package com.yzm.property.business.criteria;

import com.yzm.common.annotation.Query;
import com.yzm.framework.base.CriteriaBean;
import lombok.Data;

@Data
public class ReceiptNoteCriteria extends CriteriaBean {

    @Query(type = Query.Type.EQUAL)
    private String receiptType;

    @Query(type = Query.Type.INNER_LIKE)
    private String receiptNo;

//	@Query(type=Type.INNER_LIKE)
//	private String paramValue;
//
//	public String getParamKey() {
//		return paramKey;
//	}
//
//	public void setParamKey(String paramKey) {
//		this.paramKey = paramKey;
//	}
//
//	public String getParamValue() {
//		return paramValue;
//	}
//
//	public void setParamValue(String paramValue) {
//		this.paramValue = paramValue;
//	}

}
