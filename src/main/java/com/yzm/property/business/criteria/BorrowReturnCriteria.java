package com.yzm.property.business.criteria;

import com.yzm.common.annotation.Query;
import com.yzm.framework.base.CriteriaBean;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 11:38
 */
public class BorrowReturnCriteria extends CriteriaBean {

    @Query(type = Query.Type.INNER_LIKE)
    private String brNumber;
    @Query(type = Query.Type.INNER_LIKE)
    private String brName;

    public String getBrNumber() {
        return brNumber;
    }

    public void setBrNumber(String brNumber) {
        this.brNumber = brNumber;
    }

    public String getBrName() {
        return brName;
    }

    public void setBrName(String brName) {
        this.brName = brName;
    }
}
