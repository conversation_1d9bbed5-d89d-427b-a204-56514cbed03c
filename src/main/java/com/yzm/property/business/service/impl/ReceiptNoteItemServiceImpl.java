package com.yzm.property.business.service.impl;

import com.yzm.common.utils.RedisUtil;
import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.property.business.entity.ReceiptNoteItem;
import com.yzm.property.business.repository.ReceiptNoteItemRepository;
import com.yzm.property.business.service.IReceiptNoteItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ReceiptNoteItemServiceImpl extends BaseService<ReceiptNoteItem, Long> implements IReceiptNoteItemService {

    @Autowired
    private ReceiptNoteItemRepository receiptNoteRepository;
    @Autowired
    private RedisUtil redisUtil;


    @Override
    public BaseRepository<ReceiptNoteItem, Long> getRepository() {
        return receiptNoteRepository;
    }

    @Override
    public List<ReceiptNoteItem> findByReceiptNo(String receiptNo) {
        return receiptNoteRepository.findByReceiptNo(receiptNo);
    }
}
