package com.yzm.property.business.service;

import com.yzm.framework.bean.PageBean;
import com.yzm.property.business.entity.BusinessScrap;
import com.yzm.property.business.entity.ScrapEntity;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/6 17:07
 */
public interface BusinessScrapService {
//    void scrap(Long[] ids, LoginUser user);

    Map showAllScraps(String keyword, PageBean pageBean);

    boolean save(List<Integer> ids, BusinessScrap businessScrap);

    void delete(Long[] ids);

    BusinessScrap findById(Long id);

    boolean save(BusinessScrap businessScrap);

    void changeStatus(Long id,Integer status);

    boolean update(ScrapEntity scrapEntity);

    boolean auditByIds(Long[] ids);
}
