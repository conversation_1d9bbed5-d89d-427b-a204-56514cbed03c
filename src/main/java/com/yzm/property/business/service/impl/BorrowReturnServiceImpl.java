package com.yzm.property.business.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yzm.common.utils.OrderUtils;
import com.yzm.common.utils.ShiroUtils;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.framework.bean.PageBean;
import com.yzm.property.basis.entity.AssetsInfo;
import com.yzm.property.basis.repository.AssetsInfoRepository;
import com.yzm.property.basis.repository.DepartmentRepository;
import com.yzm.property.basis.repository.PersonnelRepository;
import com.yzm.property.basis.utils.AssetsLogUtil;
import com.yzm.property.business.entity.*;
import com.yzm.property.business.repository.BorrowReturnItemRepository;
import com.yzm.property.business.repository.BorrowReturnRepository;
import com.yzm.property.business.service.BorrowReturnService;
import com.yzm.property.consumable.entity.ConsumableListEntity;
import com.yzm.property.consumable.entity.ConsumableRepertory;
import com.yzm.property.consumable.repository.ConsumableInfoRepository;
import com.yzm.property.consumable.repository.ConsumableListRepository;
import com.yzm.property.consumable.repository.ConsumableRepertoryRepository;
import com.yzm.property.consumable.service.ConsumableRepertoryService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class BorrowReturnServiceImpl extends BaseService<BorrowReturn, Long> implements BorrowReturnService {

    @Autowired
    private BorrowReturnRepository borrowReturnRepository;
    @Autowired
    private BorrowReturnItemRepository borrowReturnItemRepository;
    @Autowired
    private AssetsInfoRepository assetsInfoRepository;
    @Autowired
    private PersonnelRepository personnelRepository;
    @Autowired
    private DepartmentRepository departmentRepository;
    @Autowired
    private ConsumableRepertoryService consumableRepertoryService;
    @Autowired
    private ConsumableListRepository consumableListRepository;
    @Autowired
    private ConsumableInfoRepository consumableInfoRepository;
    @Autowired
    private ConsumableRepertoryRepository consumableRepertoryRepository;


    @Override
    public BaseRepository<BorrowReturn, Long> getRepository() {
        return borrowReturnRepository;
    }

    @Override
    public BorrowReturn setHandlerBorrow(BorrowReturn borrowReturn) {
        //获取用户名
        borrowReturn.setHandlerName(ShiroUtils.getUserInfo().getName());
        //获取用户id
        borrowReturn.setHandlerId(ShiroUtils.getUserInfo().getId());
        return borrowReturn;
    }

    @Override
    public boolean saveBorrowReturn(BorrowReturn borrowReturn,String type) {
        //处理人

        //单据编号
        String parameterNo="";
        if("维修登记".equals(type)){
            //借用人
            borrowReturn.setHandlerName(personnelRepository.findById(borrowReturn.getHandlerId()).get().getBasisName());
            parameterNo = OrderUtils.getRepairCode();
        }else{
            setHandlerBorrow(borrowReturn);
            parameterNo = OrderUtils.getJieYongCode();
        }


        borrowReturn.setBrNumber(parameterNo);
        //借用人
        borrowReturn.setBrPeople(personnelRepository.findById(borrowReturn.getBrPeopleId()).get().getBasisName());
        //借用部门
        if(StringUtils.isNotEmpty(borrowReturn.getBrOrganizationId())){
            borrowReturn.setBrOrganization(departmentRepository.findById(borrowReturn.getBrOrganizationId()).get().getBasisName());
        }
        //业务状态
        borrowReturn.setBrBusinessStandby(type);
        int sum = 0;

        //办理状态-未送审
        borrowReturn.setBrStandby(0);
        BorrowReturn borrowReturnData = borrowReturnRepository.saveAndFlush(borrowReturn);
        //获取前台传来的表格信息
        List<BorrowReturnItem> borrowReturnItem = JSONArray
                .parseArray(borrowReturn.getItemJson(), BorrowReturnItem.class);
        for (BorrowReturnItem borrowReturnItems : borrowReturnItem) {
            borrowReturnItems.setConsumableNum(1);
            //外键ID
            borrowReturnItems.setBorrowReturnId(borrowReturnData.getId().toString());
            //单据编号
            borrowReturnItems.setBiNumber(parameterNo);
            //资产状态
            borrowReturnItems.setAssetsStatus(9);
            //资产ID
//            borrowReturnItems.setAssetsId(borrowReturnItems.getId());
            //借用人
            borrowReturnItems.setBiPeople(borrowReturnData.getBrPeople());
            //借用时间
            borrowReturnItems.setBiTime(borrowReturnData.getBrTime());
            //预计归还时间
            borrowReturnItems.setBiPredictTime(borrowReturnData.getBrPredictTime());
            //是否归还 否
            borrowReturnItems.setBiReturnStandby(0);
            //实际归还时间
            borrowReturnItems.setBiActualReturnTime(borrowReturnData.getBrActualReturnTime());
            sum += 1;

            //根据ID查询资产信息
            ConsumableRepertory byId = consumableRepertoryService.getById(borrowReturnItems.getId());
//            byId.setNowRepertory(byId.getNowRepertory() - borrowReturnItems.getConsumableNum());
            consumableRepertoryService.delete(byId);

            ConsumableListEntity consumableListEntity = new ConsumableListEntity();
            BeanUtils.copyProperties(borrowReturnItems, consumableListEntity);
            consumableListEntity.setId(null);
            consumableListEntity.setOperationName(type);
            consumableListEntity.setOperationOtherId(borrowReturn.getBrNumber());
            consumableListEntity.setOperationId(borrowReturn.getId());
            consumableListEntity.setUserName(borrowReturn.getHandlerName());
            consumableListEntity.setOutDate(borrowReturn.getBrTime());
            consumableListEntity.setCreateTime(new Date());
            consumableListRepository.save(consumableListEntity);
            //            AssetsInfo assets = assetsInfoRepository.findById(borrowReturnItems.getId()).get();
//            //修改资产表为锁定中
//            assets.setAssetsStatus(9);
//            assetsInfoRepository.saveAndFlush(assets);
            borrowReturnItemRepository.save(borrowReturnItems);
        }
        borrowReturn.setBrStorageTotal(sum);
        borrowReturnRepository.saveAndFlush(borrowReturn);
        return true;
    }


    @Override
    public boolean review(Long id, Integer status) {
        BorrowReturn borrowReturns = borrowReturnRepository.findById(id).get();
        //根据单据编号查询子表
        List<BorrowReturnItem> byBiNumber = borrowReturnItemRepository.findByBiNumber(borrowReturns.getBrNumber());
        for (BorrowReturnItem borrowReturnItem : byBiNumber) {
            AssetsInfo assetsInfo = assetsInfoRepository.findByOtherId(borrowReturnItem.getOtherId());
            assetsInfo.setAssetsStatus(3);
        }
        //设值办理状态
        borrowReturns.setBrStandby(status);
        borrowReturnRepository.save(borrowReturns);
        return true;
    }

    @Override
    @Transactional
    public boolean updateBorrowReturn(BorrowReturn borrowReturn) {
        //处理人
        setHandlerBorrow(borrowReturn);
        List<BorrowReturnItem> list = borrowReturnItemRepository.findByBiNumber(borrowReturn.getBrNumber());
        list.forEach(o -> {

            borrowReturnItemRepository.deleteById(o.getId());
        });
        List<BorrowReturnItem> entityList = JSONObject.parseArray(borrowReturn.getItemJson(), BorrowReturnItem.class);
        for (BorrowReturnItem item : entityList) {
            //外键ID
            item.setBorrowReturnId(borrowReturn.getId().toString());
            //单据编号
            item.setBiNumber(borrowReturn.getBrNumber());
            //资产ID
            item.setAssetsId(item.getId());
            //资产状态
            item.setAssetsStatus(9);
            //借用人
            item.setBiPeople(borrowReturn.getBrPeople());
            //借用时间
            item.setBiTime(borrowReturn.getBrTime());
            //预计归还时间
            item.setBiPredictTime(borrowReturn.getBrPredictTime());
            //是否归还 否
            item.setBiReturnStandby(0);
            //实际归还时间
            item.setBiActualReturnTime(borrowReturn.getBrActualReturnTime());
            ConsumableRepertory byId = consumableRepertoryService.getById(item.getId());
            byId.setNowRepertory(byId.getNowRepertory() - item.getConsumableNum());
            consumableRepertoryService.update(byId);
            borrowReturnItemRepository.save(item);
        }
        borrowReturn.setBrStandby(1);
        borrowReturnRepository.save(borrowReturn);
        return true;
    }

    @Override
    public boolean submitBorrowReturnByIds(Long[] ids) {
        Boolean code = true;
        for (Long id : ids) {
            BorrowReturn borrowReturn = this.getById(id);
            //办理状态-审核中
            borrowReturn.setBrStandby(2);
        }
        return code;
    }

    @Override
    public boolean audit(Long id, Integer status) {
        BorrowReturn borrowReturn = this.getById(id);
        //通过ID查子表信息
        List<BorrowReturnItem> borrowReturnData = borrowReturnItemRepository.findAllByBorrowReturnId(borrowReturn.getId().toString());
        if (status == 1) {
            //修改办理状态为审核不通过
            borrowReturn.setBrStandby(4);
            for (BorrowReturnItem borrowReturnDatum : borrowReturnData) {
                //根据ID查询资产信息
                ConsumableRepertory byId = consumableRepertoryService.getById(borrowReturnDatum.getId());
                byId.setNowRepertory(byId.getNowRepertory() + borrowReturnDatum.getConsumableNum());
                consumableRepertoryService.update(byId);
            }
        } else if (status == 0) {
            //审核通过
            //修改办理状态为审核通过
            borrowReturn.setBrStandby(3);
            for (BorrowReturnItem borrowReturnDatum : borrowReturnData) {
                AssetsInfo assets = assetsInfoRepository.findById(borrowReturnDatum.getAssetsId()).get();
                //修改资产状态为借用中
                assets.setAssetsStatus(4);
                //给资产部门、人员赋值
                assets.setDepartment(borrowReturn.getBrOrganizationId().intValue());
                assets.setAssetsUser(borrowReturn.getBrPeopleId().intValue());
                assetsInfoRepository.saveAndFlush(assets);
                //修改借用子表状态 为借用中
                borrowReturnDatum.setAssetsStatus(4);
                borrowReturnItemRepository.saveAndFlush(borrowReturnDatum);
                //增加资产日志表数据
                AssetsLogUtil.createAssetsLog(borrowReturn.getId(), borrowReturn.getBrNumber(),
                        borrowReturnDatum.getAssetsId(), borrowReturnDatum.getOtherId(), assets.getAssetsName(), AssetsLogUtil.OPERATION_BORROW);
            }
        } else if (status == 3) {
            //送审
            //办理状态-审核中
            borrowReturn.setBrStandby(2);

        } else {
            //取消
            borrowReturn.setBrStandby(5);
            for (BorrowReturnItem borrowReturnDatum : borrowReturnData) {
                AssetsInfo assets = assetsInfoRepository.findById(borrowReturnDatum.getAssetsId()).get();
                //设置资产状态为闲置
                assets.setAssetsStatus(0);
                assetsInfoRepository.saveAndFlush(assets);
                //设置借用归还子表的资产状态为闲置
                borrowReturnDatum.setAssetsStatus(0);
                borrowReturnItemRepository.saveAndFlush(borrowReturnDatum);
            }
        }
        borrowReturnRepository.saveAndFlush(borrowReturn);
        return true;
    }


    @Override
    public String returns(BorrowReturn borrowReturn,String type) {
        Long id = borrowReturn.getId();
        BorrowReturn receiptNote1 = borrowReturnRepository.findById(id).get();

        //处理人
        setHandlerBorrow(borrowReturn);
        String parameterNo ="";

        if("维修归还".equals(type)){
            parameterNo = OrderUtils.getRepairBackCode();
        }else{
            parameterNo =  OrderUtils.getGuiHuanCode();
        }
        //单据编号
        borrowReturn.setBrNumber(parameterNo);
        borrowReturn.setBrName(receiptNote1.getBrName());
        borrowReturn.setBrOrganization(receiptNote1.getBrOrganization());
        borrowReturn.setBrOrganizationId(receiptNote1.getBrOrganizationId());
        //业务状态
        borrowReturn.setCustom4(receiptNote1.getBrNumber());
        borrowReturn.setBrBusinessStandby(type);
        borrowReturn.setBrPredictTime(receiptNote1.getBrPredictTime());
        borrowReturn.setId(null);
        int sum =0;

        //办理状态-完成
//        borrowReturn.setBrStandby(6);
        BorrowReturn borrowReturnSave = borrowReturnRepository.save(borrowReturn);
        //通过前台传来的信息查子表信息
        List<BorrowReturnItem> borrowReturnData = JSONArray.parseArray(borrowReturn.getItemJson(), BorrowReturnItem.class);

        for (BorrowReturnItem borrowReturnDatum : borrowReturnData) {

            borrowReturnDatum.setBiReturnStandby(1);
            borrowReturnItemRepository.save(borrowReturnDatum);
            sum+=1;
//
            ConsumableRepertory consumableRepertory = new ConsumableRepertory();
            BeanUtils.copyProperties(borrowReturnDatum, consumableRepertory);
            consumableRepertory.setNowRepertory(1);//当前库存
            consumableRepertory.setWarehouseInfo(borrowReturn.getInStorageWarehouse());
            consumableRepertory.setAreaInfo(borrowReturn.getInStorageArea());
            consumableRepertory.setConsumableInfoId(borrowReturnDatum.getId());
            consumableRepertory.setId(null);
            consumableRepertory.setCreateTime(new Date());
            consumableRepertory.setOperationNumber(borrowReturn.getBrNumber());

            //通过耗材查库存
            consumableRepertoryRepository.saveAndFlush(consumableRepertory);
            borrowReturnDatum.setId(null);
//            AssetsInfo assets = assetsInfoRepository.findByOtherId(borrowReturnDatum.getOtherId());
//            assetsInfoRepository.saveAssetsInfo(assets.getId(),null);
            //设置资产状态为闲置
//            assets.setAssetsStatus(0);
//            assets.setDepartment(1);
            //收入仓库
            borrowReturnDatum.setWarehouseInfo(borrowReturn.getInStorageWarehouse());
            borrowReturnDatum.setAreaInfo(borrowReturn.getInStorageArea());

            borrowReturnDatum.setBiNumber(borrowReturnSave.getBrNumber());
            //资产ID
            borrowReturnDatum.setAssetsId(borrowReturnDatum.getId());
            //是否归还 是
            borrowReturnDatum.setBiReturnStandby(1);
            //资产状态为 闲置
            borrowReturnDatum.setAssetsStatus(0);
            //外键ID
            borrowReturnDatum.setBorrowReturnId(borrowReturnSave.getId().toString());
//            assetsInfoRepository.saveAndFlush(assets);
            borrowReturnItemRepository.save(borrowReturnDatum);


//            //增加资产日志表数据
//            AssetsLogUtil.createAssetsLog(borrowReturnSave.getId(),borrowReturnSave.getBrNumber(),
//                    borrowReturnDatum.getAssetsId(), borrowReturnDatum.getOtherId(), assets.getAssetsName(), AssetsLogUtil.OPERATION_RETURN );

            ConsumableListEntity consumableListEntity = new ConsumableListEntity();
            BeanUtils.copyProperties(borrowReturnDatum, consumableListEntity);
            consumableListEntity.setId(null);
            consumableListEntity.setOperationName(type);
            consumableListEntity.setOperationOtherId(borrowReturn.getBrNumber());
            consumableListEntity.setOperationId(borrowReturn.getId());
            consumableListEntity.setUserName(borrowReturn.getHandlerName());
            consumableListEntity.setOutDate(borrowReturn.getBrTime());
            consumableListEntity.setNowRepertory(borrowReturnDatum.getConsumableNum());
            consumableListRepository.save(consumableListEntity);


            //通过耗材查库存

        }
        //根据是否退库与单据名称查找数据
        List<BorrowReturnItem> borrowReturnItem0 = borrowReturnItemRepository.findByBorrowReturnIdAndBiReturnStandby(String.valueOf(id),0);
        int ghStatus = 2;
        if(borrowReturnItem0.size()!=0){
            ghStatus = 1;
        }
        //是否归还 是
        receiptNote1.setBrStandby(ghStatus);
        borrowReturnRepository.save(receiptNote1);
        borrowReturn.setBrStorageTotal(sum);
        borrowReturnRepository.saveAndFlush(borrowReturn);
        return "归还成功";
    }


    @Override
    public Map showAllBorrowInfo(String keyword, String type, PageBean pageBean) {
        HashMap<String, Object> map = new HashMap<>();
        if (keyword == null || "".equals(keyword)) {
            Page<BorrowReturn> result = borrowReturnRepository.findByBrBusinessStandbyOrderByCreateTimeDesc("借用", pageBean.getPagable());
            map.put("list", result.getContent());
            map.put("totalCount", result.getTotalElements());
        } else {
            List<BorrowReturn> result = borrowReturnRepository.findByBiNumberAndBrBusinessStandbyContainsOrderByCreateTimeDesc(keyword, "借用", (pageBean.getPage() - 1) * pageBean.getLimit(), pageBean.getLimit());
            map.put("list", result);
            map.put("totalCount", result.size());
        }
        return map;
    }

    @Override
    public Map showAllReturnInfo(String keyword, String type, PageBean pageBean) {
        HashMap<String, Object> map = new HashMap<>();
        if (keyword == null || "".equals(keyword)) {
            Page<BorrowReturn> result = borrowReturnRepository.findByBrBusinessStandbyOrderByCreateTimeDesc("归还", pageBean.getPagable());
            map.put("list", result.getContent());
            map.put("totalCount", result.getTotalElements());
        } else {
            List<BorrowReturn> result = borrowReturnRepository.findByBiNumberAndBrBusinessStandbyContainsOrderByCreateTimeDesc(keyword, "归还", (pageBean.getPage() - 1) * pageBean.getLimit(), pageBean.getLimit());
            map.put("list", result);
            map.put("totalCount", result.size());
        }
        return map;
    }
    @Override
    public Map showAllRepairsInfo(String keyword, String type, PageBean pageBean) {
        HashMap<String, Object> map = new HashMap<>();
        if (keyword == null || "".equals(keyword)) {
            Page<BorrowReturn> result = borrowReturnRepository.findByBrBusinessStandbyOrderByCreateTimeDesc("维修登记", pageBean.getPagable());
            map.put("list", result.getContent());
            map.put("totalCount", result.getTotalElements());
        } else {
            List<BorrowReturn> result = borrowReturnRepository.findByBiNumberAndBrBusinessStandbyContainsOrderByCreateTimeDesc(keyword, "维修登记", (pageBean.getPage() - 1) * pageBean.getLimit(), pageBean.getLimit());
            map.put("list", result);
            map.put("totalCount", result.size());
        }
        return map;
    }

    @Override
    public Map showAllRepairsInfoBack(String keyword, String type, PageBean pageBean) {
        HashMap<String, Object> map = new HashMap<>();
        if (keyword == null || "".equals(keyword)) {
            Page<BorrowReturn> result = borrowReturnRepository.findByBrBusinessStandbyOrderByCreateTimeDesc("维修归还", pageBean.getPagable());
            map.put("list", result.getContent());
            map.put("totalCount", result.getTotalElements());
        } else {
            List<BorrowReturn> result = borrowReturnRepository.findByBiNumberAndBrBusinessStandbyContainsOrderByCreateTimeDesc(keyword, "维修归还", (pageBean.getPage() - 1) * pageBean.getLimit(), pageBean.getLimit());
            map.put("list", result);
            map.put("totalCount", result.size());
        }
        return map;
    }
}