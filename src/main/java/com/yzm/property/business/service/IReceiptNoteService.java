package com.yzm.property.business.service;

import com.yzm.framework.base.IBaseService;
import com.yzm.framework.bean.PageBean;
import com.yzm.property.basis.entity.AssetsInfo;
import com.yzm.property.business.criteria.ReceiptNoteCriteria;
import com.yzm.property.business.entity.ReceiptNote;
import com.yzm.property.business.entity.ReceiptNoteItem;
import org.springframework.data.domain.PageRequest;

import java.util.List;
import java.util.Map;

public interface IReceiptNoteService extends IBaseService<ReceiptNote, Long> {

    boolean addReceiptNote(ReceiptNote receiptNote);

    List<ReceiptNoteItem> findByReceiptNoUpAssetsInfo(String receiptNo);

    boolean reviewList(Long[] id);

    boolean review(Long id, Integer status);

    boolean submitBorrowReturnByIds(Long[] ids);

    boolean audit(Long id, Integer audit);

    String addCancellingStocks(ReceiptNote receiptNote);

    List<AssetsInfo> findDetailByReceiptNoAssetsInfo(String receiptNo);

    Map<String, Object> findAllPage(ReceiptNoteCriteria criteria, PageRequest of);

    Boolean updateReceiptNote(ReceiptNote receiptNote);

    Map showAllReceiptNote(String keyword, PageBean pageBean);

    Map showAllReceiptNote(String keyword, String type, PageBean pageBean);

}
