package com.yzm.property.business.service;

import com.yzm.framework.bean.PageBean;
import com.yzm.property.business.entity.BusinessRepairs;
import com.yzm.property.business.entity.RepairsEntity;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/28 15:40
 */
public interface BusinessRepairsService {
    Map showAllRepairs(String keyword, PageBean pageBean);
    Map showAllRepairs(String keyword, String type,PageBean pageBean);

    BusinessRepairs findById(Long id);

    boolean save(BusinessRepairs businessRepairs);
    boolean save(List<Long> ids, BusinessRepairs businessRepairs);

    void delete(Long[] ids);

    void changeStatus(Long id,Integer status);

    boolean update(RepairsEntity repairsEntity);

    boolean auditByIds(Long[] ids);

    boolean returnRepairs(RepairsEntity entity);

    boolean saveInfo(RepairsEntity businessRepairs);
}
