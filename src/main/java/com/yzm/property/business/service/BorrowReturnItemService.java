package com.yzm.property.business.service;

import com.yzm.framework.base.IBaseService;
import com.yzm.property.business.entity.BorrowReturnItem;

import java.util.List;

public interface BorrowReturnItemService extends IBaseService<BorrowReturnItem, Long> {

    List<BorrowReturnItem> findByBiNumber(String biNumber);

    List<BorrowReturnItem> findByBiNumberUpAssetsInfo(String biNumber);

    List<BorrowReturnItem> findByBorrowReturnId(String id);

    List<BorrowReturnItem> findByBorrowReturnIdAndBiReturnStandby(String id, int biReturnStandby);

    BorrowReturnItem findByAssetsCodeOrAssetsRfidAndBiReturnStandbyAndBorrowReturnId(String rfid, String code, int i,String id);

    List<BorrowReturnItem> findByBorrowReturnIdAndAssetsNumber(String borrowReturnId, String assetsNumber);
}
