package com.yzm.property.business.service.impl;

import com.yzm.common.utils.OrderUtils;
import com.yzm.framework.bean.PageBean;
import com.yzm.property.basis.entity.AssetsInfo;
import com.yzm.property.basis.repository.AssetsInfoRepository;
import com.yzm.property.basis.utils.AssetsLogUtil;
import com.yzm.property.business.entity.AssetsScrapMiddle;
import com.yzm.property.business.entity.BusinessScrap;
import com.yzm.property.business.entity.ScrapEntity;
import com.yzm.property.business.repository.AssetsScrapMiddleRepository;
import com.yzm.property.business.repository.BusinessScrapRepository;
import com.yzm.property.business.service.BusinessScrapService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/6 17:08
 */
@Service
public class BusinessScrapServiceImpl implements BusinessScrapService {

    SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Autowired
    private BusinessScrapRepository businessScrapRepository;

    @Autowired
    private AssetsInfoRepository assetsInfoRepository;

    @Autowired
    private AssetsScrapMiddleRepository assetsScrapMiddleRepository;

    /**
     * 根据关键字和分页查找报废信息
     *
     * @param keyword  关键字
     * @param pageBean 分页
     * @return Map
     */
    @Override
    public Map showAllScraps(String keyword, PageBean pageBean) {
        HashMap<String, Object> map = new HashMap<>();
        if (keyword == null || "".equals(keyword)) {
            Page<BusinessScrap> result = businessScrapRepository.findByOrderByCreateTimeDesc(pageBean.getPagable());
            map.put("list", result.getContent());
            map.put("totalCount", result.getTotalElements());
        } else {
            List<BusinessScrap> result = businessScrapRepository.findByReceiptNumberContainsOrderByCreateTimeDesc(keyword, (pageBean.getPage() - 1) * pageBean.getLimit(), pageBean.getLimit());
            map.put("list", result);
            map.put("totalCount", result.size());
        }
        return map;
    }

    /**
     * 修改报废信息
     *
     * @param businessScrap 报废信息
     * @return 布尔
     */
    @Override
    @Transactional
    public boolean save(BusinessScrap businessScrap) {
        checkStatus(businessScrap);
        return businessScrapRepository.save(businessScrap) != null;
    }


    /**
     * 修改报废状态以及资产对应的状态
     *
     * @param id     报废表id
     * @param status 状态
     */
    @Override
    @Transactional
    public void changeStatus(Long id, Integer status) {
        BusinessScrap businessScrap = businessScrapRepository.findById(id).get();
        businessScrap.setScrapStatus(status);
        checkStatus(businessScrap);
        businessScrapRepository.save(businessScrap);
    }

    @Override
    @Transactional
    public boolean update(ScrapEntity scrapEntity) {
        BusinessScrap businessScrap = scrapEntity.getBusinessScrap();
        Integer status = businessScrap.getScrapStatus();
        if (status == 2 || status == 3) {
            return false;
        }
        businessScrap.setScrapStatus(1);
            List<Integer> ids = scrapEntity.getIds();
            List<AssetsScrapMiddle> allByScrapIds = assetsScrapMiddleRepository.findAllByScrapId(businessScrap.getId());
            for (AssetsScrapMiddle allByScrapId : allByScrapIds) {
                AssetsInfo assetsInfo = assetsInfoRepository.findById(allByScrapId.getAssetsId()).get();
                if (assetsInfo.getAssetsStatus() != 0 && scrapEntity.getScrapStatus() != 1) {
                    throw new RuntimeException("资产已被占用，被占用资产编号："+ assetsInfo.getOtherId() );
                }
                assetsInfo.setAssetsStatus(0);
                assetsInfoRepository.save(assetsInfo);
            }
            assetsScrapMiddleRepository.removeAllByScrapId(businessScrap.getId());
            for (Integer id : ids) {
                AssetsInfo assetsInfo = assetsInfoRepository.findById(id.longValue()).get();
                assetsInfo.setAssetsStatus(9);
                assetsInfoRepository.save(assetsInfo);
                assetsScrapMiddleRepository.save(new AssetsScrapMiddle(id.longValue(), businessScrap.getId()));
            }
            businessScrap.setScrapNumber(ids.size());
            changAssetsStatusOfUpdate(businessScrap, 9);
            businessScrapRepository.save(businessScrap);
            return true;
    }

    @Override
    @Transactional
    public boolean auditByIds(Long[] ids) {
        try {
            for (Long id : ids) {
                BusinessScrap scrap = businessScrapRepository.findById(id).get();
                scrap.setScrapStatus(2);
                businessScrapRepository.save(scrap);
                changAssetsStatusOfUpdate(scrap, 7);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 新增报废信息
     *
     * @param ids           资产id列表
     * @param businessScrap 报废信息
     * @return 布尔
     */
    @Override
    @Transactional
    public boolean save(List<Integer> ids, BusinessScrap businessScrap) {
        if (ids.size() <= 0) {
            return false;
        }
        businessScrap.setReceiptNumber(OrderUtils.getScrapCode());
        businessScrap.setScrapStatus(1);
        businessScrap.setScrapDate(format.format(new Date()));
        businessScrap.setScrapNumber(ids.size());
        BusinessScrap save = businessScrapRepository.save(businessScrap);
        Integer status = save.getScrapStatus();
        try {
            for (int i = 0; i < ids.size(); i++) {
                Long saveId = save.getId();
                long id = ids.get(i).longValue();
                switch (status) {
                    case 1:
                        changeAssetsStatusOfSave(saveId, id, 9);
                        break;
                    case 3:
                        changeAssetsStatusOfSave(saveId, id, 8);
                        break;
                    case 4:
                    case 5:
                        changeAssetsStatusOfSave(saveId, id, 0);
                        break;
                    default:
                        changeAssetsStatusOfSave(saveId, id, 7);
                        break;
                }
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 新增报废信息时同步变更资产状态
     *
     * @param scrapId 报废表id
     * @param id      资产id
     * @param status  状态
     */
    @Transactional
    public void changeAssetsStatusOfSave(Long scrapId, Long id, Integer status) {
        AssetsInfo assets = assetsInfoRepository.findById(id).get();
        assets.setAssetsStatus(status);
        assetsInfoRepository.save(assets);
        if (status == 8){
            BusinessScrap scrap = businessScrapRepository.findById(scrapId).get();
            AssetsLogUtil.createAssetsLog(scrap.getId(),scrap.getReceiptNumber(),assets,AssetsLogUtil.OPERATION_SCRAP_ADD);
        }
        assetsScrapMiddleRepository.save(new AssetsScrapMiddle(id, scrapId));
    }

    /**
     * 修改报废信息时同步资产对应的状态
     *
     * @param businessScrap 报废信息
     * @param status        状态
     */
    @Transactional
    public void changAssetsStatusOfUpdate(BusinessScrap businessScrap, Integer status) {
        List<AssetsScrapMiddle> list = assetsScrapMiddleRepository.findAllByScrapId(businessScrap.getId());
        for (AssetsScrapMiddle assetsScrapMiddle : list) {
            AssetsInfo assetsInfo = assetsInfoRepository.findById(assetsScrapMiddle.getAssetsId()).get();
            assetsInfo.setAssetsStatus(status);
            assetsInfoRepository.save(assetsInfo);
            if (status == 8){
                AssetsLogUtil.createAssetsLog(businessScrap.getId(),businessScrap.getReceiptNumber(),assetsInfo,AssetsLogUtil.OPERATION_SCRAP_ADD);
            }
        }
    }

    /**
     * 根据报废状态转换对应的资产状态
     * @param businessScrap
     */
    @Transactional
    public void checkStatus(BusinessScrap businessScrap){
        switch (businessScrap.getScrapStatus()) {
            case 1:
                changAssetsStatusOfUpdate(businessScrap, 9);
                break;
            case 3:
                changAssetsStatusOfUpdate(businessScrap, 8);
                break;
            case 4:
            case 5:
                changAssetsStatusOfUpdate(businessScrap, 0);
                break;
            default:
                changAssetsStatusOfUpdate(businessScrap, 7);
                break;
        }
    }

    /**
     * 根据id列表删除报废信息
     *
     * @param ids id列表
     */
    @Override
    @Transactional
    public void delete(Long[] ids) {
        for (int i = 0; i < ids.length; i++) {
            businessScrapRepository.deleteById(ids[i]);
        }
    }

    /**
     * 根据id查找报废信息
     *
     * @param id 报废表id
     * @return 报废信息
     */
    @Override
    public BusinessScrap findById(Long id) {
        return businessScrapRepository.findById(id).get();
    }
}
