package com.yzm.property.business.service;

import com.yzm.framework.base.IBaseService;
import com.yzm.framework.bean.PageBean;
import com.yzm.property.business.entity.BorrowReturn;

import java.util.Map;

public interface BorrowReturnService extends IBaseService<BorrowReturn, Long> {
    BorrowReturn setHandlerBorrow(BorrowReturn borrowReturn);
    boolean saveBorrowReturn(BorrowReturn borrowReturn,String type);
    boolean review(Long id, Integer status);
    boolean updateBorrowReturn(BorrowReturn borrowReturn);
    boolean submitBorrowReturnByIds(Long[] ids);
    boolean audit(Long id, Integer status);
    String returns(BorrowReturn borrowReturn,String type);

    Map showAllBorrowInfo(String keyword, String type, PageBean pageBean);

    Map showAllReturnInfo(String keyword, String type, PageBean pageBean);

    Map showAllRepairsInfo(String keyword, String type, PageBean pageBean);

    Map showAllRepairsInfoBack(String keyword, String type, PageBean pageBean);
}
