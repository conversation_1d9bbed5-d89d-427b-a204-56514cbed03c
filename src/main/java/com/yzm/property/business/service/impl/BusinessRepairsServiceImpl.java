package com.yzm.property.business.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.yzm.common.utils.OrderUtils;
import com.yzm.common.utils.ShiroUtils;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.shiro.LoginUser;
import com.yzm.property.basis.entity.AssetsInfo;
import com.yzm.property.basis.repository.AssetsInfoRepository;
import com.yzm.property.basis.repository.PersonnelRepository;
import com.yzm.property.basis.utils.AssetsLogUtil;
import com.yzm.property.business.entity.*;
import com.yzm.property.business.repository.AssetsRepairsMiddleRepository;
import com.yzm.property.business.repository.BusinessRepairsItemRepository;
import com.yzm.property.business.repository.BusinessRepairsRepository;
import com.yzm.property.business.service.BusinessRepairsService;
import com.yzm.property.consumable.entity.ConsumableListEntity;
import com.yzm.property.consumable.entity.ConsumableRepertory;
import com.yzm.property.consumable.repository.ConsumableListRepository;
import com.yzm.property.consumable.service.ConsumableRepertoryService;
import com.yzm.property.consumable.utils.ConsumableUtil;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/28 15:42
 */
@Service
public class BusinessRepairsServiceImpl implements BusinessRepairsService {

    @Autowired
    private BusinessRepairsRepository businessRepairsRepository;

    @Autowired
    private AssetsRepairsMiddleRepository assetsRepairsMiddleRepository;

    @Autowired
    private AssetsInfoRepository assetsInfoRepository;

    @Autowired
    private PersonnelRepository personnelRepository;
    @Autowired
    private BusinessRepairsItemRepository businessRepairsItemRepository;
    @Autowired
    private ConsumableRepertoryService consumableRepertoryService;
    @Autowired
    private ConsumableListRepository consumableListRepository;

    /**
     * 根据关键字和分页查找报修信息
     *
     * @param keyword  关键字
     * @param pageBean 分页
     * @return Map
     */
    @Override
    public Map showAllRepairs(String keyword, PageBean pageBean) {
        HashMap<String, Object> map = new HashMap<>();
        if (keyword == null || "".equals(keyword)) {
            Page<BusinessRepairs> result = businessRepairsRepository.findByOrderByCreateTimeDesc(pageBean.getPagable());
            map.put("list", result.getContent());
            map.put("totalCount", result.getTotalElements());
        } else {
            List<BusinessRepairs> result = businessRepairsRepository.findByReceiptNumberContainsOrderByCreateTimeDesc(keyword, (pageBean.getPage() - 1) * pageBean.getLimit(), pageBean.getLimit());
            map.put("list", result);
            map.put("totalCount", result.size());
        }
        return map;
    }

    @Override
    public Map showAllRepairs(String keyword, String type, PageBean pageBean) {
        HashMap<String, Object> map = new HashMap<>();
        if (keyword == null || "".equals(keyword)) {
            Page<BusinessRepairs> result = businessRepairsRepository.findByDocumentTypeOrderByCreateTimeDesc(type, pageBean.getPagable());
            map.put("list", result.getContent());
            map.put("totalCount", result.getTotalElements());
        } else {
            List<BusinessRepairs> result = businessRepairsRepository.findByReceiptNumberAndDocumentTypeContainsOrderByCreateTimeDesc(keyword, type, (pageBean.getPage() - 1) * pageBean.getLimit(), pageBean.getLimit());
            map.put("list", result);
            map.put("totalCount", result.size());
        }
        return map;
    }

    /**
     * 根据id查找报修信息
     *
     * @param id
     * @return
     */
    @Override
    public BusinessRepairs findById(Long id) {
        return businessRepairsRepository.findById(id).get();
    }

    /**
     * 修改报修信息
     *
     * @param businessRepairs
     * @return
     */
    @Override
    public boolean save(BusinessRepairs businessRepairs) {
        Integer status = businessRepairs.getRepairsStatus();
        switch (status) {
            case 1:
                changeAssetsStatusOfUpdate(businessRepairs, 9);
                break;
            case 3:
                changeAssetsStatusOfUpdate(businessRepairs, 6);
                break;
            case 5:
            case 6:
                changeAssetsStatusOfUpdate(businessRepairs, 0);
                break;
            default:
                changeAssetsStatusOfUpdate(businessRepairs, 5);
                break;
        }
        return businessRepairsRepository.save(businessRepairs) != null;
    }

    /**
     * 根据报修状态修改资产状态
     *
     * @param businessRepairs 报修信息
     * @param status          状态
     */
    @Transactional
    public void changeAssetsStatusOfUpdate(BusinessRepairs businessRepairs, Integer status) {
        List<AssetsRepairsMiddle> list = assetsRepairsMiddleRepository.findAllByRepairsId(businessRepairs.getId());
        for (AssetsRepairsMiddle assetsRepairsMiddle : list) {
            AssetsInfo assetsInfo = assetsInfoRepository.findById(assetsRepairsMiddle.getAssetsId()).get();
            assetsInfo.setAssetsStatus(status);
            if (status == 6) {
                assetsInfo.setAssetsUser(businessRepairs.getMaintainerId().intValue());
                AssetsLogUtil.createAssetsLog(businessRepairs.getId(), businessRepairs.getReceiptNumber(), assetsInfo, AssetsLogUtil.OPERATION_REPAIRS_ADD);
            }
            assetsInfoRepository.save(assetsInfo);
        }
    }

    /**
     * 添加报修信息，修改资产至对应的状态
     *
     * @param ids             资产id
     * @param businessRepairs 报修信息
     * @return
     */
    @Override
    @Transactional
    public boolean save(List<Long> ids, BusinessRepairs businessRepairs) {
        if (ids.size() <= 0) {
            return false;
        }
        businessRepairs.setReceiptNumber(OrderUtils.getRepairCode());
        //根据id或者用户名
        businessRepairs.setMaintainerId(businessRepairs.getMaintainer());
        businessRepairs.setMaintainerName(personnelRepository.findById(businessRepairs.getMaintainer()).get().getBasisName());
        businessRepairs.setRepairsDate(new Date());
        businessRepairs.setRepairsStatus(1);
        businessRepairs.setDocumentType("送修");
        try {
            BusinessRepairs save = businessRepairsRepository.save(businessRepairs);
            Integer status = save.getRepairsStatus();
            for (int i = 0; i < ids.size(); i++) {
                Long saveId = save.getId();
                long id = ids.get(i);
                switch (status) {
                    case 1:
                        changeAssetsStatusOfSave(saveId, id, 9);
                        break;
                    case 2:
                        changeAssetsStatusOfSave(saveId, id, 5);
                        break;
                    case 3:
                        changeAssetsStatusOfSave(saveId, id, 6);
                        break;
                    default:
                        changeAssetsStatusOfSave(saveId, id, 0);
                        break;
                }
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 添加报修信息修改资产状态
     *
     * @param repairsId 报修表id
     * @param id        资产id
     * @param status    状态
     * @throws Exception 异常上抛
     */
    @Transactional
    public void changeAssetsStatusOfSave(Long repairsId, Long id, Integer status) throws Exception {
        AssetsInfo assets = assetsInfoRepository.findById(id).get();
        assets.setAssetsStatus(status);
        if (status == 6) {
            //日志
            BusinessRepairs repairs = businessRepairsRepository.findById(repairsId).get();
            AssetsLogUtil.createAssetsLog(repairs.getId(), repairs.getReceiptNumber(), assets, AssetsLogUtil.OPERATION_REPAIRS_ADD);
        }
        assetsInfoRepository.save(assets);
        assetsRepairsMiddleRepository.save(new AssetsRepairsMiddle(id, repairsId, 0, null));
    }

    /**
     * 变更状态，报修状态和资产当前状态
     *
     * @param id     资产id
     * @param status 状态
     */
    @Override
    @Transactional
    public void changeStatus(Long id, Integer status) {
        BusinessRepairs businessRepairs = businessRepairsRepository.findById(id).get();
        businessRepairs.setRepairsStatus(status);
        switch (status) {
            case 1:
                changeAssetsStatusOfUpdate(businessRepairs, 9);
                break;
            case 2:
                changeAssetsStatusOfUpdate(businessRepairs, 5);
                break;
            case 3:
                changeAssetsStatusOfUpdate(businessRepairs, 6);
                break;
            default:
                changeAssetsStatusOfUpdate(businessRepairs, 0);
                break;
        }
        if (status == 6) {
            businessRepairs.setFinishDate(new Date());
        }
        businessRepairsRepository.save(businessRepairs);
    }

    /**
     * 修改报修信息及相关的资产状态，将修改的报修一律重置为未审核
     *
     * @param repairsEntity
     * @return
     */
    @Override
    @Transactional
    public boolean update(RepairsEntity repairsEntity) {
        BusinessRepairs businessRepairs = repairsEntity.getBusinessRepairs();
        Integer status = businessRepairs.getRepairsStatus();
        if (status != 1 && status != 4 && status != 5) {
            return false;
        }
        //将修改后的状态重置为未审核
        businessRepairs.setRepairsStatus(1);
        List<Long> ids = repairsEntity.getIds();
        //根据id或者用户名
        businessRepairs.setMaintainerName(personnelRepository.findById(businessRepairs.getMaintainer()).get().getBasisName());
        Long repairsId = businessRepairs.getId();
        List<AssetsRepairsMiddle> allByRepairsIds = assetsRepairsMiddleRepository.findAllByRepairsId(repairsId);
        //先清空中间表所有相关字段并重置资产状态为闲置
        for (AssetsRepairsMiddle allByRepairsId : allByRepairsIds) {
            AssetsInfo assetsInfo = assetsInfoRepository.findById(allByRepairsId.getAssetsId()).get();
            if (assetsInfo.getAssetsStatus() != 0 && repairsEntity.getRepairsStatus() != 1) {
//                    throw new RuntimeException("资产已被占用，被占用资产编号："+ assetsInfo.getOtherId() );
                continue;
            }
            assetsInfo.setAssetsStatus(0);
            assetsInfoRepository.save(assetsInfo);
        }
        assetsRepairsMiddleRepository.removeAllByRepairsId(businessRepairs.getId());
        //重新添加维修字段并锁定对应资产
        for (Long id : ids) {
            AssetsInfo assetsInfo = assetsInfoRepository.findById(id).get();
            if (assetsInfo.getAssetsStatus() != 0) {
                throw new RuntimeException("资产已被占用，被占用资产编号：" + assetsInfo.getOtherId());
            }
            assetsInfo.setAssetsStatus(9);
            assetsInfoRepository.save(assetsInfo);
            assetsRepairsMiddleRepository.save(new AssetsRepairsMiddle(id, businessRepairs.getId(), 0, null));
        }
        changeAssetsStatusOfUpdate(businessRepairs, 9);
        businessRepairsRepository.save(businessRepairs);
        return true;
    }

    /**
     * 根据id审核
     *
     * @param ids
     * @return
     */
    @Override
    @Transactional
    public boolean auditByIds(Long[] ids) {
        try {
            for (Long id : ids) {
                BusinessRepairs repairs = businessRepairsRepository.findById(id).get();
                LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
                repairs.setHandlerId(user.getId());
                repairs.setHandlerName(user.getUsername());
                repairs.setRepairsStatus(2);
                businessRepairsRepository.save(repairs);
                changeAssetsStatusOfUpdate(repairs, 5);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }

    }

    /**
     * 分批归还送修资产
     *
     * @param entity
     * @return
     */
    @Override
    @Transactional
    public boolean returnRepairs(RepairsEntity entity) {
        BusinessRepairs repairs = entity.getBusinessRepairs();
        repairs.setFinishDate(new Date());
        repairs.setRepairsStatus(6);
        repairs.setDocumentType("归还");
        repairs.setMaintainerName(personnelRepository.findById(repairs.getMaintainer()).get().getBasisName());
        repairs.setReceiptNumber(OrderUtils.getRepairCode());
        BusinessRepairs save = businessRepairsRepository.save(repairs);
        List<Long> ids = entity.getIds();
        for (Long id : ids) {
            assetsInfoRepository.saveAssetsInfo(id, 0);
            AssetsLogUtil.createAssetsLog(repairs.getId(), repairs.getReceiptNumber(), assetsInfoRepository.findById(id).get(), AssetsLogUtil.OPERATION_REPAIRS_RETURN);
            AssetsRepairsMiddle top = assetsRepairsMiddleRepository.findTopByAssetsIdAndReturnStatus(id, 0);
            top.setReturnStatus(2);
            top.setReturnId(save.getId());
            assetsRepairsMiddleRepository.save(top);
            Integer count = assetsRepairsMiddleRepository.countAllByRepairsIdAndReturnStatus(top.getRepairsId(), 0);
            BusinessRepairs businessRepairs = businessRepairsRepository.findById(top.getRepairsId()).get();
            if (count == 0) {
                businessRepairs.setRepairsStatus(6);
                businessRepairs.setFinishDate(new Date());
            } else {
                businessRepairs.setRepairsStatus(7);
            }
            businessRepairsRepository.save(businessRepairs);
        }
        return true;
    }


    /**
     * 根据id删除报修信息
     *
     * @param ids id列表
     */
    @Override
    @Transactional
    public void delete(Long[] ids) {
        for (int i = 0; i < ids.length; i++) {
            List<AssetsRepairsMiddle> list = assetsRepairsMiddleRepository.findAllByRepairsId(ids[i]);
            for (AssetsRepairsMiddle assetsRepairsMiddle : list) {
                AssetsInfo assetsInfo = assetsInfoRepository.findById(assetsRepairsMiddle.getAssetsId()).get();
                assetsInfo.setAssetsStatus(0);
                assetsInfoRepository.save(assetsInfo);
            }
            businessRepairsRepository.deleteById(ids[i]);
        }
    }
    @Override
    public boolean saveInfo(RepairsEntity repairsEntity) {
        BusinessRepairs businessRepairs = repairsEntity.getBusinessRepairs();

        //处理人
        businessRepairs.setHandlerName(ShiroUtils.getUserInfo().getName());
        //获取用户id
        businessRepairs.setHandlerId(ShiroUtils.getUserInfo().getId());
        //单据编号
        String parameterNo = OrderUtils.getRepairCode();
        businessRepairs.setReceiptNumber(parameterNo);
        businessRepairs.setReceiptNumber(OrderUtils.getRepairCode());
        //根据id或者用户名
        businessRepairs.setMaintainerId(businessRepairs.getMaintainer());
        businessRepairs.setMaintainerName(personnelRepository.findById(businessRepairs.getMaintainer()).get().getBasisName());
        businessRepairs.setRepairsDate(new Date());
        businessRepairs.setRepairsStatus(1);
        businessRepairs.setDocumentType("送修");

        BusinessRepairs businessRepairs1 = businessRepairsRepository.save(businessRepairs);
        //获取前台传来的表格信息
        List<BusinessRepairsItem> borrowReturnItem = JSONArray
                .parseArray(repairsEntity.getItemJson(), BusinessRepairsItem.class);
        for (BusinessRepairsItem businessRepairsItem : borrowReturnItem) {
            //外键ID
            businessRepairsItem.setRecordNumber(businessRepairs1.getReceiptNumber());
            businessRepairsItemRepository.save(businessRepairsItem);
            //根据ID查询资产信息
            ConsumableRepertory byId = consumableRepertoryService.getById(businessRepairsItem.getId());
//            byId.setNowRepertory(byId.getNowRepertory() - borrowReturnItems.getConsumableNum());
            consumableRepertoryService.delete(byId);

            ConsumableListEntity consumableListEntity = new ConsumableListEntity();
            BeanUtils.copyProperties(businessRepairsItem, consumableListEntity);
            consumableListEntity.setId(null);
            consumableListEntity.setOperationName(ConsumableUtil.OPERATION_JIEYONG);
            consumableListEntity.setOperationOtherId(businessRepairs1.getReceiptNumber());
            consumableListEntity.setOperationId(businessRepairs1.getId());
            consumableListEntity.setUserName(businessRepairs1.getHandlerName());
            consumableListEntity.setOutDate(businessRepairs1.getRepairsDate());
            consumableListRepository.save(consumableListEntity);
        }
        return true;
    }
}
