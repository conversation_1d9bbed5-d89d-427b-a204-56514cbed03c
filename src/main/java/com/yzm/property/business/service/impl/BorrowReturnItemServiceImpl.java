package com.yzm.property.business.service.impl;

import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.property.basis.repository.AssetsInfoRepository;
import com.yzm.property.business.entity.BorrowReturnItem;
import com.yzm.property.business.repository.BorrowReturnItemRepository;
import com.yzm.property.business.service.BorrowReturnItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class BorrowReturnItemServiceImpl extends BaseService<BorrowReturnItem, Long> implements BorrowReturnItemService{

    @Autowired
    private BorrowReturnItemRepository borrowReturnItemRepository;
    @Autowired
    private AssetsInfoRepository assetsInfoRepository;




    @Override
    public BaseRepository<BorrowReturnItem, Long> getRepository() {
        return borrowReturnItemRepository;
    }

    @Override
    public List<BorrowReturnItem> findByBiNumber(String biNumber) {
        return borrowReturnItemRepository.findByBiNumber(biNumber);
    }

    @Override
    public List<BorrowReturnItem> findByBiNumberUpAssetsInfo(String biNumber) {
        List<BorrowReturnItem> byBiNumberUpAssetsInfo = borrowReturnItemRepository.findByBiNumber(biNumber);

        return byBiNumberUpAssetsInfo;
       /* List<BorrowReturnItem> byReceiptNoUpAssetsInfo = borrowReturnItemRepository.findByBiNumber(biNumber);
        for (BorrowReturnItem borrowReturnItem : byReceiptNoUpAssetsInfo) {
            AssetsInfo byOtherId = assetsInfoRepository.findByOtherId(borrowReturnItem.getOtherId());
            borrowReturnItem.setAssetsStatus(byOtherId.getAssetsStatus());
            //资产名称
            borrowReturnItem.setAssetsName(byOtherId.getAssetsName());
        }
        return byReceiptNoUpAssetsInfo;*/
    }

    @Override
    public List<BorrowReturnItem> findByBorrowReturnId(String id) {
        return borrowReturnItemRepository.findByBorrowReturnId(id);
    }

    @Override
    public List<BorrowReturnItem> findByBorrowReturnIdAndBiReturnStandby(String id,int biReturnStandby) {
        return borrowReturnItemRepository.findByBorrowReturnIdAndBiReturnStandby(id,biReturnStandby);
    }

    @Override
    public BorrowReturnItem findByAssetsCodeOrAssetsRfidAndBiReturnStandbyAndBorrowReturnId(String rfid, String code, int i,String id) {
        return borrowReturnItemRepository.findByAssetsCodeOrAssetsRfidAndBiReturnStandbyAndBorrowReturnId(rfid,code,i,id);
    }

    @Override
    public List<BorrowReturnItem> findByBorrowReturnIdAndAssetsNumber(String borrowReturnId, String assetsNumber) {
        return borrowReturnItemRepository.findByBorrowReturnIdAndAssetsNumber(borrowReturnId,assetsNumber);
    }
}
