package com.yzm.property.business.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.yzm.common.utils.*;
import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.framework.bean.PageBean;
import com.yzm.property.basis.entity.AssetsInfo;
import com.yzm.property.basis.repository.AreaRepository;
import com.yzm.property.basis.repository.AssetsInfoRepository;
import com.yzm.property.basis.repository.DepartmentRepository;
import com.yzm.property.basis.repository.PersonnelRepository;
import com.yzm.property.basis.utils.AssetsLogUtil;
import com.yzm.property.business.criteria.ReceiptNoteCriteria;
import com.yzm.property.business.entity.ReceiptNote;
import com.yzm.property.business.entity.ReceiptNoteItem;
import com.yzm.property.business.repository.ReceiptNoteItemRepository;
import com.yzm.property.business.repository.ReceiptNoteRepository;
import com.yzm.property.business.service.IReceiptNoteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.*;

@Service
public class ReceiptNoteServiceImpl extends BaseService<ReceiptNote, Long> implements IReceiptNoteService {

    @Autowired
    private ReceiptNoteRepository receiptNoteRepository;
    @Autowired
    private ReceiptNoteItemRepository receiptNoteItemRepository;
    @Autowired
    private AssetsInfoRepository assetsInfoRepository;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private PersonnelRepository personnelRepository;
    @Autowired
    private DepartmentRepository departmentRepository;
    @Autowired
    private AreaRepository areaRepository;

    @Override
    public BaseRepository<ReceiptNote, Long> getRepository() {
        return receiptNoteRepository;
    }

    @Override
    public boolean addReceiptNote(ReceiptNote receiptNote) {
        String parameterNo = OrderUtils.getLingYongCode();
        receiptNote.setReceiptNo(parameterNo);
        receiptNote.setReceiptType("领用");
        receiptNote.setReceiptStatus(1);
        receiptNote.setReceiptConductor(ShiroUtils.getUserInfo().getName());

        receiptNote.setReceiptPerson(personnelRepository.findById(receiptNote.getReceiptPersonId()).get().getBasisName());
        receiptNote.setReceiptLocation(areaRepository.findById(receiptNote.getReceiptLocationId()).get().getBasisName());
        receiptNote.setReceiptDepartment(departmentRepository.findById(receiptNote.getReceiptDepartmentId()).get().getBasisName());
        receiptNote.setReceiptTime(new Date());
        ReceiptNote save = receiptNoteRepository.save(receiptNote);
        List<ReceiptNoteItem> receiptNoteItemStringJson = JSONUtil.toList(JSONUtil.parseArray(receiptNote.getItemJson()), ReceiptNoteItem.class);
        for (ReceiptNoteItem receiptNoteItem : receiptNoteItemStringJson) {
            AssetsInfo byOtherId = assetsInfoRepository.findByOtherId(receiptNoteItem.getOtherId());
            byOtherId.setAssetsStatus(9);
            receiptNoteItem.setReceiptNo(save.getReceiptNo());
            receiptNoteItem.setReceiptTime(receiptNote.getReceiptTime());
            receiptNoteItem.setExpectedReturnTime(receiptNote.getExpectedReturnTime());
//            receiptNoteItem.setPracticalReturnTime();
            receiptNoteItem.setIsWithdrawing(0);
//            receiptNoteItem.setDepartmentBefore(receiptNoteItem.getDepartmentAfter());
//            receiptNoteItem.setReceiptConductorBefore(receiptNoteItem.getReceiptConductorAfter());
//            receiptNoteItem.setReceiptLocationBefore(receiptNoteItem.getReceiptLocationAfter());
//            receiptNoteItem.setWithdrawingNumber();
            receiptNoteItem.setDepartmentAfter(departmentRepository.findById(receiptNote.getReceiptDepartmentId()).get().getBasisName());
            receiptNoteItem.setReceiptConductorAfter(personnelRepository.findById(receiptNote.getReceiptPersonId()).get().getBasisName());
            receiptNoteItem.setReceiptLocationAfter(areaRepository.findById(receiptNote.getReceiptLocationId()).get().getBasisName());

//            receiptNoteItem.setReceiptLocationAfter(receiptNote.getReceiptLocation());
            assetsInfoRepository.save(byOtherId);
            receiptNoteItemRepository.save(receiptNoteItem);
        }

        return false;
    }

    @Override
    public List<ReceiptNoteItem> findByReceiptNoUpAssetsInfo(String receiptNo) {
        List<ReceiptNoteItem> byReceiptNoUpAssetsInfo = receiptNoteItemRepository.findByReceiptNo(receiptNo);
        for (ReceiptNoteItem receiptNoteItem : byReceiptNoUpAssetsInfo) {
            AssetsInfo byOtherId = assetsInfoRepository.findByOtherId(receiptNoteItem.getOtherId());
            receiptNoteItem.setAssetsInfoList(byOtherId);
            receiptNoteItem.setAssetsStatus(byOtherId.getAssetsStatus());
        }
        return byReceiptNoUpAssetsInfo;
    }

    @Override
    public boolean reviewList(Long[] id) {
        for (int i = 0; i < id.length; i++) {
            review(id[i], 1);
        }
        return true;
    }

    @Override
    public boolean review(Long id, Integer status) {
        ReceiptNote receiptNote = receiptNoteRepository.findById(id).get();
        List<ReceiptNoteItem> byReceiptNo = receiptNoteItemRepository.findByReceiptNo(receiptNote.getReceiptNo());
        for (ReceiptNoteItem receiptNoteItem : byReceiptNo) {
            AssetsInfo byOtherId = assetsInfoRepository.findByOtherId(receiptNoteItem.getOtherId());
            if (receiptNote.getReceiptType().equals("领用")) {
                byOtherId.setAssetsStatus(1);
            } else {
                byOtherId.setAssetsStatus(10);
            }
        }
        receiptNote.setReceiptStatus(status);
        receiptNoteRepository.save(receiptNote);
        return true;
    }

    @Override
    public boolean submitBorrowReturnByIds(Long[] ids) {
        Boolean code = true;
        for (Long id : ids) {
            ReceiptNote receiptNote = this.getById(id);
            receiptNote.setReceiptStatus(2);//办理状态-审核中
            List<ReceiptNoteItem> receiptNoteItemList = receiptNoteItemRepository.findByReceiptNo(receiptNote.getReceiptNo());
            for (ReceiptNoteItem receiptNoteItem : receiptNoteItemList) {
                receiptNoteItem.setAssetsStatus(1);
                receiptNoteItemRepository.save(receiptNoteItem);
                AssetsInfo assets = assetsInfoRepository.findByOtherId(receiptNoteItem.getOtherId());
                assets.setAssetsStatus(1);//设置资产状态为借用审核中
                assetsInfoRepository.saveAndFlush(assets);
            }
            receiptNoteRepository.save(receiptNote);
        }
        return code;
    }

    @Override
    public boolean audit(Long id, Integer audit) {
        ReceiptNote receiptNote = this.getById(id);
        String auditString = "";
        if (audit == 2) {
            auditString = "作废";
        } else if (audit == 4 || audit == 5) {
            auditString = "审核不通过";
        } else if (audit == 3) {
            auditString = "审核通过";
        }
        List<ReceiptNoteItem> receiptNoteItemList = receiptNoteItemRepository.findByReceiptNo(receiptNote.getReceiptNo());

        /*审核通过*/
        receiptNote.setReceiptStatus(audit);//设置办理状态为审核通过
        for (ReceiptNoteItem receiptNoteItem : receiptNoteItemList) {
            AssetsInfo assets = assetsInfoRepository.findByOtherId(receiptNoteItem.getOtherId());
            if (receiptNote.getReceiptType().equals("领用")) {
                if (auditString.equals("审核通过")) {
                    assets.setAssetsUser(receiptNote.getReceiptPersonId().intValue());
                    assets.setAssetsArea(receiptNote.getReceiptLocationId().intValue());
                    assets.setDepartment(receiptNote.getReceiptDepartmentId().intValue());
                    receiptNoteItem.setAssetsStatus(2);
                    assets.setAssetsStatus(2);//设置资产状态为领用
                    AssetsLogUtil.createAssetsLog(receiptNote.getId(), receiptNote.getReceiptNo(), assets, AssetsLogUtil.OPERATION_RECEIPT);
                }
                if (auditString.equals("审核不通过")) {
                    receiptNoteItem.setIsWithdrawing(2);
                    receiptNote.setReceiptStatus(audit);//C:\Users\<USER>\Desktop\中间件程序设置办理状态为审核不通过
                    assets.setAssetsStatus(0);//设置资产状态为闲置
                }
            } else {
                if (auditString.equals("审核通过")) {
                    ReceiptNoteItem receiptNoteItemO = receiptNoteItemRepository.findByIsWithdrawingAndOtherId(0, receiptNoteItem.getOtherId());
                    receiptNoteItemO.setIsWithdrawing(1);
//                    receiptNoteItemO.setPracticalReturnTime(receiptNoteItem.getPracticalReturnTime());
                    receiptNoteItemO.setWithdrawingNumber(receiptNote.getReceiptNo());
                    receiptNoteItemRepository.save(receiptNoteItemO);
                    assets.setAssetsUser(null);
                    assets.setAssetsArea(null);
                    assets.setDepartment(null);
                    receiptNoteItem.setAssetsStatus(2);
                    assets.setAssetsStatus(0);//设置资产状态为领用
                    List<ReceiptNoteItem> byReceiptNo = receiptNoteItemRepository.findByReceiptNo(receiptNoteItemO.getReceiptNo());
                    Boolean isStatus = true;
                    for (ReceiptNoteItem noteItem : byReceiptNo) {
                        if (noteItem.getIsWithdrawing() == 0) {
                            isStatus = false;
                        }
                    }
                }
                if (auditString.equals("审核不通过")) {
                    receiptNote.setReceiptStatus(audit);//设置办理状态为审核不通过
                    assets.setAssetsStatus(2);//设置资产状态为闲置
                }
            }

            receiptNoteItemRepository.save(receiptNoteItem);
            assetsInfoRepository.saveAndFlush(assets);
        }
        receiptNoteRepository.save(receiptNote);
        return true;
    }

    @Override
    public String addCancellingStocks(ReceiptNote receiptNote) {
        String parameterNo = OrderUtils.getTuiKuCode();
        receiptNote.setReceiptNo(parameterNo);
        receiptNote.setReceiptType("退库");
        receiptNote.setReceiptStatus(6);
        receiptNote.setReceiptConductor(ShiroUtils.getUserInfo().getName());
        receiptNote.setReceiptTime(new Date());
        receiptNote.setReceiptPerson(personnelRepository.findById(receiptNote.getReceiptPersonId()).get().getBasisName());
//        receiptNote.setReceiptDepartment(departmentRepository.findById(receiptNote.getReceiptDepartmentId()).get().getBasisName());
        receiptNote.setReceiptLocation(areaRepository.findById(receiptNote.getReceiptLocationId()).get().getBasisName());

        ReceiptNote save = receiptNoteRepository.save(receiptNote);
        List<ReceiptNoteItem> receiptNoteItemStringJson = JSONUtil.toList(JSONUtil.parseArray(receiptNote.getItemJson()), ReceiptNoteItem.class);
        for (ReceiptNoteItem receiptNoteItem : receiptNoteItemStringJson) {
            AssetsInfo byOtherId = assetsInfoRepository.findByOtherId(receiptNoteItem.getOtherId());
            byOtherId.setAssetsStatus(0);
            receiptNoteItem.setPracticalReturnTime(receiptNote.getPracticalReturnTime());
            receiptNoteItem.setIsWithdrawing(1);
            receiptNoteItem.setReceiptNo(save.getReceiptNo());
            receiptNoteItem.setReceiptTime(new Date());
            receiptNoteItem.setPracticalReturnTime(new Date());
            receiptNoteItem.setWithdrawingNumber(receiptNote.getReceiptNo());
//            receiptNoteItem.setDepartmentBefore(byOtherId.getDepartment() + "");
//            receiptNoteItem.setReceiptConductorBefore(byOtherId.getAssetsUser() + "");
//            receiptNoteItem.setReceiptLocationBefore(byOtherId.getAssetsArea() + "");
            receiptNoteItem.setDepartmentBefore(departmentRepository.findById(Long.valueOf(byOtherId.getDepartment())).get().getBasisName());
            receiptNoteItem.setReceiptConductorBefore(personnelRepository.findById(Long.valueOf(byOtherId.getAssetsUser())).get().getBasisName());
            receiptNoteItem.setReceiptLocationBefore(areaRepository.findById(Long.valueOf(byOtherId.getAssetsArea())).get().getBasisName());
//
//            receiptNoteItem.setWithdrawingNumber();
            receiptNoteItem.setDepartmentAfter("");
            receiptNoteItem.setReceiptConductorAfter("");
            receiptNoteItem.setReceiptLocationAfter("");
            assetsInfoRepository.save(byOtherId);
            receiptNoteItemRepository.save(receiptNoteItem);

            ReceiptNoteItem receiptNoteItemO = receiptNoteItemRepository.findByIsWithdrawingAndOtherId(0, receiptNoteItem.getOtherId());
            receiptNoteItemO.setIsWithdrawing(1);
            receiptNoteItemO.setPracticalReturnTime(receiptNoteItem.getPracticalReturnTime());
            receiptNoteItemO.setWithdrawingNumber(receiptNote.getReceiptNo());
            receiptNoteItemRepository.save(receiptNoteItemO);
            List<ReceiptNoteItem> byReceiptNo = receiptNoteItemRepository.findByReceiptNo(receiptNoteItem.getReceiptNo());
            Boolean isStatus = true;
            for (ReceiptNoteItem noteItem : byReceiptNo) {
                if (noteItem.getIsWithdrawing() == 0) {
                    isStatus = false;
                    break;
                }
            }
            if (isStatus) {
                ReceiptNote receiptNote1 = receiptNoteRepository.findByReceiptNo(receiptNoteItem.getReceiptNo());
                receiptNote1.setReceiptStatus(6);
                receiptNoteRepository.save(receiptNote1);
            }
            AssetsLogUtil.createAssetsLog(receiptNote.getId(), receiptNote.getReceiptNo(), byOtherId, AssetsLogUtil.OPERATION_CANCELLING_STOCKS);
        }
        return null;
    }

    @Override
    public List<AssetsInfo> findDetailByReceiptNoAssetsInfo(String receiptNo) {
        List<AssetsInfo> list = new ArrayList<>();
        List<ReceiptNoteItem> byReceiptNoUpAssetsInfo = receiptNoteItemRepository.findByReceiptNo(receiptNo);
        for (ReceiptNoteItem receiptNoteItem : byReceiptNoUpAssetsInfo) {
            AssetsInfo byOtherId = assetsInfoRepository.findByOtherId(receiptNoteItem.getOtherId());
            list.add(byOtherId);
        }
        return list;
    }

    @Override
    public Map<String, Object> findAllPage(ReceiptNoteCriteria criteria, PageRequest of) {
        Page<ReceiptNote> datas = getRepository().findAll(new Specification<ReceiptNote>() {
            @Override
            public Predicate toPredicate(Root<ReceiptNote> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
                return QueryHelp.getPredicate(root, criteria, criteriaBuilder);
            }
        }, of);
        return PageUtil.toPage(datas);
    }

    @Override
    @Transactional
    public Boolean updateReceiptNote(ReceiptNote receiptNote) {
        List<ReceiptNoteItem> list = receiptNoteItemRepository.findByReceiptNo(receiptNote.getReceiptNo());
        list.forEach(o -> {
            AssetsInfo byOtherId = assetsInfoRepository.findByOtherId(o.getOtherId());
            if (byOtherId.getAssetsStatus() != 0 && receiptNote.getReceiptStatus() != 1) {
                return;
            }
            byOtherId.setAssetsStatus(0);
            assetsInfoRepository.save(byOtherId);
            receiptNoteItemRepository.deleteById(o.getId());
        });
        receiptNote.setReceiptPerson(personnelRepository.findById(receiptNote.getReceiptPersonId()).get().getBasisName());
        receiptNote.setReceiptLocation(areaRepository.findById(receiptNote.getReceiptLocationId()).get().getBasisName());
        receiptNote.setReceiptDepartment(departmentRepository.findById(receiptNote.getReceiptDepartmentId()).get().getBasisName());
        List<ReceiptNoteItem> entityList = JSONObject.parseArray(receiptNote.getItemJson(), ReceiptNoteItem.class);
        for (ReceiptNoteItem item : entityList) {
            AssetsInfo byOtherId = assetsInfoRepository.findByOtherId(item.getOtherId());
            if (byOtherId.getAssetsStatus() != 0 && receiptNote.getReceiptStatus() != 1) {
                throw new RuntimeException("资产已被占用，被占用资产编号：" + byOtherId.getOtherId());
            }
            byOtherId.setAssetsStatus(9);
            item.setReceiptTime(receiptNote.getReceiptTime());
            item.setExpectedReturnTime(receiptNote.getExpectedReturnTime());
//            receiptNoteItem.setPracticalReturnTime();
            item.setIsWithdrawing(0);
//            receiptNoteItem.setDepartmentBefore();
//            receiptNoteItem.setReceiptConductorBefore();
//            receiptNoteItem.setReceiptLocationBefore();
//            receiptNoteItem.setWithdrawingNumber();
            item.setDepartmentAfter(receiptNote.getReceiptDepartment());
            item.setReceiptConductorAfter(receiptNote.getReceiptConductor());
            item.setReceiptLocationAfter(receiptNote.getReceiptLocation());
            assetsInfoRepository.save(byOtherId);
            item.setReceiptNo(receiptNote.getReceiptNo());
            receiptNoteItemRepository.save(item);
        }

        receiptNote.setReceiptStatus(1);
        receiptNoteRepository.save(receiptNote);
        return null;
    }

    @Override
    public Map showAllReceiptNote(String keyword, PageBean pageBean) {
        HashMap<String, Object> map = new HashMap<>();
        if (keyword == null || "".equals(keyword)) {
            Page<ReceiptNote> result = receiptNoteRepository.findByOrderByCreateTimeDesc(pageBean.getPagable());
            map.put("list", result.getContent());
            map.put("totalCount", result.getTotalElements());
        } else {
            List<ReceiptNote> result = receiptNoteRepository.findByReceiptNoteNumberContainsOrderByCreateTimeDesc(keyword, (pageBean.getPage() - 1) * pageBean.getLimit(), pageBean.getLimit());
            map.put("list", result);
            map.put("totalCount", result.size());
        }
        return map;
    }

    @Override
    public Map showAllReceiptNote(String keyword, String type, PageBean pageBean) {
        HashMap<String, Object> map = new HashMap<>();
        if (keyword == null || "".equals(keyword)) {
            Page<ReceiptNote> result = null;
            if ("".equals(type)) {
                result = receiptNoteRepository.findAll(pageBean.getPagable());
            } else {
                result = receiptNoteRepository.findByReceiptType(type, pageBean.getPagable());
            }

            map.put("list", result.getContent());
            map.put("totalCount", result.getTotalElements());
        } else {
            List<ReceiptNote> result = receiptNoteRepository.findByReceiptNoteNumberAndDocumentTypeContainsOrderByCreateTimeDesc(keyword, type, (pageBean.getPage() - 1) * pageBean.getLimit(), pageBean.getLimit());
            map.put("list", result);
            map.put("totalCount", result.size());
        }
        return map;
    }
}
