package com.yzm.property.business.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.business.entity.BorrowReturnItem;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface BorrowReturnItemRepository extends BaseRepository<BorrowReturnItem, Long> {

    List<BorrowReturnItem> findAllByBorrowReturnId(String borrowReturnId);

    List<BorrowReturnItem> getByBorrowReturnId(String borrowRetuenId);

    List<BorrowReturnItem> findByBiNumber(String biNumber);

    BorrowReturnItem findByBiReturnStandbyAndOtherId(int i, String otherId);

    List<BorrowReturnItem> findByBorrowReturnIdAndBiReturnStandby(String id,int biReturnStandby);

    List<BorrowReturnItem> findByBorrowReturnId(String id);
    @Query(value = "select * from borrow_return_item where (assets_code=?1 or assets_rfid = ?2) and bi_return_standby=?3 and borrow_return_id=?4 ",nativeQuery = true)
    BorrowReturnItem findByAssetsCodeOrAssetsRfidAndBiReturnStandbyAndBorrowReturnId(String rfid, String code, int i,String id);

    @Query(value = "select * from borrow_return_item where  assets_rfid = ?1 and bi_return_standby=?2",nativeQuery = true)
    BorrowReturnItem findByAssetsRfidAndBiReturnStandby(String rfid, int i);

    List<BorrowReturnItem> findByBorrowReturnIdAndAssetsNumber(String borrowReturnId, String assetsNumber);
}
