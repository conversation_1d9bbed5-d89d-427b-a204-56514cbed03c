package com.yzm.property.business.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.business.entity.BusinessScrap;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/7 15:43
 */
public interface BusinessScrapRepository extends BaseRepository<BusinessScrap, Long> {

    @Query(nativeQuery=true,value = "select distinct s.* from business_scrap s,assets_info a,assets_scrap_middle m " +
            "where s.id = m.scrap_id and a.id = m.assets_id and ( s.receipt_number like '%' ?1 '%' or a.other_id like '%' ?1 '%' or a.assets_name like '%' ?1 '%' ) " +
            "order by s.scrap_date desc limit ?2 , ?3 ")
    List<BusinessScrap> findByReceiptNumberContainsOrderByCreateTimeDesc(String key, Integer start, Integer size);

    Page<BusinessScrap> findByOrderByCreateTimeDesc(Pageable pageable);
//    Page<BusinessScrap> findAllByReceiptNumberContains(String keyword, Pageable pageable);
}
