package com.yzm.property.business.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.business.entity.AssetsScrapMiddle;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/7 9:18
 */
public interface AssetsScrapMiddleRepository extends BaseRepository<AssetsScrapMiddle,Long> {
    List<AssetsScrapMiddle> findAllByScrapId(Long id);

    AssetsScrapMiddle findTopByAssetsIdAndScrapId(Long assetsId,Long scrapId);

    void removeAllByScrapId(Long id);
}
