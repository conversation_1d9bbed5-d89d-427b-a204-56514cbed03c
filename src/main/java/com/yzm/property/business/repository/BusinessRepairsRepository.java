package com.yzm.property.business.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.business.entity.BusinessRepairs;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/28 15:43
 */
public interface BusinessRepairsRepository extends BaseRepository<BusinessRepairs, Long> {
    @Query(nativeQuery=true,value = "select distinct r.* from business_repairs r,assets_info a,assets_repairs_middle m " +
            "where ( r.id = m.repairs_id or r.id = m.return_id ) and a.id = m.assets_id and ( r.receipt_number like '%' ?1 '%' or a.other_id like '%' ?1 '%' or a.assets_name like '%' ?1 '%' ) " +
            "order by r.create_time desc limit ?2 , ?3 ")
    List<BusinessRepairs> findByReceiptNumberContainsOrderByCreateTimeDesc(String key, Integer start, Integer size);

    Page<BusinessRepairs> findByOrderByCreateTimeDesc(Pageable pageable);

    @Query(nativeQuery=true,value = "select distinct r.* from business_repairs r,assets_info a,assets_repairs_middle m " +
            "where ( r.id = m.repairs_id or r.id = m.return_id ) and r.document_type = ?2 and a.id = m.assets_id and ( r.receipt_number like '%' ?1 '%' or a.other_id like '%' ?1 '%' or a.assets_name like '%' ?1 '%' ) " +
            "order by r.create_time desc limit ?3 , ?4 ")
    List<BusinessRepairs> findByReceiptNumberAndDocumentTypeContainsOrderByCreateTimeDesc(String key, String type,Integer start, Integer size);

    Page<BusinessRepairs> findByDocumentTypeOrderByCreateTimeDesc(String type,Pageable pageable);

//    Page<BusinessRepairs> findAllByReceiptNumberContains(String key, Pageable pageable);
}
