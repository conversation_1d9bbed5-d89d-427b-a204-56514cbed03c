package com.yzm.property.business.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.business.entity.BorrowReturn;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface BorrowReturnRepository extends BaseRepository<BorrowReturn, Long> {
    @Query(nativeQuery=true,value = "select distinct c.* from borrow_return_item b ,borrow_return c where  b.borrow_return_id = c.id and (  b.bi_number like '%' ?1 '%' or b.assets_number like '%' ?1 '%' or  b.assets_specifications like '%' ?1 '%' or b.assets_rfid = ?1 ) " +
            "order by c.create_time desc  limit ?2 , ?3 ")
    List<BorrowReturn> findByBiNumberContainsOrderByCreateTimeDesc(String keyword, int start, Integer size);

    Page<BorrowReturn> findByOrderByCreateTimeDesc(Pageable pagable);

    Page<BorrowReturn> findByBrBusinessStandbyOrderByCreateTimeDesc(String type, Pageable pagable);
    @Query(nativeQuery=true,value = "select distinct c.* from borrow_return_item b ,borrow_return c where   b.borrow_return_id = c.id and c.br_business_standby = ?2 and (  b.bi_number like '%' ?1 '%' or b.assets_number like '%' ?1 '%' or  b.assets_specifications like '%' ?1 '%' or b.assets_rfid = ?1 ) " +
            "order by c.create_time desc  limit ?3 , ?4 ")
    List<BorrowReturn> findByBiNumberAndBrBusinessStandbyContainsOrderByCreateTimeDesc(String keyword, String type, int i, Integer limit);

    BorrowReturn findByBrNumber(String biNumber);
}
