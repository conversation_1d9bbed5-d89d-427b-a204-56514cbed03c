package com.yzm.property.business.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.business.entity.BusinessRepairsItem;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/28 15:43
 */
public interface BusinessRepairsItemRepository extends BaseRepository<BusinessRepairsItem, Long> {
    List<BusinessRepairsItem> findByRecordNumber(String recordNumber);

}
