package com.yzm.property.business.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.business.entity.AssetsRepairsMiddle;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/29 17:05
 */
public interface AssetsRepairsMiddleRepository extends BaseRepository<AssetsRepairsMiddle,Long> {
    //根据维修表的id查找报修中间表的报修相关信息
    List<AssetsRepairsMiddle> findAllByRepairsId(Long id);
    //根据维修表的id查找报修中间表的归还相关信息
    List<AssetsRepairsMiddle> findAllByReturnId(Long id);
    //查找
    AssetsRepairsMiddle findTopByAssetsIdAndReturnStatus(Long id,Integer status);

    Integer countAllByRepairsIdAndReturnStatus(Long id,Integer status);

    void removeAllByRepairsId(Long id);

}
