package com.yzm.property.business.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.business.entity.ReceiptNote;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface ReceiptNoteRepository extends BaseRepository<ReceiptNote, Long> {


    ReceiptNote findByReceiptNo(String receiptNo);

    Page<ReceiptNote> findByOrderByCreateTimeDesc(Pageable pagable);

    @Query(nativeQuery = true, value = "select distinct c.* from borrow_return_item b,assets_info a ,borrow_return c where a.id = b.assets_id and b.borrow_return_id = c.id and (  b.bi_number like '%' ?1 '%' or a.other_id like '%' ?1 '%' or  a.assets_name like '%' ?1 '%' ) " +
            "order by c.create_time desc  limit ?2 , ?3 ")
    List<ReceiptNote> findByReceiptNoteNumberContainsOrderByCreateTimeDesc(String keyword, int i, Integer limit);


    @Query(nativeQuery = true, value = "select distinct c.* from receipt_note_item b,assets_info a ,receipt_note c where a.other_id = b.other_id and b.receipt_no = c.receipt_no and  if(?2 !='',c.receipt_type =  ?2,1=1)  and  (c.receipt_no like '%' ?1 '%' or a.specifications like '%' ?1 '%' or a.other_id like '%' ?1 '%' or  a.assets_name like '%' ?1 '%' ) " +
            "order by c.create_time desc  limit ?3 , ?4")
    List<ReceiptNote> findByReceiptNoteNumberAndDocumentTypeContainsOrderByCreateTimeDesc(String keyword, String type, int i, Integer limit);

    Page<ReceiptNote> findByReceiptType(String type, Pageable pagable);
}
