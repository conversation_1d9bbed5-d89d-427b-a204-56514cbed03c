package com.yzm.property.business.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.business.entity.ReceiptNoteItem;

import java.util.List;

public interface ReceiptNoteItemRepository extends BaseRepository<ReceiptNoteItem, Long> {


    List<ReceiptNoteItem> findByReceiptNo(String receiptNo);

    ReceiptNoteItem findByIsWithdrawingAndOtherId(int i, String otherId);
}
