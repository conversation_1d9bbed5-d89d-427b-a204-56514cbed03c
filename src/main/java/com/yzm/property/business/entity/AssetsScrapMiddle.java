package com.yzm.property.business.entity;

import com.yzm.framework.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/7 9:12
 */
@Entity
@Table(name = "assets_scrap_middle")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AssetsScrapMiddle extends BaseEntity {
    @Column
    private Long assetsId;

    @Column
    private Long scrapId;
}
