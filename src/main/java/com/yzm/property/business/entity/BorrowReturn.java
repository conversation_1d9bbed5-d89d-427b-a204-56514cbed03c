package com.yzm.property.business.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yzm.framework.base.BaseEntity;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

/**
 * 借用归还
 */
@Data
@Entity
@Table(name = "borrow_return")
public class BorrowReturn extends BaseEntity {
    @Column(name = "br_number", columnDefinition = "varchar(100) COMMENT '单据编号'")
    private String brNumber;
    @Column(name = "br_name", columnDefinition = "varchar(100) COMMENT '单据名称'")
    private String brName;
    @Column(name = "br_standby", columnDefinition = "bigint COMMENT '办理状态'")
    private int brStandby;
    @Column(name = "br_business_standby", columnDefinition = "varchar(100) COMMENT '业务状态'")
    private String brBusinessStandby;
    //借用人或归还人
    @Column(name = "br_people", columnDefinition = "varchar(100) COMMENT '借用人'")
    private String brPeople;
    @Column(name = "br_people_id", columnDefinition = "varchar(100) COMMENT '借用人ID'")
    private Long brPeopleId;
    /*@Column(name = "br_return_people", columnDefinition = "varchar(100) COMMENT '归还人'")
    private String brReturnPeople;*/
    @Column(name = "br_organization", columnDefinition = "varchar(100) COMMENT '借用人所属部门'")
    private String brOrganization;
    @Column(name = "br_organization_id", columnDefinition = "varchar(100) COMMENT '借用人所属部门ID'")
    private Long brOrganizationId;

    @Column(name = "in_storage_warehouse", columnDefinition = "varchar(100) COMMENT '收入仓库'")
    private String inStorageWarehouse;//收入仓库
    @Column(name = "in_storage_area", columnDefinition = "varchar(100) COMMENT '收入库位'")
    private String inStorageArea;//收入库位
    @Column(name = "br_time", columnDefinition = "DATETIME COMMENT '借用时间'")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date brTime;
    @Column(name = "br_predict_time", columnDefinition = "DATETIME COMMENT '预计归还时间'")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date brPredictTime;
    @Column(name = "br_actual_return_time", columnDefinition = "DATETIME COMMENT '实际归还时间'")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date brActualReturnTime;
    @Column(name = "br_remark", columnDefinition = "varchar(100) COMMENT '借用备注'")
    private String brRemark;
    @Column(name = "br_return_remark", columnDefinition = "varchar(100) COMMENT '归还备注'")
    private String brReturnRemark;
    @Column(name = "br_storage_total", columnDefinition = "int(10) COMMENT '借用数量'")
    private Integer brStorageTotal; //入库总数
    @Column(name = "handler_id", columnDefinition = "varchar(100) COMMENT '处理人ID'")
    private Long handlerId;
    @Column(name = "handler_name", columnDefinition = "varchar(100) COMMENT '处理人名称'")
    private String handlerName;
    /** 产品子表json */
    @Transient
    private String itemJson;


    public Long getHandlerId() {
        return handlerId;
    }

    public void setHandlerId(Long handlerId) {
        this.handlerId = handlerId;
    }

    public String getHandlerName() {
        return handlerName;
    }

    public void setHandlerName(String handlerName) {
        this.handlerName = handlerName;
    }

    public void setBrNumber(String brNumber) {
        this.brNumber = brNumber;
    }

    public String getBrName() {
        return brName;
    }

    public void setBrName(String brName) {
        this.brName = brName;
    }

    public int getBrStandby() {
        return brStandby;
    }

    public void setBrStandby(int brStandby) {
        this.brStandby = brStandby;
    }

    public String getBrBusinessStandby() {
        return brBusinessStandby;
    }

    public void setBrBusinessStandby(String brBusinessStandby) {
        this.brBusinessStandby = brBusinessStandby;
    }

    public String getBrPeople() {
        return brPeople;
    }

    public void setBrPeople(String brPeople) {
        this.brPeople = brPeople;
    }

    public String getBrOrganization() {
        return brOrganization;
    }

    public void setBrOrganization(String brOrganization) {
        this.brOrganization = brOrganization;
    }



    public Date getBrPredictTime() {
        return brPredictTime;
    }

    public void setBrPredictTime(Date brPredictTime) {
        this.brPredictTime = brPredictTime;
    }

    public Date getBrActualReturnTime() {
        return brActualReturnTime;
    }

    public void setBrActualReturnTime(Date brActualReturnTime) {
        this.brActualReturnTime = brActualReturnTime;
    }

    public Date getBrTime() {
        return brTime;
    }

    public void setBrTime(Date brTime) {
        this.brTime = brTime;
    }

    public String getBrRemark() {
        return brRemark;
    }

    public void setBrRemark(String brRemark) {
        this.brRemark = brRemark;
    }

    public String getBrNumber() {
        return brNumber;
    }

    public String getBrReturnRemark() {
        return brReturnRemark;
    }

    public void setBrReturnRemark(String brReturnRemark) {
        this.brReturnRemark = brReturnRemark;
    }
}
