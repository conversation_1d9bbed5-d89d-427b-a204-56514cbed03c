package com.yzm.property.business.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yzm.framework.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/6 16:59
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "business_scrap")
public class BusinessScrap extends BaseEntity {

    public BusinessScrap(Long id,Long userId, String receiptNumber, String userName, Long assetsId, String scrapDate, String cause, String note, Integer scrapStatus, Integer scrapNumber) {
        super.id = id;
        this.userId = userId;
        this.receiptNumber = receiptNumber;
        this.userName = userName;
        this.assetsId = assetsId;
        this.scrapDate = scrapDate;
        this.cause = cause;
        this.note = note;
        this.scrapStatus = scrapStatus;
        this.scrapNumber = scrapNumber;
    }

    @Column
    private Long userId;

    @Column
    private String receiptNumber;

    @Column
    private String userName;

    @Column
    private Long assetsId;

    @Column
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String scrapDate;

    @Column
    private String cause;

    @Column
    private String note;

    @Column
    private Integer scrapStatus;

    @Column
    private Integer scrapNumber;
}
