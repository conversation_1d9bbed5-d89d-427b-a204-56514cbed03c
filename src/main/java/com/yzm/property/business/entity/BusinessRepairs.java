package com.yzm.property.business.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yzm.framework.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/28 14:42
 */
@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "business_repairs")
public class BusinessRepairs extends BaseEntity {

    public BusinessRepairs(Long id,String receiptNumber, Integer repairsStatus, Long maintainer, String maintainerName,Date repairsDate,Date planDate, Date finishDate, Double price, String cause, String note, String file,String documentType,Long handlerId,String handlerName) {
        super.id = id;
        this.receiptNumber = receiptNumber;
        this.repairsStatus = repairsStatus;
        this.maintainer = maintainer;
        this.maintainerName = maintainerName;
        this.repairsDate = repairsDate;
        this.planDate = planDate;
        this.finishDate = finishDate;
        this.price = price;
        this.cause = cause;
        this.note = note;
        this.file = file;
        this.documentType = documentType;
        this.handlerId = handlerId;
        this.handlerName = handlerName;
    }

    @Column
    private String receiptNumber;

    @Column
    private Integer repairsStatus;

    @Column
    private Long maintainer;

    @Column
    private Long maintainerId;

    @Column
    private String maintainerName;

    @Column
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date repairsDate;

    @Column
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date planDate;

    @Column
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date finishDate;

    @Column
    private Double price;

    @Column
    private String cause;

    @Column
    private String note;

    @Column
    private String file;

    @Column
    private String documentType;

    @Column
    private Long handlerId;

    @Column
    private String handlerName;


}
