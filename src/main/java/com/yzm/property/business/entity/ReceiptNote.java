package com.yzm.property.business.entity;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yzm.framework.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

/**
 * 领用退库表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:34
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "receipt_note")
public class ReceiptNote extends BaseEntity {
    @Column(name = "receipt_no", columnDefinition = "varchar(100) COMMENT '单据编号'")
    private String receiptNo;
    @Column(name = "receipt_name", columnDefinition = "varchar(100) COMMENT '单据名称'")
    private String receiptName;
    @Column(name = "receipt_type", columnDefinition = "varchar(100) COMMENT '单据类型'")
    private String receiptType;
    @Column(name = "receipt_status", columnDefinition = "bigint COMMENT '状态'")
    private int receiptStatus;
    @Column(name = "receipt_person", columnDefinition = "varchar(100) COMMENT '领用/退库人'")
    private String receiptPerson;
    @Column(name = "receipt_person_id", columnDefinition = "varchar(100) COMMENT '领用/退库人ID'")
    private Long receiptPersonId;
    @Column(name = "receipt_time", columnDefinition = "DATETIME COMMENT '领用时间'")
    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date receiptTime;
    @Column(name = "expected_return_time", columnDefinition = "DATETIME COMMENT '预计退库时间'")
    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date expectedReturnTime;
    @Column(name = "practical_return_time", columnDefinition = "DATETIME COMMENT '实际退库时间'")
    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date practicalReturnTime;
    @Column(name = "receipt_department", columnDefinition = "varchar(100) COMMENT '领用/退库后使用部门'")
    private String receiptDepartment;
    @Column(name = "receipt_department_id", columnDefinition = "varchar(100) COMMENT '领用/退库后使用部门'")
    private Long receiptDepartmentId;
    @Column(name = "receipt_location", columnDefinition = "varchar(100) COMMENT '领用/退库后位置'")
    private String receiptLocation;
    @Column(name = "receipt_location_id", columnDefinition = "varchar(100) COMMENT '领用/退库后位置'")
    private Long receiptLocationId;
    @Column(name = "receipt_conductor", columnDefinition = "varchar(100) COMMENT '领用/退库处理人'")
    private String receiptConductor;
    @Column(name = "memo", columnDefinition = "varchar(100) COMMENT '备注'")
    private String memo;
    /**
     * 产品子表json
     */
    @Transient
    private String itemJson;
}
