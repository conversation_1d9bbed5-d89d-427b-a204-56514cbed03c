package com.yzm.property.business.entity;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yzm.framework.base.BaseEntity;
import com.yzm.property.basis.entity.AssetsInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

/**
 * 领用退库表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:34
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "receipt_note_item")
public class ReceiptNoteItem extends BaseEntity {
    @Column(name = "receipt_no", columnDefinition = "varchar(100) COMMENT '单据编号'")
    private String receiptNo;
    @Column(name = "other_id", columnDefinition = "varchar(100) COMMENT '资产编号'")
    private String otherId;
    @Column(name = "specifications", columnDefinition = "varchar(100) COMMENT '规格型号'")
    private String specifications;
    @Column(name = "assets_status", columnDefinition = "varchar(100) COMMENT '资产状态'")
    private int assetsStatus;
    @Column(name = "receipt_time", columnDefinition = "DATETIME COMMENT '领用时间'")
    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date receiptTime;
    @Column(name = "expected_return_time", columnDefinition = "DATETIME COMMENT '预计退库时间'")
    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date expectedReturnTime;
    @Column(name = "practical_return_time", columnDefinition = "DATETIME COMMENT '实际退库时间'")
    @DateTimeFormat("yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date practicalReturnTime;
    @Column(name = "is_withdrawing", columnDefinition = "INT(10) COMMENT '是否退库'")
    private int isWithdrawing;
    @Column(name = "withdrawing_number", columnDefinition = "varchar(100) COMMENT '退库编号'")
    private String withdrawingNumber;
    @Column(name = "department_after", columnDefinition = "varchar(100) COMMENT '使用部门(变更后)'")
    private String departmentAfter;
    @Column(name = "receipt_conductor_after", columnDefinition = "varchar(100) COMMENT '使用人(变更后)'")
    private String receiptConductorAfter;
    @Column(name = "receipt_location_after", columnDefinition = "varchar(100) COMMENT '位置(变更后)'")
    private String receiptLocationAfter;
    @Column(name = "department_before", columnDefinition = "varchar(100) COMMENT '使用部门(变更前)'")
    private String departmentBefore;
    @Column(name = "receipt_conductor_before", columnDefinition = "varchar(100) COMMENT '使用人(变更前)'")
    private String receiptConductorBefore;
    @Column(name = "receipt_location_before", columnDefinition = "varchar(100) COMMENT '位置(变更前)'")
    private String receiptLocationBefore;

    /**
     * 产品子表
     */
    @Transient
    private AssetsInfo assetsInfoList;
}
