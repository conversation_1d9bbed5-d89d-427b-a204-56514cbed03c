package com.yzm.property.business.entity;

import com.yzm.framework.base.BaseEntity;
import com.yzm.property.basis.entity.AssetsInfo;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

/**
 * 借用归还子表
 */
@Data
@Entity
@Table(name = "borrow_return_item")
public class BorrowReturnItem extends BaseEntity {
    @Column(name = "borrow_return_id", columnDefinition = "varchar(100) COMMENT '外键ID'")
    private String borrowReturnId;
    @Column(name = "bi_number", columnDefinition = "varchar(100) COMMENT '单据编号'")
    private String biNumber;
    @Column(name = "assets_id", columnDefinition = "bigint COMMENT '资产ID'")
    private Long assetsId;
    @Column(name = "other_id", columnDefinition = "varchar(100) COMMENT '资产编号'")
    private String otherId;
    @Column(name = "assets_status", columnDefinition = "varchar(100) COMMENT '资产状态'")
    private int assetsStatus;
    @Column(name = "specifications", columnDefinition = "varchar(100) COMMENT '规格型号'")
    private String specifications;
    @Column(name = "sequence", columnDefinition = "varchar(100) COMMENT '序列'")
    private String sequence;
    @Column(name = "bi_business_standby", columnDefinition = "varchar(100) COMMENT '业务状态'")
    private String biBusinessStandby;
    @Column(name = "bi_people", columnDefinition = "varchar(100) COMMENT '借用人'")
    private String biPeople;
    @Column(name = "bi_organization", columnDefinition = "varchar(100) COMMENT '借用人所属组织'")
    private String biOrganization;
    @Column(name = "bi_time", columnDefinition = "DATETIME COMMENT '借用时间'")
    private Date biTime;
    @Column(name = "bi_predict_time", columnDefinition = "DATETIME COMMENT '预计归还时间'")
    private Date biPredictTime;
    @Column(name = "bi_actual_return_time", columnDefinition = "DATETIME COMMENT '实际归还时间'")
    private Date biActualReturnTime;
    @Column(name = "bi_return_standby", columnDefinition = "INT(10) COMMENT '是否归还'")
    private int biReturnStandby;
    @Column(name = "bi_remark", columnDefinition = "varchar(100) COMMENT '借用备注'")
    private String biRemark;
    @Column(name = "bi_return_remark", columnDefinition = "varchar(100) COMMENT '归还备注'")
    private String biReturnRemark;
    @Column(name = "assets_name", columnDefinition = "varchar(100) COMMENT '资产名称'")
    private String assetsName;


    @Column(name = "assets_number", columnDefinition = "varchar(100) COMMENT '材料编号'")
    private String assetsNumber; //材料编号
    //    @Column(name = "assets_name", columnDefinition = "varchar(100) COMMENT '材料名称'")
//    private String assetsName; //材料名称
    @Column(name = "assets_specifications", columnDefinition = "varchar(100) COMMENT '规格型号'")
    private String assetsSpecifications;//规格型号
    @Column(name = "assets_unit", columnDefinition = "int(20) COMMENT '材料单位ID'")
    private Long assetsUnit; //材料单位ID
    @Column(name = "assets_unit_name", columnDefinition = "varchar(20) COMMENT '材料单位'")
    private String assetsUnitName; //材料单位
    @Column(name = "assets_code", columnDefinition = "varchar(255) COMMENT '材料条码'")
    private String assetsCode; //材料条码
    @Column(name = "assets_rfid", columnDefinition = "varchar(255) COMMENT '材料RFID'")
    private String assetsRfid; //材料RFID
    @Column(name = "assets_type_name", columnDefinition = "varchar(255) COMMENT '材料类型'")
    private String assetsTypeName; //材料类型
    @Column(name = "assets_type", columnDefinition = "varchar(255) COMMENT '材料类型Id'")
    private Long assetsType; //材料类型
    @Column(name = "assets_position", columnDefinition = "varchar(255) COMMENT '常用货位'")
    private String assetsPosition; //常用货位
    @Column(name = "consumable_num", columnDefinition = "varchar(255) COMMENT '数量'")
    private Integer consumableNum; //数量
    @Column(name = "warehouse_info", columnDefinition = "varchar(255) COMMENT '库存仓库'")
    private String warehouseInfo; //库存仓库
    @Column(name = "area_info", columnDefinition = "varchar(255) COMMENT '库存仓库'")
    private String areaInfo; //库存仓库

    /**
     * 产品子表
     */
    @Transient
    private AssetsInfo assetsInfoList;
    /**
     * 产品子表
     */
    @Transient
    private Integer nowRepertory;


    public int getAssetsStatus() {
        return assetsStatus;
    }

    public void setAssetsStatus(int assetsStatus) {
        this.assetsStatus = assetsStatus;
    }

    public String getAssetsName() {
        return assetsName;
    }

    public void setAssetsName(String assetsName) {
        this.assetsName = assetsName;
    }

    public String getBorrowReturnId() {
        return borrowReturnId;
    }

    public void setBorrowReturnId(String borrowReturnId) {
        this.borrowReturnId = borrowReturnId;
    }

    public String getBiNumber() {
        return biNumber;
    }

    public void setBiNumber(String biNumber) {
        this.biNumber = biNumber;
    }

    public Long getAssetsId() {
        return assetsId;
    }

    public void setAssetsId(Long assetsId) {
        this.assetsId = assetsId;
    }

    public String getOtherId() {
        return otherId;
    }

    public void setOtherId(String otherId) {
        this.otherId = otherId;
    }

    public String getSpecifications() {
        return specifications;
    }

    public void setSpecifications(String specifications) {
        this.specifications = specifications;
    }

    public String getSequence() {
        return sequence;
    }

    public void setSequence(String sequence) {
        this.sequence = sequence;
    }

    public String getBiBusinessStandby() {
        return biBusinessStandby;
    }

    public void setBiBusinessStandby(String biBusinessStandby) {
        this.biBusinessStandby = biBusinessStandby;
    }

    public String getBiPeople() {
        return biPeople;
    }

    public void setBiPeople(String biPeople) {
        this.biPeople = biPeople;
    }

    public String getBiOrganization() {
        return biOrganization;
    }

    public void setBiOrganization(String biOrganization) {
        this.biOrganization = biOrganization;
    }

    public Date getBiTime() {
        return biTime;
    }

    public void setBiTime(Date biTime) {
        this.biTime = biTime;
    }

    public Date getBiPredictTime() {
        return biPredictTime;
    }

    public void setBiPredictTime(Date biPredictTime) {
        this.biPredictTime = biPredictTime;
    }

    public Date getBiActualReturnTime() {
        return biActualReturnTime;
    }

    public void setBiActualReturnTime(Date biActualReturnTime) {
        this.biActualReturnTime = biActualReturnTime;
    }

    public int getBiReturnStandby() {
        return biReturnStandby;
    }

    public void setBiReturnStandby(int biReturnStandby) {
        this.biReturnStandby = biReturnStandby;
    }

    public String getBiRemark() {
        return biRemark;
    }

    public void setBiRemark(String biRemark) {
        this.biRemark = biRemark;
    }

    public String getBiReturnRemark() {
        return biReturnRemark;
    }

    public void setBiReturnRemark(String biReturnRemark) {
        this.biReturnRemark = biReturnRemark;
    }
}
