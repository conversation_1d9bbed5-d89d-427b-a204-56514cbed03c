package com.yzm.property.business.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/29 16:33
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ScrapEntity extends BusinessScrap{
    private List<Integer> ids;

    public BusinessScrap getBusinessScrap(){
        return new BusinessScrap(this.getId(),this.getUserId(),this.getReceiptNumber(),this.getUserName(),this.getAssetsId(),this.getScrapDate(),this.getCause(),this.getNote(),this.getScrapStatus(),this.getScrapNumber());
    }
}
