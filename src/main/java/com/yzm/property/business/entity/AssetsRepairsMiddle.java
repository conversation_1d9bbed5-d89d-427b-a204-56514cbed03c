package com.yzm.property.business.entity;

import com.yzm.framework.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/28 14:59
 */
@Entity
@Table(name = "assets_repairs_middle")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AssetsRepairsMiddle extends BaseEntity {
    @Column
    private Long assetsId;

    @Column
    private Long repairsId;

    @Column
    private Integer returnStatus;

    @Column
    private Long returnId;
}
