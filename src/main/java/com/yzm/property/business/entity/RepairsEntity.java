package com.yzm.property.business.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Transient;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/29 16:33
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RepairsEntity extends BusinessRepairs{

    private List<Long> ids;
    /**
     * 入库子表json
     */
    @Transient
    private String itemJson;

    public BusinessRepairs getBusinessRepairs(){
        return new BusinessRepairs(this.getId(),this.getReceiptNumber(),this.getRepairsStatus(),this.getMaintainer(),this.getMaintainerName(),this.getRepairsDate(),this.getPlanDate(),this.getFinishDate(),this.getPrice(),this.getCause(),this.getNote(),this.getFile(),this.getDocumentType(),this.getHandlerId(),this.getHandlerName());
    }
}
