package com.yzm.property.consumable.utils;

import com.yzm.property.consumable.repository.ConsumableListRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ConsumableUtil {
    private static ConsumableListRepository consumableListRepository;
    public static final String OPERATION_RUKU = "入库";
    public static final String OPERATION_CHUKU="出库";
    public static final String OPERATION_JIEYONG="借用";
    public static final String OPERATION_GUIHUAN="归还";
    @Autowired
    public void init(ConsumableListRepository consumableListRepository){
        ConsumableUtil.consumableListRepository=consumableListRepository;
    }

//    public static void createConsumableList(Long operationId, String operationOtherId, String operationName, String consumableId ,
//                                            String consumableName, String userName, Date outDate,Integer nowRepertory,Double price){
//        ConsumableListEntity consumableListEntity=new ConsumableListEntity(operationId,operationOtherId,operationName,consumableId,consumableName,userName,outDate,nowRepertory,price);
//        ConsumableListEntity save=consumableListRepository.save(consumableListEntity);
//        if(save==null){
//            throw new RuntimeException("材料流水存储失败！");
//        }
//    }
}
