package com.yzm.property.consumable.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yzm.framework.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name="out_warehouse")
public class OutWarehouseEntity extends BaseEntity {
    @Column(name = "in_out_reson", columnDefinition = "varchar(100) COMMENT '出库原由'")
    private String outReson; //记录原由
    @Column(name = "out_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date outDate;//出库时间
    @Column(name = "record_number", columnDefinition = "varchar(100) COMMENT '记录编号'")
    private String recordNumber; //记录编号
    @Column(name = "assets_name", columnDefinition = "varchar(100) COMMENT '资产名称'")
    private String assetsName; //资产名称
    @Column(name = "assets_specifications", columnDefinition = "varchar(100) COMMENT '规格型号'")
    private String assetsSpecifications; //规格型号
    @Column(name = "assets_brand", columnDefinition = "varchar(100) COMMENT '资产品牌'")
    private String assetsBrand; //资产品牌
    @Column(name = "assets_unit", columnDefinition = "varchar(20) COMMENT '单位'")
    private String assetsUnit; //单位
    @Column(name = "now_repertory", columnDefinition = "int(20) COMMENT '当前库存'")
    private Integer nowRepertory; //当前库存
    @Column(name="consumable_num",columnDefinition = "int(20) COMMENT '出库数量'")
    private Integer consumableNum;//出库数量
    @Column(name="user_name",columnDefinition = "varchar(20) COMMENT '出库人'")
    private String userName;//出库人
    @Column(name="consumable_price",columnDefinition = "varchar(20) COMMENT '单价'")
    private String consumablePrice;//单价
     @Column(name="consumable_company",columnDefinition = "varchar(20) COMMENT '往来单位'")
    private String consumableCompany;//往来单位
     @Column(name="consumable_department",columnDefinition = "varchar(20) COMMENT '部门'")
    private String consumableDepartment;//部门

    /** 入库子表json */
    @Transient
    private String itemJson;
}
