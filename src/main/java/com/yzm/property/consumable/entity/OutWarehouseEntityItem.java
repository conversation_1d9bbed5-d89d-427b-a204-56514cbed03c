package com.yzm.property.consumable.entity;

import com.yzm.framework.base.BaseEntity;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Data
@Table(name="out_warehouse_item")
public class OutWarehouseEntityItem extends BaseEntity {
    @Column(name = "record_number", columnDefinition = "varchar(100) COMMENT '记录编号'")
    private String recordNumber; //记录编号
    @Column(name = "assets_number", columnDefinition = "varchar(100) COMMENT '材料编号'")
    private String assetsNumber; //材料编号
    @Column(name = "assets_name", columnDefinition = "varchar(100) COMMENT '材料名称'")
    private String assetsName; //材料名称
    @Column(name = "assets_specifications", columnDefinition = "varchar(100) COMMENT '规格型号'")
    private String assetsSpecifications;//规格型号
    @Column(name = "assets_unit", columnDefinition = "int(20) COMMENT '材料单位ID'")
    private Long assetsUnit; //材料单位ID
    @Column(name = "assets_unit_name", columnDefinition = "varchar(20) COMMENT '材料单位'")
    private String assetsUnitName; //材料单位
    @Column(name = "assets_code", columnDefinition = "varchar(255) COMMENT '材料条码'")
    private String assetsCode; //材料条码
    @Column(name = "assets_rfid", columnDefinition = "varchar(255) COMMENT '材料RFID'")
    private String assetsRfid; //材料RFID
    @Column(name = "assets_type_name", columnDefinition = "varchar(255) COMMENT '材料类型'")
    private String assetsTypeName; //材料类型
    @Column(name = "assets_type", columnDefinition = "varchar(255) COMMENT '材料类型Id'")
    private Long assetsType; //材料类型
    @Column(name = "assets_position", columnDefinition = "varchar(255) COMMENT '常用货位'")
    private String assetsPosition; //常用货位
    @Column(name="consumable_num",columnDefinition = "int(20) COMMENT '出库数量'")
    private Integer consumableNum;//数量
    @Column(name = "assets_id", columnDefinition = "bigint COMMENT '耗材ID'")
    private Long assetsId;
    @Column(name = "now_repertory", columnDefinition = "int(20) COMMENT '当前库存'")
    private Integer nowRepertory; //当前库存
    @Column(name = "warehouse_info", columnDefinition = "varchar(255) COMMENT '库存仓库'")
    private String warehouseInfo; //库存仓库
    @Column(name = "area_info", columnDefinition = "varchar(255) COMMENT '库位'")
    private String areaInfo; //库位
    @Column(name = "operation_number", columnDefinition = "varchar(255) COMMENT '操作单号'")
    private String operationNumber; //操作单号
    @Column(name = "price", columnDefinition = "float(100,2) COMMENT '单价'")
    private Double price;//单价
}
