package com.yzm.property.consumable.entity;

import com.yzm.framework.base.BaseEntity;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 借用归还子表
 */
@Data
@Entity
@Table(name = "transfer_item")
public class TransferItem extends BaseEntity {

    @Column(name = "record_number", columnDefinition = "varchar(100) COMMENT '记录编号(关联主表)'")
    private String recordNumber; //记录编号关联主表
    @Column(name = "assets_id", columnDefinition = "bigint COMMENT '材料ID'")
    private Long assetsId;

    @Column(name = "assets_number", columnDefinition = "varchar(100) COMMENT '材料编号'")
    private String assetsNumber; //材料编号
    @Column(name = "assets_name", columnDefinition = "varchar(100) COMMENT '材料名称'")
    private String assetsName; //材料名称
    @Column(name = "assets_specifications", columnDefinition = "varchar(100) COMMENT '规格型号'")
    private String assetsSpecifications;//规格型号
    @Column(name = "assets_unit", columnDefinition = "int(20) COMMENT '材料单位ID'")
    private Long assetsUnit; //材料单位ID
    @Column(name = "assets_unit_name", columnDefinition = "varchar(20) COMMENT '材料单位'")
    private String assetsUnitName; //材料单位
    @Column(name = "assets_code", columnDefinition = "varchar(255) COMMENT '材料条码'")
    private String assetsCode; //材料条码
    @Column(name = "assets_rfid", columnDefinition = "varchar(255) COMMENT '材料RFID'")
    private String assetsRfid; //材料RFID
    @Column(name = "assets_type_name", columnDefinition = "varchar(255) COMMENT '材料类型'")
    private String assetsTypeName; //材料类型
    @Column(name = "assets_type", columnDefinition = "varchar(255) COMMENT '材料类型Id'")
    private Long assetsType; //材料类型
    @Column(name = "assets_position", columnDefinition = "varchar(255) COMMENT '常用货位'")
    private String assetsPosition; //常用货位

    @Column(name = "warehouse_info", columnDefinition = "varchar(100) COMMENT '收入仓库'")
    private String warehouseInfo;//收入仓库
    @Column(name = "area_info", columnDefinition = "varchar(100) COMMENT '收入库位'")
    private String areaInfo;//收入库位

    @Column(name = "in_storage_warehouse_original", columnDefinition = "varchar(100) COMMENT '原收入仓库'")
    private String inStorageWarehouseOriginal;//原收入仓库
    @Column(name = "in_storage_area_original", columnDefinition = "varchar(100) COMMENT '原收入库位'")
    private String inStorageAreaOriginal;//原位置

    @Column(name = "in_storage_count", columnDefinition = "int(02) COMMENT '入库数量'")
    private Integer inStorageCount; //入库数量
    @Column(name = "in_storage_price", columnDefinition = "float(100,2) COMMENT '入库单价'")
    private Double inStoragePrice; //入库单价



}
