package com.yzm.property.consumable.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yzm.framework.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name="consumable_list_item")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ConsumableListItemEntity extends BaseEntity {
    @Column
    private Long operationId;//操作id

    @Column
    private String operationOtherId;//操作编号

    @Column
    private String operationName;//操作方式

    @Column
    private String consumableId;//耗材编号

    @Column
    private String consumableName;//耗材名称

    @Column
    private String userName;//领用人

    @Column
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date outDate;//出库时间

    @Column
    private Integer nowRepertory;//库存数量

    @Column
    private Double price;//单价
}
