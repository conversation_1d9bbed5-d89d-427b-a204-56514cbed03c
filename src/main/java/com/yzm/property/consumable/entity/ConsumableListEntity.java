package com.yzm.property.consumable.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yzm.framework.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name="consumable_list")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ConsumableListEntity extends BaseEntity {
    @Column
    @ExcelProperty(value = "操作id",index = 0)
    private Long operationId;//操作id

    @Column
    @ExcelProperty(value = "操作编号",index = 1)
    private String operationOtherId;//操作编号

    @Column
    private String operationName;//操作方式

    @Column(name = "assets_number", columnDefinition = "varchar(100) COMMENT '材料编号'")
    private String assetsNumber; //材料编号
    @Column(name = "assets_name", columnDefinition = "varchar(100) COMMENT '材料名称'")
    private String assetsName; //材料名称
    @Column(name = "assets_specifications", columnDefinition = "varchar(100) COMMENT '规格型号'")
    private String assetsSpecifications;//规格型号
    @Column(name = "assets_unit", columnDefinition = "int(20) COMMENT '材料单位ID'")
    private Long assetsUnit; //材料单位ID
    @Column(name = "assets_unit_name", columnDefinition = "varchar(20) COMMENT '材料单位'")
    private String assetsUnitName; //材料单位
    @Column(name = "assets_code", columnDefinition = "varchar(255) COMMENT '材料条码'")
    private String assetsCode; //材料条码
    @Column(name = "assets_rfid", columnDefinition = "varchar(255) COMMENT '材料RFID'")
    private String assetsRfid; //材料RFID
    @Column(name = "assets_type_name", columnDefinition = "varchar(255) COMMENT '材料类型'")
    private String assetsTypeName; //材料类型
    @Column(name = "assets_type", columnDefinition = "varchar(255) COMMENT '材料类型Id'")
    private Long assetsType; //材料类型
    @Column(name = "assets_position", columnDefinition = "varchar(255) COMMENT '常用货位'")
    private String assetsPosition; //常用货位
    @Column(name = "warehouse_info", columnDefinition = "varchar(255) COMMENT '库存仓库'")
    private String warehouseInfo; //库存仓库
    @Column(name = "area_info", columnDefinition = "varchar(255) COMMENT '库位'")
    private String areaInfo; //库位
    @Column(name = "count_info", columnDefinition = "varchar(255) COMMENT '数量'")
    private int countInfo; //库位










    @Column
    private String userName;//领用人

    @Column
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date outDate;//出库时间

    @Column
    private Integer nowRepertory;//库存数量

    @Column
    @ExcelProperty(value = "单价",index = 8)
    private Double price;//单价
}
