package com.yzm.property.consumable.entity;

import com.yzm.framework.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * 材料库存
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:33
 */
@Entity
@Table(name = "consumable_repertory")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ConsumableRepertory extends BaseEntity {
    @Column(name = "assets_number", columnDefinition = "varchar(100) COMMENT '材料编号'")
    private String assetsNumber; //材料编号
    @Column(name = "assets_name", columnDefinition = "varchar(100) COMMENT '材料名称'")
    private String assetsName; //材料名称
    @Column(name = "assets_specifications", columnDefinition = "varchar(100) COMMENT '规格型号'")
    private String assetsSpecifications;//规格型号
    @Column(name = "assets_unit", columnDefinition = "int(20) COMMENT '材料单位ID'")
    private Long assetsUnit; //材料单位ID
    @Column(name = "assets_unit_name", columnDefinition = "varchar(20) COMMENT '材料单位'")
    private String assetsUnitName; //材料单位
    @Column(name = "assets_code", columnDefinition = "varchar(255) COMMENT '材料条码'")
    private String assetsCode; //材料条码
    @Column(name = "assets_rfid", columnDefinition = "varchar(255) COMMENT '材料RFID'")
    private String assetsRfid; //材料RFID
    @Column(name = "assets_type_name", columnDefinition = "varchar(255) COMMENT '材料类型'")
    private String assetsTypeName; //材料类型
    @Column(name = "assets_type", columnDefinition = "varchar(255) COMMENT '材料类型Id'")
    private Long assetsType; //材料类型
    @Column(name = "assets_position", columnDefinition = "varchar(255) COMMENT '常用货位'")
    private String assetsPosition; //常用货位
    @Column(name = "now_repertory", columnDefinition = "int(20) COMMENT '当前库存'")
    private Integer nowRepertory; //当前库存
    @Column(name = "warehouse_info", columnDefinition = "varchar(255) COMMENT '库存仓库'")
    private String warehouseInfo; //库存仓库
    @Column(name = "area_info", columnDefinition = "varchar(255) COMMENT '库区'")
    private String areaInfo; //库位
    @Column(name = "location_info", columnDefinition = "varchar(255) COMMENT '库位'")
    private String locationInfo; //库位
    @Column(name = "operation_number", columnDefinition = "varchar(255) COMMENT '操作单号'")
    private String operationNumber; //操作单号
  /*  @Column(name = "plan_im_repertory", columnDefinition = "int(20) COMMENT '待入库'")
    private Integer planImRepertory; //待入库
    @Column(name = "plan_ex_repertory", columnDefinition = "int(20) COMMENT '待出库'")
    private Integer planExRepertory; //待出库*/
    @Column(name = "consumable_info_id", columnDefinition = "bigint(20) COMMENT '材料ID'")
    private Long consumableInfoId; //材料ID
    /**
     * 入库子表json
     */
    @Transient
    private Long nowCount;

}
