package com.yzm.property.consumable.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yzm.framework.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * 材料总览
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:33
 */
@Entity
@Table(name = "consumable_info")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ConsumableInfo extends BaseEntity {
    @Column(name = "assets_number", columnDefinition = "varchar(100) COMMENT '材料编号'")
    @ExcelProperty(value = "材料编号")
    private String assetsNumber; //材料编号
    @Column(name = "assets_name", columnDefinition = "varchar(100) COMMENT '材料名称'")
    @ExcelProperty(value = "材料名称")
    private String assetsName; //材料名称
    @Column(name = "assets_specifications", columnDefinition = "varchar(100) COMMENT '规格型号'")
    @ExcelProperty(value = "规格型号")
    private String assetsSpecifications;//规格型号
    @Column(name = "assets_unit", columnDefinition = "int(20) COMMENT '材料单位ID'")
    private Long assetsUnit; //材料单位ID
    @Column(name = "assets_unit_name", columnDefinition = "varchar(20) COMMENT '材料单位'")
    @ExcelProperty(value = "材料单位")
    private String assetsUnitName; //材料单位
    //    @Column(name = "assets_code", columnDefinition = "varchar(255) COMMENT '材料条码'")
//    private String assetsCode; //材料条码
//    @Column(name = "assets_rfid", columnDefinition = "varchar(255) COMMENT '材料RFID'")
//    private String assetsRfid; //材料RFID
    @Column(name = "assets_type_name", columnDefinition = "varchar(255) COMMENT '材料类型'")
    @ExcelProperty(value = "材料类型")
    private String assetsTypeName; //材料类型
    @ExcelProperty(value = "材料类型上级")
    @Transient
    private String assetsTypeNameSuperior; //材料类型
    @Column(name = "assets_type", columnDefinition = "varchar(255) COMMENT '材料类型Id'")
    private Long assetsType; //材料类型
    @Column(name = "assets_position", columnDefinition = "varchar(255) COMMENT '常用货位'")
    @ExcelProperty(value = "常用货位")
    private String assetsPosition; //常用货位
}
