package com.yzm.property.consumable.service;

import com.yzm.framework.base.IBaseService;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.consumable.entity.InStorage;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:47
 */
public interface InStorageService extends IBaseService<InStorage,Long> {



   Map showAllInStorageInfo(String keyword, PageBean pageBean);

    ResponseData saveInStorage(InStorage inStorage);

    ResponseData deleteInfo(Long[] ids);

 /*
    InStorage findById(Long id);*/
}
