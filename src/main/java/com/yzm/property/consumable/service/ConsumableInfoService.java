package com.yzm.property.consumable.service;

import com.yzm.framework.base.IBaseService;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.consumable.entity.ConsumableInfo;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:47
 */
public interface ConsumableInfoService extends IBaseService<ConsumableInfo,Long> {



    Map showAllConsumableInfo(String keyword, PageBean pageBean);

    ConsumableInfo findById(Long id);

    ResponseData addConsumableInfo(ConsumableInfo consumableInfo);

    ResponseData updateConsumableInfo(ConsumableInfo consumableInfo);

//    ConsumableInfo findByAssetsCodeOrAssetsRfid(String rfid,String code);
}
