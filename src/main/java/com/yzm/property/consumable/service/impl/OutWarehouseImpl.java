package com.yzm.property.consumable.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.yzm.common.utils.OrderUtils;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.basis.repository.DepartmentRepository;
import com.yzm.property.consumable.entity.*;
import com.yzm.property.consumable.repository.*;
import com.yzm.property.consumable.service.OutWarehouseService;
import com.yzm.property.consumable.utils.ConsumableUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class OutWarehouseImpl extends BaseService<OutWarehouseEntity,Long> implements OutWarehouseService {
    @Autowired
    private ConsumableInfoRepository consumableInfoRepository;
    @Autowired
    private OutWarehouseRepository outWarehouseRepository;
    @Autowired
    private OutWarehouseItemRepository outWarehouseItemRepository;

    @Autowired
    private ConsumableRepertoryRepository consumableRepertoryRepository;
    @Autowired
    private ConsumableStandingBookRepository consumableStandingBookRepository;
    @Autowired
    private ConsumableListRepository consumableListRepository;

    @Autowired
    private DepartmentRepository departmentRepository;

    @Override
    public BaseRepository<OutWarehouseEntity, Long> getRepository() {
        return outWarehouseRepository;
    }

    @Override
    @Transactional
    public OutWarehouseEntity save(OutWarehouseEntity outWarehouseEntity) {
        outWarehouseEntity = outWarehouseRepository.save(outWarehouseEntity);
        // AssetsLogUtil.createAssetsLog(null,null,inStorage.getId(),inStorage.getOtherId(),inStorage.getAssetsName(),consumableInfo.getAssetsSource());
        return outWarehouseEntity;
    }

    @Override
    public Map showAllOutWarehouseInfo(String keyword, PageBean pageBean) {
        HashMap<String, Object> map = new HashMap<>();
        if (keyword == null || "".equals(keyword)) {
            Page<OutWarehouseEntity> result = outWarehouseRepository.findByOrderByCreateTimeDesc(pageBean.getPagable());
            map.put("list", result.getContent());
            map.put("totalCount", result.getTotalElements());
        } else {                                                                                                            //从第几页开始                                 //显示几个
            List<OutWarehouseEntity> result = outWarehouseRepository.findByRecordNumberContainsOrderByCreateTimeDesc(keyword,(pageBean.getPage() - 1 )*pageBean.getLimit(), pageBean.getLimit());
          //List<InStorage>          result = inStorageRepository.findByRecordNumberContainsOrderByCreateTimeDesc   (keyword,(pageBean.getPage() - 1 )      *pageBean.getLimit(), pageBean.getLimit());
            map.put("list", result);
            map.put("totalCount", result.size());
        }
        return map;
    }

    @Override
    public Map<String,String> saveOutWarehouse(OutWarehouseEntity outWarehouseEntity) {
        //单据编号
        Map<String,String> map = new HashMap<>();
        String parameterNo = OrderUtils.getCuKuCode();
        outWarehouseEntity.setRecordNumber(parameterNo);//出库编号
        outWarehouseEntity.setOutDate(outWarehouseEntity.getOutDate());//出库时间
        if(StringUtils.isNotEmpty(outWarehouseEntity.getConsumableDepartment())){
            outWarehouseEntity.setConsumableDepartment(departmentRepository.findById(Long.parseLong(outWarehouseEntity.getConsumableDepartment())).get().getBasisName());
        }
//        outWarehouseEntity.setUserName(ShiroUtils.getUserInfo().getName());//出库人
        outWarehouseEntity.setConsumableNum(outWarehouseEntity.getConsumableNum());//出库数量
        outWarehouseRepository.save(outWarehouseEntity);
        int sum = 0;
        //获取前台传来的表格信息
        List<OutWarehouseEntityItem> outWarehouseEntityItem = JSONArray.parseArray(outWarehouseEntity.getItemJson(), OutWarehouseEntityItem.class);
        boolean code  = true;
        for (OutWarehouseEntityItem storageItem : outWarehouseEntityItem) {
            storageItem.setConsumableNum(1);
            /*更新库存的当前库存、待入库*/
            //通过出库子表查库存
            ConsumableRepertory consumableRepertory= consumableRepertoryRepository.getOne(storageItem.getId());
            storageItem.setOperationNumber(consumableRepertory.getOperationNumber());
            /*ConsumableRepertory consumableRepertory = consumableRepertoryRepository.getOne(storageItem.getId());*/
//            if(consumableRepertory.getNowRepertory()<storageItem.getConsumableNum()){
//                code=false;
//                break;
//            }
            /*if(consumableRepertory.getNowRepertory()<storageItem.getConsumableNum()){
                code =false;
                break;
            }*/
          /*  //通过耗材查库存
            ConsumableRepertory consumableRepertory =  consumableRepertoryRepository.findByConsumableInfoId(consumableInfo.getId());*/
//            consumableRepertory.setNowRepertory(consumableRepertory.getNowRepertory()-storageItem.getConsumableNum());//当前库存
//            if(consumableRepertory.getNowRepertory()==0){
                consumableRepertoryRepository.delete(consumableRepertory);
//            }else{
//                consumableRepertoryRepository.saveAndFlush(consumableRepertory);
//            }
            sum+=1;
            //新建入库子表
            OutWarehouseEntityItem o = new OutWarehouseEntityItem();
            BeanUtils.copyProperties(storageItem, o);

            o.setConsumableNum(storageItem.getConsumableNum());//数量
//            o.setPrice(storageItem.getPrice());//单价
//            o.setAssetsName(storageItem.getAssetsName());//资产名称
            o.setRecordNumber(outWarehouseEntity.getRecordNumber());//记录编号主表
//            o.setAssetsSpecifications(storageItem.getAssetsSpecifications());//规格型号
//            o.setAssetsUnit(consumableStandingBook.getAssetsUnit());//单位
            outWarehouseItemRepository.save(o);
            ConsumableListEntity consumableListEntity = new ConsumableListEntity();
            BeanUtils.copyProperties(o, consumableListEntity);
            consumableListEntity.setId(null);
            consumableListEntity.setOperationName(ConsumableUtil.OPERATION_CHUKU);
            consumableListEntity.setOperationOtherId(o.getRecordNumber());
            consumableListEntity.setOperationId(outWarehouseEntity.getId());
            consumableListEntity.setUserName(outWarehouseEntity.getUserName());
            consumableListEntity.setOutDate(outWarehouseEntity.getCreateTime());
            consumableListEntity.setCreateTime(new Date());

            consumableListRepository.save(consumableListEntity);
        }
        outWarehouseEntity.setConsumableNum(sum);//入库总数

        if(code){
            if (StringUtils.isNotEmpty(outWarehouseRepository.saveAndFlush(outWarehouseEntity))){
                map.put("code","000");
            }else{
                map.put("code","500");
            };
        }else {
            map.put("code","200");
        }
        return map;
    }

    @Override
    @Transactional
    public ResponseData deleteInfo(Long[] ids) {
        for (Long id : ids) {
            OutWarehouseEntity one = outWarehouseRepository.getOne(id);
            List<OutWarehouseEntityItem> byRecordNumber = outWarehouseItemRepository.findByRecordNumber(one.getRecordNumber());
            for (OutWarehouseEntityItem outWarehouseEntityItem : byRecordNumber) {
                ConsumableRepertory  consumableRepertory = new ConsumableRepertory();
                BeanUtils.copyProperties(outWarehouseEntityItem, consumableRepertory);
                consumableRepertory.setNowRepertory(outWarehouseEntityItem.getNowRepertory());//当前库存
                consumableRepertory.setWarehouseInfo(outWarehouseEntityItem.getWarehouseInfo());
                consumableRepertory.setAreaInfo(outWarehouseEntityItem.getAreaInfo());
//                consumableRepertory.setConsumableInfoId(outWarehouseEntityItem.getConsumableNum());
                consumableRepertory.setNowCount(1L);
                consumableRepertory.setAssetsRfid(outWarehouseEntityItem.getAssetsRfid());
                consumableRepertory.setCreateTime(new Date());
                consumableRepertory.setId(null);
                consumableRepertory.setOperationNumber(outWarehouseEntityItem.getOperationNumber());
                //通过耗材查库存
                consumableRepertoryRepository.saveAndFlush(consumableRepertory);
                outWarehouseItemRepository.delete(outWarehouseEntityItem);
            }
            outWarehouseRepository.delete(one);
        }
        return ResponseData.success("删除成功！");
    }
}
