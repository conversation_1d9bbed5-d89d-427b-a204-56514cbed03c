package com.yzm.property.consumable.service;

import com.yzm.framework.base.IBaseService;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.consumable.entity.OutWarehouseEntity;

import java.util.Map;

public interface OutWarehouseService  extends IBaseService<OutWarehouseEntity,Long> {
    Map showAllOutWarehouseInfo(String keyword, PageBean pageBean);

    Map<String,String> saveOutWarehouse(OutWarehouseEntity outWarehouseEntity);

    ResponseData deleteInfo(Long[] ids);
}
