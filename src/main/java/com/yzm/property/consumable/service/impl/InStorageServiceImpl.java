package com.yzm.property.consumable.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.yzm.common.utils.OrderUtils;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.business.repository.BorrowReturnItemRepository;
import com.yzm.property.consumable.entity.*;
import com.yzm.property.consumable.repository.*;
import com.yzm.property.consumable.service.InStorageService;
import com.yzm.property.consumable.utils.ConsumableUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:47
 */
@Service
public class InStorageServiceImpl extends BaseService<InStorage, Long> implements InStorageService {

    @Autowired
    private ConsumableInfoRepository consumableInfoRepository;
    @Autowired
    private InStorageRepository inStorageRepository;
    @Autowired
    private InStorageItemRepository inStorageItemRepository;
    @Autowired
    private  ConsumableStandingBookRepository consumableStandingBookRepository;
    @Autowired
    private BorrowReturnItemRepository borrowReturnItemRepository;

    @Autowired
    private ConsumableRepertoryRepository consumableRepertoryRepository;
    @Autowired
    private ConsumableListRepository consumableListRepository;

    @Override
    public BaseRepository<InStorage, Long> getRepository() {
        return inStorageRepository;
    }

    @Override
    @Transactional
    public InStorage save(InStorage inStorage) {
        inStorage = inStorageRepository.save(inStorage);
       // AssetsLogUtil.createAssetsLog(null,null,inStorage.getId(),inStorage.getOtherId(),inStorage.getAssetsName(),consumableInfo.getAssetsSource());
        return inStorage;
    }

    @Override
    public Map showAllInStorageInfo(String keyword, PageBean pageBean) {
        HashMap<String, Object> map = new HashMap<>();
        if (keyword == null || "".equals(keyword)) {
            Page<InStorage> result = inStorageRepository.findByOrderByCreateTimeDesc(pageBean.getPagable());
            map.put("list", result.getContent());
            map.put("totalCount", result.getTotalElements());
        } else {
            List<InStorage> result = inStorageRepository.findByRecordNumberContainsOrderByCreateTimeDesc(keyword,(pageBean.getPage() - 1 )*pageBean.getLimit(), pageBean.getLimit());
            map.put("list", result);
            map.put("totalCount", result.size());
        }


        return map;
    }

    @Override
    @Transactional
    public ResponseData saveInStorage(InStorage inStorage) {
        //单据编号
        String parameterNo = OrderUtils.getRuKuCode();
        inStorage.setRecordNumber(parameterNo);//入库编号
//        inStorage.setInStoragePeople(ShiroUtils.getUserInfo().getName());//入库人
        inStorageRepository.save(inStorage);
        //获取前台传来的表格信息
        List<InStorageItem> inStorageItem = JSONArray.parseArray(inStorage.getItemJson(), InStorageItem.class);
        int sum = 0;
        for (InStorageItem storageItem : inStorageItem) {
            ConsumableInfo consumableInfo = consumableInfoRepository.getOne(storageItem.getId());
            String[] split = storageItem.getAssetsRfid().split(",");
            for (String rfid : split) {

                ConsumableRepertory  consumableRepertory = new ConsumableRepertory();
                BeanUtils.copyProperties(consumableInfo, consumableRepertory);
                consumableRepertory.setNowRepertory(storageItem.getInStorageCount());//当前库存
                consumableRepertory.setWarehouseInfo(inStorage.getInStorageWarehouse());
                consumableRepertory.setAreaInfo(inStorage.getInStorageArea());
                consumableRepertory.setConsumableInfoId(consumableInfo.getId());
                consumableRepertory.setNowCount(1L);
                consumableRepertory.setAssetsRfid(rfid);
                consumableRepertory.setCreateTime(new Date());
                consumableRepertory.setId(null);
                consumableRepertory.setOperationNumber(inStorage.getRecordNumber());

                //通过耗材查库存
                consumableRepertoryRepository.saveAndFlush(consumableRepertory);
            }
            sum += storageItem.getInStorageCount();
            inStorage.setInStorageTotal(sum);//入库总数
            //新建入库子表
            InStorageItem s = new InStorageItem();
            BeanUtils.copyProperties(consumableInfo, s);
            s.setAssetsRfid(storageItem.getAssetsRfid());
            s.setInStorageCount(storageItem.getInStorageCount());//数量
            s.setInStoragePrice(storageItem.getInStoragePrice());//单价
            s.setRecordNumber(inStorage.getRecordNumber());//记录编号主表
            s.setAssetsId(storageItem.getId());//耗材ID
            inStorageItemRepository.save(s);
            ConsumableListEntity consumableListEntity = new ConsumableListEntity();
            BeanUtils.copyProperties(consumableInfo, consumableListEntity);
            consumableListEntity.setId(null);
            consumableListEntity.setOperationName(ConsumableUtil.OPERATION_RUKU);
            consumableListEntity.setOperationOtherId(inStorage.getRecordNumber());
            consumableListEntity.setOperationId(inStorage.getId());
            consumableListEntity.setAssetsRfid(storageItem.getAssetsRfid());
            consumableListEntity.setCountInfo(storageItem.getInStorageCount());
            consumableListEntity.setNowRepertory(storageItem.getInStorageCount());
            consumableListEntity.setUserName(inStorage.getInStoragePeople());
            consumableListEntity.setOutDate(inStorage.getInStorageDate());
            consumableListEntity.setAreaInfo(inStorage.getInStorageArea());
            consumableListEntity.setWarehouseInfo(inStorage.getInStorageWarehouse());
            consumableListEntity.setCreateTime(new Date());
            consumableListRepository.save(consumableListEntity);
        }


        return ResponseData.success( inStorageRepository.saveAndFlush(inStorage));
    }

    @Override
    @Transactional
    public ResponseData deleteInfo(Long[] ids) {
        for (Long id : ids) {
            InStorage one = inStorageRepository.getOne(id);
            List<InStorageItem> byRecordNumber = inStorageItemRepository.findByRecordNumber(one.getRecordNumber());
            for (InStorageItem inStorageItem : byRecordNumber) {
                String[] rfidSplit = inStorageItem.getAssetsRfid().split(",");
                for (String rfid : rfidSplit) {
                    ConsumableRepertory byAssetsCodeOrAssetsRfid = consumableRepertoryRepository.findByAssetsCodeOrAssetsRfid(rfid,rfid);
                    if(StringUtils.isEmpty(byAssetsCodeOrAssetsRfid)){
                        return ResponseData.error(one.getRecordNumber()+"当前单据无法删除");
                    }
                    if(!byAssetsCodeOrAssetsRfid.getOperationNumber().equals(one.getRecordNumber())){
                        return ResponseData.error(one.getRecordNumber()+"当前单据无法删除-物料已经变动");
                    }
                    consumableRepertoryRepository.delete(byAssetsCodeOrAssetsRfid);
                }
                inStorageItemRepository.delete(inStorageItem);
            }
            inStorageRepository.delete(one);
        }
        return ResponseData.success("删除成功！");
    }

   /* @Override
    public Map showAllConsumableInfo(String keyword, PageBean pageBean) {
        Page<ConsumableInfo> result;
        if (keyword == null || "".equals(keyword)) {
            result = consumableInfoRepository.findAll(pageBean.getPagable());
        } else {
            result = consumableInfoRepository.findAllByAssetsNameContainingOrOtherIdContaining(keyword, keyword, pageBean.getPagable());
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("list", result.getContent());
        map.put("totalCount", result.getTotalElements());
        return map;
    }

    @Override
    public InStorage findById(Long id) {
        return inStorageRepository.findById(id).get();
    }*/
}
