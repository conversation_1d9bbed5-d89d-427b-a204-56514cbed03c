package com.yzm.property.consumable.service.impl;

import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.property.consumable.entity.InStorageItem;
import com.yzm.property.consumable.repository.InStorageItemRepository;
import com.yzm.property.consumable.service.InStorageItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:47
 */
@Service
public class InStorageItemServiceImpl extends BaseService<InStorageItem, Long> implements InStorageItemService {
    @Autowired
    private InStorageItemRepository inStorageItemRepository;

    @Override
    public BaseRepository<InStorageItem, Long> getRepository() {
        return inStorageItemRepository;
    }


    @Override
    public List<InStorageItem> findByRecordNumber(String recordNumber) {
        return inStorageItemRepository.findByRecordNumber(recordNumber);
    }

    @Override
    public List<InStorageItem> findByRecordNumberAndAssetsNumber(String recordNumber, String assetsNumber) {
        return inStorageItemRepository.findByRecordNumberAndAssetsNumber(recordNumber,assetsNumber);
    }
}
