package com.yzm.property.consumable.service.impl;

import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.basis.entity.BasisUnit;
import com.yzm.property.basis.repository.BrandRepository;
import com.yzm.property.basis.repository.TypeRepository;
import com.yzm.property.basis.repository.UnitRepository;
import com.yzm.property.consumable.entity.ConsumableInfo;
import com.yzm.property.consumable.repository.ConsumableInfoRepository;
import com.yzm.property.consumable.repository.ConsumableRepertoryRepository;
import com.yzm.property.consumable.service.ConsumableInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:47
 */
@Service
public class ConsumableInfoServiceImpl extends BaseService<ConsumableInfo, Long> implements ConsumableInfoService {

    @Autowired
    private ConsumableInfoRepository consumableInfoRepository;
    @Autowired
    private ConsumableRepertoryRepository consumableRepertoryRepository;
    @Autowired
    private UnitRepository unitRepository;
    @Autowired
    private BrandRepository brandRepository;
    @Autowired
    private TypeRepository typeRepository;


    @Override
    public BaseRepository<ConsumableInfo, Long> getRepository() {
        return consumableInfoRepository;
    }

    @Override
    @Transactional
    public ConsumableInfo save(ConsumableInfo consumableInfo) {
        consumableInfo = consumableInfoRepository.save(consumableInfo);
        //AssetsLogUtil.createAssetsLog(null,null,consumableInfo.getId(),consumableInfo.getOtherId(),consumableInfo.getAssetsName(),consumableInfo.getAssetsSource());
        return consumableInfo;
    }

    @Override
    public Map showAllConsumableInfo(String keyword, PageBean pageBean) {
        Page<ConsumableInfo> result;
        if (keyword == null || "".equals(keyword)) {
            result = consumableInfoRepository.findAll(pageBean.getPagable());
        } else {
            result = consumableInfoRepository.findAllByAssetsNameContainingOrAssetsNumberContaining(keyword, keyword, pageBean.getPagable());
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("list", result.getContent());
        map.put("totalCount", result.getTotalElements());
        return map;
    }

    @Override
    public ConsumableInfo findById(Long id) {
        return consumableInfoRepository.findById(id).get();
    }

    @Override
    public ResponseData addConsumableInfo(ConsumableInfo consumableInfo) {
        ConsumableInfo consumableInfo1 =   consumableInfoRepository.findByAssetsNumber(consumableInfo.getAssetsNumber());
        if(StringUtils.isNotEmpty(consumableInfo1)){
            return ResponseData.error("材料编号重复请重新输入！");
        }
//        ConsumableInfo consumableInfo2 =   consumableInfoRepository.findByAssetsCodeOrAssetsRfid(consumableInfo.getAssetsRfid(),consumableInfo.getAssetsRfid());
//        if(StringUtils.isNotEmpty(consumableInfo2)){
//            return ResponseData.error("RFID重复请重新输入！");
//        }
        consumableInfo.setAssetsTypeName(typeRepository.findById(consumableInfo.getAssetsType()).get().getBasisName());
        BasisUnit unit = unitRepository.findById(consumableInfo.getAssetsUnit()).get();
        //耗材编号
//        String parameterNo = OrderUtils.getHaoCaiCode();
//        consumableInfo.setAssetsNumber(parameterNo);
//        consumableInfo.setAssetsUnitName(unit.getUnitName());

        //新建耗材库存信息
//        ConsumableRepertory consumableRepertory = new ConsumableRepertory();
//        BeanUtils.copyProperties(consumableInfo, consumableRepertory);
//        consumableRepertory.setId(null);
////        consumableRepertory.setAssetsNumber(consumableInfo.getAssetsNumber());//耗材编号
//        consumableRepertory.setConsumableInfoId(consumableInfo.getId());//耗材ID
////        consumableRepertory.setAssetsName(consumableInfo.getAssetsName());//资产名称
////        consumableRepertory.setAssetsSpecifications(consumableInfo.getAssetsSpecifications());//规格型号
////        consumableRepertory.setAssetsUnit(consumableInfo.getAssetsUnit());//单位ID
        consumableInfo.setAssetsUnitName(unit.getUnitName());//单位名称
//        consumableRepertory.setNowRepertory(0); //当前库存库
       /* consumableRepertory.setPlanExRepertory(0); //待出库
        consumableRepertory.setPlanImRepertory(0);//待入库*/

        return consumableInfoRepository.save(consumableInfo)!= null ? ResponseData.success() : ResponseData.error("新增失败");
    }

    @Override
    public ResponseData updateConsumableInfo(ConsumableInfo consumableInfo) {
        ConsumableInfo consumableInfo1 =   consumableInfoRepository.findByAssetsNumber(consumableInfo.getAssetsNumber());
        if(StringUtils.isNotEmpty(consumableInfo1)){
            return ResponseData.error("材料编号重复请重新输入！");
        }
        BasisUnit unit = unitRepository.findById(consumableInfo.getAssetsUnit()).get();
        consumableInfo.setAssetsUnitName(unit.getUnitName());
        consumableInfo.setAssetsTypeName(typeRepository.findById(consumableInfo.getAssetsType()).get().getBasisName());
//        Long consumableInfoId = consumableInfo.getId();
        //通过ID查耗材库存
//        ConsumableRepertory consumableRepertory = consumableRepertoryRepository.findByConsumableInfoId(consumableInfoId);
//        consumableRepertory.setAssetsName(consumableInfo.getAssetsName());//资产名称
//        consumableRepertory.setAssetsSpecifications(consumableInfo.getAssetsSpecifications());//规格型号
//        consumableRepertory.setAssetsUnit(consumableInfo.getAssetsUnit());//单位ID
//        consumableRepertory.setAssetsUnitName(consumableInfo.getAssetsUnitName());//单位名称

//        consumableRepertoryRepository.saveAndFlush(consumableRepertory);
        return  consumableInfoRepository.saveAndFlush(consumableInfo)!= null ? ResponseData.success() : ResponseData.error("新增失败");
    }
//
//    @Override
//    public ConsumableInfo findByAssetsCodeOrAssetsRfid(String rfid,String code) {
//        return consumableInfoRepository.findByAssetsCodeOrAssetsRfid(rfid,code);
//    }
}
