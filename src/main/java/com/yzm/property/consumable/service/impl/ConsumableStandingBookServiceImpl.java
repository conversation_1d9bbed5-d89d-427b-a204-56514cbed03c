package com.yzm.property.consumable.service.impl;

import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.framework.bean.PageBean;
import com.yzm.property.consumable.entity.ConsumableStandingBook;
import com.yzm.property.consumable.repository.ConsumableStandingBookRepository;
import com.yzm.property.consumable.service.ConsumableStandingBookService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class ConsumableStandingBookServiceImpl extends BaseService<ConsumableStandingBook,Long> implements ConsumableStandingBookService {
    @Autowired
    private ConsumableStandingBookRepository consumableStandingBookRepository;
    @Override
    public BaseRepository<ConsumableStandingBook,Long> getRepository(){

        return consumableStandingBookRepository;
    }

    @Override
    public Map showAllConsumableStandingBook(String keyword, PageBean pageBean) {
        Page<ConsumableStandingBook> result;
        if (keyword == null || "".equals(keyword)) {
            result = consumableStandingBookRepository.findAll(pageBean.getPagable());
        } else {
            result = consumableStandingBookRepository.findAllByAssetsNameContaining(keyword,pageBean.getPagable());
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("list", result.getContent());
        map.put("totalCount", result.getTotalElements());
        return map;
    }
}
