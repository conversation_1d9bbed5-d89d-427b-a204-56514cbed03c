package com.yzm.property.consumable.service.impl;

import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.property.consumable.entity.OutWarehouseEntityItem;
import com.yzm.property.consumable.repository.OutWarehouseItemRepository;
import com.yzm.property.consumable.service.OutWarehouseItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class OutWarehouseItemServiceImpl extends BaseService<OutWarehouseEntityItem, Long> implements OutWarehouseItemService {
    @Autowired
    private OutWarehouseItemRepository outWarehouseItemRepository;

    @Override
    public BaseRepository<OutWarehouseEntityItem, Long> getRepository() {
        return outWarehouseItemRepository;
    }


    @Override
    //通过记录编号查询字表信息
    public List<OutWarehouseEntityItem> findByRecordNumber(String recordNumber) {
        return outWarehouseItemRepository.findByRecordNumber(recordNumber);
    }

    @Override
    public List<OutWarehouseEntityItem> findByRecordNumberAndAssetsNumber(String recordNumber, String assetsNumber) {
        return outWarehouseItemRepository.findByRecordNumberAndAssetsNumber(recordNumber,assetsNumber);
    }

}
