package com.yzm.property.consumable.service;

import com.yzm.framework.base.IBaseService;
import com.yzm.framework.bean.PageBean;
import com.yzm.property.consumable.entity.ConsumableRepertory;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:47
 */
public interface ConsumableRepertoryService extends IBaseService<ConsumableRepertory,Long> {

    
    Map showAllConsumableRepository(String keyword, PageBean pageBean);
    Map showAllConsumableSummaryInfo(String keyword, PageBean pageBean);

    boolean findByConsumableInfoId(Long id);

    ConsumableRepertory findByAssetsCodeOrAssetsRfid(String rfid, String code);

    List<ConsumableRepertory> findByAssetsNumber(String assetsNumber);

    List<ConsumableRepertory> findByAssetsNumberAndWarehouseInfoAndAreaInfo(String assetsNumber, String warehouseInfo, String areaInfo);

    ConsumableRepertory findByAssetsRfidAndAssetsNumber(String rfid, String assetsNumber);
}
