package com.yzm.property.consumable.service.impl;

import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.framework.bean.PageBean;
import com.yzm.property.basis.utils.BeanMapUtils;
import com.yzm.property.consumable.entity.ConsumableInfo;
import com.yzm.property.consumable.entity.ConsumableRepertory;
import com.yzm.property.consumable.repository.ConsumableInfoRepository;
import com.yzm.property.consumable.repository.ConsumableRepertoryRepository;
import com.yzm.property.consumable.service.ConsumableRepertoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:47
 */
@Service
public class ConsumableRepertoryServiceImpl extends BaseService<ConsumableRepertory, Long> implements ConsumableRepertoryService {
    @Autowired
    private ConsumableRepertoryRepository consumableRepertoryRepository;
    @Autowired
    private ConsumableInfoRepository consumableInfoRepository;

    @Override
    public BaseRepository<ConsumableRepertory, Long> getRepository() {
        return consumableRepertoryRepository;
    }

    @Override
    public Map showAllConsumableRepository(String keyword, PageBean pageBean) {
        Page<ConsumableRepertory> result;
//        if (keyword == null || "".equals(keyword)) {
        result = consumableRepertoryRepository.findAll(pageBean.getPagable());

        HashMap<String, Object> map = new HashMap<>();
        map.put("list", result.getContent());
        map.put("totalCount", result.getTotalElements());
        return map;
    }

    @Override
    public Map showAllConsumableSummaryInfo(String keyword, PageBean pageBean) {
        HashMap<String, Object> map = new HashMap<>();
        List<Map<String, Object>> result ;
        if (keyword == null || "".equals(keyword)) {
            result = consumableRepertoryRepository.findByConsumableRepertory((pageBean.getPage() - 1) * pageBean.getLimit(), pageBean.getLimit());
        } else {
           result = consumableRepertoryRepository.findByAssetsNumberOrderByCreateTimeDesc(keyword, (pageBean.getPage() - 1) * pageBean.getLimit(), pageBean.getLimit());
        }
        List<ConsumableRepertory> list = new ArrayList<>();
        for (Map<String,Object> map1 : result) {
            try {
                ConsumableRepertory consumableRepertory = BeanMapUtils.mapToBean(map1, ConsumableRepertory.class);
                list.add(consumableRepertory);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        map.put("list", list);
        map.put("totalCount", result.size());
        return map;
    }

    @Override
    public boolean findByConsumableInfoId(Long id) {
        ConsumableInfo consumableInfo = consumableInfoRepository.getOne(id);
        ConsumableRepertory consumableRepertory = consumableRepertoryRepository.findByConsumableInfoId(consumableInfo.getId());
        if(StringUtils.isEmpty(consumableRepertory)){
            consumableInfoRepository.delete(consumableInfo);
            return true;
        }
        if (consumableRepertory.getNowRepertory() == 0) {
            consumableInfoRepository.delete(consumableInfo);
            consumableRepertoryRepository.delete(consumableRepertory);
            System.out.println("库存为0 可删除");
        } else {
            throw new RuntimeException("库存不为0,不可进行删除");
        }

        return true;
    }

    @Override
    public ConsumableRepertory findByAssetsCodeOrAssetsRfid(String rfid, String code) {
        return consumableRepertoryRepository.findByAssetsCodeOrAssetsRfid(rfid,code);
    }

    @Override
    public List<ConsumableRepertory> findByAssetsNumber(String assetsNumber) {
        return consumableRepertoryRepository.findByAssetsNumber(assetsNumber);
    }

    @Override
    public List<ConsumableRepertory> findByAssetsNumberAndWarehouseInfoAndAreaInfo(String assetsNumber, String warehouseInfo, String areaInfo) {
        return consumableRepertoryRepository.findByAssetsNumberAndWarehouseInfoAndAreaInfo(assetsNumber,warehouseInfo,areaInfo);
    }

    @Override
    public ConsumableRepertory findByAssetsRfidAndAssetsNumber(String rfid, String assetsNumber) {
        return consumableRepertoryRepository.findByAssetsRfidAndAssetsNumber(rfid,assetsNumber);
    }
}
