package com.yzm.property.consumable.service;

import com.yzm.framework.base.IBaseService;
import com.yzm.property.consumable.entity.OutWarehouseEntityItem;

import java.util.List;

public interface OutWarehouseItemService extends IBaseService<OutWarehouseEntityItem,Long> {
    List<OutWarehouseEntityItem> findByRecordNumber(String recordNumber);

    List<OutWarehouseEntityItem> findByRecordNumberAndAssetsNumber(String recordNumber, String assetsNumber);
}
