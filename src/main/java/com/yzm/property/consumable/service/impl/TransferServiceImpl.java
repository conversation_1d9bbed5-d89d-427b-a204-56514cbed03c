package com.yzm.property.consumable.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.yzm.common.utils.OrderUtils;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.framework.bean.PageBean;
import com.yzm.property.basis.repository.PersonnelRepository;
import com.yzm.property.consumable.entity.*;
import com.yzm.property.consumable.repository.*;
import com.yzm.property.consumable.service.TransferService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:47
 */
@Service
public class TransferServiceImpl extends BaseService<Transfer, Long> implements TransferService {

    @Autowired
    private TransferRepository transferRepository;

    @Autowired
    private TransferItemRepository transferItemRepository;

    @Autowired
    private ConsumableInfoRepository consumableInfoRepository;
    @Autowired
    private ConsumableRepertoryRepository consumableRepertoryRepository;
    @Autowired
    private PersonnelRepository personnelRepository;

    @Override
    public BaseRepository<Transfer, Long> getRepository() {
        return transferRepository;
    }


    @Override
    public Map showAllTransferInfo(String keyword, PageBean pageBean) {
        HashMap<String, Object> map = new HashMap<>();
        if (keyword == null || "".equals(keyword)) {
            Page<Transfer> result = transferRepository.findByOrderByCreateTimeDesc(pageBean.getPagable());
            map.put("list", result.getContent());
            map.put("totalCount", result.getTotalElements());
        } else {
            List<Transfer> result = transferRepository.findByRecordNumberContainsOrderByCreateTimeDesc(keyword,(pageBean.getPage() - 1 )*pageBean.getLimit(), pageBean.getLimit());
            map.put("list", result);
            map.put("totalCount", result.size());
        }
        return map;
    }
    @Override
    public boolean saveTransfer(Transfer transfer) {
        //单据编号
        String parameterNo = OrderUtils.getDiaoBoCode();
        transfer.setRecordNumber(parameterNo);//入库编号
        if(StringUtils.isNotEmpty(transfer.getInStoragePeople())){
            transfer.setInStoragePeople(personnelRepository.findById(Long.valueOf(transfer.getInStoragePeople())).get().getBasisName());//入库人
        }
        transferRepository.save(transfer);
        //获取前台传来的表格信息
        List<TransferItem> transferItem = JSONArray.parseArray(transfer.getItemJson(), TransferItem.class);
        int sum = 0;
        for (TransferItem storageItem : transferItem) {
            String[] rfidSplit = storageItem.getAssetsRfid().split(",");
            for (String rfid : rfidSplit) {
                ConsumableRepertory consumableRepertory = consumableRepertoryRepository.findByAssetsCodeOrAssetsRfid(rfid, rfid);
                consumableRepertory.setWarehouseInfo(transfer.getInStorageWarehouse());
                consumableRepertory.setAreaInfo(transfer.getInStorageArea());
                consumableRepertory.setOperationNumber(transfer.getRecordNumber());
                //通过耗材查库存
                consumableRepertoryRepository.saveAndFlush(consumableRepertory);
            }

            sum += storageItem.getInStorageCount();
            transfer.setInStorageTotal(sum);//入库总数
            //新建入库子表
            TransferItem sInfo = new TransferItem();
            BeanUtils.copyProperties(storageItem, sInfo);
            sInfo.setId(null);
            sInfo.setInStorageAreaOriginal(storageItem.getAreaInfo());
            sInfo.setInStorageWarehouseOriginal(storageItem.getWarehouseInfo());
            sInfo.setAreaInfo(transfer.getInStorageArea());
            sInfo.setWarehouseInfo(transfer.getInStorageWarehouse());
            sInfo.setInStorageCount(storageItem.getInStorageCount());//数量
            sInfo.setInStoragePrice(storageItem.getInStoragePrice());//单价
            sInfo.setRecordNumber(transfer.getRecordNumber());//记录编号主表
            sInfo.setAssetsId(storageItem.getId());//耗材ID
            transferItemRepository.save(sInfo);
//            ConsumableListEntity consumableListEntity = new ConsumableListEntity();
//            BeanUtils.copyProperties(consumableRepertory, consumableListEntity);
//            consumableListEntity.setId(null);
//            consumableListEntity.setOperationName(ConsumableUtil.OPERATION_RUKU);
//            consumableListEntity.setOperationOtherId(inStorage.getRecordNumber());
//            consumableListEntity.setOperationId(inStorage.getId());
//            consumableListEntity.setUserName(inStorage.getInStoragePeople());
//            consumableListEntity.setOutDate(inStorage.getInStorageDate());
//            consumableListRepository.save(consumableListEntity);

        }
        transferRepository.saveAndFlush(transfer);
        return true;
    }
}
