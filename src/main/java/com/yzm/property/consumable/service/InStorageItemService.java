package com.yzm.property.consumable.service;

import com.yzm.framework.base.IBaseService;
import com.yzm.property.consumable.entity.InStorageItem;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:47
 */
public interface InStorageItemService extends IBaseService<InStorageItem,Long> {


    List<InStorageItem> findByRecordNumber(String recordNumber);

    List<InStorageItem> findByRecordNumberAndAssetsNumber(String recordNumber, String assetsNumber);
}
