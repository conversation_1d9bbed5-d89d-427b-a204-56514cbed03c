package com.yzm.property.consumable.service;

import com.yzm.framework.base.IBaseService;
import com.yzm.framework.bean.PageBean;
import com.yzm.property.consumable.entity.Transfer;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:47
 */
public interface TransferService extends IBaseService<Transfer, Long> {

    Map showAllTransferInfo(String keyword, PageBean pageBean);

    boolean saveTransfer(Transfer transfer);
}
