package com.yzm.property.consumable.service.impl;

import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.property.consumable.entity.ConsumableListEntity;
import com.yzm.property.consumable.repository.ConsumableListRepository;
import com.yzm.property.consumable.service.ConsumableListService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ConsumableListImpl extends BaseService<ConsumableListEntity,Long> implements ConsumableListService {
    @Autowired
    private ConsumableListRepository consumableListRepository;

    public BaseRepository<ConsumableListEntity,Long> getRepository(){
        return consumableListRepository;
    }

    /*public Map showAllOutWarehouseInfo(String keyword, String operationName, Date startTime,Date endTime, PageBean pageBean){
        Map<String,Object> map=new HashMap<>();
        if("全部".equals(operationName))
        return map;
    }*/
    /*public void select(String keyword,String operationName){
        if ("全部".equals(operationName)){
            return consumableListRepository.findByKeyword();
        }else {
            return consumableListRepository.findByKeywordAndOperation(operationName);
        }
    }*/
}
