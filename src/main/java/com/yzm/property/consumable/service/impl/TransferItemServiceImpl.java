package com.yzm.property.consumable.service.impl;

import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.property.consumable.entity.TransferItem;
import com.yzm.property.consumable.repository.TransferItemRepository;
import com.yzm.property.consumable.service.TransferItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:47
 */
@Service
public class TransferItemServiceImpl extends BaseService<TransferItem, Long> implements TransferItemService {
    @Autowired
    private TransferItemRepository transferItemRepository;

    @Override
    public BaseRepository<TransferItem, Long> getRepository() {
        return transferItemRepository;
    }


    @Override
    public List<TransferItem> findByRecordNumber(String recordNumber) {
        return transferItemRepository.findByRecordNumber(recordNumber);
    }
}
