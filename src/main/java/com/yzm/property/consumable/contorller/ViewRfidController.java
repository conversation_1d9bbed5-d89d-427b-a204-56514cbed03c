package com.yzm.property.consumable.contorller;

import com.alibaba.fastjson.JSONArray;
import com.yzm.property.business.entity.BorrowReturnItem;
import com.yzm.property.business.service.BorrowReturnItemService;
import com.yzm.property.business.service.BorrowReturnService;
import com.yzm.property.consumable.entity.*;
import com.yzm.property.consumable.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName ViewRfidController * @Description TODO
 * <AUTHOR>
 * @Date 14:42 2023/5/25
 * @Version 1.0
 **/
@Controller(value = "consumableViewRfidController")
@RequestMapping("/viewRfid")
public class ViewRfidController {
    private String urlPrefix = "consumable/viewRfid";
    @Autowired
    private InStorageService inStorageService;
    @Autowired
    private InStorageItemService inStorageItemService;
    @Autowired
    private OutWarehouseService outWarehouseService;
    @Autowired
    private OutWarehouseItemService outWarehouseItemService;
    @Autowired
    private BorrowReturnItemService borrowReturnItemService;
    @Autowired
    private BorrowReturnService borrowReturnService;
    @Autowired
    private ConsumableRepertoryService consumableRepertoryService;
    @Autowired
    private TransferService transferService;
    @Autowired
    private TransferItemService transferItemService;

    private final SimpleDateFormat TIME_FMT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    /**
     * 扫码rfid页面
     */
    @GetMapping("/showRfid")
    public String showRfid(ModelMap mmap, Long id, String type) {
        List list = new ArrayList();
        switch (type) {
            case "inStorage":
                InStorage byId = inStorageService.getById(id);
                List<InStorageItem> byRecordNumber = inStorageItemService.findByRecordNumber(byId.getRecordNumber());
                for (InStorageItem inStorageItem : byRecordNumber) {
                    String[] split = inStorageItem.getAssetsRfid().split(",");
                    for (String rfid : split) {
                        Map<String, Object> map = new HashMap<>();
                        map.put("rfid", rfid);
                        map.put("qcbh", inStorageItem.getAssetsNumber());
                        map.put("qcmc", inStorageItem.getAssetsName());
                        list.add(map);
                    }
                }
                break;
            case "inStorageView":
                InStorageItem byId3 = inStorageItemService.getById(id);
                List<InStorageItem> byRecordNumberlist = inStorageItemService.findByRecordNumberAndAssetsNumber(byId3.getRecordNumber(), byId3.getAssetsNumber());
                for (InStorageItem inStorageItem : byRecordNumberlist) {
                    String[] split = inStorageItem.getAssetsRfid().split(",");
                    for (String rfid : split) {
                        Map<String, Object> map = new HashMap<>();
                        map.put("rfid", rfid);
                        map.put("qcbh", inStorageItem.getAssetsNumber());
                        map.put("qcmc", inStorageItem.getAssetsName());
                        list.add(map);
                    }
                }
                break;
            case "outWarehouse":
                OutWarehouseEntity byId1 = outWarehouseService.getById(id);
                List<OutWarehouseEntityItem> byRecordNumber1 = outWarehouseItemService.findByRecordNumber(byId1.getRecordNumber());
                for (OutWarehouseEntityItem inStorageItem : byRecordNumber1) {
                    String[] split = inStorageItem.getAssetsRfid().split(",");
                    for (String rfid : split) {
                        Map<String, Object> map = new HashMap<>();
                        map.put("rfid", rfid);
                        map.put("qcbh", inStorageItem.getAssetsNumber());
                        map.put("qcmc", inStorageItem.getAssetsName());
                        list.add(map);
                    }
                }
                break;
            case "outWarehouseView":
                OutWarehouseEntityItem byId4 = outWarehouseItemService.getById(id);
                List<OutWarehouseEntityItem> outWarehouseEntityItems = outWarehouseItemService.findByRecordNumberAndAssetsNumber(byId4.getRecordNumber(), byId4.getAssetsNumber());
                for (OutWarehouseEntityItem inStorageItem : outWarehouseEntityItems) {
                    String[] split = inStorageItem.getAssetsRfid().split(",");
                    for (String rfid : split) {
                        Map<String, Object> map = new HashMap<>();
                        map.put("rfid", rfid);
                        map.put("qcbh", inStorageItem.getAssetsNumber());
                        map.put("qcmc", inStorageItem.getAssetsName());
                        list.add(map);
                    }
                }
                break;
            case "borrowReturn":
                List<BorrowReturnItem> byRecordNumber2 = borrowReturnItemService.findByBorrowReturnId(String.valueOf(id));
                for (BorrowReturnItem inStorageItem : byRecordNumber2) {
                    String[] split = inStorageItem.getAssetsRfid().split(",");
                    for (String rfid : split) {
                        Map<String, Object> map = new HashMap<>();
                        map.put("rfid", rfid);
                        map.put("qcbh", inStorageItem.getAssetsNumber());
                        map.put("qcmc", inStorageItem.getAssetsName());
                        list.add(map);
                    }
                }
                break;

            case "borrowReturnView":
                BorrowReturnItem byRecordNumberView2 = borrowReturnItemService.getById(id);
                List<BorrowReturnItem> borrowReturnItems = borrowReturnItemService.findByBorrowReturnIdAndAssetsNumber(byRecordNumberView2.getBorrowReturnId(), byRecordNumberView2.getAssetsNumber());
                for (BorrowReturnItem inStorageItem : borrowReturnItems) {
                    String[] split = inStorageItem.getAssetsRfid().split(",");
                    for (String rfid : split) {
                        Map<String, Object> map = new HashMap<>();
                        map.put("rfid", rfid);
                        map.put("qcbh", inStorageItem.getAssetsNumber());
                        map.put("qcmc", inStorageItem.getAssetsName());
                        map.put("sfgh", inStorageItem.getBiReturnStandby());
                        list.add(map);
                    }
                }
                mmap.put("rfidList", JSONArray.toJSONString(list).replaceAll("\"", "'"));
                return urlPrefix + "/rfidItemView";
            case "listSummary":
                ConsumableRepertory byId2 = consumableRepertoryService.getById(id);
                List<ConsumableRepertory> consumableRepertories = consumableRepertoryService.findByAssetsNumberAndWarehouseInfoAndAreaInfo(byId2.getAssetsNumber(), byId2.getWarehouseInfo(), byId2.getAreaInfo());
                for (ConsumableRepertory inStorageItem : consumableRepertories) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("rfid", inStorageItem.getAssetsRfid());
                    map.put("qcbh", inStorageItem.getAssetsNumber());
                    map.put("qcmc", inStorageItem.getAssetsName());
                    map.put("time", TIME_FMT.format(inStorageItem.getCreateTime()));
                    list.add(map);
                }
                mmap.put("rfidList", JSONArray.toJSONString(list).replaceAll("\"", "'"));
                return urlPrefix + "/rfidItem";
            case "transfer":
                Transfer byId5 = transferService.getById(id);
                List<TransferItem> byRecordNumber3 = transferItemService.findByRecordNumber(byId5.getRecordNumber());
                for (TransferItem transferItem : byRecordNumber3) {
                    String[] split = transferItem.getAssetsRfid().split(",");
                    for (String rfid : split) {
                        Map<String, Object> map = new HashMap<>();
                        map.put("rfid", rfid);
                        map.put("qcbh", transferItem.getAssetsNumber());
                        map.put("qcmc", transferItem.getAssetsName());
                        list.add(map);
                    }
                }
                break;
            case "transferItem":
                TransferItem byId6 = transferItemService.getById(id);
                String[] split = byId6.getAssetsRfid().split(",");
                for (String rfid : split) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("rfid", rfid);
                    map.put("qcbh", byId6.getAssetsNumber());
                    map.put("qcmc", byId6.getAssetsName());
                    list.add(map);
                }
                break;
//                mmap.put("rfidList", JSONArray.toJSONString(list).replaceAll("\"", "'"));
//                return urlPrefix + "/rfidItem";
        }
        mmap.put("rfidList", JSONArray.toJSONString(list).replaceAll("\"", "'"));
        return urlPrefix + "/rfid";
    }

}
