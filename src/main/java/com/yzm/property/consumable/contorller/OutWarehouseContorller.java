package com.yzm.property.consumable.contorller;

import com.yzm.common.annotation.BussinessLog;
import com.yzm.common.enums.BusinessType;
import com.yzm.common.utils.PDFTemplateUtil;
import com.yzm.common.utils.ShiroUtils;
import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.basis.service.PersonnelService;
import com.yzm.property.consumable.entity.OutWarehouseEntity;
import com.yzm.property.consumable.entity.OutWarehouseEntityItem;
import com.yzm.property.consumable.service.OutWarehouseItemService;
import com.yzm.property.consumable.service.OutWarehouseService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Controller(value = "consumableOutWarehouseContorller")
@RequestMapping( "consumable/outWarehouse")
public class OutWarehouseContorller extends BaseController {
    private String urlPrefix = "consumable/outWarehouse";
    @Autowired
    private OutWarehouseService outWarehouseService;
    @Autowired
    private OutWarehouseItemService outWarehouseItemService;
    @Autowired
    private PersonnelService personnelService;

    @RequiresPermissions("consumable:outWarehouse:view")
    @GetMapping
    public String client(ModelMap map) {
        return urlPrefix + "/list";
    }

    /**
     * 材料出库
     */
    @ResponseBody
    @RequestMapping("/list")
    public ResponseData getOutWarehouse(@RequestParam(value = "keyword", required = false)
                                                    String keyword,
                                        @RequestParam(value = "type", required = false)
                                                    String type,
                                        PageBean pageBean){
        Map<String,Object> datas=outWarehouseService.showAllOutWarehouseInfo(keyword,pageBean);
        return success(datas);
    }

    /*public ResponseData showAllOutWarehouseInfo(
            @RequestParam(value = "keyword", required = false)
                    String keyword,
            @RequestParam(value = "type", required = false)
                    String type,
            PageBean pageBean
    ) {
        return success(outWarehouseService.showAllOutWarehouseInfo(keyword, pageBean));

    }*/

    @GetMapping("add")
    public String add( ModelMap mmap) {
        mmap.put("personnelList",personnelService.findAll());
        return urlPrefix + "/addOutWarehouse";
    }

    @RequestMapping("/add")
    @RequiresPermissions("consumable:outWarehouse:add")
    @ResponseBody
    @BussinessLog(businessType=BusinessType.INSERT,title="新增材料待出库")
    public ResponseData save(OutWarehouseEntity outWarehouseEntity) {
        Map<String,String> map = outWarehouseService.saveOutWarehouse(outWarehouseEntity);
         if (map.get("code").equals("500")){
            return error("材料出库失败");
        }else  if (map.get("code").equals("200")){
            return error("库存数量不足");
        }else {
             return success();
         }
    }

    /**
     * 入库(选择材料信息)页面
     */
    @GetMapping("/addOutWarehouseItem")
    public String addInStorageItem() {
        return urlPrefix + "/addOutWarehouseItem";
    }

    /**
     * 根据单据号查询详细
     * @return
     */
    @GetMapping("/view/{id}")
    public String viewPage(@PathVariable("id") Long id, ModelMap mmap) {
        OutWarehouseEntity outWarehouseEntity = outWarehouseService.getById(id);
        List<OutWarehouseEntityItem> list = outWarehouseItemService.findByRecordNumber(outWarehouseEntity.getRecordNumber());
        mmap.put("outWarehouseEntity", outWarehouseEntity);
        mmap.put("outWarehouseEntityList", list);
        return urlPrefix + "/show";

    }
    /**
     * 删除报废信息
     *
     * @param ids
     * @return
     */
    @PostMapping("/del")
    @ResponseBody
    @BussinessLog(businessType = BusinessType.DELETE, title = "删除出库信息")
    public ResponseData deleteScrap(Long[] ids) {

        return outWarehouseService.deleteInfo(ids);
    }
    /**
     * 根据单据号查询详细
     * @param recordNumber
     * @return
     */
    @GetMapping("/findDetailByRecordNumber")
    @ResponseBody
    public ResponseData findDetailByRecordNumber(@RequestParam String recordNumber) {
        List<OutWarehouseEntityItem> list = outWarehouseItemService.findByRecordNumber(recordNumber);
        // 1. 根据 number
        Map<String, List<OutWarehouseEntityItem>> map = list.stream().collect(Collectors.groupingBy(user -> user.getAssetsNumber()));

// 2. 对于每个分组内的用户，将其 rfid 字段进行拼接
        List<OutWarehouseEntityItem> result = new ArrayList<>();
        for (Map.Entry<String, List<OutWarehouseEntityItem>> entry : map.entrySet()) {
            List<OutWarehouseEntityItem> userList = entry.getValue();
            OutWarehouseEntityItem user1 = userList.get(0);
            StringBuilder builder = new StringBuilder();
            int count = 0;
            for (OutWarehouseEntityItem user : userList) {
                builder.append(user.getAssetsRfid()).append(",");
                count++;
            }
            user1.setNowRepertory(count);
            user1.setConsumableNum(count);
            user1.setAssetsRfid(builder.toString());
            result.add(user1);
        }
        return success().put("OutWarehouseItemList", result);
    }

    @GetMapping("exportInformation")
//    @BussinessLog(title = "出库单", businessType = BusinessType.EXPORT)
    public void downloadFile(HttpServletResponse response, Long id) throws Exception {
        ByteArrayOutputStream baos = null;
        OutputStream out = null;
        try {
            OutWarehouseEntity inStorage = outWarehouseService.getById(id);
            Map<String, Object> data = new HashMap<>();
            data.put("title", "预览PDF");
            //申领编号
            data.put("recordNumber",inStorage.getRecordNumber());
            data.put("inStoragePeople",inStorage.getUserName());
//            data.put("inStorageWarehouse",inStorage.getInStorageWarehouse());
//            data.put("inStorageArea",inStorage.getInStorageArea());
            data.put("inStorageReson", inStorage.getOutReson());
            data.put("consumableCompany",inStorage.getConsumableCompany());
            data.put("consumableDepartment",inStorage.getConsumableDepartment());

            data.put("custom1",inStorage.getCustom1());
            data.put("outTime",inStorage.getCreateTime());
            data.put("user", ShiroUtils.getUserInfo().getName());
            data.put("now", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy.MM.dd HH:mm:ss")));
            List<OutWarehouseEntityItem> overList = outWarehouseItemService.findByRecordNumber(inStorage.getRecordNumber());
// 1. 根据 number
            Map<String, List<OutWarehouseEntityItem>> map = overList.stream().collect(Collectors.groupingBy(user -> user.getAssetsNumber()));

// 2. 对于每个分组内的用户，将其 rfid 字段进行拼接
            List<OutWarehouseEntityItem> result = new ArrayList<>();
            for (Map.Entry<String, List<OutWarehouseEntityItem>> entry : map.entrySet()) {
                List<OutWarehouseEntityItem> userList = entry.getValue();
                OutWarehouseEntityItem user1 = userList.get(0);
                StringBuilder builder = new StringBuilder();
                int count = 0;
                for (OutWarehouseEntityItem user : userList) {
                    builder.append(user.getAssetsRfid()).append(",");
                    count++;
                }
                user1.setNowRepertory(count);
                user1.setConsumableNum(count);
                user1.setAssetsRfid(builder.toString());
                result.add(user1);
            }
            data.put("detailList", result);
            data.put("count", inStorage.getConsumableNum());
            baos = PDFTemplateUtil.createPDF(data, "出库单.ftl");


            // 设置响应消息头，告诉浏览器当前响应是一个下载文件
            response.setContentType("application/pdf;charset=UTF-8");
            // 告诉浏览器，当前响应数据要求用户干预保存到文件中，以及文件名是什么 如果文件名有中文，必须URL编码
            String fileName = URLEncoder.encode(inStorage.getRecordNumber() + ".pdf", "UTF-8");
            response.setHeader("Content-Disposition", "inline;filename=" + fileName);
            out = response.getOutputStream();
            baos.writeTo(out);
            baos.close();
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("导出失败：" + e.getMessage());
        } finally {
            if (baos != null) {
                baos.close();
            }
            if (out != null) {
                out.close();
            }
        }
    }

}
