package com.yzm.property.consumable.contorller;

import cn.hutool.core.io.FileUtil;
import com.alibaba.excel.EasyExcel;
import com.yzm.common.annotation.BussinessLog;
import com.yzm.common.enums.BusinessType;
import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.framework.freemark.Global;
import com.yzm.property.consumable.criteria.ConsumableListCriteria;
import com.yzm.property.consumable.entity.ConsumableListEntity;
import com.yzm.property.consumable.pojo.ConsumableListExcel;
import com.yzm.property.consumable.repository.ConsumableInfoRepository;
import com.yzm.property.consumable.repository.ConsumableListRepository;
import com.yzm.property.consumable.service.ConsumableListService;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;

@Controller
@RequestMapping("consumable/consumableList")
@Slf4j
public class ConsumableListController extends BaseController {
    private String urlPrefix="consumable/consumableList";

    @Autowired
    private ConsumableListService consumableListService;
    @Autowired
    private ConsumableListRepository consumableListRepository;
    @Autowired
    private ConsumableInfoRepository consumableInfoRepository;

    /*private static List<AssetsAttribution> sourceAttrs = new ArrayList<>();
    static {
        sourceAttrs.add(new AssetsAttribution(""));
        sourceAttrs.add(new AssetsAttribution("入库"));
        sourceAttrs.add(new AssetsAttribution("出库"));
    }*/

    @RequiresPermissions("consumable:consumableList:view")
    @GetMapping
    public String client(ModelMap map){
        map.put("consumableInfoList",consumableInfoRepository.findAll());
        return urlPrefix+"/consumableList";
    }

    /**
     * 材料流水
     */
    @ResponseBody
    @RequestMapping("list")
    public ResponseData getConsumableList(PageBean pageBean,ConsumableListCriteria criteria){
        if(criteria.getStartTime()!=null){
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date endTime=criteria.getEndTime();
            Calendar c=Calendar.getInstance();
            c.setTime(endTime);
            c.add(Calendar.DATE,1);
            criteria.setEndTime(c.getTime());
            System.out.println(criteria);
        }
        Map<String,Object> datas=consumableListService.findAllByPage(criteria,pageBean.getPagable(Sort.by(Direction.DESC,"id")));
        return success(datas);
    }

    @BussinessLog(title = "申领单", businessType = BusinessType.EXPORT)
    @PostMapping("/toExport")
    @ResponseBody
    public ResponseData export(ConsumableListCriteria consumableListCriteria) throws Exception {
        String fileName = "申领单";
        fileName = encodingExcelFilename(fileName);
        String folder = Global.getTempPath() + File.separator + "pio" + File.separator;
        FileUtil.touch(folder + fileName);
        List<ConsumableListEntity> listData = consumableListService.findAll(consumableListCriteria);
        List<ConsumableListExcel> listExcels=new ArrayList<>();
        ConsumableListExcel consumableListExcel=new ConsumableListExcel();
        for (ConsumableListEntity  consumableListEntity : listData){
            consumableListExcel.setOperationOtherId(consumableListEntity.getOperationOtherId());
            consumableListExcel.setOperationName(consumableListEntity.getOperationName());
//            consumableListExcel.setConsumableId(consumableListEntity.getConsumableId());
//            consumableListExcel.setConsumableName(consumableListEntity.getConsumableName());
            consumableListExcel.setUserName(consumableListEntity.getUserName());
            consumableListExcel.setNowRepertory(consumableListEntity.getNowRepertory());
            consumableListExcel.setPrice(consumableListExcel.getPrice());
            consumableListExcel.setCreateTime(consumableListEntity.getCreateTime());
            listExcels.add(consumableListExcel);
        }
//        List<ConsumableListItemEntity> outPlanItems = new ArrayList<>();
//        listData.forEach(ConsumableListEntity -> {
//			/*outPlan.setOrderTypeName(sysDictDataRepository.findByDictTypeAndDictValue("plan_out_type",outPlan.getOrderType().toString()).getDictLabel());
//			outPlan.setStatusName(sysDictDataRepository.findByDictTypeAndDictValue("planIn_storage_status",outPlan.getStatus().toString()).getDictLabel());
//*/
//            outPlanItems.addAll(consumableListRepository.getByOutPlanId(ConsumableListEntity.getId()));
//        });

        EasyExcel.write(folder + fileName, ConsumableListExcel.class).sheet("模板").doWrite(listExcels);
        return success(fileName);
    }

    String encodingExcelFilename(String filename) {
        filename = UUID.randomUUID().toString() + "_" + filename + ".xlsx";
        return filename;
    }
}
