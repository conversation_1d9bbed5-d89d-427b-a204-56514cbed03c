package com.yzm.property.consumable.contorller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.yzm.common.annotation.BussinessLog;
import com.yzm.common.enums.BusinessType;
import com.yzm.common.utils.PDFTemplateUtil;
import com.yzm.common.utils.ShiroUtils;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.basis.service.AreaService;
import com.yzm.property.basis.service.PersonnelService;
import com.yzm.property.basis.service.WarehouseService;
import com.yzm.property.business.entity.BorrowReturnItem;
import com.yzm.property.business.repository.BorrowReturnItemRepository;
import com.yzm.property.consumable.entity.*;
import com.yzm.property.consumable.repository.ConsumableRepertoryRepository;
import com.yzm.property.consumable.service.InStorageItemService;
import com.yzm.property.consumable.service.InStorageService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 材料入库
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 11:33
 */
@Controller(value = "consumableInStorageController")
@RequestMapping(value = "consumable/inStorage")
public class InStorageController extends BaseController {
    private String urlPrefix = "consumable/inStorage";

    @Autowired
    private InStorageService inStorageService;
    @Autowired
    private InStorageItemService inStorageItemService;
    @Autowired
    private WarehouseService warehouseService;
    @Autowired
    private AreaService areaService;
    @Autowired
    private PersonnelService personnelService;
    @Autowired
    private ConsumableRepertoryRepository consumableRepertoryRepository;
    @Autowired
    private BorrowReturnItemRepository borrowReturnItemRepository;


    @RequiresPermissions("consumable:inStorage:view")
    @RequestMapping()
    public String inStorageList(ModelMap mmap) {
        return urlPrefix + "/list";
    }


    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @ResponseBody
    public ResponseData showAllInStorageInfo(
            @RequestParam(value = "keyword", required = false)
                    String keyword,
            @RequestParam(value = "type", required = false)
                    String type,
            PageBean pageBean
    ) {
        return success(inStorageService.showAllInStorageInfo(keyword, pageBean));

    }

    @GetMapping("add")
    public String add( ModelMap mmap) {
        mmap.put("warehouseList",warehouseService.findAll());
        mmap.put("areaList",new ArrayList<>());
        mmap.put("personnelList",personnelService.findAll());

        return urlPrefix + "/addInStorage";
    }

    @RequestMapping("/add")
    @RequiresPermissions("business:area:add")
    @ResponseBody
    @BussinessLog(businessType=BusinessType.INSERT,title="新增材料待入库")
    public ResponseData save(InStorage inStorage) {
        return inStorageService.saveInStorage(inStorage);
    }

    /**
     * 入库(选择材料信息)页面
     */
    @GetMapping("/addInStorageItem")
    public String addInStorageItem() {
        return urlPrefix + "/addInStorageItem";
    }

    /**
     * 扫码rfid页面
     */
    @GetMapping("/addRfid")
    public String addRfid( ModelMap mmap,String rfid,int index,String field) {
        mmap.put("rfid",rfid);
        mmap.put("index",index);
        mmap.put("field",field);
        return urlPrefix + "/addRfid";
    }
    /**
     * 删除报废信息
     *
     * @param ids
     * @return
     */
    @PostMapping("/del")
    @ResponseBody
    @BussinessLog(businessType = BusinessType.DELETE, title = "删除入库信息")
    public ResponseData deleteScrap(Long[] ids) {

        return inStorageService.deleteInfo(ids);
    }
    /**
     * 根据单据号查询详细
     * @return
     */
    @GetMapping("/view/{id}")
    public String viewPage(@PathVariable("id") Long id, ModelMap mmap) {
        InStorage inStorage = inStorageService.getById(id);
        List<InStorageItem> list = inStorageItemService.findByRecordNumber(inStorage.getRecordNumber());
        mmap.put("inStorage", inStorage);
        mmap.put("inStorageList", list);
        return urlPrefix + "/show";

    }

    /**
     * 根据单据号查询详细
     * @param recordNumber
     * @return
     */
    @GetMapping("/findDetailByRecordNumber")
    @ResponseBody
    public ResponseData findDetailByRecordNumber(@RequestParam String recordNumber) {
        List<InStorageItem> list = inStorageItemService.findByRecordNumber(recordNumber);

        return success().put("inStorageItemList", list);
    }
    /**
     * 导入物料
     *
     * @param excelFile 物料导入
     * @return
     */
    @RequestMapping("/ReadFileText")
    @ResponseBody
    public ResponseData readFileText2(@RequestParam(value = "file", required = false) MultipartFile excelFile) {
        //============================使用模板======================================================
        InputStream inputStream = null;
        try {
            // 使用模板导入——接收上传文件
            inputStream = excelFile.getInputStream();
            List<String> list  = new ArrayList<>();
            EasyExcel.read(inputStream, Rfid.class, new AnalysisEventListener<Rfid>() {
                @Override
                public void invoke(Rfid rfid, AnalysisContext analysisContext) {
                    ConsumableRepertory byAssetsCodeOrAssetsRfid = consumableRepertoryRepository.findByAssetsCodeOrAssetsRfid(rfid.getRfid(), rfid.getRfid());
                    BorrowReturnItem byAssetsRfidAndBiReturnStandby = borrowReturnItemRepository.findByAssetsRfidAndBiReturnStandby(rfid.getRfid(), 0);
                    if(StringUtils.isNotEmpty(byAssetsRfidAndBiReturnStandby)){
                        throw new ExcelAnalysisException(rfid.getRfid()+":当前RFID数据存在未归还信息！");
                    }

                    if(StringUtils.isEmpty(byAssetsCodeOrAssetsRfid)){
                        list.add(rfid.getRfid());
                    }else{
                        throw new ExcelAnalysisException(rfid+":RFID已经入库!");
                    }
                }
                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {}
            }).sheet().doRead();
            return success(list);
        } catch (IOException e) {
            e.printStackTrace();
            return new ResponseData("500", "RFID" + "'" + "'" + "不正确", null);
        } catch (ExcelAnalysisException e) {
            if (e.getCause() instanceof ExcelDataConvertException) {
                ExcelDataConvertException excelDataConvertException = (ExcelDataConvertException) e.getCause();
                return new ResponseData("500", "RFID" + "'" + "'" + "不正确", null);
            } else {
                return new ResponseData("500", e.getMessage(), null);
            }
        }catch (Exception e){
            e.printStackTrace();
            return new ResponseData("500", "请检查导入模板！", null);
        }finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    @GetMapping("exportInformation")
//    @BussinessLog(title = "出库单", businessType = BusinessType.EXPORT)
    public void downloadFile(HttpServletResponse response, Long id) throws Exception {
        ByteArrayOutputStream baos = null;
        OutputStream out = null;
        try {
            InStorage inStorage = inStorageService.getById(id);
            Map<String, Object> data = new HashMap<>();
            data.put("title", "预览PDF");
            //申领编号
            data.put("recordNumber",inStorage.getRecordNumber());
            data.put("inStoragePeople",inStorage.getInStoragePeople());
            data.put("inStorageWarehouse",inStorage.getInStorageWarehouse());
            data.put("inStorageArea",inStorage.getInStorageArea());
            data.put("inStorageReson",inStorage.getInStorageReson());
            data.put("user", ShiroUtils.getUserInfo().getName());
            data.put("now", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy.MM.dd HH:mm:ss")));
            List<InStorageItem> overList = inStorageItemService.findByRecordNumber(inStorage.getRecordNumber());
            int count =0;
            for (InStorageItem inStorageItem : overList) {
                count+=inStorageItem.getInStorageCount();
            }
            data.put("detailList", overList);
            data.put("count", count);
            baos = PDFTemplateUtil.createPDF(data, "入库单.ftl");


            // 设置响应消息头，告诉浏览器当前响应是一个下载文件
            response.setContentType("application/pdf;charset=UTF-8");
            // 告诉浏览器，当前响应数据要求用户干预保存到文件中，以及文件名是什么 如果文件名有中文，必须URL编码
            String fileName = URLEncoder.encode(inStorage.getRecordNumber() + ".pdf", "UTF-8");
            response.setHeader("Content-Disposition", "inline;filename=" + fileName);
            out = response.getOutputStream();
            baos.writeTo(out);
            baos.close();
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("导出失败：" + e.getMessage());
        } finally {
            if (baos != null) {
                baos.close();
            }
            if (out != null) {
                out.close();
            }
        }
    }



}
