package com.yzm.property.consumable.contorller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.yzm.common.annotation.BussinessLog;
import com.yzm.common.enums.BusinessType;
import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.basis.service.*;

import com.yzm.property.consumable.entity.ConsumableInfo;
import com.yzm.property.consumable.importHeadListener.ConsumableInfoHeadDataListener;
import com.yzm.property.consumable.service.ConsumableInfoService;
import com.yzm.property.consumable.service.ConsumableRepertoryService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:46
 */
@Controller
@RequestMapping("/consumable_info")
public class ConsumableInfoController extends BaseController {

    private String urlPrefix = "consumable/consumableInfo";


    @Autowired
    private ConsumableInfoService consumableInfoService;
    @Autowired
    private ConsumableRepertoryService consumableRepertoryService;
    @Autowired
    private BrandService brandService;
    @Autowired
    private UnitService unitService;//资产计量单位

    /**
     * 跳转至list页面
     *
     * @param mmap
     * @return
     */
    @RequiresPermissions("con:consumable:view")
    @RequestMapping
    public String assets(ModelMap mmap) {
        return urlPrefix + "/list";
    }

    /**
     * 跳转至新增页面，携带资产类型以及资产分类数据
     *
     * @param mmap
     * @return
     */
    @GetMapping("/add")
    @RequiresPermissions("con:consumable:add")
    public String addConsumable(ModelMap mmap) {
        mmap.put("unit", unitService.findAll());
        mmap.put("brandList", brandService.findAll());
        return urlPrefix + "/add";
    }

    /**
     * 修改跳转页面
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        mmap.put("consumableInfo", consumableInfoService.findById(id));
        mmap.put("unit", unitService.findAll());
        mmap.put("brandList", brandService.findAll());
        return urlPrefix + "/edit";
    }


    /**
     * 新增材料
     *
     * @param consumableInfo
     * @return
     */
    @PostMapping("/add")
    @ResponseBody
    @RequiresPermissions("con:consumable:add")
    @BussinessLog(businessType = BusinessType.INSERT, title = "新增材料")
    public ResponseData addConsumableInfo(ConsumableInfo consumableInfo) {
        return consumableInfoService.addConsumableInfo(consumableInfo);
    }


    /**
     * 修改资产
     */
    @RequestMapping(value = "/edit", method = RequestMethod.POST)
    @RequiresPermissions("basis:area:edit")
    @ResponseBody
    @BussinessLog(businessType = BusinessType.UPDATE, title = "修改材料信息")
    public ResponseData updateConsumableInfo(ConsumableInfo consumableInfo) {
        return consumableInfoService.updateConsumableInfo(consumableInfo);
    }

    /**
     * 根据id列表删除资产
     *
     * @param ids
     * @return
     */
    @PostMapping("/delete")
    @ResponseBody
    @BussinessLog(businessType = BusinessType.DELETE, title = "删除材料")
    public ResponseData deleteConsumableInfo(Long ids) {
        consumableRepertoryService.findByConsumableInfoId(ids);
        return success();
    }

    /**
     * 根据关键字和分页查询资产
     *
     * @param keyword
     * @param pageBean
     * @return
     */
    @PostMapping("/list")
    @ResponseBody
    public ResponseData showAllConsumableInfo(
            @RequestParam(value = "keyword", required = false)
                    String keyword,
            PageBean pageBean
    ) {
        return success(consumableInfoService.showAllConsumableInfo(keyword, pageBean));
    }

    /**
     * 查看详情
     *
     * @param id
     * @return
     */
    @RequestMapping("/show/{id}")
    @RequiresPermissions("sys:assets:show")
    public ModelAndView showConsumableInfoById(@PathVariable("id") Long id) {
        ConsumableInfo consumableInfo = consumableInfoService.findById(id);
        ModelAndView modelAndView = new ModelAndView(urlPrefix + "/show");
        modelAndView.addObject("unit", unitService.findAll());
        modelAndView.addObject("consumableInfo", consumableInfo);
        return modelAndView;
    }

//    /**
//     * 查询材料信息根据条码和rfid
//     *
//     * @param rfid
//     * @param code
//     * @return
//     */
//    @GetMapping("/findDetailByRfidOrCode")
//    @ResponseBody
//    public ResponseData findDetailByRfidOrCode(@RequestParam String rfid, @RequestParam String code) {
//        ConsumableInfo consumableInfo = consumableInfoService.findByAssetsCodeOrAssetsRfid(rfid, code);
//        if (StringUtils.isEmpty(consumableInfo)) {
//            return error("当前条码/RFID未绑定材料！");
//        }
//        ConsumableRepertory byAssetsCodeOrAssetsRfid = consumableRepertoryService.findByAssetsCodeOrAssetsRfid(rfid, code);
//        if (StringUtils.isNotEmpty(byAssetsCodeOrAssetsRfid)) {
//            return error("当前条码/RFID:"+rfid+"-无法重复入库！");
//        }
//        return success().put("consumableInfo", consumableInfo);
//    }
    /**
     * 导入物料
     *
     * @param excelFile 物料导入
     * @return
     */
    @RequestMapping("/ReadFileText")
    @ResponseBody
    public ResponseData readFileText2(@RequestParam(value = "file", required = false) MultipartFile excelFile) {
        //============================使用模板======================================================
        InputStream inputStream = null;
        try {
            // 使用模板导入——接收上传文件
            inputStream = excelFile.getInputStream();
            // 读取表格数据
            try {
                EasyExcel.read(inputStream, ConsumableInfo.class, new ConsumableInfoHeadDataListener()).headRowNumber(1).doReadAll();
            }catch (ExcelAnalysisException e){
                System.out.printf("aaaaaaaa");
            }
            // TODO 根据业务处理objectList……
            return null;

        } catch (IOException e) {
            e.printStackTrace();
            return new ResponseData("500", "物料编码" + "'" + "'" + "不正确", null);
        } catch (ExcelAnalysisException e) {
            if (e.getCause() instanceof ExcelDataConvertException) {
                ExcelDataConvertException excelDataConvertException = (ExcelDataConvertException) e.getCause();
                return new ResponseData("500", "物料编码" + "'" + "'" + "不正确", null);
            } else {
                return new ResponseData("0", e.getMessage(), null);
            }
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }
}
