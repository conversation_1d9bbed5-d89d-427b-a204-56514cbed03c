package com.yzm.property.consumable.contorller;

import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.business.entity.BorrowReturnItem;
import com.yzm.property.business.repository.BorrowReturnItemRepository;
import com.yzm.property.consumable.entity.ConsumableRepertory;
import com.yzm.property.consumable.service.ConsumableRepertoryService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

/**
 * 材料库存列表
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:46
 */
@Controller
@RequestMapping("/consumable_repertory")
public class ConsumableRepertoryController extends BaseController {

    private String urlPrefix = "consumable/consumableRepertory";




    @Autowired
    private ConsumableRepertoryService consumableRepertoryService;

    @Autowired
    private BorrowReturnItemRepository borrowReturnItemRepository;


    /**
     * 跳转至list页面
     * @param mmap
     * @return
     */
    @RequiresPermissions("con:consumableRepertory:view")
    @RequestMapping
    public String assets(ModelMap mmap) {
        return urlPrefix + "/list";
    }
    /**
     * 跳转至list页面
     * @param mmap
     * @return
     */
    @RequestMapping("/listSummary")
    public String listSummary(ModelMap mmap) {
        return urlPrefix + "/listSummary";
    }

    /**
     * 根据关键字和分页查询资产
     * @param keyword
     * @param pageBean
     * @return
     */
    @PostMapping("/list")
    @ResponseBody
    public ResponseData showAllConsumableRepository(
            @RequestParam(value = "keyword", required = false)
                    String keyword,
            PageBean pageBean
    ) {
        return success(consumableRepertoryService.showAllConsumableRepository(keyword, pageBean));
    }
    /**
     * 根据关键字和分页查询资产
     * @param keyword
     * @param pageBean
     * @return
             */
    @PostMapping("/listSummaryInfo")
    @ResponseBody
    public ResponseData listSummaryInfo(
            @RequestParam(value = "keyword", required = false)
                    String keyword,
            PageBean pageBean
    ) {
        return success(consumableRepertoryService.showAllConsumableSummaryInfo(keyword, pageBean));
    }



    /**
     * 查询材料信息根据条码和rfid
     *
     * @param rfid
     * @param code
     * @return
     */
    @GetMapping("/findDetailByRfidOrCodeDB")
    @ResponseBody
    public ResponseData findDetailByRfidOrCodeDB(@RequestParam String rfid, @RequestParam String code,@RequestParam String assetsNumber) {
        ConsumableRepertory consumableRepertory = consumableRepertoryService.findByAssetsRfidAndAssetsNumber(rfid, assetsNumber);
        if (StringUtils.isEmpty(consumableRepertory)) {
            return error("当前条码/RFID未在库存中查询到材料！");
        }
        return success().put("consumableInfo", consumableRepertory);
    }
    /**
     * 查询材料信息根据条码和rfid
     *
     * @param rfid
     * @param code
     * @return
     */
    @GetMapping("/findDetailByRfidOrCode")
    @ResponseBody
    public ResponseData findDetailByRfidOrCode(@RequestParam String rfid, @RequestParam String code) {
        ConsumableRepertory consumableRepertory = consumableRepertoryService.findByAssetsCodeOrAssetsRfid(rfid, code);
        if (StringUtils.isEmpty(consumableRepertory)) {
            return error("当前条码/RFID未查询到库存！");
        }
        return success().put("consumableInfo", consumableRepertory);
    }

    /**
     * 查询材料信息根据条码和rfid
     *
     * @param rfid
     * @param code
     * @return
     */
    @GetMapping("/findDetailByRfidOrCodeCheck")
    @ResponseBody
    public ResponseData findDetailByRfidOrCodeCheck(@RequestParam String rfid, @RequestParam String code) {
        ConsumableRepertory consumableRepertory = consumableRepertoryService.findByAssetsCodeOrAssetsRfid(rfid, code);
        BorrowReturnItem byAssetsRfidAndBiReturnStandby = borrowReturnItemRepository.findByAssetsRfidAndBiReturnStandby(rfid, 0);
        if(StringUtils.isNotEmpty(byAssetsRfidAndBiReturnStandby)){
            return ResponseData.error(rfid+":当前RFID数据存在未归还信息！");
        }
        if (StringUtils.isEmpty(consumableRepertory)) {
            return success("OK");
        }else{
            return error("当前RFID号已经入库!");
        }
    }
}
