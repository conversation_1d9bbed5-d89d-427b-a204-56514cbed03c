package com.yzm.property.consumable.contorller;

import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.consumable.service.ConsumableStandingBookService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

@Controller
@RequestMapping("consumable/consumableStandingBook")
public class ConsumableStandingBookController extends BaseController {
    private String urlPrefix="consumable/consumableStandingBook";

    @Autowired
    private ConsumableStandingBookService consumableStandingBookService;

    @RequiresPermissions("consumable:consumableStandingBook:view")
    @GetMapping
    public String consumableStandingBook(ModelMap map){
        return urlPrefix+"/list";
    }

    /**
     * 材料台账流水
     */
    @PostMapping("/list")
    @ResponseBody
    public ResponseData showAllConsumableStandingBook(
            @RequestParam(value = "keyword", required = false)
                    String keyword,
            PageBean pageBean
    ) {
        return success(consumableStandingBookService.showAllConsumableStandingBook(keyword, pageBean));
    }
}
