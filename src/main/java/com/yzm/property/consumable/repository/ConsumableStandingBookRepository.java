package com.yzm.property.consumable.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.consumable.entity.ConsumableStandingBook;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface ConsumableStandingBookRepository extends BaseRepository<ConsumableStandingBook,Long> {
    Page<ConsumableStandingBook> findAllByAssetsNameContaining(String keyword, Pageable pagable);

}
