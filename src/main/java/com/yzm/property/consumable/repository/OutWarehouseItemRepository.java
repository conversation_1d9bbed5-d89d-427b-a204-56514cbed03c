package com.yzm.property.consumable.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.consumable.entity.OutWarehouseEntityItem;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository(value = "consumableOutWarehouseItem")
public interface OutWarehouseItemRepository extends BaseRepository<OutWarehouseEntityItem, Long>{
    List<OutWarehouseEntityItem> findByRecordNumber(String recordNumber);//通过记录编号来查数据库的资表数据

    List<OutWarehouseEntityItem> findByRecordNumberAndAssetsNumber(String recordNumber, String assetsNumber);
}
