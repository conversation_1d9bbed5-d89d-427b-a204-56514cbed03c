package com.yzm.property.consumable.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.consumable.entity.TransferItem;
import org.springframework.stereotype.Repository;

import java.util.List;
@Repository(value = "consumableTransferItem")
public interface TransferItemRepository extends BaseRepository<TransferItem, Long> {
    List<TransferItem> findByRecordNumber(String recordNumber);


}
