package com.yzm.property.consumable.repository;

import org.springframework.data.jpa.repository.Query;
import com.yzm.framework.base.BaseRepository;
import com.yzm.property.consumable.entity.OutWarehouseEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.util.List;
@Repository(value = "consumableOutWarehouse")
public interface OutWarehouseRepository extends BaseRepository<OutWarehouseEntity,Long> {
    @Query(nativeQuery=true,value = "select DISTINCT o.* from out_warehouse  o,out_warehouse_item  i where  o.record_number = i.record_number and ( i.assets_name like '%' ?1 '%' or o.record_number like '%' ?1 '%' ) ORDER by o.create_time LIMIT ?2 , ?3")
    List<OutWarehouseEntity> findByRecordNumberContainsOrderByCreateTimeDesc(String keyword, int start, Integer size);

    Page<OutWarehouseEntity> findByOrderByCreateTimeDesc(Pageable pagable);
}
