package com.yzm.property.consumable.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.consumable.entity.InStorage;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:49*/

@Repository(value = "consumableInStorage")
public interface InStorageRepository extends BaseRepository<InStorage, Long> {
    @Query(nativeQuery=true,value = "select distinct c.* from consumable_in_storage_item b ,consumable_in_storage c where  b.record_number = c.record_number and (  b.record_number like '%' ?1 '%' or b.assets_number like '%' ?1 '%' or  b.assets_name like '%' ?1 '%' )  " +
            "order by c.create_time desc  limit ?2 , ?3 ")
    List<InStorage> findByRecordNumberContainsOrderByCreateTimeDesc(String keyword, int start, Integer size);

    Page<InStorage> findByOrderByCreateTimeDesc(Pageable pagable);
  /*  Page<InStorage> findAllByAssetsNameContainingOrOtherIdContaining(String keyword, String otherId, Pageable pageable);

    InStorage findByOtherId(String otherId);

    List<AssetsInfo> findAllByAssetsStatus(Integer status);

    Page<InStorage> findAllByAssetsNameContainingOrOtherIdContainingAndAssetsStatus(String keyword,String otherId,Integer status,Pageable pageable);

    Integer countAllByAssetsType(Integer assetsType);

    Integer countAllByAssetsArea(Integer type);

    Integer countAllByDepartment(Integer type);

    Integer countAllByAssetsUser(Integer type);
    //根据状态分页查询资产
    Page<AssetsInfo> findAllByAssetsStatus(Integer assetsStatus,Pageable pageable);
    //根据区域分页查询资产
    Page<AssetsInfo> findAllByAssetsArea(Integer assetsArea, Pageable pageable);
    //根据使用人分页查询资产
    Page<AssetsInfo> findAllByAssetsUser(Integer assetsUser, Pageable pageable);
    //根据部门分页查询资产
    Page<AssetsInfo> findAllByDepartment(Integer department, Pageable pageable);
    //根据类型分页查询资产
    Page<AssetsInfo> findAllByAssetsType(Integer assetsType, Pageable pageable);

//    Page<AssetsInfo> findAllByAssetsNameContainingAndMaintenanceStatusNotContainsAndMaintenanceStatusNotContains(String keyword, String repairs,String scrap,Pageable pageable);


    @Query(value="select COALESCE(SUM(purchase_price),0) from assets_info", nativeQuery = true)
    int findByPurchasePriceSum();
    @Query(value="select count(*) from assets_info where maintenance_status = '脱保'", nativeQuery = true)
    int findByMaintenanceStatusCount();
    @Query(value="SELECT count(*) AS sum FROM assets_info WHERE MONTH(create_time) = MONTH( NOW( ) )", nativeQuery = true)
    int findAssetsMonthCount();
    @Query(value="SELECT count(*) AS sum FROM assets_info WHERE YEAR(create_time) = YEAR( NOW( ) )", nativeQuery = true)
    int findAssetsYearCount();*/
}
