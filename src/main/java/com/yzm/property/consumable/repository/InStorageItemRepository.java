package com.yzm.property.consumable.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.consumable.entity.InStorageItem;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:49*/

@Repository(value = "consumableInStorageItem")
public interface InStorageItemRepository extends BaseRepository<InStorageItem, Long> {
    List<InStorageItem> findByRecordNumber(String recordNumber);


    List<InStorageItem> findByRecordNumberAndAssetsNumber(String recordNumber, String assetsNumber);
}
