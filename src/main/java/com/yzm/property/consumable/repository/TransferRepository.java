package com.yzm.property.consumable.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.consumable.entity.Transfer;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:49*/

@Repository(value = "consumableTransfer")
public interface TransferRepository extends BaseRepository<Transfer, Long> {

    Page<Transfer> findByOrderByCreateTimeDesc(Pageable pagable);
    @Query(nativeQuery=true,value = "select distinct c.* from transfer_item b ,transfer c where   b.record_number = c.record_number and (  b.record_number like '%' ?1 '%' or b.assets_number like '%' ?1 '%' or  b.assets_specifications like '%' ?1 '%' or b.assets_rfid = ?1 ) " +
            "order by c.create_time desc  limit ?2 , ?3 ")
    List<Transfer> findByRecordNumberContainsOrderByCreateTimeDesc(String keyword, int i, Integer limit);
}
