package com.yzm.property.consumable.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.consumable.entity.ConsumableRepertory;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:49
 */
public interface ConsumableRepertoryRepository extends BaseRepository<ConsumableRepertory, Long> {

    ConsumableRepertory findByConsumableInfoId(Long  consumableInfoId);

    ConsumableRepertory findByConsumableInfoIdAndWarehouseInfo(Long  consumableInfoId,String warehouseInfo);
    @Query(nativeQuery = true,value = "select *,COUNT(assets_number) AS now_count from consumable_repertory GROUP BY assets_number,warehouse_info,area_info order by create_time desc  limit ?1 , ?2 ")
    List<Map<String, Object>> findByConsumableRepertory(int i, int limit);

    @Query(nativeQuery = true,value = "select *,COUNT(assets_number) AS now_count from consumable_repertory where (assets_number  like '%' ?1 '%' or assets_name like '%' ?1 '%' or assets_specifications  like '%' ?1 '%') GROUP BY assets_number order by create_time desc  limit ?2 , ?3 ")
    List<Map<String, Object>> findByAssetsNumberOrderByCreateTimeDesc(String keyword, int i, int limit);

    ConsumableRepertory findByAssetsCodeOrAssetsRfid(String rfid, String code);

    List<ConsumableRepertory> findByAssetsNumber(String assetsNumber);

    List<ConsumableRepertory> findByAssetsNumberAndWarehouseInfoAndAreaInfo(String assetsNumber, String warehouseInfo, String areaInfo);

    ConsumableRepertory findByAssetsRfidAndAssetsNumber(String rfid, String assetsNumber);

//    Page<ConsumableRepertory> findAllByAssetsNameContainingOrAssetsBrandContaining(String keyword, String assetsBrand, Pageable pagable);
}
