package com.yzm.property.consumable.repository;

import com.yzm.framework.base.BaseRepository;

import com.yzm.property.consumable.entity.ConsumableInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:49
 */
public interface ConsumableInfoRepository extends BaseRepository<ConsumableInfo, Long> {
    Page<ConsumableInfo> findAllByAssetsNameContainingOrAssetsNumberContaining(String keyword, String assetsNumber, Pageable pageable);

    ConsumableInfo findByAssetsNumber(String assetsNumber);

//    ConsumableInfo findByAssetsCodeOrAssetsRfid(String rfid,String code);
}
