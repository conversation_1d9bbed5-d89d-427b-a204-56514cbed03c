package com.yzm.property.consumable.pojo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class ConsumableListExcel {


    @ExcelProperty(value = "操作编号",index = 0)
    private String operationOtherId;//操作编号

    @ExcelProperty(value = "操作方式",index = 1)
    private String operationName;//操作方式

    @ExcelProperty(value = "耗材编号",index = 2)
    private String consumableId;//耗材编号

    @ExcelProperty(value = "耗材名称",index = 3)
    private String consumableName;//耗材名称

    @ExcelProperty(value = "领用人",index = 4)
    private String userName;//领用人


    @ExcelProperty(value = "库存数量",index = 5)
    private Integer nowRepertory;//库存数量

    @ExcelProperty(value = "单价",index = 6)
    private Double price;//单价

    @ExcelProperty(value = "操作时间",index = 7)
    private Date createTime;//操作时间

}
