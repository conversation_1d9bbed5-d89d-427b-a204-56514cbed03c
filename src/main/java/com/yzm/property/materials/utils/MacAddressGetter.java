package com.yzm.property.materials.utils;

import java.net.InetAddress;
import java.net.NetworkInterface;

public class Mac<PERSON>ddressGetter {
    public static void main(String[] args) {
        try {
            InetAddress localhost = InetAddress.getLocalHost();
            NetworkInterface networkInterface = NetworkInterface.getByInetAddress(localhost);

            if (networkInterface != null) {
                byte[] macBytes = networkInterface.getHardwareAddress();

                if (macBytes != null) {
                    StringBuilder macBuilder = new StringBuilder();
                    for (int i = 0; i < macBytes.length; i++) {
                        macBuilder.append(String.format("%02X", macBytes[i]));
                        if (i < macBytes.length - 1) {
                            macBuilder.append("-");
                        }
                    }
                    System.out.println("Mac 地址为：" + macBuilder.toString());
                } else {
                    System.out.println("无法获取 Mac 地址。");
                }
            } else {
                System.out.println("无法获取 NetworkInterface 实例。");
            }
        } catch (Exception e) {
            System.out.println("获取 Mac 地址时发生了异常：" + e.getMessage());
        }
    }
}
