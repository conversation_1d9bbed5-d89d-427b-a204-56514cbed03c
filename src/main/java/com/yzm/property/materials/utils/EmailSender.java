package com.yzm.property.materials.utils;

import com.alibaba.excel.EasyExcel;
import com.yzm.framework.freemark.Global;
import com.yzm.property.materials.entity.DelinquentMaterial;
import com.yzm.property.materials.pojo.DelinquentMaterialExcel;
import jakarta.mail.*;
import jakarta.mail.internet.InternetAddress;
import jakarta.mail.internet.MimeBodyPart;
import jakarta.mail.internet.MimeMessage;
import jakarta.mail.internet.MimeMultipart;
import org.springframework.beans.BeanUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Properties;

import static cn.hutool.json.XMLTokener.entity;

public class EmailSender {


    public static void sendEmail(List<DelinquentMaterial> entities, String recipient) {

        // 邮件发送者邮箱和授权码
        final String username = "<EMAIL>";
        // 邮件服务器设置
        Properties props = new Properties();
        props.put("mail.smtp.auth", "false");
        props.put("mail.smtp.starttls.enable", "true");
//        props.put("mail.smtp.host", "smtp.qq.com");
        props.put("mail.smtp.host", "smtp.sws.co.jp");
        props.put("mail.smtp.port", "25");
//        props.put("mail.smtp.port", "25");

        // 获取Session实例
        Session session = Session.getInstance(props);
//        Session session = Session.getInstance(props, new jakarta.mail.Authenticator() {
//            protected PasswordAuthentication getPasswordAuthentication() {
////                return new PasswordAuthentication(username, password);
//                return null;
//            }
//        });
        try {
            // 创建邮件对象
            Message message = new MimeMessage(session);
            message.setFrom(new InternetAddress(username));
            message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(recipient));
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy/MM/dd");

            // 设置邮件主题
            message.setSubject(simpleDateFormat.format(new Date()) + "-材料欠料联络");

            // 自动生成 HTML 表格内容
            StringBuilder htmlContent = new StringBuilder();
            htmlContent.append("<html><body>");
            htmlContent.append("<p>各位，</p>");
            htmlContent.append("<p>最新欠料信息展开，请确认。</p>");
            htmlContent.append("<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>");
            htmlContent.append("<tr><th>电脑号码</th><th>物料名称</th><th>欠料数量</th></tr>");
            if (entities.size() > 0) {
                DelinquentMaterial entity = entities.get(0);
                htmlContent.append("<tr>")
                        .append("<td>").append(entity.getAssetsNumber()).append("</td>")
                        .append("<td>").append(entity.getAssetsName()).append("</td>")
                        .append("<td>").append(entity.getDelinquentCount()).append("</td>")
                        .append("</tr>");
            }
            // 遍历列表，生成表格内容

            htmlContent.append("</table>");
            htmlContent.append("<p>详见附件欠料跟进表</p>");
            htmlContent.append("<p>以上</p>");
            htmlContent.append("<p>附件：更新后的欠料跟进表</p>");
            htmlContent.append("</body></html>");

            MimeBodyPart messageBodyPart = new MimeBodyPart();
            messageBodyPart.setContent(htmlContent.toString(), "text/html; charset=utf-8");


            Multipart multipart = new MimeMultipart();
            multipart.addBodyPart(messageBodyPart);

            // 附件路径
//            MimeBodyPart attachmentPart = new MimeBodyPart();
//            String filename = "/Users/<USER>/工作/yizengming/erp_system_data/src/main/resources/static/ImportTemplates/产品导入模版.xlsx";
//            attachmentPart.attachFile(filename);
//            multipart.addBodyPart(attachmentPart);
            // 将列表数据写入 Excel 文件
            String excelFilePath = "D:" + File.separator + "file" + File.separator + generateFileName();
//            String excelFilePath = "/Users/<USER>/工作/程序/" + generateFileName();

//            String excelFilePath = recipients_config + File.separator + "file" + File.separator + generateFileName();
            createExcelFile(entities, excelFilePath);
// 添加 Excel 文件作为附件
            MimeBodyPart attachmentPart = new MimeBodyPart();
            attachmentPart.attachFile(new File(excelFilePath));
            multipart.addBodyPart(attachmentPart);
            // 设置邮件内容
            message.setContent(multipart);

            // 发送邮件
            Transport.send(message);

            System.out.println("邮件发送成功！");

        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public static void main(String[] args) {
        // 邮件发送者邮箱和授权码
//        final String username = "<EMAIL>";
        final String username = "<EMAIL>";
        final String password = "kaxurjstipqbghgg";

        // 收件人邮箱
//        String recipient = "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>";
        String recipient = "<EMAIL>,<EMAIL>,<EMAIL>";

        // 邮件服务器设置
        Properties props = new Properties();
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.starttls.enable", "true");
        props.put("mail.smtp.host", "smtp.qq.com");
//        props.put("mail.smtp.host", "smtp.sws.co.jp");
//        props.put("mail.smtp.port", "25");
        props.put("mail.smtp.port", "25");

        // 获取Session实例
        Session session = Session.getInstance(props, new jakarta.mail.Authenticator() {
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(username, password);
            }
        });
        List<DelinquentMaterial> entities = new ArrayList<>();
        DelinquentMaterial ne = new DelinquentMaterial();
        ne.setAssetsName("ces");
        ne.setAssetsNumber("ces");
        ne.setDelinquentCount(new BigDecimal(12));
        DelinquentMaterial ne2 = new DelinquentMaterial();
        ne2.setAssetsName("ces");
        ne2.setAssetsNumber("ces");
        ne2.setDelinquentCount(new BigDecimal(43));
        entities.add(ne);
        entities.add(ne2);
        try {
            // 创建邮件对象
            Message message = new MimeMessage(session);
            message.setFrom(new InternetAddress(username));
            message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(recipient));
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy/MM/dd");

            // 设置邮件主题
            message.setSubject(simpleDateFormat.format(new Date()) + "-材料欠料联络");

            // 自动生成 HTML 表格内容
            StringBuilder htmlContent = new StringBuilder();
            htmlContent.append("<html><body>");
            htmlContent.append("<p>各位，</p>");
            htmlContent.append("<p>最新欠料信息展开，请确认。</p>");
            htmlContent.append("<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>");
            htmlContent.append("<tr><th>电脑号码</th><th>物料名称</th><th>欠料数量</th></tr>");

            // 遍历列表，生成表格内容
            for (DelinquentMaterial entity : entities) {
                htmlContent.append("<tr>")
                        .append("<td>").append(entity.getAssetsNumber()).append("</td>")
                        .append("<td>").append(entity.getAssetsName()).append("</td>")
                        .append("<td>").append(entity.getDelinquentCount()).append("</td>")
                        .append("</tr>");
            }

            htmlContent.append("</table>");
            htmlContent.append("<p>详见附件欠料跟进表</p>");
            htmlContent.append("<p>以上</p>");
            htmlContent.append("<p>附件：更新后的欠料跟进表</p>");
            htmlContent.append("</body></html>");

            MimeBodyPart messageBodyPart = new MimeBodyPart();
            messageBodyPart.setContent(htmlContent.toString(), "text/html; charset=utf-8");


            Multipart multipart = new MimeMultipart();
            multipart.addBodyPart(messageBodyPart);

            // 附件路径
//            MimeBodyPart attachmentPart = new MimeBodyPart();
//            String filename = "/Users/<USER>/工作/yizengming/erp_system_data/src/main/resources/static/ImportTemplates/产品导入模版.xlsx";
//            attachmentPart.attachFile(filename);
//            multipart.addBodyPart(attachmentPart);
            // 将列表数据写入 Excel 文件
            String excelFilePath = "/Users/<USER>/工作/程序/" + generateFileName();
            createExcelFile(entities, excelFilePath);
// 添加 Excel 文件作为附件
            MimeBodyPart attachmentPart = new MimeBodyPart();
            attachmentPart.attachFile(new File(excelFilePath));
            multipart.addBodyPart(attachmentPart);
            // 设置邮件内容
            message.setContent(multipart);

            // 发送邮件
            Transport.send(message);

            System.out.println("邮件发送成功！");

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // 使用 EasyExcel 将数据写入 Excel 文件
    private static void createExcelFile(List<DelinquentMaterial> entities, String filePath) {
        List<DelinquentMaterialExcel> list = new ArrayList<>();
        for (DelinquentMaterial entity : entities) {
            DelinquentMaterialExcel excel = new DelinquentMaterialExcel();
            BeanUtils.copyProperties(entity, excel);
            list.add(excel);
        }

        try (FileOutputStream fileOut = new FileOutputStream(filePath)) {
            EasyExcel.write(fileOut, DelinquentMaterialExcel.class).sheet("欠料跟进表").doWrite(list);
            System.out.println("Excel 文件创建成功：" + filePath);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // 根据当前日期生成文件名
    public static String generateFileName() {

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        // 格式化为 "yyyy-MM-dd" 格式
        String formattedDate = simpleDateFormat.format(new Date());
        String all = formattedDate.replaceAll("", "_");
        // 构建文件名
        return "欠料跟进表_" + System.currentTimeMillis() + ".xlsx";
    }
}