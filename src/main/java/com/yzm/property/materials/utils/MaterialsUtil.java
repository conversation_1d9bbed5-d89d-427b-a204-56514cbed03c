package com.yzm.property.materials.utils;

import com.yzm.property.materials.repository.MaterialsListRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class MaterialsUtil {
    private static MaterialsListRepository consumableListRepository;
    public static final String OPERATION_RUKU = "入库";
    public static final String OPERATION_CHUKU="出库";
    public static final String OPERATION_JIEYONG="借用";
    public static final String OPERATION_GUIHUAN="归还";
    @Autowired
    public void init(MaterialsListRepository consumableListRepository){
        MaterialsUtil.consumableListRepository=consumableListRepository;
    }

//    public static void createMaterialsList(Long operationId, String operationOtherId, String operationName, String consumableId ,
//                                            String consumableName, String userName, Date outDate,Integer nowRepertory,Double price){
//        MaterialsListEntity consumableListEntity=new MaterialsListEntity(operationId,operationOtherId,operationName,consumableId,consumableName,userName,outDate,nowRepertory,price);
//        MaterialsListEntity save=consumableListRepository.save(consumableListEntity);
//        if(save==null){
//            throw new RuntimeException("材料流水存储失败！");
//        }
//    }
}
