package com.yzm.property.materials.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class DateParser {
    public static Date parseDate(String dateString) {
        // 定义支持的日期格式
        List<SimpleDateFormat> formats = new ArrayList<>();
        formats.add(new SimpleDateFormat("yyyy-MM-dd"));
        formats.add(new SimpleDateFormat("yyyyMMdd"));
        formats.add(new SimpleDateFormat("yyyy/MM/dd"));
        formats.add(new SimpleDateFormat("dd-MM-yyyy"));
        formats.add(new SimpleDateFormat("dd/MM/yyyy"));

        // 尝试解析日期
        for (SimpleDateFormat format : formats) {
            try {
                // 解析日期字符串
                return format.parse(dateString);
            } catch (ParseException e) {
                // 当前格式解析失败，尝试下一个
            }
        }

        // 如果没有任何格式成功解析，抛出异常
        throw new IllegalArgumentException("无法解析日期: " + dateString);
    }


}