package com.yzm.property.materials.importHeadListener;

import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.alibaba.excel.util.DateUtils;
import com.alibaba.fastjson.JSON;
import com.yzm.common.utils.SpringUtil;
import com.yzm.common.utils.StringUtils;
import com.yzm.property.basis.entity.BasisArea;
import com.yzm.property.basis.entity.BasisType;
import com.yzm.property.basis.entity.BasisUnit;
import com.yzm.property.basis.entity.BasisWarehouse;
import com.yzm.property.basis.repository.AreaRepository;
import com.yzm.property.basis.repository.TypeRepository;
import com.yzm.property.basis.repository.UnitRepository;
import com.yzm.property.basis.repository.WarehouseRepository;
import com.yzm.property.materials.entity.*;
import com.yzm.property.materials.pojo.DelinquentMaterialExcel;
import com.yzm.property.materials.repository.*;
import com.yzm.property.materials.utils.DateParser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 读取头
 *
 * <AUTHOR> Zhuang
 */
public class DelinquentMaterialHeadDataListener extends AnalysisEventListener<DelinquentMaterialExcel> {
    private static final Logger LOGGER = LoggerFactory.getLogger(MaterialsInfoHeadDataListener.class);
    /**
     * 每隔5条存储数据库，实际使用中可以3000条，然后清理list ，方便内存回收
     */
    private static final int BATCH_COUNT = 200;
    List<DelinquentMaterialExcel> list = new ArrayList<DelinquentMaterialExcel>();

    /**
     * 这里会一行行的返回头
     *
     * @param headMap
     * @param context
     */
    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        LOGGER.info("解析到一条头数据:{}", JSON.toJSONString(headMap));
    }

    @Override
    public void invoke(DelinquentMaterialExcel data, AnalysisContext context) {
        LOGGER.info("解析到一条数据:{}", JSON.toJSONString(data));
        list.add(data);
        if (list.size() >= BATCH_COUNT) {
            saveData();
            list.clear();
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        saveData();
        LOGGER.info("所有数据解析完成！");
    }

    /**
     * 加上存储数据库
     */
    private void saveData() {
        DelinquentMaterialRepository delinquentMaterialRepository = SpringUtil.getBean(DelinquentMaterialRepository.class);
        IssueTempDtlRepository issueTempDtlRepository = SpringUtil.getBean(IssueTempDtlRepository.class);
        IssueTempDtlItemRepository issueTempDtlItemRepository = SpringUtil.getBean(IssueTempDtlItemRepository.class);
        IssueTempDtlTubeRepository issueTempDtlTubeRepository = SpringUtil.getBean(IssueTempDtlTubeRepository.class);

        for (DelinquentMaterialExcel delinquentMaterialExcel : list) {
//            if (StringUtils.isEmpty(delinquentMaterialExcel.getKitNo())) {
                List<DelinquentMaterial> list = delinquentMaterialRepository.findByLotNoAndAssetsNumber(delinquentMaterialExcel.getLotNo(), delinquentMaterialExcel.getAssetsNumber());
                for (DelinquentMaterial delinquentMaterial : list) {

                    delinquentMaterial.setEstimatedTime(DateParser.parseDate(delinquentMaterialExcel.getPlannedArrivalTime()));
                    delinquentMaterial.setStatus(1L);
                    delinquentMaterialRepository.saveAndFlush(delinquentMaterial);

                    Date plannedIssueDt = delinquentMaterial.getPlannedIssueDt();
                    Date estimatedTime = delinquentMaterial.getEstimatedTime();
                    if (StringUtils.isEmpty(estimatedTime)) {
                        continue;
                    }
                    if (plannedIssueDt.compareTo(estimatedTime) < 0) {
                        // plannedIssueDt 在 estimatedTime 之前
                        IssueTempDtlTube byKitNo = issueTempDtlTubeRepository.findByWorkOrdNoAndMatlNo(delinquentMaterial.getLotNo(), delinquentMaterialExcel.getAssetsNumber());
                        byKitNo.setShortFeed("欠料");
                        issueTempDtlTubeRepository.saveAndFlush(byKitNo);
                        break;
                    } else {
                        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
// 将 Date 转换为 LocalDate
                        LocalDate localDate = plannedIssueDt.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

// 获取前一天的日期
                        LocalDate previousDay = localDate.minusDays(1);

// 将前一天的 LocalDate 转回 Date
                        Date previousDayDate = Date.from(previousDay.atStartOfDay(ZoneId.systemDefault()).toInstant());

                        IssueTempDtlTube byKitNo = issueTempDtlTubeRepository.findByWorkOrdNoAndMatlNo(delinquentMaterial.getLotNo(), delinquentMaterialExcel.getAssetsNumber());
                        byKitNo.setShortFeed(simpleDateFormat.format(previousDayDate));
                        issueTempDtlTubeRepository.saveAndFlush(byKitNo);
                    }
                }
//            } else {
//                List<DelinquentMaterial> list = delinquentMaterialRepository.findByKitNoAndLotNoAndAssetsNumber(delinquentMaterialExcel.getKitNo(), delinquentMaterialExcel.getLotNo(), delinquentMaterialExcel.getAssetsNumber());
//                for (DelinquentMaterial delinquentMaterial : list) {
//
//                    delinquentMaterial.setEstimatedTime(DateParser.parseDate(delinquentMaterialExcel.getPlannedArrivalTime()));
//                    delinquentMaterial.setStatus(1L);
//                    delinquentMaterialRepository.saveAndFlush(delinquentMaterial);
//
//                    List<DelinquentMaterial> delinquentMaterialList = delinquentMaterialRepository.findByKitNo(delinquentMaterial.getKitNo());
//                    for (DelinquentMaterial material : delinquentMaterialList) {
//                        Date plannedIssueDt = material.getPlannedIssueDt();
//                        Date estimatedTime = material.getEstimatedTime();
//                        if (StringUtils.isEmpty(estimatedTime)) {
//                            continue;
//                        }
//                        if (plannedIssueDt.compareTo(estimatedTime) < 0) {
//                            // plannedIssueDt 在 estimatedTime 之前
//                            IssueTempDtl byKitNo = issueTempDtlRepository.findByKitNo(material.getKitNo());
//                            byKitNo.setShortFeed("欠料");
//                            issueTempDtlRepository.saveAndFlush(byKitNo);
//
//                            List<IssueTempDtlItem> byKitNoAndMaterialNo = issueTempDtlItemRepository.findByKitNoAndMaterialNo(material.getKitNo(), material.getAssetsNumber());
//                            for (IssueTempDtlItem item : byKitNoAndMaterialNo) {
//                                item.setShortFeed("欠料");
//                                issueTempDtlItemRepository.saveAndFlush(item);
//                            }
//                            break;
//                        } else {
//                            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
//// 将 Date 转换为 LocalDate
//                            LocalDate localDate = plannedIssueDt.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
//
//// 获取前一天的日期
//                            LocalDate previousDay = localDate.minusDays(1);
//
//// 将前一天的 LocalDate 转回 Date
//                            Date previousDayDate = Date.from(previousDay.atStartOfDay(ZoneId.systemDefault()).toInstant());
//
//                            IssueTempDtl byKitNo = issueTempDtlRepository.findByKitNo(material.getKitNo());
//                            byKitNo.setShortFeed(simpleDateFormat.format(previousDayDate));
//                            issueTempDtlRepository.saveAndFlush(byKitNo);
//
//                            List<IssueTempDtlItem> byKitNoAndMaterialNo = issueTempDtlItemRepository.findByKitNoAndMaterialNo(material.getKitNo(), material.getAssetsNumber());
//                            for (IssueTempDtlItem item : byKitNoAndMaterialNo) {
//                                item.setShortFeed("欠料");
//                                item.setShortFeed(simpleDateFormat.format(previousDayDate));
//                                issueTempDtlItemRepository.saveAndFlush(item);
//                            }
//                        }
//                    }
//                }
//
//
//            }
            LOGGER.info("{}条数据，开始存储数据库！", list.size());
            LOGGER.info("存储数据库成功！");
        }
    }
}