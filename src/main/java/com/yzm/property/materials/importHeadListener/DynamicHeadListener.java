package com.yzm.property.materials.importHeadListener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.excel.metadata.CellData;
import com.yzm.common.utils.ShiroUtils;
import com.yzm.common.utils.SpringUtil;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.shiro.LoginUser;
import com.yzm.property.materials.entity.MaterialsInfo;
import com.yzm.property.materials.entity.MaterialsInfoBom;
import com.yzm.property.materials.repository.MaterialsInfoBomRepository;
import com.yzm.property.materials.repository.MaterialsInfoRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class DynamicHeadListener extends AnalysisEventListener<Map<Integer, String>> {
    private Map<String, Integer> headMap = new HashMap<>();  // 存储表头名称和对应的列索引
    private List<MaterialsInfoBom> dataList = new ArrayList<>();  // 存储解析后的数据
    private static final Logger LOGGER = LoggerFactory.getLogger(MaterialsInfoTransitHeadDataListener.class);
    private int count = 0;
    private int countOk = 0;

    public int getCount() {
        return count;
    }

    public int getCountOk() {
        return countOk;
    }

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        // 解析表头行，将表头与列索引对应起来
        for (Map.Entry<Integer, String> entry : headMap.entrySet()) {
            this.headMap.put(entry.getValue().trim(), entry.getKey());
        }
    }

    @Override
    public void invoke(Map<Integer, String> data, AnalysisContext context) {
        MaterialsInfoBom inventoryData = new MaterialsInfoBom();
        try {
            count++;
            inventoryData.setInvoiceNumber(data.get(headMap.get("inv_no")) != null ? data.get(headMap.get("inv_no")) : "");
            inventoryData.setContainerNumber(data.get(headMap.get("cntnr_no")) != null ? data.get(headMap.get("cntnr_no")) : "");
            inventoryData.setAssetsNumber(data.get(headMap.get("matl_no")) != null ? data.get(headMap.get("matl_no")) : "");
            inventoryData.setAssetsName(data.get(headMap.get("matl_nm")) != null ? data.get(headMap.get("matl_nm")) : "");
            inventoryData.setAssetsUnitName(data.get(headMap.get("po_unit")) != null ? data.get(headMap.get("po_unit")) : "");
            inventoryData.setPltNo(data.get(headMap.get("plt_no")) != null ? data.get(headMap.get("plt_no")) : "");
            inventoryData.setPoNo(data.get(headMap.get("po_no")) != null ? data.get(headMap.get("po_no")) : "");
            inventoryData.setDosage(new BigDecimal(data.get(headMap.get("recv_qty"))));

            if (StringUtils.isEmpty(headMap.get("mode_of_trade"))) {
                inventoryData.setModeOfTrade("保税");
            } else {
                String mode_of_trade = data.get(headMap.get("mode_of_trade"));
                inventoryData.setModeOfTrade(mode_of_trade);
            }
            inventoryData.setAssetsStatus("到港");
        } catch (Exception e) {
            throw new ExcelAnalysisException("模版错误请核对模版");
        }

//        // 根据表头名设置对应的字段
//        inventoryData.setSectCd(data.get(headMap.get("sect_cd")));
//        inventoryData.setWhseAreaCd(data.get(headMap.get("whse_area_cd")));
//        inventoryData.setInvNo(data.get(headMap.get("inv_no")));
//        inventoryData.setSuppCd(data.get(headMap.get("supp_cd")));
//        inventoryData.setCntnrNo(data.get(headMap.get("cntnr_no")));
//        inventoryData.setPltNo(data.get(headMap.get("plt_no")));
//        inventoryData.setLocCd(data.get(headMap.get("loc_cd")));
//        inventoryData.setMatlNo(data.get(headMap.get("matl_no")));
//        inventoryData.setMatlNm(data.get(headMap.get("matl_nm")));
//        inventoryData.setDueDate(data.get(headMap.get("due_date")));
//        inventoryData.setPoNo(data.get(headMap.get("po_no")));
//        inventoryData.setRecvQty(parseInt(data.get(headMap.get("recv_qty"))));
//        inventoryData.setNetWeight(parseDouble(data.get(headMap.get("net_weight"))));
//        inventoryData.setWgt(parseInt(data.get(headMap.get("wgt"))));
//        inventoryData.setPoUnit(data.get(headMap.get("po_unit")));
//        inventoryData.setUnitPrice(parseDouble(data.get(headMap.get("unit_price"))));
//        inventoryData.setCurrCd(data.get(headMap.get("curr_cd")));
//        inventoryData.setAmount(parseDouble(data.get(headMap.get("amount"))));
//        inventoryData.setBoxLblF(parseInt(data.get(headMap.get("box_lbl_f"))));
        dataList.add(inventoryData);

//        for (MaterialsInfoBom materialsInfoBom : dataList) {
//            if(materialsInfoBom.getAssetsNumber().equals(inventoryData.getAssetsNumber())&&materialsInfoBom.getInvoiceNumber().equals(inventoryData.getInvoiceNumber())){
//                materialsInfoBom.setDosage(materialsInfoBom.getDosage().add(inventoryData.getDosage()));
//                dataList.remove(inventoryData);
//            }
//        }

        // 添加到数据列表中
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        saveData();
    }

    /**
     * 加上存储数据库
     */
    private void saveData() {
        MaterialsInfoBomRepository materialsInfoBomRepository = SpringUtil.getBean(MaterialsInfoBomRepository.class);
        MaterialsInfoRepository materialsInfoRepository = SpringUtil.getBean(MaterialsInfoRepository.class);
//        materialsInfoBomRepository.deleteAll();

        // 使用流 API 去重和合并 dosage
//        Map<String, MaterialsInfoBom> resultMap = new HashMap<>();
//        for (MaterialsInfoBom item : dataList) {
//            String key = item.getAssetsNumber() + "-" + item.getInvoiceNumber() + "-" + item.getContainerNumber() + "-" + item.getPoNo();
//            resultMap.merge(key, item, (existing, newItem) -> {
//                existing.setDosage(existing.getDosage().add(newItem.getDosage()));
//                return existing; // 返回更新后的对象
//            });
//        }

//        List<MaterialsInfoBom> result = new ArrayList<>(resultMap.values());

        for (MaterialsInfoBom materialsInfoBom : dataList) {
            List<MaterialsInfoBom> byAssetsNumberAndInvoiceNumber = materialsInfoBomRepository.findByAssetsNumberAndInvoiceNumberAndContainerNumberAndPoNoAndPltNo(materialsInfoBom.getAssetsNumber(), materialsInfoBom.getInvoiceNumber(),materialsInfoBom.getContainerNumber(),materialsInfoBom.getPoNo(),materialsInfoBom.getPltNo());
            if (byAssetsNumberAndInvoiceNumber.size() > 0) {
//                MaterialsInfoBom materialsInfoBom1 = byAssetsNumberAndInvoiceNumber.get(0);
//                materialsInfoBom1.setDosage(materialsInfoBom1.getDosage().add(materialsInfoBom.getDosage()));
//                materialsInfoBomRepository.saveAndFlush(materialsInfoBom1);
                continue;
            }
            MaterialsInfo byAssetsNumber = materialsInfoRepository.findByAssetsNumber(materialsInfoBom.getAssetsNumber());
            if (StringUtils.isEmpty(byAssetsNumber)) {
                byAssetsNumber = new MaterialsInfo();
                byAssetsNumber.setAssetsNumber(materialsInfoBom.getAssetsNumber());
//            return error("系统没有该材料！" + number);
            }

            BigDecimal decimal = new BigDecimal(1);
            if (StringUtils.isNotEmpty(byAssetsNumber.getMinLot())) {
                decimal = byAssetsNumber.getMinLot();
            }
            materialsInfoBom.setDosage(materialsInfoBom.getDosage().multiply(decimal));
            materialsInfoBom.setQuantityArrived(new BigDecimal(0));
            LoginUser userInfo = ShiroUtils.getUserInfo();
            materialsInfoBom.setCustom1(userInfo.getName());
            materialsInfoBomRepository.save(materialsInfoBom);
            LOGGER.info("{}条数据，开始存储数据库！", dataList.size());
            LOGGER.info("存储数据库成功！");
        }
    }
}
