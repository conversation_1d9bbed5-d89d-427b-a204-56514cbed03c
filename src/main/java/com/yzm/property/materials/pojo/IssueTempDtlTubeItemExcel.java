package com.yzm.property.materials.pojo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class IssueTempDtlTubeItemExcel {
    @ExcelProperty(value = "计划日期", index = 0)
    @ColumnWidth(20)
    private String planIssDate1; // 材料编号

    @ExcelProperty(value = "作业日期", index = 1)
    @ColumnWidth(20)
    private String operatingTime; // 材料编号

    @ExcelProperty(value = "品番", index = 2)
    @ColumnWidth(20)
    private String prodNo; // 材料名称

    @ExcelProperty(value = "批号", index = 3)
    @ColumnWidth(20)
    private String workOrdNo; // 材料单位

    @ExcelProperty(value = "手配数（条）", index = 4)
    @ColumnWidth(20)
    private String cutingQty; // 材料条码

    @ExcelProperty(value = "电脑号码", index = 5)
    @ColumnWidth(20)
    private String matlNo; // 当前库存

    @ExcelProperty(value = "材料名称", index = 6)
    @ColumnWidth(20)
    private String matlNm; // 库存仓库

    @ExcelProperty(value = "材料批号", index = 7)
    @ColumnWidth(20)
    private String batchNumber; //                       库位

    @ExcelProperty(value = "长度（MM）", index = 8)
    @ColumnWidth(20)
    private String cutLen; // 盘点数量

    @ExcelProperty(value = "首条实测", index = 9)
    @ColumnWidth(20)
    private String firstLen; // 盘盈

    @ExcelProperty(value = "末条实测", index = 10)
    @ColumnWidth(20)
    private String endLen; // 盘亏

    @ExcelProperty(value = "数量（条）", index = 11)
    @ColumnWidth(20)
    private String quantityDelivered; // 盘点状态
    @ExcelProperty(value = "出库数量（M）", index = 12)
    @ColumnWidth(20)
    private String quantityDeliveredLen; // 盘点状态
    @ExcelProperty(value = "操作人", index = 13)
    @ColumnWidth(20)
    private String operName; // 盘点状态


}
