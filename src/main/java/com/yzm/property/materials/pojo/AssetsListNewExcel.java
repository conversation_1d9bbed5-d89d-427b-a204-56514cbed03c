package com.yzm.property.materials.pojo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Date;


@Data
public class AssetsListNewExcel {
    @ExcelProperty(value = "电脑号码",index = 0)
    private String assetsNumber;//操作方式
    @ExcelProperty(value = "物料名称",index = 1)
    private String assetsName;//操作方式
    @ExcelProperty(value = "部门代码",index = 2)
    private String departmentCode;//操作方式
    @ExcelProperty(value = "数量",index = 3)
    private String nowRepertory;//操作方式
    @ExcelProperty(value = "库位信息",index = 4)
    private String location;//操作方式
}
