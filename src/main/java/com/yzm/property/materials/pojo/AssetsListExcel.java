package com.yzm.property.materials.pojo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Date;


@Data
public class AssetsListExcel {
    @ExcelProperty(value = "操作方式",index = 0)
    private String operationName;//操作方式

    @ExcelProperty(value = "资产编号",index = 1)
    private String assetsOtherId;//资产编号

    @ExcelProperty(value = "资产名称",index = 2)
    private String assetsName;//资产名称

    @ExcelProperty(value = "操作者",index = 3)
    private String createBy;//操作者

    @ExcelProperty(value = "操作时间",index = 4)
    private Date createTime;//操作时间
}
