package com.yzm.property.materials.pojo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class MaterialsListExcel {


    @ExcelProperty(value = "操作编号", index = 0)
    private String operationOtherId;//操作编号

    @ExcelProperty(value = "操作方式", index = 1)
    private String operationName;//操作方式

    @ExcelProperty(value = "操作原由", index = 2)
    private String operationNameType;//操作方式

    @ExcelProperty(value = "材料编号", index = 3)
    private String consumableId;//材料编号

    @ExcelProperty(value = "材料名称", index = 4)
    private String consumableName;//材料名称

    @ExcelProperty(value = "操作人", index = 5)
    private String userName;//领用人


    @ExcelProperty(value = "操作数量", index = 6)
    private BigDecimal nowRepertory;//库存数量

    @ExcelProperty(value = "单位", index = 7)
    private String assetsUnitName;//库存数量

    @ExcelProperty(value = "操作仓库", index = 8)
    private String warehouseInfo;//库存数量

    @ExcelProperty(value = "库位信息", index = 9)
    private String areaInfo;//库存数量


    @ExcelProperty(value = "操作时间", index = 10)
    private String createTime;//操作时间

    @ExcelProperty(value = "贸易方式", index = 11)

    private String modeOfTrade;//贸易方式

    @ExcelProperty(value = "来料日期", index = 12)

    private String dateArrival;//来料日期

    @ExcelProperty(value = "部门代码", index = 13)
    private String departmentCode;//部门代码

    @ExcelProperty(value = "发票号码", index = 14)
    private String invoiceNumber; //发票号码



}
