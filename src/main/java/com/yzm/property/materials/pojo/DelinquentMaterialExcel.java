package com.yzm.property.materials.pojo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class DelinquentMaterialExcel {

    //
//    @ExcelProperty(value = "发料指示书号", index = 0)
//
//    private String kitNo; //编号
    @ExcelProperty(value = "电脑号码", index = 0)
    private String assetsNumber; //编号
    @ExcelProperty(value = "物料名称", index = 1)
    private String assetsName; //部品名称
    @ExcelProperty(value = "品番", index = 2)
    private String productNo;//品番
    @ExcelProperty(value = "批号", index = 3)
    private String lotNo; //批号

    @ExcelProperty(value = "应发数量", index = 4)

    private BigDecimal sumData; //欠料数量
    @ExcelProperty(value = "可发数量", index = 5)
    private BigDecimal quantityArrived;//已到数量

    @ExcelProperty(value = "欠料数量", index = 6)
    private BigDecimal delinquentCount; //欠料数量

    @ExcelProperty(value = "在途数量", index = 7)
    private BigDecimal transitCount;//在途数量
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat("yyyy-MM-dd")
    @ExcelProperty(value = "计划发料日", index =8)
    private Date plannedIssueDt; //计划日期

    @ExcelProperty(value = "生产组别", index = 9)
    private String plannedArrivalTime; //计划日期
    @ExcelProperty(value = "预计来料日", index = 10)
    private String groupInfo; //计划日期
    @ExcelProperty(value = "備注", index = 11)
    private String remark; //计划日期



}
