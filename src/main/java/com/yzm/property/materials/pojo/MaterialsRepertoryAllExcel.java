package com.yzm.property.materials.pojo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.Data;

import java.util.Date;

@Data
public class MaterialsRepertoryAllExcel {



    @ExcelProperty("材料编号")
    private String assetsNumber;

    @ExcelProperty("材料名称")
    private String assetsName;

    @ExcelProperty("单位")
    private String assetsUnitName;


    @ExcelProperty("库位信息")
    private String areaInfo;

    @ExcelProperty("部门代码")
    private String departmentCode;


    @ExcelProperty("当前库存")
    private String nowRepertory;


}
