package com.yzm.property.materials.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yzm.framework.base.BaseEntity;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

@Data
public class ProductionPlanExcel {

    private String productionArea;  // 生产区域

    private String carModel;  // 车种

    private String productNo;  // 品番

    private String designChange;  // 设变

    private String batchNo;  // 批号

    private String quantity;  // 数量

    private String remarks;  // 备注

    private String issueDate;  // 发料日

    private String deliveryDate;  // 纳期


}
