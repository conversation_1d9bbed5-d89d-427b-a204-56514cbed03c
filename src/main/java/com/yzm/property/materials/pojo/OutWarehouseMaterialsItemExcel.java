package com.yzm.property.materials.pojo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.yzm.framework.base.BaseEntity;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;

@Data
public class OutWarehouseMaterialsItemExcel {

    @ExcelProperty("记录编号")
    @ColumnWidth(30)
    private String recordNumber; // 记录编号

    @ExcelProperty("材料编号")
    @ColumnWidth(30)
    private String assetsNumber; // 材料编号

    @ExcelProperty("材料名称")
    @ColumnWidth(30)
    private String assetsName; // 材料名称

    @ExcelProperty("材料单位")
    @ColumnWidth(30)
    private String assetsUnitName; // 材料单位

    @ExcelProperty("材料条码")
    @ColumnWidth(30)
    private String assetsCode; // 材料条码

    @ExcelProperty("材料RFID")
    @ColumnWidth(30)
    private String assetsRfid; // 材料RFID

    @ExcelProperty("数量")
    @ColumnWidth(30)
    private BigDecimal consumableNum; // 数量

    @ExcelProperty("库位")
    @ColumnWidth(30)
    private String areaInfo; // 库位

    @ExcelProperty("操作单号")
    @ColumnWidth(30)
    private String operationNumber; // 操作单号

    @ExcelProperty("材料批号")
    @ColumnWidth(30)
    private String batchNumber; // 材料批号

    @ExcelProperty("部门代码")
    @ColumnWidth(30)
    private String departmentCode; // 部门代码

    @ExcelProperty("批号")
    @ColumnWidth(30)
    private String lotNo; // 批号

    @ExcelProperty("品番")
    @ColumnWidth(30)
    private String productNo; // 品番

    @ExcelProperty("发料指示书号")
    @ColumnWidth(30)
    private String kitNo; // 发料指示书号

    @ExcelProperty("贸易方式")
    @ColumnWidth(30)
    private String modeOfTrade; // 贸易方式

    @ExcelProperty("发料人")
    @ColumnWidth(30)
    private String flr; // 发料人

    @ExcelProperty("发料时间")
    @ColumnWidth(30)
    private String time; // 发料时间

    @ExcelProperty("是否替代")
    @ColumnWidth(30)
    private String replaceInfo; // 是否替代
    @ExcelProperty("存在差异")
    @ColumnWidth(30)
    private String difference; // 存在差异
}
