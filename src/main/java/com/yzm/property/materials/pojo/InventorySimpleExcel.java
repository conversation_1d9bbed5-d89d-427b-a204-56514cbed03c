package com.yzm.property.materials.pojo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class InventorySimpleExcel {
    @ExcelProperty(value = "材料编号", index = 0)
    private String assetsNumber; // 材料编号

    @ExcelProperty(value = "材料名称", index = 1)
    private String assetsName; // 材料名称

    @ExcelProperty(value = "材料单位", index = 2)
    private String assetsUnitName; // 材料单位

    @ExcelProperty(value = "材料条码", index = 3)
    private String assetsCode; // 材料条码

    @ExcelProperty(value = "当前库存", index = 4)
    private BigDecimal nowRepertorySum; // 当前库存

    @ExcelProperty(value = "部门代码", index = 5)
    private String departmentCode; // 库存仓库

    @ExcelProperty(value = "库位", index = 6)
    private String areaInfo; // 库位

    @ExcelProperty(value = "盘点数量", index = 7)
    private BigDecimal resultAmountSum; // 盘点数量

    @ExcelProperty(value = "盘盈", index = 8)
    private BigDecimal profitSum; // 盘盈

    @ExcelProperty(value = "盘亏", index = 9)
    private BigDecimal lossSum; // 盘亏

    @ExcelProperty(value = "盘点状态", index = 10)
    private String inventoryStatus; // 盘点状态

    @ExcelProperty(value = "盘点编码", index = 11)
    private String inventoryNumber; // 盘点编码
//
//    @ExcelProperty(value = "贸易方式", index = 12)
//    private String modeOfTrade; // 贸易方式
//
//    @ExcelProperty(value = "来料日期", index = 13)
//    private Date dateArrival; // 来料日期
//
//    @ExcelProperty(value = "部门代码", index = 14)
//    private String departmentCode; // 部门代码
//
//    @ExcelProperty(value = "材料批号", index = 15)
//    private String batchNumber; // 材料批号



}
