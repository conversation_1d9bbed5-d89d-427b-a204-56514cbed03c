package com.yzm.property.materials.pojo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class OutWarehouseMaterialsExcel {
    @ExcelProperty("架位")
    @ColumnWidth(30)
    private String areaInfo; // 库位


    @ExcelProperty("材料编码")
    @ColumnWidth(30)
    private String assetsNumber; // 材料编号


    @ExcelProperty("材料名称")
    @ColumnWidth(30)
    private String assetsName; // 材料名称


    @ExcelProperty("数量")
    @ColumnWidth(30)
    private BigDecimal consumableNum; // 数量

    @ExcelProperty("材料单位")
    @ColumnWidth(30)
    private String assetsUnitName; // 材料单位


    @ExcelProperty("贸易方式")
    @ColumnWidth(30)
    private String modeOfTrade; // 贸易方式


    @ExcelProperty("是否替代")
    @ColumnWidth(30)
    private String replaceInfo; // 是否替代
    @ExcelProperty("存在差异")
    @ColumnWidth(30)
    private String difference; // 存在差异


}
