package com.yzm.property.materials.pojo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class MaterialsRepertoryExcel {


    @ExcelProperty("入库编号")
    private String operationNumber;

    @ExcelProperty("批号")
    private String batchNumber;

    @ExcelProperty("材料编号")
    private String assetsNumber;

    @ExcelProperty("材料名称")
    private String assetsName;

    @ExcelProperty("单位")
    private String assetsUnitName;

    @ExcelProperty("材料条码")
    private String assetsCode;

    @ExcelProperty("库位信息")
    private String areaInfo;

    @ExcelProperty("部门代码")
    private String departmentCode;

    @ExcelProperty("贸易方式")
    private String modeOfTrade;


    @ExcelProperty("当前库存")
    private String nowRepertory;

    @ExcelProperty("来料日期")
    @DateTimeFormat("yyyy-MM-dd")
    private Date dateArrival;

    @ExcelProperty("操作时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ExcelProperty("备注")
    private String custom1;

}
