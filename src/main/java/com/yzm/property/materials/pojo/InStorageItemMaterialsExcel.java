package com.yzm.property.materials.pojo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yzm.framework.base.BaseEntity;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 计划入库
 */
@Data
public class InStorageItemMaterialsExcel  {

    @ExcelProperty("记录编号")
    @ColumnWidth(30)
    private String recordNumber; //记录编号关联主表

    @ExcelProperty("批次号")
    @ColumnWidth(30)
    private String batchNumber; //批次号


    @ExcelProperty("材料编号")
    @ColumnWidth(30)
    private String assetsNumber; //材料编号

    @ExcelProperty("材料名称")
    @ColumnWidth(30)
    private String assetsName; //材料名称

    @ExcelProperty("材料单位")
    @ColumnWidth(30)
    private String assetsUnitName; //材料单位
//
//    @ExcelProperty("材料条码")
//    @ColumnWidth(30)
//    private String assetsCode; //材料条码

    @ExcelProperty("发票号码")
    @ColumnWidth(30)
    private String invoiceNumber; //发票号码

    @ExcelProperty("贸易方式")
    @ColumnWidth(30)
    private String modeOfTrade; //贸易方式

    @ExcelProperty("入库数量")
    @ColumnWidth(30)
    private BigDecimal inStorageCount; //入库数量

    @ExcelProperty("部门代码")
    @ColumnWidth(30)
    private String departmentCode; //部门代码

    @ExcelProperty("来料日期")
    @ColumnWidth(30)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat("yyyy-MM-dd")
    private Date dateArrival; //来料日期

    @ExcelProperty("库位名称")
    @ColumnWidth(30)
    private String storageArea; //库位名称

    @ExcelProperty("入库人")
    @ColumnWidth(30)
    private String inStoragePeople; //入库人

    @ExcelProperty("审核人")
    @ColumnWidth(30)
    private String auditor; //入库人

    @ExcelProperty("入库时间")
    @ColumnWidth(30)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat("yyyy-MM-dd")
    private Date inStorageDate; //入库时间

    @ExcelProperty("记录原由")
    @ColumnWidth(30)
    private String inStorageReson; //记录原由
}
