package com.yzm.property.materials.pojo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Transient;
import java.math.BigDecimal;

@Data
public class MaterialsInfoBomExcel {



    @ExcelProperty(value = "产品编号", index = 0)

    private String assetsNumber; //编号
    @ExcelProperty(value = "部品名称", index = 1)
    private String assetsName; //部品名称
    @ExcelProperty(value = "部品数量", index = 2)
    private BigDecimal dosage; //用量
    @ExcelProperty(value = "部品单位", index = 3)
    private String assetsUnitName; //部品单位
    @ExcelProperty(value = "集装箱号", index = 4)
    private String containerNumber; //集装箱号
    @ExcelProperty(value = "发票号码", index = 5)
    private String invoiceNumber; //发票号码
    @ExcelProperty(value = "贸易方式", index = 6)
    private String modeOfTrade;//贸易方式
    @ExcelProperty(value = "PO号码", index = 7)
    private String poNo;//PO号码




}
