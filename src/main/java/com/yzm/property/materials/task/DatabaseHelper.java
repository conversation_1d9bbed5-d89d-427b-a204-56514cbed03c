package com.yzm.property.materials.task;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.yzm.common.utils.PDFTemplateUtil;
import com.yzm.common.utils.ShiroUtils;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseEntity;
import com.yzm.framework.freemark.DictUtil;
import com.yzm.framework.freemark.Global;
import com.yzm.property.basis.utils.BeanMapUtils;
import com.yzm.property.materials.criteria.*;
import com.yzm.property.materials.entity.*;
import com.yzm.property.materials.pojo.*;
import com.yzm.property.materials.repository.*;
import com.yzm.property.materials.service.*;
import com.yzm.property.materials.utils.EmailSender;
import lombok.SneakyThrows;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RestController;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Month;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

@RestController
@Component
public class DatabaseHelper {
    private static Logger log = LoggerFactory.getLogger(DatabaseHelper.class);

    @Value("${sqlserver.url}")
    private String URL;
    @Value("${sqlserver.username}")
    private String USER;
    @Value("${sqlserver.password}")
    private String PASSWORD;
    @Value("${sqlserver.storeIssueTable}")
    private String storeIssueTable;


    @Autowired
    private IssueTempDtlRepository issueTempDtlRepository;
    @Autowired
    private IssueTempDtlService issueTempDtlService;
    @Autowired
    private IssueTempDtlItemRepository issueTempDtlItemRepository;
    @Autowired
    private MaterialsInfoRepository materialsInfoRepository;
    @Autowired
    private MaterialsInfoService materialsInfoService;
    @Autowired
    private DelinquentMaterialService delinquentMaterialService;
    @Autowired
    private OutWarehouseService outWarehouseService;
    @Autowired
    private OutWarehouseItemService outWarehouseItemService;
    @Autowired
    private InStorageService inStorageService;
    @Autowired
    private InStorageItemService inStorageItemService;
    @Autowired
    private MaterialsInfoBomService materialsInfoBomService;

    @Autowired
    private MaterialsListService materialsListService;

    @Autowired
    private MaterialsRepertoryRepository materialsRepertoryRepository;




    /**
     * 欠料任务
     */
//    @Scheduled(fixedRate = 9999999999L) // 每天凌晨0点0分0秒执行
    @Scheduled(cron = "0 0 0 * * ?") // 每天0点执行
//    @Scheduled(cron = "0 0 * * * ?") // 每小时的第0分钟执行
//    @Scheduled(cron = "0 0 * * * ?") // 每小时的第0分钟执行
//    @Scheduled(fixedRate = 9999999999L) // 每天凌晨0点0分0秒执行
    public void delinquentMaterial() {

        log.error("-----------欠料邮件运行");
        List<DelinquentMaterial> sendList = new ArrayList<>();
//        List<DelinquentMaterial> dateListMap = delinquentMaterialService.calculateMaterialShortage();
        List<DelinquentMaterial> dateListMap = delinquentMaterialService.calculateMaterialShortageNew();
        log.error("-----------欠料邮件运行数量" + dateListMap.size());

        for (DelinquentMaterial delinquentMaterial : dateListMap) {
            try {
                DelinquentMaterial delinquentMaterial1 = delinquentMaterialService.findByAssetsNumberAndLotNo(delinquentMaterial.getAssetsNumber(), delinquentMaterial.getLotNo());
                if (delinquentMaterial1 == null) {
                    sendList.add(delinquentMaterial);
                    delinquentMaterialService.save(delinquentMaterial);
                }
            } catch (Exception e) {
                log.error("-----------多条信息" + delinquentMaterial.getAssetsNumber() + "=====" + delinquentMaterial.getLotNo());
                continue;
            }

        }

        if (sendList.size() > 0) {
            String recipients_config = Global.getConfig("RECIPIENTS_CONFIG");
            String recipient = "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>";

            try {
                log.error("-----------邮件发送");

                EmailSender.sendEmail(sendList, recipients_config);

            } catch (Exception e) {
                log.error("-----------邮件发送失败");
            }

        }
    }


//    @Scheduled(fixedRate = 9999999999L) // 每天凌晨0点0分0秒执行

    @Scheduled(cron = "0 0 1 * * ?") // 每天凌晨 1 点执行
    public void data() {

        try {
            issueTempDtlService.synchronizationData();
        } catch (Exception e) {
            e.printStackTrace();
        }

        try {
            issueTempDtlService.synchronizationData3();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Autowired
    DictUtil dictUtil;

    @Autowired
    ProductionPlanService productionPlanService;
    @Autowired
    ProductionPlanBomRepository productionPlanBomRepository;

    // 转换 LocalDateTime 到 Date
    public static java.util.Date convertToDate(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }


    @Scheduled(cron = "0 0 * * * ?")
    public void readExcel() {
        String recipientsConfig = Global.getConfig("READ_FILE_URL");
        String directoryPath = recipientsConfig;  // Excel文件所在文件夹的路径
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy/MM/dd");
        int currentYear = Calendar.getInstance().get(Calendar.YEAR);

        try {
            // 获取指定路径下的所有 Excel 文件
            File folder = new File(directoryPath);

//            // 增强的文件夹可访问性检查（支持网络路径）
//            if (!isFolderAccessible(folder)) {
//                log.error("指定路径无效或无法访问: " + directoryPath);
//                return;
//            }

            // 调用递归方法获取所有 Excel 文件
            List<File> excelFiles = listExcelFilesRecursively(folder);

            if (excelFiles.isEmpty()) {
                log.warn("指定路径没有找到Excel文件: " + directoryPath);
                return;
            }

            for (File file : excelFiles) {
                try {
                    log.info("开始读取文件: {}", file.getName());
                    List<ProductionPlanExcel> list = new ArrayList<>();

                    // 使用try-with-resources确保资源释放
                    EasyExcel.read(file, ProductionPlanExcel.class, new AnalysisEventListener<ProductionPlanExcel>() {
                        @Override
                        public void invoke(ProductionPlanExcel data, AnalysisContext context) {
                            list.add(data);
                        }

                        @Override
                        public void doAfterAllAnalysed(AnalysisContext context) {
                            processExcelData(list, simpleDateFormat, currentYear);
                            log.info("文件 {} 数据读取完成，共处理 {} 条记录", file.getName(), list.size());
                        }
                    }).sheet().headRowNumber(4).doRead();

                } catch (Exception e) {
                    log.error("读取文件 {} 时发生错误: {}", file.getName(), e.getMessage(), e);
                }
            }
        } catch (Exception e) {
            log.error("处理Excel文件时发生全局错误: {}", e.getMessage(), e);
        }
    }

    // 增强的文件夹可访问性检查方法
    private boolean isFolderAccessible(File folder) {
        try {
            // 对于网络路径，需要额外检查
            if (folder.getPath().startsWith("\\\\")) {
                // 尝试列出文件来验证网络路径可访问性
                File[] files = folder.listFiles();
                return files != null || folder.isDirectory();
            }
            return folder.exists() && folder.isDirectory();
        } catch (SecurityException e) {
            log.error("访问文件夹 {} 时权限不足: {}", folder.getPath(), e.getMessage());
            return false;
        }
    }

    // 递归查找Excel文件的方法（返回List更灵活）
    private List<File> listExcelFilesRecursively(File folder) {
        List<File> excelFiles = new ArrayList<>();
        try {
            File[] files = folder.listFiles();
            if (files == null) return excelFiles;

            for (File file : files) {
                if (file.isDirectory()) {
                    excelFiles.addAll(listExcelFilesRecursively(file));
                } else if (isExcelFile(file)) {
                    excelFiles.add(file);
                }
            }
        } catch (Exception e) {
            log.error("遍历文件夹 {} 时发生错误: {}", folder.getPath(), e.getMessage());
        }
        return excelFiles;
    }

    // 判断是否为Excel文件
    private boolean isExcelFile(File file) {
        String name = file.getName().toLowerCase();
        return name.endsWith(".xlsx") || name.endsWith(".xls");
    }

    // 提取出的数据处理方法
    private void processExcelData(List<ProductionPlanExcel> list, SimpleDateFormat simpleDateFormat, int currentYear) {
        for (ProductionPlanExcel excelData : list) {
            try {
                if (StringUtils.isEmpty(excelData.getBatchNo()) ||
                        StringUtils.isEmpty(excelData.getDeliveryDate()) ||
                        StringUtils.isEmpty(excelData.getQuantity())) {
                    continue;
                }

                // 检查批次号是否已存在
                if (productionPlanService.findByBatchNo(excelData.getBatchNo()) != null) {
                    continue;
                }

                // 创建生产计划
                ProductionPlan productionPlan = new ProductionPlan();
                BeanUtils.copyProperties(excelData, productionPlan);

                // 设置日期
                productionPlan.setDeliveryDate(simpleDateFormat.parse(currentYear + "/" + excelData.getDeliveryDate()));
                if (!StringUtils.isEmpty(excelData.getIssueDate())) {
                    productionPlan.setIssueDate(simpleDateFormat.parse(currentYear + "/" + excelData.getIssueDate()));
                }

                // 设置数量
                productionPlan.setQuantity(new BigDecimal(excelData.getQuantity()));
                productionPlanService.save(productionPlan);

                // 处理BOM信息
                processBomInfo(productionPlan);

            } catch (Exception e) {
                log.error("处理Excel记录时发生错误，批次号: {}: {}",
                        excelData.getBatchNo(), e.getMessage(), e);
            }
        }
    }

    // 处理BOM信息的方法
    private void processBomInfo(ProductionPlan productionPlan) {
        List<BomMst> materialsInfoBom = materialsInfoService.getMaterialsInfoBom(
                productionPlan.getProductNo(), "983C");

        for (BomMst bomMst : materialsInfoBom) {
            try {
                ProductionPlanBom productionPlanBom = new ProductionPlanBom();
                BeanUtils.copyProperties(productionPlan, productionPlanBom);
                productionPlanBom.setId(null);
                productionPlanBom.setBomQty(productionPlan.getQuantity().multiply(bomMst.getBomQty()));
                productionPlanBom.setBomUnit(bomMst.getBomUnit());
                productionPlanBom.setMaterialNo(bomMst.getMaterialNo().trim());

                List<String> materialsInfoData = materialsInfoService.getMaterialsInfoData(
                        productionPlanBom.getMaterialNo());
                if (!materialsInfoData.isEmpty()) {
                    productionPlanBom.setMaterialNm(materialsInfoData.get(0));
                }

                productionPlanBom.setSectionC(bomMst.getSectionC());
                productionPlanBomRepository.save(productionPlanBom);
            } catch (Exception e) {
                log.error("处理BOM信息时发生错误，物料号: {}: {}",
                        bomMst.getMaterialNo(), e.getMessage(), e);
            }
        }
    }



    //    @Scheduled(fixedRate = 9999999999L) // 每天凌晨0点0分0秒执行

//    public void readExcel() {
//        String recipientsConfig = Global.getConfig("READ_FILE_URL");
//        String directoryPath = recipientsConfig;  // Excel文件所在文件夹的路径
//        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy/MM/dd");
//
//        // 获取指定路径下的所有 Excel 文件
//        File folder = new File(directoryPath);
//        int currentYear = Calendar.getInstance().get(Calendar.YEAR);
//
//        // 如果文件夹存在且为目录，则递归查找文件
//        if (folder.exists() && folder.isDirectory()) {
//            // 调用递归方法获取所有 Excel 文件
//            File[] files = listExcelFiles(folder);
//
//            if (files != null && files.length > 0) {
//                for (File file : files) {
//                    log.info("开始读取文件: " + file.getName());
//                    List<ProductionPlanExcel> list = new ArrayList<>();
//                    // 调用EasyExcel读取每个文件
//                    EasyExcel.read(file, ProductionPlanExcel.class, new AnalysisEventListener<ProductionPlanExcel>() {
//                                @Override
//                                public void invoke(ProductionPlanExcel data, AnalysisContext context) {
//                                    // 这里可以处理每一行的数据
//                                    list.add(data);
//                                }
//
//                                @SneakyThrows
//                                @Override
//                                public void doAfterAllAnalysed(AnalysisContext context) {
//                                    for (ProductionPlanExcel productionPlanExcel : list) {
//                                        if (StringUtils.isEmpty(productionPlanExcel.getBatchNo())) {
//                                            continue;
//                                        }
//                                        ProductionPlan productionPlanOld = productionPlanService.findByBatchNo(productionPlanExcel.getBatchNo());
//                                        if (productionPlanOld != null) {
//                                            continue;
//                                        }
//                                        ProductionPlan productionPlan = new ProductionPlan();
//                                        BeanUtils.copyProperties(productionPlanExcel, productionPlan);
//                                        if (!StringUtils.isEmpty(productionPlanExcel.getDeliveryDate())) {
//                                            productionPlan.setDeliveryDate(simpleDateFormat.parse(currentYear + "/" + productionPlanExcel.getDeliveryDate()));
//                                        } else {
//                                            continue;
//                                        }
//                                        if (!StringUtils.isEmpty(productionPlanExcel.getIssueDate())) {
//                                            productionPlan.setIssueDate(simpleDateFormat.parse(currentYear + "/" + productionPlanExcel.getIssueDate()));
//                                        }
//                                        if (StringUtils.isEmpty(productionPlanExcel.getQuantity())) {
//                                            continue;
//                                        }
//                                        productionPlan.setQuantity(new BigDecimal(productionPlanExcel.getQuantity()));
//                                        productionPlanService.save(productionPlan);
//
//
//                                        List<BomMst> materialsInfoBom = materialsInfoService.getMaterialsInfoBom(productionPlan.getProductNo(), "983C");
//                                        for (BomMst bomMst : materialsInfoBom) {
//                                            ProductionPlanBom productionPlanBom = new ProductionPlanBom();
//                                            BeanUtils.copyProperties(productionPlan, productionPlanBom);
//                                            productionPlanBom.setId(null);
//                                            productionPlanBom.setBomQty(productionPlan.getQuantity().multiply(bomMst.getBomQty()));
//                                            productionPlanBom.setBomUnit(bomMst.getBomUnit());
//                                            productionPlanBom.setMaterialNo(bomMst.getMaterialNo().trim());
//                                            List<String> materialsInfoData = materialsInfoService.getMaterialsInfoData(productionPlanBom.getMaterialNo());
//                                            if (materialsInfoData.size() > 0) {
//                                                productionPlanBom.setMaterialNm(materialsInfoData.get(0));
//                                            }
//                                            productionPlanBom.setSectionC(bomMst.getSectionC());
//                                            productionPlanBomRepository.save(productionPlanBom);
//                                        }
//
//                                    }
//                                    // 在所有数据读取完后执行的操作
//                                    log.info("文件 " + file.getName() + " 数据读取完成");
//                                }
//                            }).sheet().headRowNumber(4)  // 设定第四行为表头
//                            .doRead();  // 默认读取第一个sheet
//                }
//            } else {
//                log.error("指定路径没有 Excel 文件！");
//            }
//        } else {
//            log.error("指定路径无效或不是目录！");
//        }
//    }

    // 递归方法，遍历目录及其子目录中的所有 Excel 文件
    private File[] listExcelFiles(File folder) {
        // 获取文件夹下的所有文件和子目录
        File[] files = folder.listFiles();
        if (files == null) {
            return new File[0]; // 如果读取失败，返回空数组
        }

        // 用来存储所有符合条件的 Excel 文件
        List<File> excelFiles = new ArrayList<>();
        for (File file : files) {
            if (file.isDirectory()) {
                // 如果是目录，则递归调用
                excelFiles.addAll(Arrays.asList(listExcelFiles(file)));
            } else if (file.getName().endsWith(".xlsx") || file.getName().endsWith(".xls")) {
                // 如果是 Excel 文件，加入结果列表
                excelFiles.add(file);
            }
        }
        return excelFiles.toArray(new File[0]); // 转换为数组并返回
    }


    @Scheduled(cron = "0 59 23 * * ?") // 每天晚上 23:59 执行
//    @Scheduled(fixedRate = 9999999999L) // 每天凌晨0点0分0秒执行

    public void exportMonthlyExcel() throws Exception {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        ByteArrayOutputStream baos = null;
        OutputStream out = null;
        try {
            // 这里假设需要获取的 ID 可以从数据库或其他配置中确定
            // 获取当前日期
            LocalDate today = LocalDate.now();

            // 获取上个月的第一天
//            LocalDate firstDayOfLastMonth = today.minusMonths(1).withDayOfMonth(1);
//            LocalDateTime firstDayOfLastMonthDateTime = firstDayOfLastMonth.atStartOfDay();  // 设置时分秒为 00:00:00
//
//            // 获取上个月的最后一天
//            LocalDate lastDayOfLastMonth = today.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
//            LocalDateTime lastDayOfLastMonthDateTime = lastDayOfLastMonth.atTime(23, 59, 59);  // 设置时分秒为 23:59:59
            // 获取今天的日期字符串（格式：yyyyMMdd）
            String currentDateStr = today.format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            // 获取今天的起始时间（00:00:00）和结束时间（23:59:59）
            LocalDateTime startOfDay = today.atStartOfDay();  // 设置时分秒为 00:00:00
            LocalDateTime endOfDay = today.atTime(23, 59, 59);  // 设置时分秒为 23:59:59


            // 将 LocalDateTime 转换为 Date
            java.util.Date firstDayOfLastMonthDate = convertToDate(startOfDay);
            java.util.Date lastDayOfLastMonthDate = convertToDate(endOfDay);
            OutWarehouseCriteria outWarehouseCriteria = new OutWarehouseCriteria();
            outWarehouseCriteria.setStartTime(firstDayOfLastMonthDate);
            outWarehouseCriteria.setEndTime(lastDayOfLastMonthDate);
            Map<String, List<OutWarehouseMaterialsEntity>> map = new HashMap<>();
            List<OutWarehouseMaterialsEntity> all1 = outWarehouseService.findAll(outWarehouseCriteria);
            for (OutWarehouseMaterialsEntity outWarehouseMaterialsEntity : all1) {

                String key = (outWarehouseMaterialsEntity.getKitNo() == null) ? "无" : outWarehouseMaterialsEntity.getKitNo();
                key = key + "_" + outWarehouseMaterialsEntity.getProductNo() + "_" + outWarehouseMaterialsEntity.getLotNo();
                // 使用 computeIfAbsent 来简化 map 操作
                map.computeIfAbsent(key, k -> new ArrayList<>()).add(outWarehouseMaterialsEntity);
            }
            for (Map.Entry<String, List<OutWarehouseMaterialsEntity>> stringListEntry : map.entrySet()) {
                String key = stringListEntry.getKey();
                String[] value = key.split("_");

                List<OutWarehouseMaterialsItem> issueTempDtl = outWarehouseItemService.findByKitNo(value[0]);
                if (issueTempDtl.size() == 0) {
                    continue;
                }
                OutWarehouseMaterialsEntity outWarehouseMaterialsEntity = stringListEntry.getValue().get(0);

                List<OutWarehouseMaterialsItemExcel> list = new ArrayList<>();
                boolean chayi = false;
                for (OutWarehouseMaterialsItem outWarehouseMaterialsItem : issueTempDtl) {
                    if (outWarehouseMaterialsItem.getRecordNumber().indexOf("QTCK") != -1) {
                        continue;
                    }
                    OutWarehouseMaterialsItemExcel outWarehouseMaterialsItemExcel = new OutWarehouseMaterialsItemExcel();
                    BeanUtils.copyProperties(outWarehouseMaterialsItem, outWarehouseMaterialsItemExcel);
                    if (StringUtils.isEmpty(outWarehouseMaterialsItem.getModeOfTrade())) {
                        outWarehouseMaterialsItemExcel.setModeOfTrade(dictUtil.getLabel("mode_of_trade_type", "1"));
                    } else {
                        outWarehouseMaterialsItemExcel.setModeOfTrade(dictUtil.getLabel("mode_of_trade_type", outWarehouseMaterialsItem.getModeOfTrade().toString()));
                    }
                    if (StringUtils.isNotEmpty(outWarehouseMaterialsItem.getReplaceInfo()) && "是".equals(outWarehouseMaterialsItem.getReplaceInfo())) {
                        outWarehouseMaterialsItemExcel.setDifference("是");
                        chayi = true;
                    } else {
                        outWarehouseMaterialsItemExcel.setDifference("否");
                    }
                    outWarehouseMaterialsItemExcel.setFlr(outWarehouseMaterialsEntity.getUserName());
                    outWarehouseMaterialsItemExcel.setTime(simpleDateFormat.format(outWarehouseMaterialsItem.getCreateTime()));
                    outWarehouseMaterialsItemExcel.setKitNo(outWarehouseMaterialsEntity.getKitNo());
                    outWarehouseMaterialsItemExcel.setProductNo(outWarehouseMaterialsEntity.getProductNo());
                    outWarehouseMaterialsItemExcel.setLotNo(outWarehouseMaterialsEntity.getLotNo());
                    outWarehouseMaterialsItemExcel.setDepartmentCode(outWarehouseMaterialsEntity.getDepartmentCode());
                    list.add(outWarehouseMaterialsItemExcel);
                }
// ======================= 新增：生成汇总数据 =======================
                Map<String, OutWarehouseMaterialsExcel> summaryMap = new HashMap<>();
                for (OutWarehouseMaterialsItemExcel item : list) {
                    String compositeKey = item.getAssetsNumber() + "|" + item.getModeOfTrade();
                    OutWarehouseMaterialsExcel summary = summaryMap.computeIfAbsent(compositeKey, k -> {
                        OutWarehouseMaterialsExcel newSummary = new OutWarehouseMaterialsExcel();
                        newSummary.setAreaInfo(item.getAreaInfo());
                        newSummary.setAssetsNumber(item.getAssetsNumber());
                        newSummary.setAssetsName(item.getAssetsName());
                        newSummary.setAssetsUnitName(item.getAssetsUnitName());
                        newSummary.setModeOfTrade(item.getModeOfTrade());
                        newSummary.setReplaceInfo(item.getReplaceInfo());
                        newSummary.setDifference(item.getDifference());
                        return newSummary;
                    });
                    if ("是".equals(item.getReplaceInfo())) {
                        summary.setReplaceInfo(item.getReplaceInfo());
                    }
                    if ("是".equals(item.getDifference())) {
                        summary.setDifference(item.getDifference());
                    }

                    // 累加数量
                    BigDecimal itemNum = item.getConsumableNum() != null ? item.getConsumableNum() : BigDecimal.ZERO;
                    BigDecimal currentTotal = summary.getConsumableNum() != null ? summary.getConsumableNum() : BigDecimal.ZERO;
                    summary.setConsumableNum(currentTotal.add(itemNum));
                }
                List<OutWarehouseMaterialsExcel> summaryList = new ArrayList<>(summaryMap.values());
                // ======================= 结束新增 =======================


                int year = startOfDay.getYear();
                int month = endOfDay.getMonth().getValue();
                String yearMonth = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy/MM"));
                String recipients_config = Global.getConfig("OUT_FILE_URL");

//                String excelFilePath = "D:" + File.separator + "fileSave" + File.separator + yearMonth;
                if (chayi) {
                    key = key + "_差异";
                }
                String excelFilePath = recipients_config + "/" + year + "/" + month + "/出库单据" + "/";
                File directory = new File(excelFilePath);  // 替换为实际的路径
                if (!directory.exists()) {
                    directory.mkdirs();
                }
                ExcelWriter excelWriter = null;
                try {

                    excelWriter = EasyExcel.write(excelFilePath + key + ".xlsx").build();
                    // 写入汇总数据Sheet
                    WriteSheet summarySheet = EasyExcel.writerSheet("汇总数据")
                            .head(OutWarehouseMaterialsExcel.class)
                            .build();
                    excelWriter.write(summaryList, summarySheet);
                    // 写入原始数据Sheet
                    WriteSheet detailSheet = EasyExcel.writerSheet("出库明细")
                            .head(OutWarehouseMaterialsItemExcel.class)
                            .build();
                    excelWriter.write(list, detailSheet);


                } finally {
                    if (excelWriter != null) {
                        excelWriter.finish();
                    }
                }


//                EasyExcel.write(excelFilePath + key + ".xlsx", OutWarehouseMaterialsItemExcel.class).sheet("出库单据").doWrite(list);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("导出失败：" + e.getMessage());
        } finally {
            if (baos != null) {
                baos.close();
            }
            if (out != null) {
                out.close();
            }
        }
    }

    //    @Scheduled(cron = "0 0 0 * * ?") // 每天凌晨 00:00 执行
//        @Scheduled(fixedRate = 9999999999L) // 每天凌晨0点0分0秒执行
    @Scheduled(cron = "0 59 23 * * ?") // 每天晚上 23:59 执行
    public void exportDailyExcel2() throws Exception {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        ByteArrayOutputStream baos = null;
        OutputStream out = null;
        try {
            // 获取当前日期
            LocalDate today = LocalDate.now();

            // 获取今天的日期字符串（格式：yyyyMMdd）
            String currentDateStr = today.format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            // 获取今天的起始时间（00:00:00）和结束时间（23:59:59）
            LocalDateTime startOfDay = today.atStartOfDay();  // 设置时分秒为 00:00:00
            LocalDateTime endOfDay = today.atTime(23, 59, 59);  // 设置时分秒为 23:59:59

            // 将 LocalDateTime 转换为 java.util.Date
            java.util.Date startOfDayDate = convertToDate(startOfDay);
            java.util.Date endOfDayDate = convertToDate(endOfDay);

            // 设置查询条件
            InStorageCriteria outWarehouseCriteria = new InStorageCriteria();
            outWarehouseCriteria.setStartTime(startOfDayDate);
            outWarehouseCriteria.setEndTime(endOfDayDate);
            List<String> list = new ArrayList<>();
            list.add("0");
            outWarehouseCriteria.setType(list);
            // 查询当天的出库数据
            List<InStorageMaterials> all1 = inStorageService.findAll(outWarehouseCriteria);
//            for (InStorageMaterials outWarehouseMaterialsEntity : all1) {
//                String key = (outWarehouseMaterialsEntity.getKitNo() == null) ? "无" : outWarehouseMaterialsEntity.getKitNo();
//                // 使用 computeIfAbsent 来简化 map 操作
//                map.computeIfAbsent(key, k -> new ArrayList<>()).add(outWarehouseMaterialsEntity);
//            }

            List<InStorageItemMaterialsExcel> inStorageItemMaterialsExcels = new ArrayList<>();
            for (InStorageMaterials inStorageMaterials : all1) {
                List<InStorageItemMaterials> byRecordNumber = inStorageItemService.findByRecordNumber(inStorageMaterials.getRecordNumber());
                for (InStorageItemMaterials inStorageItemMaterials : byRecordNumber) {
                    InStorageItemMaterialsExcel inStorageItemMaterialsExcel = new InStorageItemMaterialsExcel();
                    BeanUtils.copyProperties(inStorageItemMaterials, inStorageItemMaterialsExcel);
                    // 设置交易方式
                    if (StringUtils.isEmpty(inStorageItemMaterials.getModeOfTrade())) {
                        inStorageItemMaterialsExcel.setModeOfTrade(dictUtil.getLabel("mode_of_trade_type", "1"));
                    } else {
                        inStorageItemMaterialsExcel.setModeOfTrade(dictUtil.getLabel("mode_of_trade_type", inStorageItemMaterials.getModeOfTrade().toString()));
                    }
                    if (!StringUtils.isEmpty(inStorageMaterials.getInStorageReson())) {
                        inStorageItemMaterialsExcel.setInStorageReson(dictUtil.getLabel("in_storage_type", inStorageMaterials.getInStorageReson()));
                    }
                    inStorageItemMaterialsExcel.setInStoragePeople(inStorageMaterials.getInStoragePeople());
                    inStorageItemMaterialsExcel.setInStorageDate(inStorageMaterials.getInStorageDate());
                    inStorageItemMaterialsExcels.add(inStorageItemMaterialsExcel);
                }
            }
            // 生成保存路径
            String recipientsConfig = Global.getConfig("OUT_FILE_URL");
            int year = startOfDay.getYear();
            int month = startOfDay.getMonth().getValue();

            // 文件保存路径：使用当天的日期创建文件夹
            String excelFilePath = recipientsConfig + "/" + year + "/" + month + "/计划入库" + "/";

//            String excelFilePath = recipientsConfig + "/计划入库/" + year + "/" + month + "/";

            // 创建文件夹
            File directory = new File(excelFilePath);
            if (!directory.exists()) {
                directory.mkdirs();
            }

            // 使用 EasyExcel 导出数据
            EasyExcel.write(excelFilePath + currentDateStr + "_计划入库.xlsx", InStorageItemMaterialsExcel.class)
                    .sheet("计划入库")
                    .doWrite(inStorageItemMaterialsExcels);
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("导出失败：" + e.getMessage());
        } finally {
            // 关闭流
            if (baos != null) {
                baos.close();
            }
            if (out != null) {
                out.close();
            }
        }
    }

    @Scheduled(cron = "0 59 23 * * ?") // 每天晚上 23:59 执行
//    @Scheduled(fixedRate = 9999999999L) // 每天凌晨0点0分0秒执行
    public void exportDailyExcel3() throws Exception {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        ByteArrayOutputStream baos = null;
        OutputStream out = null;
        try {
            // 获取当前日期
            LocalDate today = LocalDate.now();

            // 获取今天的日期字符串（格式：yyyyMMdd）
            String currentDateStr = today.format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            // 获取今天的起始时间（00:00:00）和结束时间（23:59:59）
            LocalDateTime startOfDay = today.atStartOfDay();  // 设置时分秒为 00:00:00
            LocalDateTime endOfDay = today.atTime(23, 59, 59);  // 设置时分秒为 23:59:59

            // 将 LocalDateTime 转换为 java.util.Date
            java.util.Date startOfDayDate = convertToDate(startOfDay);
            java.util.Date endOfDayDate = convertToDate(endOfDay);

            // 设置查询条件
            InStorageItemCriteria outWarehouseCriteria = new InStorageItemCriteria();
            outWarehouseCriteria.setStartTime(startOfDayDate);
            outWarehouseCriteria.setEndTime(endOfDayDate);
            // 查询当天的出库数据
//            List<InStorageMaterials> all1 = inStorageService.findAll(outWarehouseCriteria);
//            for (InStorageMaterials outWarehouseMaterialsEntity : all1) {
//                String key = (outWarehouseMaterialsEntity.getKitNo() == null) ? "无" : outWarehouseMaterialsEntity.getKitNo();
//                // 使用 computeIfAbsent 来简化 map 操作
//                map.computeIfAbsent(key, k -> new ArrayList<>()).add(outWarehouseMaterialsEntity);
//            }

            List<InStorageItemMaterials> all = inStorageItemService.findAll(outWarehouseCriteria);
            List<InStorageItemMaterialsExcel> inStorageItemMaterialsExcels = new ArrayList<>();
            for (InStorageItemMaterials inStorageItemMaterials : all) {
                InStorageMaterials inStorageMaterials = inStorageService.findByRecordNumber(inStorageItemMaterials.getRecordNumber());
                if (inStorageMaterials == null) {
                    continue;
                }
                if ("0".equals(inStorageMaterials.getInStorageReson())) {
                    continue;
                }
                InStorageItemMaterialsExcel inStorageItemMaterialsExcel = new InStorageItemMaterialsExcel();
                BeanUtils.copyProperties(inStorageItemMaterials, inStorageItemMaterialsExcel);
                // 设置交易方式
                if (StringUtils.isEmpty(inStorageItemMaterials.getModeOfTrade())) {
                    inStorageItemMaterialsExcel.setModeOfTrade(dictUtil.getLabel("mode_of_trade_type", "1"));
                } else {
                    inStorageItemMaterialsExcel.setModeOfTrade(dictUtil.getLabel("mode_of_trade_type", inStorageItemMaterials.getModeOfTrade().toString()));
                }
                inStorageItemMaterialsExcel.setInStoragePeople(inStorageMaterials.getInStoragePeople());
                inStorageItemMaterialsExcel.setInStorageDate(inStorageMaterials.getInStorageDate());
                inStorageItemMaterialsExcel.setAuditor(inStorageMaterials.getAuditor());
                inStorageItemMaterialsExcels.add(inStorageItemMaterialsExcel);
            }
            // 生成保存路径
            String recipientsConfig = Global.getConfig("OUT_FILE_URL");
            int year = startOfDay.getYear();
            int month = startOfDay.getMonth().getValue();

            // 文件保存路径：使用当天的日期创建文件夹
//            String excelFilePath = recipientsConfig + "/其它入库/" + year + "/" + month + "/";
            String excelFilePath = recipientsConfig + "/" + year + "/" + month + "/其它入库" + "/";

            // 创建文件夹
            File directory = new File(excelFilePath);
            if (!directory.exists()) {
                directory.mkdirs();
            }

            // 使用 EasyExcel 导出数据
            EasyExcel.write(excelFilePath + currentDateStr + "其它入库.xlsx", InStorageItemMaterialsExcel.class)
                    .sheet("其它入库")
                    .doWrite(inStorageItemMaterialsExcels);
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("导出失败：" + e.getMessage());
        } finally {
            // 关闭流
            if (baos != null) {
                baos.close();
            }
            if (out != null) {
                out.close();
            }
        }
    }

    //    @Scheduled(fixedRate = 9999999999L) // 每天凌晨0点0分0秒执行
    @Scheduled(cron = "0 59 23 * * ?") // 每天晚上 23:59 执行
    public void exportDailyExcel4() throws Exception {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        ByteArrayOutputStream baos = null;
        OutputStream out = null;
        try {
            // 获取当前日期
            LocalDate today = LocalDate.now();

            // 获取今天的日期字符串（格式：yyyyMMdd）
            String currentDateStr = today.format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            // 获取今天的起始时间（00:00:00）和结束时间（23:59:59）
            LocalDateTime startOfDay = today.atStartOfDay();  // 设置时分秒为 00:00:00
            LocalDateTime endOfDay = today.atTime(23, 59, 59);  // 设置时分秒为 23:59:59

            // 将 LocalDateTime 转换为 java.util.Date
            java.util.Date startOfDayDate = convertToDate(startOfDay);
            java.util.Date endOfDayDate = convertToDate(endOfDay);

            // 设置查询条件
            InStorageItemCriteria outWarehouseCriteria = new InStorageItemCriteria();
            outWarehouseCriteria.setStartTime(startOfDayDate);
            outWarehouseCriteria.setEndTime(endOfDayDate);
            // 查询当天的出库数据
//            List<InStorageMaterials> all1 = inStorageService.findAll(outWarehouseCriteria);
//            for (InStorageMaterials outWarehouseMaterialsEntity : all1) {
//                String key = (outWarehouseMaterialsEntity.getKitNo() == null) ? "无" : outWarehouseMaterialsEntity.getKitNo();
//                // 使用 computeIfAbsent 来简化 map 操作
//                map.computeIfAbsent(key, k -> new ArrayList<>()).add(outWarehouseMaterialsEntity);
//            }
            MaterialsListCriteria materialsListCriteria = new MaterialsListCriteria();
            materialsListCriteria.setStartTime(startOfDayDate);
            materialsListCriteria.setEndTime(endOfDayDate);
            materialsListCriteria.setReason("4");
            List<MaterialsListEntity> all1 = materialsListService.findAll(materialsListCriteria);
            List<OutWarehouseMaterialsItem> all = outWarehouseItemService.findAll(outWarehouseCriteria);
            Map<String, OutWarehouseMaterialsItem> map = new HashMap<>();
            for (OutWarehouseMaterialsItem outWarehouseMaterialsItem : all) {
                map.put(outWarehouseMaterialsItem.getRecordNumber() + "_" + outWarehouseMaterialsItem.getAssetsNumber(), outWarehouseMaterialsItem);
            }

            List<OutWarehouseMaterialsItemOhterExcel> inStorageItemMaterialsExcels = new ArrayList<>();
            for (MaterialsListEntity inStorageItemMaterials : all1) {
                OutWarehouseMaterialsEntity inStorageMaterials = outWarehouseService.findByRecordNumber(inStorageItemMaterials.getOperationOtherId());
                if (inStorageMaterials == null) {
                    continue;
                }
                if (!"4".equals(inStorageMaterials.getOutReson())) {
                    continue;
                }

                OutWarehouseMaterialsItemOhterExcel inStorageItemMaterialsExcel = new OutWarehouseMaterialsItemOhterExcel();

                BeanUtils.copyProperties(inStorageItemMaterials, inStorageItemMaterialsExcel);
                OutWarehouseMaterialsItem outWarehouseMaterialsItem = map.get(inStorageItemMaterials.getOperationOtherId() + "_" + inStorageItemMaterials.getAssetsNumber());

                if (outWarehouseMaterialsItem != null) {
                    // 安全地调用 getter 方法
                    if (outWarehouseMaterialsItem.getKitNo() != null) {
                        inStorageItemMaterialsExcel.setKitNo(outWarehouseMaterialsItem.getKitNo());
                    }
                    if (outWarehouseMaterialsItem.getProductNo() != null) {
                        inStorageItemMaterialsExcel.setProductNo(outWarehouseMaterialsItem.getProductNo());
                    }
                    if (outWarehouseMaterialsItem.getLotNo() != null) {
                        inStorageItemMaterialsExcel.setLotNo(outWarehouseMaterialsItem.getLotNo());
                    }
                }
                // 设置交易方式
                if (StringUtils.isEmpty(inStorageItemMaterials.getModeOfTrade())) {
                    inStorageItemMaterialsExcel.setModeOfTrade(dictUtil.getLabel("mode_of_trade_type", "1"));
                } else {
                    inStorageItemMaterialsExcel.setModeOfTrade(dictUtil.getLabel("mode_of_trade_type", inStorageItemMaterials.getModeOfTrade().toString()));
                }

                inStorageItemMaterialsExcel.setConsumableNum(inStorageItemMaterials.getNowRepertory());
                inStorageItemMaterialsExcel.setRecordNumber(inStorageMaterials.getRecordNumber());
                inStorageItemMaterialsExcel.setOperationNumber(inStorageItemMaterials.getOperationOtherId());
                inStorageItemMaterialsExcel.setApplicant(inStorageMaterials.getApplicant());
                inStorageItemMaterialsExcel.setAuditor(inStorageMaterials.getAuditor());
                inStorageItemMaterialsExcel.setSucceedDate(simpleDateFormat.format(inStorageItemMaterials.getCreateTime()));
                inStorageItemMaterialsExcels.add(inStorageItemMaterialsExcel);
            }
            // 生成保存路径
            String recipientsConfig = Global.getConfig("OUT_FILE_URL");
            int year = startOfDay.getYear();
            int month = startOfDay.getMonth().getValue();

            // 文件保存路径：使用当天的日期创建文件夹
//            String excelFilePath = recipientsConfig + "/其他出库/" + year + "/" + month + "/";
            String excelFilePath = recipientsConfig + "/" + year + "/" + month + "/其他出库" + "/";

            // 创建文件夹
            File directory = new File(excelFilePath);
            if (!directory.exists()) {
                directory.mkdirs();
            }

            // 使用 EasyExcel 导出数据
            EasyExcel.write(excelFilePath + currentDateStr + "_其他出库.xlsx", OutWarehouseMaterialsItemOhterExcel.class)
                    .sheet("其他出库")
                    .doWrite(inStorageItemMaterialsExcels);
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("导出失败：" + e.getMessage());
        } finally {
            // 关闭流
            if (baos != null) {
                baos.close();
            }
            if (out != null) {
                out.close();
            }
        }
    }

    //
//        public static void main(String[] args) {
//        LocalDate today = LocalDate.now();
//
//        // 获取上个月的第一天
//        LocalDate firstDayOfLastMonth = today.withDayOfMonth(1);
//        LocalDateTime firstDayOfLastMonthDateTime = firstDayOfLastMonth.atStartOfDay();  // 设置时分秒为 00:00:00
//
//        // 获取上个月的最后一天
//        LocalDate lastDayOfLastMonth = today.withDayOfMonth(1);
//        LocalDateTime lastDayOfLastMonthDateTime = lastDayOfLastMonth.atTime(23, 59, 59);  // 设置时分秒为 23:59:59
//    }
//        @Scheduled(cron = "0 0 0 1 * ?")  // 每月1号的凌晨0点触发
//    @Scheduled(cron = "0 0 1 * * ?")  // 每天凌晨12点执行
//    @Scheduled(fixedRate = 9999999999L) // 每天凌晨0点0分0秒执行
    public void exportMonthlyPdf() throws Exception {
        ByteArrayOutputStream baos = null;
        OutputStream out = null;
        try {
            // 这里假设需要获取的 ID 可以从数据库或其他配置中确定
            // 获取当前日期
            LocalDate today = LocalDate.now();

            // 获取上个月的第一天
            LocalDate firstDayOfLastMonth = today.minusMonths(1).withDayOfMonth(1);
            LocalDateTime firstDayOfLastMonthDateTime = firstDayOfLastMonth.atStartOfDay();  // 设置时分秒为 00:00:00

            // 获取上个月的最后一天
            LocalDate lastDayOfLastMonth = today.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
            LocalDateTime lastDayOfLastMonthDateTime = lastDayOfLastMonth.atTime(23, 59, 59);  // 设置时分秒为 23:59:59
            // 将 LocalDateTime 转换为 Date
            java.util.Date firstDayOfLastMonthDate = convertToDate(firstDayOfLastMonthDateTime);
            java.util.Date lastDayOfLastMonthDate = convertToDate(lastDayOfLastMonthDateTime);
            OutWarehouseCriteria outWarehouseCriteria = new OutWarehouseCriteria();
            outWarehouseCriteria.setStartTime(firstDayOfLastMonthDate);
            outWarehouseCriteria.setEndTime(lastDayOfLastMonthDate);
            Map<String, List<OutWarehouseMaterialsItem>> map = new HashMap<>();
            List<OutWarehouseMaterialsItem> all1 = outWarehouseItemService.findAll(outWarehouseCriteria);
            for (OutWarehouseMaterialsItem outWarehouseMaterialsItem : all1) {
                String key = (outWarehouseMaterialsItem.getKitNo() == null) ? "无" : outWarehouseMaterialsItem.getKitNo();

                // 使用 computeIfAbsent 来简化 map 操作
                map.computeIfAbsent(key, k -> new ArrayList<>()).add(outWarehouseMaterialsItem);
            }
            for (Map.Entry<String, List<OutWarehouseMaterialsItem>> stringListEntry : map.entrySet()) {
                String key = stringListEntry.getKey();
                IssueTempDtl issueTempDtl = issueTempDtlService.findByKitNo(key);
                if (issueTempDtl == null) {
                    continue;
                }
                List<OutWarehouseMaterialsItem> value = stringListEntry.getValue();
                OutWarehouseMaterialsItem outWarehouseMaterialsItem1 = value.get(0);
//            outWarehouseService.findByRecordNumber
                //            List<OutWarehouseMaterialsEntity> inStorageList = outWarehouseService.findAll();
//            for (OutWarehouseMaterialsEntity inStorage : inStorageList) {
                Map<String, Object> data = new HashMap<>();
                data.put("title", "预览PDF");
                //申领编号
                data.put("out", issueTempDtl);
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                data.put("outTime", simpleDateFormat.format(issueTempDtl.getPlannedIssueDt()));

//                data.put("outTime", issueTempDtl.getCreateTime());
//                data.put("user", value.get(0).getu);
                data.put("now", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy.MM.dd HH:mm:ss")));
                List<OutWarehouseMaterialsItem> overList = value;
                BigDecimal sum = new BigDecimal(0);
                for (OutWarehouseMaterialsItem outWarehouseMaterialsItem : overList) {
                    sum = sum.add(outWarehouseMaterialsItem.getConsumableNum());
                }
                data.put("detailList", overList);
                data.put("count", sum.toString());

                baos = PDFTemplateUtil.createPDF(data, "出库单.ftl");
                String yearMonth = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy/MM"));
                String excelFilePath = "D:" + File.separator + "fileSave" + File.separator + yearMonth;
//                String excelFilePath = "/Users/<USER>/" + yearMonth;

                // 定义文件路径和名称
                File directory = new File(excelFilePath);  // 替换为实际的路径
                if (!directory.exists()) {
                    directory.mkdirs();
                }
                String fileName = issueTempDtl.getKitNo() + ".pdf";
                File file = new File(directory, fileName);

                // 将 PDF 写入文件
                out = new FileOutputStream(file);
                baos.writeTo(out);
//            }
            }


        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("导出失败：" + e.getMessage());
        } finally {
            if (baos != null) {
                baos.close();
            }
            if (out != null) {
                out.close();
            }
        }
    }


    //        @Scheduled(cron = "0 59 23 * * ?") // 每天晚上 23:59 执行
    @Scheduled(cron = "0 0 0 1 1,4,7,10 ?")  // 每年1/4/7/10月的1号0点执行
    public void expired() throws Exception {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        ByteArrayOutputStream baos = null;
        OutputStream out = null;
        try {
            // 获取当前日期
            LocalDate today = LocalDate.now();

            // 获取今天的日期字符串（格式：yyyyMMdd）
            String currentDateStr = today.format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            // 获取今天的起始时间（00:00:00）和结束时间（23:59:59）
            LocalDateTime startOfDay = today.atStartOfDay();  // 设置时分秒为 00:00:00
            LocalDateTime endOfDay = today.atTime(23, 59, 59);  // 设置时分秒为 23:59:59

            // 将 LocalDateTime 转换为 java.util.Date
            java.util.Date startOfDayDate = convertToDate(startOfDay);
            java.util.Date endOfDayDate = convertToDate(endOfDay);

            // 设置查询条件
            InStorageItemCriteria outWarehouseCriteria = new InStorageItemCriteria();
            outWarehouseCriteria.setStartTime(startOfDayDate);
            outWarehouseCriteria.setEndTime(endOfDayDate);
            List<AssetsListNewExcel> excelDataList = new ArrayList<>(); // 存放要导出的数据
            List<Map<String, Object>> list = materialsRepertoryRepository.groupByAssetsNumber();
            for (Map<String, Object> objectMap : list) {
                String number = Optional.ofNullable(objectMap.get("assets_number")).map(Object::toString).orElse("");
                String assets_name = Optional.ofNullable(objectMap.get("assets_name")).map(Object::toString).orElse("");
                String nowRepertorySum = Optional.ofNullable(objectMap.get("nowRepertorySum")).map(Object::toString).orElse("");
                String department_code = Optional.ofNullable(objectMap.get("department_code")).map(Object::toString).orElse("");
                String location = Optional.ofNullable(objectMap.get("area_info")).map(Object::toString).orElse("");
                List<Map<String, Object>> records = materialsListService.findByAssetsNumberGruopByReason(number);

                boolean allOlderThanSixMonths = true; // 假设所有记录都超过6个月

                for (Map<String, Object> map : records) {
                    MaterialsListEntity entity = BeanMapUtils.mapToBean(map, MaterialsListEntity.class);
                    java.util.Date createTime = entity.getCreateTime(); // 假设是 Date 类型

                    // 计算6个月前的日期
                    Calendar calendar = Calendar.getInstance();
                    calendar.add(Calendar.MONTH, -6);
                    Date sixMonthsAgo = calendar.getTime();

                    // 如果有一条记录不满足条件，则标记为 false
                    if (!createTime.before(sixMonthsAgo)) {
                        allOlderThanSixMonths = false;
                        break; // 只要有一条不满足，直接跳出循环
                    }
                }

                // 如果所有记录都超过6个月，则添加到结果列表
                if (allOlderThanSixMonths) {
                    AssetsListNewExcel rowData = new AssetsListNewExcel();
                    rowData.setAssetsNumber(number); // 列名: 值
                    rowData.setAssetsName(assets_name); // 列名: 值
                    rowData.setDepartmentCode(department_code                                                                                                                                                                                                                                                                                                                                                                                                                                                                       ); // 列名: 值
                    rowData.setNowRepertory(nowRepertorySum); // 列名: 值
                    rowData.setLocation(location);
                    excelDataList.add(rowData);
                }
            }

            // 生成保存路径
            String recipientsConfig = Global.getConfig("OUT_FILE_URL");
            int year = startOfDay.getYear();
            int month = startOfDay.getMonth().getValue();

            // 文件保存路径：使用当天的日期创建文件夹
//            String excelFilePath = recipientsConfig + "/其它入库/" + year + "/" + month + "/";
            String excelFilePath = recipientsConfig + "/" + year + "/" + month + "/胶着品明细" + "/";

            // 创建文件夹
            File directory = new File(excelFilePath);
            if (!directory.exists()) {
                directory.mkdirs();
            }

//            // 使用 EasyExcel 导出数据
            EasyExcel.write(excelFilePath + currentDateStr + "胶着品明细.xlsx", AssetsListNewExcel.class)
                    .sheet("胶着品明细")
                    .doWrite(excelDataList);
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("导出失败：" + e.getMessage());
        } finally {
            // 关闭流
            if (baos != null) {
                baos.close();
            }
            if (out != null) {
                out.close();
            }
        }
    }
}
