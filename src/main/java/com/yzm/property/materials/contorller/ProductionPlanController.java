package com.yzm.property.materials.contorller;

import com.yzm.common.annotation.BussinessLog;
import com.yzm.common.enums.BusinessType;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.materials.criteria.BindingCarCriteria;
import com.yzm.property.materials.entity.BindingCar;
import com.yzm.property.materials.entity.IssueTempDtl;
import com.yzm.property.materials.entity.OutWarehouseMaterialsItem;
import com.yzm.property.materials.repository.BindingCarRepository;
import com.yzm.property.materials.repository.MaterialsInfoRepository;
import com.yzm.property.materials.repository.OutWarehouseItemMaterialsRepository;
import com.yzm.property.materials.service.BindingCarService;
import com.yzm.property.materials.service.IssueTempDtlService;
import com.yzm.property.materials.service.ProductionPlanService;
import com.yzm.property.materials.task.DatabaseHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.*;

@Controller
@RequestMapping("materials/productionPlan")
@Slf4j
public class ProductionPlanController extends BaseController {
    private String urlPrefix = "materials/productionPlan";

    @Autowired
    private ProductionPlanService productionPlanService;
  @Autowired
    private DatabaseHelper databaseHelper;

    /**
     * 新增参数配置
     */
    @GetMapping("/add")
    public String add() {
        return urlPrefix + "/add";
    }

    @GetMapping
    public String client(ModelMap map) {
        return urlPrefix + "/productionPlan";
    }



    /**
     * 删除配置
     */
    @RequestMapping(value = "/del", method = RequestMethod.POST)
    @ResponseBody
    @BussinessLog(businessType = BusinessType.DELETE, title = "删除绑定小车信息")
    public ResponseData delete(Long[] ids) {

        return success();
    }

    /**
     * 修改参数配置
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        return urlPrefix + "/edit";
    }


    /**
     * 材料流水
     */
    @ResponseBody
    @RequestMapping("list")
    public ResponseData getBindingCar(PageBean pageBean, BindingCarCriteria criteria) {
        if (criteria.getStartTime() != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date endTime = criteria.getEndTime();
            Calendar c = Calendar.getInstance();
            c.setTime(endTime);
            c.add(Calendar.DATE, 1);
            criteria.setEndTime(c.getTime());
            System.out.println(criteria);
        }
        Map<String, Object> datas = productionPlanService.findAllByPage(criteria, pageBean.getPagable());
        return success(datas);
    }
    /**
     * 同步数据
     *
     * @return
     */
    @RequestMapping("/synchronizationData")
    @ResponseBody
    public ResponseData synchronizationData() {
//        DatabaseHelper databaseHelper = new DatabaseHelper();
        databaseHelper.readExcel();
        databaseHelper.delinquentMaterial();
        return ResponseData.success();
    }


}
