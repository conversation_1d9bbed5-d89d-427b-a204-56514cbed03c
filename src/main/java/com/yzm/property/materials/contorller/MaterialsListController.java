package com.yzm.property.materials.contorller;

import cn.hutool.core.io.FileUtil;
import com.alibaba.excel.EasyExcel;
import com.yzm.common.annotation.BussinessLog;
import com.yzm.common.enums.BusinessType;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.framework.freemark.DictUtil;
import com.yzm.framework.freemark.Global;
import com.yzm.property.materials.criteria.MaterialsListCriteria;
import com.yzm.property.materials.entity.MaterialsListEntity;
import com.yzm.property.materials.pojo.MaterialsListExcel;
import com.yzm.property.materials.repository.MaterialsInfoRepository;
import com.yzm.property.materials.repository.MaterialsListRepository;
import com.yzm.property.materials.service.MaterialsListService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;

@Controller
@RequestMapping("materials/materialsList")
@Slf4j
public class MaterialsListController extends BaseController {
    private String urlPrefix = "materials/materialsList";

    @Autowired
    private MaterialsListService materialsListService;
    @Autowired
    private MaterialsListRepository materialsListRepository;
    @Autowired
    private MaterialsInfoRepository materialsInfoRepository;
    @Autowired
    private DictUtil dictUtil;
    /*private static List<AssetsAttribution> sourceAttrs = new ArrayList<>();
    static {
        sourceAttrs.add(new AssetsAttribution(""));
        sourceAttrs.add(new AssetsAttribution("入库"));
        sourceAttrs.add(new AssetsAttribution("出库"));
    }*/

    @GetMapping
    public String client(ModelMap map) {
        map.put("materialsInfoList", materialsInfoRepository.findAll());
        return urlPrefix + "/materialsList";
    }

    /**
     * 材料流水
     */
    @ResponseBody
    @RequestMapping("list")
    public ResponseData getMaterialsList(PageBean pageBean, MaterialsListCriteria criteria) {
        if (criteria.getStartTime() != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date endTime = criteria.getEndTime();
            Calendar c = Calendar.getInstance();
            c.setTime(endTime);
            c.add(Calendar.DATE, 1);
            criteria.setEndTime(c.getTime());
            System.out.println(criteria);
        }
        Map<String, Object> datas = materialsListService.findAllByPage(criteria, pageBean.getPagable(Sort.by(Direction.DESC, "createTime")));
        return success(datas);
    }

    @BussinessLog(title = "流水数据", businessType = BusinessType.EXPORT)
    @PostMapping("/toExport")
    @ResponseBody
    public ResponseData export(MaterialsListCriteria materialsListCriteria) throws Exception {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String fileName = "流水数据";
        fileName = encodingExcelFilename(fileName);
        String folder = Global.getTempPath() + File.separator + "pio" + File.separator;
        FileUtil.touch(folder + fileName);
        List<MaterialsListEntity> listData = materialsListService.findAll(materialsListCriteria);
        List<MaterialsListExcel> listExcels = new ArrayList<>();
        for (MaterialsListEntity materialsListEntity : listData) {
            MaterialsListExcel materialsListExcel = new MaterialsListExcel();
            materialsListExcel.setOperationNameType(dictUtil.getLabel("materials_list_type", materialsListEntity.getReason()));
            if(StringUtils.isNotEmpty( materialsListEntity.getModeOfTrade())){
                materialsListExcel.setModeOfTrade(dictUtil.getLabel("mode_of_trade_type", materialsListEntity.getModeOfTrade().toString()));
            }
            materialsListExcel.setInvoiceNumber(materialsListEntity.getInvoiceNumber());
            materialsListExcel.setDepartmentCode(materialsListEntity.getDepartmentCode());
            if(StringUtils.isNotEmpty( materialsListEntity.getDateArrival())){
                materialsListExcel.setDateArrival(simpleDateFormat.format(materialsListEntity.getDateArrival()));
            }



            materialsListExcel.setOperationOtherId(materialsListEntity.getOperationOtherId());
            materialsListExcel.setOperationName(materialsListEntity.getOperationName());
            materialsListExcel.setWarehouseInfo(materialsListEntity.getWarehouseInfo());
            materialsListExcel.setAreaInfo(materialsListEntity.getAreaInfo());
            materialsListExcel.setAssetsUnitName(materialsListEntity.getAssetsUnitName());
            materialsListExcel.setConsumableId(materialsListEntity.getAssetsNumber());
            materialsListExcel.setConsumableName(materialsListEntity.getAssetsName());
            materialsListExcel.setUserName(materialsListEntity.getUserName());
            materialsListExcel.setNowRepertory(materialsListEntity.getNowRepertory());
            materialsListExcel.setCreateTime(simpleDateFormat.format(materialsListEntity.getCreateTime()));
            listExcels.add(materialsListExcel);
        }
//        List<MaterialsListItemEntity> outPlanItems = new ArrayList<>();
//        listData.forEach(MaterialsListEntity -> {
//			/*outPlan.setOrderTypeName(sysDictDataRepository.findByDictTypeAndDictValue("plan_out_type",outPlan.getOrderType().toString()).getDictLabel());
//			outPlan.setStatusName(sysDictDataRepository.findByDictTypeAndDictValue("planIn_storage_status",outPlan.getStatus().toString()).getDictLabel());
//*/
//            outPlanItems.addAll(materialsListRepository.getByOutPlanId(MaterialsListEntity.getId()));
//        });

        EasyExcel.write(folder + fileName, MaterialsListExcel.class).sheet("数据").doWrite(listExcels);
        return success(fileName);
    }

    String encodingExcelFilename(String filename) {
        filename = UUID.randomUUID().toString() + "_" + filename + ".xlsx";
        return filename;
    }
}
