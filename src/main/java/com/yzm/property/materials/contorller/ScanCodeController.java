package com.yzm.property.materials.contorller;

/**
 * @ClassName ScanCodeController * @Description TODO
 * <AUTHOR>
 * @Date 11:50 2024/9/7
 * @Version 1.0
 **/

import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
@RequestMapping(value = "scanCode")
public class ScanCodeController {
    private String urlPrefix = "materials";


    @GetMapping("/scan")
    public String scanCode(ModelMap mmap, String type,Long id){
        mmap.put("type",type);
        mmap.put("materialsId",id);
        return urlPrefix + "/scanCode";
    }


}
