package com.yzm.property.materials.contorller;

import com.yzm.common.annotation.BussinessLog;
import com.yzm.common.enums.BusinessType;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.basis.entity.BasisUnit;
import com.yzm.property.materials.criteria.BindingCarCriteria;
import com.yzm.property.materials.entity.BindingCar;
import com.yzm.property.materials.entity.IssueTempDtl;
import com.yzm.property.materials.entity.OutWarehouseMaterialsItem;
import com.yzm.property.materials.repository.BindingCarRepository;
import com.yzm.property.materials.repository.MaterialsInfoRepository;
import com.yzm.property.materials.repository.OutWarehouseItemMaterialsRepository;
import com.yzm.property.materials.service.BindingCarService;
import com.yzm.property.materials.service.IssueTempDtlService;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.*;

@Controller
@RequestMapping("materials/bindingCar")
@Slf4j
public class BindingCarController extends BaseController {
    private String urlPrefix = "materials/bindingCar";

    @Autowired
    private BindingCarService bindingCarService;
    @Autowired
    private IssueTempDtlService issueTempDtlService;
    @Autowired
    private BindingCarRepository bindingCarRepository;
    @Autowired
    private MaterialsInfoRepository materialsInfoRepository;

    @Autowired
    private OutWarehouseItemMaterialsRepository outWarehouseItemMaterialsRepository;

    /**
     * 新增参数配置
     */
    @GetMapping("/add")
    public String add() {
        return urlPrefix + "/add";
    }

    @GetMapping
    public String client(ModelMap map) {
        return urlPrefix + "/bindingCar";
    }

    /**
     * 保存配置
     */
    @RequestMapping("/add")
    @ResponseBody
    @BussinessLog(businessType = BusinessType.INSERT, title = "新增绑定小车信息")
    public ResponseData save(BindingCar bindingCar) {
        bindingCar.setStatus(0L);
        BindingCar basisUnit = null;
        OutWarehouseMaterialsItem firstByAssetsCodeOrderByIdDesc = outWarehouseItemMaterialsRepository.findFirstByAssetsCodeOrderByIdDesc(bindingCar.getProduct1());
        if (firstByAssetsCodeOrderByIdDesc == null) {
            return error("产品码没有楼层信息");
        }
        bindingCar.setMatlPrcsCd1(firstByAssetsCodeOrderByIdDesc.getMatlPrcsCd());
        basisUnit = bindingCarService.save(bindingCar);

        return StringUtils.isNotEmpty(basisUnit) ? success() : error("新增失败!");
    }

    /**
     * 删除配置
     */
    @RequestMapping(value = "/del", method = RequestMethod.POST)
    @ResponseBody
    @BussinessLog(businessType = BusinessType.DELETE, title = "删除绑定小车信息")
    public ResponseData delete(Long[] ids) {
        List<String> list = new ArrayList<>();
        for (Long id : ids) {
            BindingCar byId = bindingCarService.getById(id);
            String product2 = byId.getProduct2();
            String[] split = product2.split(",");
            for (String kitNo : split) {
                if (!list.contains(kitNo)) {
                    list.add(kitNo);
                }
            }
        }
        bindingCarService.deleteBatchByIds(ids);
        for (String kitNo : list) {
            IssueTempDtl byKitNo = issueTempDtlService.findByKitNo(kitNo);
            Long aLong = bindingCarRepository.countByProduct2Like(kitNo);
            byKitNo.setTrolleySum(aLong);
            issueTempDtlService.update(byKitNo);
        }

        return success();
    }

    /**
     * 修改参数配置
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        mmap.put("unit", bindingCarService.getById(id));
        return urlPrefix + "/edit";
    }

    /**
     * 修改配置
     */
    @RequestMapping(value = "/edit", method = RequestMethod.POST)
    @ResponseBody
    public ResponseData update(BindingCar config) {
        return StringUtils.isNotEmpty(bindingCarService.update(config)) ? success() : error("修改失败!");
    }

    /**
     * 材料流水
     */
    @ResponseBody
    @RequestMapping("list")
    public ResponseData getBindingCar(PageBean pageBean, BindingCarCriteria criteria) {
        if (criteria.getStartTime() != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date endTime = criteria.getEndTime();
            Calendar c = Calendar.getInstance();
            c.setTime(endTime);
            c.add(Calendar.DATE, 1);
            criteria.setEndTime(c.getTime());
            System.out.println(criteria);
        }
        Map<String, Object> datas = bindingCarService.findAllByPage(criteria, pageBean.getPagable());
        return success(datas);
    }


}
