package com.yzm.property.materials.contorller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.yzm.common.annotation.BussinessLog;
import com.yzm.common.annotation.RepeatSubmit;
import com.yzm.common.enums.BusinessType;
import com.yzm.common.utils.PDFTemplateUtil;
import com.yzm.common.utils.ShiroUtils;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.framework.freemark.DictUtil;
import com.yzm.framework.shiro.LoginUser;
import com.yzm.property.basis.service.AreaService;
import com.yzm.property.basis.service.PersonnelService;
import com.yzm.property.basis.service.WarehouseService;
import com.yzm.property.business.entity.BorrowReturnItem;
import com.yzm.property.business.repository.BorrowReturnItemRepository;
import com.yzm.property.materials.criteria.BindingCarCriteria;
import com.yzm.property.materials.criteria.InStorageCriteria;
import com.yzm.property.materials.entity.*;
import com.yzm.property.materials.repository.MaterialsRepertoryRepository;
import com.yzm.property.materials.service.InStorageItemService;
import com.yzm.property.materials.service.InStorageService;
import com.yzm.property.materials.service.MaterialsInfoBomService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 材料入库
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 11:33
 */
@Controller(value = "materialsInStorageController")
@RequestMapping(value = "materials/inStorage")
public class InStorageController extends BaseController {
    private String urlPrefix = "materials/inStorage";

    @Autowired
    private InStorageService inStorageService;
    @Autowired
    private MaterialsInfoBomService materialsInfoBomService;
    @Autowired
    private InStorageItemService inStorageItemService;
    @Autowired
    private WarehouseService warehouseService;
    @Autowired
    private AreaService areaService;
    @Autowired
    private PersonnelService personnelService;
    @Autowired
    private MaterialsRepertoryRepository materialsRepertoryRepository;
    @Autowired
    private BorrowReturnItemRepository borrowReturnItemRepository;
    @Autowired
    private DictUtil dictUtil;


    @RequestMapping()
    public String inStorageList(ModelMap mmap) {
        return urlPrefix + "/list";
    }


    @RequestMapping("/dys")
    public String inStorageDysList(ModelMap mmap) {
        return urlPrefix + "/dysList";
    }

    /**
     * 材料出库
     */
    @ResponseBody
    @RequestMapping("/list")
    public ResponseData getOutWarehouse(@RequestParam(value = "keyword", required = false)
                                                String keyword,
                                        @RequestParam(value = "type", required = false)
                                                List<String> type,
                                        PageBean pageBean,String departmentCode) {
        Map<String, Object> datas = inStorageService.showAllOutWarehouseInfoPage(keyword, type, pageBean,departmentCode);
        return success(datas);
    }
    /**
     * 材料出库
     */
    @ResponseBody
    @RequestMapping("/listDys")
    public ResponseData getOutWarehouseDys(@RequestParam(value = "keyword", required = false)
                                                String keyword,
                                        @RequestParam(value = "type", required = false)
                                                List<String> type,
                                        PageBean pageBean) {
        Map<String, Object> datas = inStorageService.showAllOutWarehouseInfoPageDys(keyword, type, pageBean);
        return success(datas);
    }

//    /**
//     * 材料流水
//     */
//    @ResponseBody
//    @RequestMapping("/list")
//    public ResponseData getBindingCar(PageBean pageBean, InStorageCriteria criteria) {
//        if (criteria.getStartTime() != null) {
//            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
//            Date endTime = criteria.getEndTime();
//            Calendar c = Calendar.getInstance();
//            c.setTime(endTime);
//            c.add(Calendar.DATE, 1);
//            criteria.setEndTime(c.getTime());
//            System.out.println(criteria);
//        }
//        Map<String, Object> datas = inStorageService.findAllByPage(criteria, pageBean.getPagable());
//        return success(datas);
//    }
//
//
//
//    @RequestMapping(value = "/list", method = RequestMethod.POST)
//    @ResponseBody
//    public ResponseData showAllInStorageInfo(
//            @RequestParam(value = "keyword", required = false)
//                    String keyword,
//            @RequestParam(value = "type", required = false)
//                    List<String> type,
//            PageBean pageBean
//    ) {
//        return success(inStorageService.showAllInStorageInfo(keyword, type, pageBean));
//    }

    @GetMapping("add")
    public String add(ModelMap mmap) {
        mmap.put("warehouseList", warehouseService.findAll());
        mmap.put("areaList", new ArrayList<>());
        mmap.put("personnelList", personnelService.findAll());
        mmap.put("materialsInfoBomList", materialsInfoBomService.findGroupByNumber());
        LoginUser userInfo = ShiroUtils.getUserInfo();
        mmap.put("userInfo", userInfo);

        return urlPrefix + "/addInStorage";
    }

    @GetMapping("addInStorageDys")
    public String addInStorageDys(ModelMap mmap) {
        mmap.put("warehouseList", warehouseService.findAll());
        mmap.put("areaList", new ArrayList<>());
        mmap.put("personnelList", personnelService.findAll());
        LoginUser userInfo = ShiroUtils.getUserInfo();
        mmap.put("userInfo", userInfo);

        return urlPrefix + "/addInStorageDys";
    }
    @RequestMapping("/addVerify")
    @ResponseBody
    public ResponseData addVerify(InStorageMaterials inStorage) {
        return inStorageService.saveInStorageVerify(inStorage);
    }

    @RequestMapping("/add")
    @ResponseBody
    @BussinessLog(businessType = BusinessType.INSERT, title = "新增材料入库")
    public ResponseData save(InStorageMaterials inStorage) {
        return inStorageService.saveInStorage(inStorage);
    }

    @RequestMapping("/addReturning")
    @ResponseBody
    @BussinessLog(businessType = BusinessType.INSERT, title = "新增材料退库/不良")
    public ResponseData addReturning(InStorageMaterials inStorage) {
        return inStorageService.saveInStorageReturning(inStorage);
    }
    @RequestMapping("/inStorageSave")
    @ResponseBody
    @BussinessLog(businessType = BusinessType.INSERT, title = "入库操作")
    public ResponseData inStorageSave(InStorageMaterials inStorage) {
        return inStorageService.inStorageSave(inStorage);
    }
    /**
     * 提交审核
     */
    @RepeatSubmit
    @BussinessLog(title = "材料退库/不良", businessType = BusinessType.RUN)
    @RequestMapping(value = "/submit", method = RequestMethod.POST)
    @ResponseBody
    public ResponseData submit(Long[] ids) {
        return inStorageService.submitByIds(ids) ? success() : error("提交失败!");
    }
//    /**
//     * 送审、同意、拒绝、取消(借用)
//     */
//    @RequestMapping("/audit/{id}/{status}")
//    @BussinessLog(businessType = BusinessType.UPDATE, title = "审核退库")
//    public String audit(@PathVariable("id") Long id, @PathVariable("status") Integer status, ModelMap mmap) {
//
//        inStorageService.audit(id, status);
//        return urlPrefix + "/dysList";
//    }



    /**
     * 送审、同意、拒绝、取消(借用)
     */
    @RequestMapping("/audit")
    @ResponseBody
    @BussinessLog(businessType = BusinessType.UPDATE, title = "其它入库审核")
    public ResponseData audit(Long id,Integer status) {
        inStorageService.audit(id, status);
        return ResponseData.success();
    }


    /**
     * 入库(选择材料信息)页面
     */
    @GetMapping("/addInStorageItem")
    public String addInStorageItem() {
        return urlPrefix + "/addInStorageItem";
    }
  /**
     * 入库(选择材料信息)页面
     */
    @GetMapping("/addInStorageItemDtl")
    public String addInStorageItemDtl() {
        return urlPrefix + "/addInStorageItemDtl";
    }

    @RequestMapping("/inStorage")
    public String inStorage(ModelMap mmap, Long id) {
        InStorageMaterials byId = inStorageService.getById(id);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String formattedDate = sdf.format(byId.getAuditorDate());
        mmap.put("formattedPlannedIssueDt", formattedDate);
        mmap.put("inStorageMaterials", byId);
        mmap.put("modeOfTrade", byId.getModeOfTrade()+"");
        LoginUser userInfo = ShiroUtils.getUserInfo();
        mmap.put("userInfo", userInfo);
        return urlPrefix + "/inStorage";
    }
    /**
     * 根据ID查询详细
     * @param id
     * @return
     */
    @RequestMapping("/findDetailsData")
    @ResponseBody
    public ResponseData findDetailsData(@RequestParam Long id) {
        InStorageMaterials byId = inStorageService.getById(id);
        List<InStorageItemMaterials> list = new ArrayList<>();
        List<InStorageItemMaterials> byRecordNumber = inStorageItemService.findByRecordNumber(byId.getRecordNumber());
        for (InStorageItemMaterials inStorageItemMaterials : byRecordNumber) {
            if (StringUtils.isNotEmpty(inStorageItemMaterials.getSucceed())) {
                if (inStorageItemMaterials.getSucceed() == 1L) {
                    continue;
                }
            }
            list.add(inStorageItemMaterials);
        }
        return success().put("inStorageItemMaterialsList", list);
    }

    /**
     * 根据ID查询详细
     * @param id
     * @return
     */
    @RequestMapping("/findDetails")
    @ResponseBody
    public ResponseData findDetail(@RequestParam Long id) {
        InStorageMaterials byId = inStorageService.getById(id);
        List<InStorageItemMaterials> byRecordNumber = inStorageItemService.findByRecordNumber(byId.getRecordNumber());


        return success().put("inStorageItemMaterialsList", byRecordNumber);
    }

    /**
     * 扫码rfid页面
     */
    @GetMapping("/addRfid")
    public String addRfid(ModelMap mmap, String rfid, int index, String field) {
        mmap.put("rfid", rfid);
        mmap.put("index", index);
        mmap.put("field", field);
        return urlPrefix + "/addRfid";
    }

    /**
     * 删除报废信息
     *
     * @param ids
     * @return
     */
    @PostMapping("/del")
    @ResponseBody
    @BussinessLog(businessType = BusinessType.DELETE, title = "删除入库信息")
    public ResponseData deleteScrap(Long[] ids) {

        return inStorageService.deleteInfo(ids);
    }

    /**
     * 根据单据号查询详细
     *
     * @return
     */
    @GetMapping("/view/{id}")
    public String viewPage(@PathVariable("id") Long id, ModelMap mmap) {
        InStorageMaterials inStorage = inStorageService.getById(id);
        List<InStorageItemMaterials> list = inStorageItemService.findByRecordNumber(inStorage.getRecordNumber());
        mmap.put("inStorage", inStorage);
        mmap.put("inStorageList", list);
        return urlPrefix + "/show";

    }

    /**
     * 根据单据号查询详细
     *
     * @param recordNumber
     * @return
     */
    @GetMapping("/findDetailByRecordNumber")
    @ResponseBody
    public ResponseData findDetailByRecordNumber(@RequestParam String recordNumber) {
        List<InStorageItemMaterials> list = inStorageItemService.findByRecordNumber(recordNumber);

        return success().put("inStorageItemList", list);
    }

    /**
     * 导入物料
     *
     * @param excelFile 物料导入
     * @return
     */
    @RequestMapping("/ReadFileText")
    @ResponseBody
    public ResponseData readFileText2(@RequestParam(value = "file", required = false) MultipartFile excelFile) {
        //============================使用模板======================================================
        InputStream inputStream = null;
        try {
            // 使用模板导入——接收上传文件
            inputStream = excelFile.getInputStream();
            List<String> list = new ArrayList<>();
            EasyExcel.read(inputStream, Rfid.class, new AnalysisEventListener<Rfid>() {
                @Override
                public void invoke(Rfid rfid, AnalysisContext analysisContext) {
                    MaterialsRepertory byAssetsCodeOrAssetsRfid = materialsRepertoryRepository.findByAssetsCodeOrAssetsRfid(rfid.getRfid(), rfid.getRfid());
                    BorrowReturnItem byAssetsRfidAndBiReturnStandby = borrowReturnItemRepository.findByAssetsRfidAndBiReturnStandby(rfid.getRfid(), 0);
                    if (StringUtils.isNotEmpty(byAssetsRfidAndBiReturnStandby)) {
                        throw new ExcelAnalysisException(rfid.getRfid() + ":当前RFID数据存在未归还信息！");
                    }

                    if (StringUtils.isEmpty(byAssetsCodeOrAssetsRfid)) {
                        list.add(rfid.getRfid());
                    } else {
                        throw new ExcelAnalysisException(rfid + ":RFID已经入库!");
                    }
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                }
            }).sheet().doRead();
            return success(list);
        } catch (IOException e) {
            e.printStackTrace();
            return new ResponseData("500", "RFID" + "'" + "'" + "不正确", null);
        } catch (ExcelAnalysisException e) {
            if (e.getCause() instanceof ExcelDataConvertException) {
                ExcelDataConvertException excelDataConvertException = (ExcelDataConvertException) e.getCause();
                return new ResponseData("500", "RFID" + "'" + "'" + "不正确", null);
            } else {
                return new ResponseData("500", e.getMessage(), null);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return new ResponseData("500", "请检查导入模板！", null);
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    @GetMapping("exportInformation")
//    @BussinessLog(title = "出库单", businessType = BusinessType.EXPORT)
    public void downloadFile(HttpServletResponse response, Long id) throws Exception {
        ByteArrayOutputStream baos = null;
        OutputStream out = null;
        try {
            InStorageMaterials inStorage = inStorageService.getById(id);
            Map<String, Object> data = new HashMap<>();
            data.put("title", "预览PDF");
            //申领编号
            data.put("recordNumber", inStorage.getRecordNumber());
            data.put("inStoragePeople", inStorage.getInStoragePeople());
            data.put("inStorageWarehouse", inStorage.getInStorageWarehouse());
            data.put("inStorageArea", inStorage.getInStorageArea());
            if(inStorage.getInStorageReson().equals("0")){
                data.put("inStorageReson",dictUtil.getLabel("in_storage_type",inStorage.getInStorageReson()));
            }else{
                data.put("inStorageReson",dictUtil.getLabel("in_storage_type_dys",inStorage.getInStorageReson()));
            }
            data.put("user", ShiroUtils.getUserInfo().getName());
            data.put("now", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy.MM.dd HH:mm:ss")));
            List<InStorageItemMaterials> overList = inStorageItemService.findByRecordNumber(inStorage.getRecordNumber());
            BigDecimal count = new BigDecimal(0);
            for (InStorageItemMaterials inStorageItem : overList) {
                count = count.add(inStorageItem.getInStorageCount());
            }
            data.put("detailList", overList);
            data.put("count", count);
            baos = PDFTemplateUtil.createPDF(data, "入库单.ftl");


            // 设置响应消息头，告诉浏览器当前响应是一个下载文件
            response.setContentType("application/pdf;charset=UTF-8");
            // 告诉浏览器，当前响应数据要求用户干预保存到文件中，以及文件名是什么 如果文件名有中文，必须URL编码
            String fileName = URLEncoder.encode(inStorage.getRecordNumber() + ".pdf", "UTF-8");
            response.setHeader("Content-Disposition", "inline;filename=" + fileName);
            out = response.getOutputStream();
            baos.writeTo(out);
            baos.close();
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("导出失败：" + e.getMessage());
        } finally {
            if (baos != null) {
                baos.close();
            }
            if (out != null) {
                out.close();
            }
        }
    }


}
