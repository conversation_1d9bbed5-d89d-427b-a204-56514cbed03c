package com.yzm.property.materials.contorller;

import java.io.OutputStream;
import java.net.Socket;
import java.io.IOException;

public class SatoPrinterWiFiDemo {
    private Socket socket;
    private OutputStream outputStream;

    // 连接到打印机
    public void connect(String ipAddress, int port) throws IOException {
        socket = new Socket(ipAddress, port);
        outputStream = socket.getOutputStream();
    }

    // 发送 SBPL 命令
    public void sendSBPLCommand(String command) throws IOException {
        outputStream.write(command.getBytes("UTF-8"));  // 使用 UTF-8 编码
        outputStream.flush();
    }

    // 断开连接
    public void disconnect() throws IOException {
        if (socket != null) {
            socket.close();
        }
    }

    public static void main(String[] args) {
        SatoPrinterWiFiDemo printerDemo = new SatoPrinterWiFiDemo();
        try {
            // 替换为打印机的 IP 地址和端口（通常 SATO 打印机使用 9100 端口）
            printerDemo.connect("**************", 9100);

            // SBPL 命令（横向打印）
            String sbplCommand =
                    "\u001B" + "A" +  // 开始命令
                            "\u001B" + "W1" + // 不旋转打印内容
                            "\u001B" + "V00100" + // 垂直位置（100 像素）
                            "\u001B" + "H00200" + // 水平位置（200 像素）
                            "\u001B" + "L0202" +  // 字体大小（宽度 2x，高度 2x）
                            "\u001B" + "XM电梯号码 13107448" + // 打印文本 电梯号码
                            "\u001B" + "V00200" + // 垂直位置（200 像素）
                            "\u001B" + "H00200" + // 水平位置（200 像素）
                            "\u001B" + "XM部材名称 15GX-6S" + // 打印文本 部材名称
                            "\u001B" + "V00300" + // 垂直位置（300 像素）
                            "\u001B" + "H00200" + // 水平位置（200 像素）
                            "\u001B" + "XM批号 7338850004" + // 打印文本 批号
                            "\u001B" + "B1037338850004" + // 打印条形码 批号
                            "\u001B" + "V00400" + // 垂直位置（400 像素）
                            "\u001B" + "H00200" + // 水平位置（200 像素）
                            "\u001B" + "XM批次数量 500" + // 打印文本 批次数量
                            "\u001B" + "V00500" + // 垂直位置（500 像素）
                            "\u001B" + "H00200" + // 水平位置（200 像素）
                            "\u001B" + "B103500" + // 打印条形码 批次数量
                            "\u001B" + "V00600" + // 垂直位置（600 像素）
                            "\u001B" + "H00200" + // 水平位置（200 像素）
                            "\u001B" + "QRCODE2D,500" +  // 打印二维码
                            "\u001B" + "Z";  // 结束命令

            // 发送命令并断开连接
            printerDemo.sendSBPLCommand(sbplCommand);
            printerDemo.disconnect();

        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
