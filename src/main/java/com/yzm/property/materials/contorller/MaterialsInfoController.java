package com.yzm.property.materials.contorller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.yzm.common.annotation.BussinessLog;
import com.yzm.common.enums.BusinessType;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.basis.entity.BasisArea;
import com.yzm.property.basis.entity.InventorySimple;
import com.yzm.property.basis.entity.InventorySimpleItem;
import com.yzm.property.basis.repository.InventorySimpleItemRepository;
import com.yzm.property.basis.repository.InventorySimpleRepository;
import com.yzm.property.basis.service.AreaService;
import com.yzm.property.basis.service.BrandService;
import com.yzm.property.basis.service.UnitService;
import com.yzm.property.basis.service.WarehouseService;
import com.yzm.property.materials.entity.MaterialsInfo;
import com.yzm.property.materials.entity.MaterialsInfoBom;
import com.yzm.property.materials.entity.MaterialsRepertory;
import com.yzm.property.materials.entity.OutWarehouseMaterialsItem;
import com.yzm.property.materials.importHeadListener.MaterialsInfoHeadDataListener;
import com.yzm.property.materials.repository.MaterialsInfoBomRepository;
import com.yzm.property.materials.repository.MaterialsRepertoryRepository;
import com.yzm.property.materials.repository.OutWarehouseItemMaterialsRepository;
import com.yzm.property.materials.service.MaterialsInfoService;
import com.yzm.property.materials.service.MaterialsRepertoryService;
import com.yzm.property.materials.task.DatabaseHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:46
 */
@Controller
@RequestMapping("/materials_info")
public class MaterialsInfoController extends BaseController {
    private static Logger log = LoggerFactory.getLogger(MaterialsInfoController.class);

    private String urlPrefix = "materials/materialsInfo";


    @Autowired
    private WarehouseService warehouseService;
    @Autowired
    private AreaService areaService;
    @Autowired
    private MaterialsInfoService materialsInfoService;
    @Autowired
    private MaterialsInfoBomRepository materialsInfoBomRepository;
    @Autowired
    private MaterialsRepertoryService materialsRepertoryService;
    @Autowired
    private MaterialsRepertoryRepository materialsRepertoryRepository;
    @Autowired
    private BrandService brandService;
    @Autowired
    private UnitService unitService;//资产计量单位
    @Autowired
    private OutWarehouseItemMaterialsRepository outWarehouseItemMaterialsRepository;
    @Autowired
    private InventorySimpleItemRepository inventorySimpleItemRepository;
    @Autowired
    private InventorySimpleRepository inventorySimpleRepository;

    /**
     * 跳转至list页面
     *
     * @param mmap
     * @return
     */
    @RequestMapping
    public String assets(ModelMap mmap) {
        return urlPrefix + "/list";
    }

    /**
     * 跳转至list页面
     *
     * @param mmap
     * @return
     */
    @RequestMapping("/bom")
    public String Materials(ModelMap mmap) {
        return urlPrefix + "/bom";
    }

    /**
     * 跳转至新增页面，携带资产类型以及资产分类数据
     *
     * @param mmap
     * @return
     */
    @GetMapping("/add")
    public String addMaterials(ModelMap mmap) {

        mmap.put("warehouseList", warehouseService.findAll());
        mmap.put("areaList", new ArrayList<>());

        mmap.put("unit", unitService.findAll());
        mmap.put("brandList", brandService.findAll());
        return urlPrefix + "/add";
    }

    /**
     * 修改跳转页面
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        MaterialsInfo byId = materialsInfoService.findById(id);
        mmap.put("materialsInfo", byId);
        mmap.put("warehouseList", warehouseService.findAll());
//        List<BasisArea> list = new ArrayList<>();
//        list.add(areaService.getById(byId.getStorageAreaId()));

//        mmap.put("areaList", list);
        mmap.put("unit", unitService.findAll());
        mmap.put("brandList", brandService.findAll());
        return urlPrefix + "/edit";
    }


    /**
     * 新增材料
     *
     * @param materialsInfo
     * @return
     */
    @PostMapping("/add")
    @ResponseBody
    @BussinessLog(businessType = BusinessType.INSERT, title = "新增材料")
    public ResponseData addMaterialsInfo(MaterialsInfo materialsInfo) {
        return materialsInfoService.addMaterialsInfo(materialsInfo);
    }


    /**
     * 修改资产
     */
    @RequestMapping(value = "/edit", method = RequestMethod.POST)
    @ResponseBody
    @BussinessLog(businessType = BusinessType.UPDATE, title = "修改材料信息")
    public ResponseData updateMaterialsInfo(MaterialsInfo materialsInfo) {
        return materialsInfoService.updateMaterialsInfo(materialsInfo);
    }

    /**
     * 根据id列表删除资产
     *
     * @param ids
     * @return
     */
    @PostMapping("/delete")
    @ResponseBody
    @BussinessLog(businessType = BusinessType.DELETE, title = "删除材料")
    public ResponseData deleteMaterialsInfo(Long ids) {
        materialsRepertoryService.findByMaterialsInfoId(ids);
        return success();
    }

    /**
     * 根据关键字和分页查询资产
     *
     * @param keyword
     * @param pageBean
     * @return
     */
    @PostMapping("/list")
    @ResponseBody
    public ResponseData showAllMaterialsInfo(
            @RequestParam(value = "keyword", required = false)
                    String keyword,
            PageBean pageBean
    ) {
        return success(materialsInfoService.showAllMaterialsInfo(keyword, pageBean));
    }

    /**
     * 查看详情
     *
     * @param id
     * @return
     */
    @RequestMapping("/show/{id}")
    public ModelAndView showMaterialsInfoById(@PathVariable("id") Long id) {
        MaterialsInfo materialsInfo = materialsInfoService.findById(id);
        ModelAndView modelAndView = new ModelAndView(urlPrefix + "/show");
        modelAndView.addObject("unit", unitService.findAll());
        modelAndView.addObject("materialsInfo", materialsInfo);
        return modelAndView;
    }

    //
//    /**
//     * 查询材料信息根据条码和rfid
//     *
//     * @param rfid
//     * @param code
//     * @return
//     */
//    @GetMapping("/findDetailByRfidOrCode")
//    @ResponseBody
//    public ResponseData findDetailByRfidOrCode(@RequestParam String rfid, @RequestParam String code) {
//        MaterialsInfo materialsInfo = materialsInfoService.findByAssetsCodeOrAssetsRfid(rfid, code);
//        if (StringUtils.isEmpty(materialsInfo)) {
//            return error("当前条码/RFID未绑定材料！");
//        }
//        MaterialsRepertory byAssetsCodeOrAssetsRfid = materialsRepertoryService.findByAssetsCodeOrAssetsRfid(rfid, code);
//        if (StringUtils.isNotEmpty(byAssetsCodeOrAssetsRfid)) {
//            return error("当前条码/RFID:"+rfid+"-无法重复入库！");
//        }
//        return success().put("materialsInfo", materialsInfo);
//    }
    private static final String REGEX = "^([^,]*,){7}[^,]*$";

    public static boolean isValidFormat(String text) {
        Pattern pattern = Pattern.compile(REGEX);
        Matcher matcher = pattern.matcher(text);
        return matcher.matches();
    }

    public static String[] parseFormat(String text) {
        // 使用下划线分隔字符串
        String[] parts = text.split(",");

        // 检查是否有3部分
        if (parts.length == 8) {
            return parts;
        } else {
            return null;
        }
    }

    /**
     * 校验条码内容
     *
     * @param code
     * @return
     */
    @GetMapping("/verifyCodeProduct")
    @ResponseBody
    public ResponseData verifyCodeProduct(@RequestParam String code) {
        String[] strings = code.split("~");
        Set<String> matlPrcsCd = new HashSet<>();
        String matlPrcsCdValue = "";
        for (String string : strings) {
            OutWarehouseMaterialsItem firstByAssetsCodeOrderByIdDesc = outWarehouseItemMaterialsRepository.findFirstByAssetsCodeOrderByIdDesc(string);

            if (firstByAssetsCodeOrderByIdDesc != null) {
                String matlPrcsCd1 = firstByAssetsCodeOrderByIdDesc.getMatlPrcsCd();
                // 如果集合为空，则添加第一个 assetsCode
                if (matlPrcsCd.isEmpty()) {
                    matlPrcsCd.add(matlPrcsCd1);
                } else {
                    // 检查是否与已有的 assetsCode 不同
                    if (!matlPrcsCd.contains(matlPrcsCd1)) {
                        // 返回或处理逻辑
                        System.out.println("Found different assetsCode: " + matlPrcsCd1);
                        return ResponseData.error("扫码标签去往楼层不符"); // 或者你可以抛出异常，或者其他处理方式
                    }
                }
                matlPrcsCdValue = matlPrcsCd1;
            }
        }
        return ResponseData.success("成功", matlPrcsCdValue);
    }

    public static void main(String[] args) {
        String s = "1234-56";
//        System.out.println(replaceFifthCharacter(s));
    }

    /**
     * 校验条码内容
     *
     * @param code
     * @param type 查询类型 0 正常入库 1退料入库
     * @return
     */
    @GetMapping("/verifyCode")
    @ResponseBody
    public ResponseData verifyCode(@RequestParam String code, @RequestParam String type, @RequestParam(required = false, defaultValue = "") String invoiceNumber, @RequestParam(required = false, defaultValue = "") String deptCode) throws ParseException {

        if (!isValidFormat(code)) {
            return ResponseData.error("条码格式错误请重新扫描");
        }
        // 解析
        String[] parts = parseFormat(code);

        if (parts != null) {
            System.out.println("编号: " + parts[0]);
            System.out.println("名称: " + parts[1]);
            System.out.println("批号: " + parts[2]);
            System.out.println("批次数量: " + parts[3]);
            System.out.println("总数量: " + parts[4]);
            System.out.println("来料日期: " + parts[5]);
            System.out.println("包装数量: " + parts[6]);
            System.out.println("流水号: " + parts[7]);
        } else {
            System.out.println(code + "-格式无效");
        }


        if (StringUtils.isEmpty(parts)) {
            return error(code + "-条码格式错误！");
        }
        String number = parts[0].trim();

        number = StringUtils.replaceFifthCharacter(number);

        MaterialsInfo materialsInfo = materialsInfoService.findByAssetsNumber(number);
        if (StringUtils.isEmpty(materialsInfo)) {
            materialsInfo = new MaterialsInfo();
            materialsInfo.setAssetsNumber(number);
            materialsInfo.setAssetsName(parts[1]);
//            return error("系统没有该材料！" + number);
        }

        BigDecimal decimal = new BigDecimal(1);
        if (StringUtils.isNotEmpty(materialsInfo.getMinLot())) {
            decimal = materialsInfo.getMinLot();
        }


        if (type.equals("0") || type.equals("1")) {
            List<String> materialsInfo1 = materialsInfoService.getMaterialsInfo(number, deptCode);
            if (materialsInfo1.size() > 0) {
                materialsInfo.setStorageArea(materialsInfo1.get(0));
            }
//            if (StringUtils.isEmpty(materialsInfo.getStorageArea())) {
//                return error("部门代码" + deptCode + "中没有找到材料：" + number);
//            }
            if (StringUtils.isNotEmpty(invoiceNumber)) {
                List<MaterialsInfoBom> list1 = materialsInfoBomRepository.findByInvoiceNumberAndAssetsNumber(invoiceNumber, number.replaceAll("\\s+", ""));
                if (list1.size() == 0) {
                    throw new ExcelAnalysisException(invoiceNumber + "发票中未找到" + number + "材料。不可入库！");
                }
            }
            MaterialsRepertory byAssetsCodeOrAssetsRfid = materialsRepertoryService.findByAssetsCodeOrAssetsRfid(code, code);
            if (StringUtils.isNotEmpty(byAssetsCodeOrAssetsRfid)) {
                return error("当前条码:" + code + "-无法重复入库！");
            }

            BigDecimal count = new BigDecimal(parts[4]);
            materialsInfo.setCount(count.multiply(decimal).toString());
        } else if (type.equals("2")) {


            MaterialsRepertory byAssetsCodeOrAssetsRfid = materialsRepertoryService.findByAssetsCodeOrAssetsRfid(code, code);
            if (byAssetsCodeOrAssetsRfid == null) {
                byAssetsCodeOrAssetsRfid = materialsRepertoryRepository.findByAssetsNumberAndAssetsCodeIsNull(number);
                if (byAssetsCodeOrAssetsRfid == null) {
                    return error(number + "未查询到库存信息");
                }
                byAssetsCodeOrAssetsRfid.setNowRepertory(new BigDecimal(parts[4]));
                byAssetsCodeOrAssetsRfid.setAssetsCode(code);
                byAssetsCodeOrAssetsRfid.setBatchNumber(parts[2]);
            }
            if (StringUtils.isEmpty(byAssetsCodeOrAssetsRfid)) {
                return error("当前条码:" + code + "在库存中查询不到！");
            }
//            checkFifoAndProcess(code);
            List<MaterialsRepertory> materialsList = materialsRepertoryService.findByAssetsNumberAndFifoStatusOrderByDateArrivalAsc(byAssetsCodeOrAssetsRfid.getAssetsNumber(), 1L);
            if (StringUtils.isEmpty(byAssetsCodeOrAssetsRfid.getInDataType())) {
                byAssetsCodeOrAssetsRfid.setInDataType(0L);
            }

            if (!StringUtils.isEmpty(byAssetsCodeOrAssetsRfid.getPriority()) && byAssetsCodeOrAssetsRfid.getPriority() == 1L || byAssetsCodeOrAssetsRfid.getInDataType() == 1L) {

            } else {
// 获取入库最早的物料
                MaterialsRepertory oldestMaterial = materialsList.get(0);
                // 获取当前扫描的物料
                MaterialsRepertory currentMaterial = byAssetsCodeOrAssetsRfid; // 假设最后一个是当前的

                // 计算最早物料与当前物料的入库时间差
                Duration duration = Duration.between(oldestMaterial.getDateArrival().toInstant(), currentMaterial.getDateArrival().toInstant());
                long daysDifference = duration.toDays();
                boolean statusInfo = false;

                // 如果两个物料的入库时间差小于或等于30天，则不严格要求FIFO
                if (daysDifference <= 30) {
                    statusInfo = true;
//                    return success("入库时间相差小于或等于30天，物料无需判断先进先出(FIFO)。");
                }

                // 如果入库时间差超过30天，则需要判断FIFO
                if (!oldestMaterial.equals(currentMaterial) && statusInfo == false) {
                    return error("当前物料未满足先进先出原则！");
                }

                // 如果符合FIFO规则
//                return success("物料符合先进先出(FIFO)原则");
            }

            BigDecimal count = byAssetsCodeOrAssetsRfid.getNowRepertory().multiply(decimal);

            materialsInfo.setCount(count.toString());


        } else if (type.equals("3")) {//绑定产品
            OutWarehouseMaterialsItem outWarehouseMaterialsItems = outWarehouseItemMaterialsRepository.findFirstByAssetsCodeOrderByIdDesc(code);
            if (outWarehouseMaterialsItems == null) {
                return error("当前条码:" + code + "在出库信息中查询不到！");
            }
            BigDecimal count = outWarehouseMaterialsItems.getConsumableNum().multiply(decimal);

            materialsInfo.setCount(count.toString());
//            materialsInfo.setCount(outWarehouseMaterialsItems.getConsumableNum().toString());
        } else if (type.substring(0, 1).equals("4")) {
            InventorySimple one1 = inventorySimpleRepository.getOne(Long.parseLong(type.substring(2)));
            InventorySimpleItem one = inventorySimpleItemRepository.findByInventoryNumberAndAssetsCode(one1.getInventoryNumber(), code);
            if (one == null) {
                MaterialsRepertory byAssetsCodeOrAssetsRfid = materialsRepertoryService.findByAssetsCodeOrAssetsRfid(code, code);
                if (byAssetsCodeOrAssetsRfid == null) {
                    byAssetsCodeOrAssetsRfid = new MaterialsRepertory();
                    BeanUtils.copyProperties(materialsInfo, byAssetsCodeOrAssetsRfid);
                    BigDecimal count = new BigDecimal(parts[4]);
                    byAssetsCodeOrAssetsRfid.setNowRepertory(count.multiply(decimal));
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
                    byAssetsCodeOrAssetsRfid.setDateArrival(simpleDateFormat.parse(parts[5]));
                    byAssetsCodeOrAssetsRfid.setAssetsCode(code);
                    byAssetsCodeOrAssetsRfid.setBatchNumber(parts[2]);
                    byAssetsCodeOrAssetsRfid.setWarehouseInfo(materialsInfo.getStorageWarehouse());
                    byAssetsCodeOrAssetsRfid.setAreaInfo(byAssetsCodeOrAssetsRfid.getAreaInfo());
                }
                return success("扫码成功").put("materialsInfo", byAssetsCodeOrAssetsRfid);
            }
            if (one.getInventoryStatus() == 2L) {
                return error("条码" + code + "重复盘点");
            }
            return success("扫码成功").put("materialsInfo", one);
        }
        materialsInfo.setDateArrival(parts[5]);
        materialsInfo.setAssetsCode(code);
        materialsInfo.setBatchNumber(parts[2]);
        return success("扫码成功").put("materialsInfo", materialsInfo);
    }


    /**
     * 导入物料
     *
     * @param excelFile 物料导入
     * @return
     */
    @RequestMapping("/ReadFileText")
    @ResponseBody
    public ResponseData readFileText2(@RequestParam(value = "file", required = false) MultipartFile excelFile) {
        //============================使用模板======================================================
        InputStream inputStream = null;
        MaterialsInfoHeadDataListener materialsInfoHeadDataListener = new MaterialsInfoHeadDataListener();

        try {
            // 使用模板导入——接收上传文件
            inputStream = excelFile.getInputStream();
            // 读取表格数据
            EasyExcel.read(inputStream, MaterialsInfo.class, materialsInfoHeadDataListener).headRowNumber(1).doReadAll();

            return ResponseData.success("共" + materialsInfoHeadDataListener.getCount() + "条信息导入成功" + materialsInfoHeadDataListener.getCountOk() + "条");

            // TODO 根据业务处理objectList……
        } catch (IOException e) {
            e.printStackTrace();
        } catch (ExcelAnalysisException e) {
            return ResponseData.error(e.getMessage());
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return success("导入成功");
    }
}
