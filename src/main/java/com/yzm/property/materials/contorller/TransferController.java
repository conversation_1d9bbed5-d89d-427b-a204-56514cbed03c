package com.yzm.property.materials.contorller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.yzm.common.annotation.BussinessLog;
import com.yzm.common.enums.BusinessType;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.basis.service.AreaService;
import com.yzm.property.basis.service.PersonnelService;
import com.yzm.property.basis.service.WarehouseService;
import com.yzm.property.materials.entity.MaterialsRepertory;
import com.yzm.property.materials.entity.Rfid;
import com.yzm.property.materials.entity.TransferMaterials;
import com.yzm.property.materials.entity.TransferMaterialsItem;
import com.yzm.property.materials.repository.MaterialsRepertoryRepository;
import com.yzm.property.materials.service.TransferItemService;
import com.yzm.property.materials.service.TransferService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * 材料调拨
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 11:33
 */
@Controller(value = "materialsTransferController")
@RequestMapping(value = "materials/transfer")
public class TransferController extends BaseController {
    private String urlPrefix = "materials/transfer";

    @Autowired
    private TransferService transferService;
    @Autowired
    private TransferItemService transferItemService;
    @Autowired
    private WarehouseService warehouseService;
    @Autowired
    private AreaService areaService;
    @Autowired
    private PersonnelService personnelService;
    @Autowired
    private MaterialsRepertoryRepository materialsRepertoryRepository;


    @RequestMapping()
    public String inStorageList(ModelMap mmap) {
        return urlPrefix + "/list";
    }


    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @ResponseBody
    public ResponseData showAllInStorageInfo(
            @RequestParam(value = "keyword", required = false)
                    String keyword,
            @RequestParam(value = "type", required = false)
                    String type,
            PageBean pageBean
    ) {
        return success(transferService.showAllTransferInfo(keyword, pageBean));

    }

    @GetMapping("/addInStorageItem")
    public String addInStorageItem() {
        return urlPrefix + "/addInStorageItem";
    }

    @GetMapping("add")
    public String add( ModelMap mmap) {
        mmap.put("warehouseList",warehouseService.findAll());
        mmap.put("areaList",new ArrayList<>());
        mmap.addAttribute("personnelList", personnelService.findByDeleteFlag(0));

        return urlPrefix + "/addInStorage";
    }

    @RequestMapping("/add")
    @ResponseBody
    @BussinessLog(businessType=BusinessType.INSERT,title="库存调拨")
    public ResponseData save(TransferMaterials inStorage) {

        return transferService.saveTransfer(inStorage);
    }

    /**
     * 扫码rfid页面
     */
    @GetMapping("/addRfid")
    public String addRfid( ModelMap mmap,String rfid,int index,String field,String assetsNumber) {
        mmap.put("rfid",rfid);
        mmap.put("index",index);
        mmap.put("field",field);
        mmap.put("assetsNumber",assetsNumber);
        return urlPrefix + "/addRfid";
    }
    /**
     * 根据单据号查询详细
     * @return
     */
    @GetMapping("/view/{id}")
    public String viewPage(@PathVariable("id") Long id, ModelMap mmap) {
        TransferMaterials inStorage = transferService.getById(id);
        List<TransferMaterialsItem> list = transferItemService.findByRecordNumber(inStorage.getRecordNumber());
        mmap.put("inStorage", inStorage);
        mmap.put("inStorageList", list);
        return urlPrefix + "/show";

    }



    /**
     * 根据单据号查询详细
     * @param recordNumber
     * @return
     */
    @GetMapping("/findDetailByRecordNumber")
    @ResponseBody
    public ResponseData findDetailByRecordNumber(@RequestParam String recordNumber) {
        List<TransferMaterialsItem> list = transferItemService.findByRecordNumber(recordNumber);
        return success().put("transferItem", list);
    }

    /**
     * 导入物料
     *
     * @param excelFile 物料导入
     * @return
     */
    @RequestMapping("/ReadFileText")
    @ResponseBody
    public ResponseData readFileText2(@RequestParam(value = "file", required = false) MultipartFile excelFile,String assetsNumber) {
        //============================使用模板======================================================
        InputStream inputStream = null;
        try {
            // 使用模板导入——接收上传文件
            inputStream = excelFile.getInputStream();
            List<String> list  = new ArrayList<>();
            EasyExcel.read(inputStream, Rfid.class, new AnalysisEventListener<Rfid>() {
                @Override
                public void invoke(Rfid rfid, AnalysisContext analysisContext) {

                    MaterialsRepertory byAssetsCodeOrAssetsRfid = materialsRepertoryRepository.findByAssetsRfidAndAssetsNumber(rfid.getRfid(),assetsNumber);
                    if(StringUtils.isNotEmpty(byAssetsCodeOrAssetsRfid)){
                        throw new ExcelAnalysisException(rfid.getRfid()+"当前条码/RFID未在库存中查询到材料！");
                    }else{
                        list.add(rfid.getRfid());
                    }
                }
                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {}
            }).sheet().doRead();
            return success(list);
        } catch (IOException e) {
            e.printStackTrace();
            return new ResponseData("500", "RFID" + "'" + "'" + "不正确", null);
        } catch (ExcelAnalysisException e) {
            if (e.getCause() instanceof ExcelDataConvertException) {
                ExcelDataConvertException excelDataConvertException = (ExcelDataConvertException) e.getCause();
                return new ResponseData("500", "RFID" + "'" + "'" + "不正确", null);
            } else {
                return new ResponseData("500", e.getMessage(), null);
            }
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }



}
