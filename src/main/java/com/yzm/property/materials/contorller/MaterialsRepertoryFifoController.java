package com.yzm.property.materials.contorller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.yzm.common.annotation.BussinessLog;
import com.yzm.common.annotation.RepeatSubmit;
import com.yzm.common.enums.BusinessType;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.PageBeanNew;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.basis.entity.InventorySimple;
import com.yzm.property.basis.entity.InventorySimpleItem;
import com.yzm.property.basis.repository.InventorySimpleItemRepository;
import com.yzm.property.basis.repository.InventorySimpleRepository;
import com.yzm.property.basis.service.AreaService;
import com.yzm.property.basis.service.BrandService;
import com.yzm.property.basis.service.UnitService;
import com.yzm.property.basis.service.WarehouseService;
import com.yzm.property.materials.criteria.BindingCarCriteria;
import com.yzm.property.materials.criteria.MaterialsRepertoryFifoCriteria;
import com.yzm.property.materials.entity.*;
import com.yzm.property.materials.importHeadListener.MaterialsInfoHeadDataListener;
import com.yzm.property.materials.repository.MaterialsInfoBomRepository;
import com.yzm.property.materials.repository.MaterialsRepertoryRepository;
import com.yzm.property.materials.repository.OutWarehouseItemMaterialsRepository;
import com.yzm.property.materials.service.MaterialsInfoService;
import com.yzm.property.materials.service.MaterialsRepertoryFifoService;
import com.yzm.property.materials.service.MaterialsRepertoryService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:46
 */
@Controller
@RequestMapping("/materials_repertory_fifo")
public class MaterialsRepertoryFifoController extends BaseController {
    private static Logger log = LoggerFactory.getLogger(MaterialsRepertoryFifoController.class);

    private String urlPrefix = "materials/materialsRepertoryFifo";

    @Autowired
    private MaterialsRepertoryFifoService materialsRepertoryFifoService;


    /**
     * 跳转至list页面
     *
     * @param mmap
     * @return
     */
    @RequestMapping
    public String assets(ModelMap mmap) {
        return urlPrefix + "/list";
    }


    /**
     * 材料流水
     */
    @ResponseBody
    @RequestMapping("list")
    public ResponseData getBindingCar(PageBeanNew pageBean, MaterialsRepertoryFifoCriteria criteria) {
//        if (criteria.getStartTime() != null) {
//            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
//            Date endTime = criteria.getEndTime();
//            Calendar c = Calendar.getInstance();
//            c.setTime(endTime);
//            c.add(Calendar.DATE, 1);
//            criteria.setEndTime(c.getTime());
//            System.out.println(criteria);
//        }
//
//        Sort sort = Sort.by(
//                Sort.Order.asc("status"),  // 升序排序，按字段 field1 排序
//                Sort.Order.desc("createTime")  // 降序排序，按字段 field2 排序
//        );
//        Pageable pageable = PageRequest.of(pageBean.getPage(), pageBean.getLimit(), sort);

        Map<String, Object> datas = materialsRepertoryFifoService.findAllByPage(criteria, pageBean.getPagable());
        return success(datas);
    }

    /**
     * 提交审核
     */
    @RepeatSubmit
    @RequestMapping(value = "/submit", method = RequestMethod.POST)
    @ResponseBody
    public ResponseData submit(Long[] ids) {
        return materialsRepertoryFifoService.submitByIds(ids) ? success() : error("提交失败!");
    }
    /**
     * 删除配置
     */
    @RequestMapping(value = "/del", method = RequestMethod.POST)
    @ResponseBody
    @BussinessLog(businessType=BusinessType.DELETE,title="删除部门信息")
    public ResponseData delete(Long[] ids) {
        return materialsRepertoryFifoService.deleteBatchByIds(ids) ? success() : error("删除失败!");
    }

    /**
     * 送审、同意、拒绝、取消(借用)
     */
    @RequestMapping("/audit")
    @ResponseBody
    @BussinessLog(businessType = BusinessType.UPDATE, title = "审核库存先进先出")
    public ResponseData audit(Long id,Integer status) {
        materialsRepertoryFifoService.audit(id, status);
        return ResponseData.success();
    }


    /**
     * 查看详情
     *
     * @param id
     * @return
     */
    @RequestMapping("/show/{id}")
    public ModelAndView showMaterialsInfoById(@PathVariable("id") Long id) {
        MaterialsRepertoryFifo materialsInfo = materialsRepertoryFifoService.getById(id);
        ModelAndView modelAndView = new ModelAndView(urlPrefix + "/show");
        modelAndView.addObject("materialsInfo", materialsInfo);
        return modelAndView;
    }


}
