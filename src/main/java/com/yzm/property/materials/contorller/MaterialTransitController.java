package com.yzm.property.materials.contorller;

import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.materials.criteria.MaterialTransitCriteria;
import com.yzm.property.materials.repository.MaterialTransitRepository;
import com.yzm.property.materials.service.MaterialTransitService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Map;
import java.util.UUID;

@Controller
@RequestMapping("materials/materialTransit")
@Slf4j
public class MaterialTransitController extends BaseController {
    private String urlPrefix = "materials/materialTransit";

    @Autowired
    private MaterialTransitService materialTransitService;
    @Autowired
    private MaterialTransitRepository materialTransitRepository;
    ;


    @GetMapping
    public String client(ModelMap map) {
        return urlPrefix + "/materialTransit";
    }

    /**
     * 中途材料
     */
    @ResponseBody
    @RequestMapping("list")
    public ResponseData getMaterialTransit(PageBean pageBean, MaterialTransitCriteria criteria) {
        if (criteria.getStartTime() != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date endTime = criteria.getEndTime();
            Calendar c = Calendar.getInstance();
            c.setTime(endTime);
            c.add(Calendar.DATE, 1);
            criteria.setEndTime(c.getTime());
            System.out.println(criteria);
        }
        Map<String, Object> datas = materialTransitService.findAllByPage(criteria, pageBean.getPagable(Sort.by(Direction.DESC, "id")));
        return success(datas);
    }



    String encodingExcelFilename(String filename) {
        filename = UUID.randomUUID().toString() + "_" + filename + ".xlsx";
        return filename;
    }
}
