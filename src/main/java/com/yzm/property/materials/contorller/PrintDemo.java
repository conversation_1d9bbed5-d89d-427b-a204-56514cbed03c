package com.yzm.property.materials.contorller;

import java.io.OutputStream;
import java.net.Socket;

public class PrintDemo {
    public static void main(String[] args) {
        String printerIp = "**************"; // 替换为打印机的实际 IP 地址
        int printerPort = 9100; // 打印机的端口号

        try (Socket socket = new Socket(printerIp, printerPort);
             OutputStream out = socket.getOutputStream()) {

            // 打印标签内容
            String labelContent = "<A>\n<V>100<H>200<P>2<L>0202<XM>ABCD\n<Z>";
            
            // 要打印的标签数量
            int printCount = 1;

            // 构建 ESC + Q 命令
            byte[] escQCommand = new byte[]{(byte) 0x1B, 0x51, (byte) printCount};

            // 发送命令到打印机
            out.write(labelContent.getBytes()); // 发送标签内容
            out.write(escQCommand); // 发送打印数量命令
            out.flush();

            System.out.println("命令已发送到打印机，打印数量设置为: " + printCount);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
