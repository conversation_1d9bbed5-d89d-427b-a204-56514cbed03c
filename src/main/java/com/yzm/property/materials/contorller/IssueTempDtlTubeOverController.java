package com.yzm.property.materials.contorller;

import cn.hutool.core.io.FileUtil;
import com.alibaba.excel.EasyExcel;
import com.yzm.common.annotation.BussinessLog;
import com.yzm.common.enums.BusinessType;
import com.yzm.common.utils.ShiroUtils;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.framework.freemark.Global;
import com.yzm.framework.shiro.LoginUser;
import com.yzm.property.basis.entity.InventorySimple;
import com.yzm.property.basis.entity.InventorySimpleItem;
import com.yzm.property.consumable.criteria.ConsumableListCriteria;
import com.yzm.property.materials.criteria.IssueTempDtlCriteria;
import com.yzm.property.materials.criteria.IssueTempDtlTubeCriteria;
import com.yzm.property.materials.criteria.IssueTempDtlTubeItemCriteria;
import com.yzm.property.materials.entity.IssueTempDtlTube;
import com.yzm.property.materials.entity.IssueTempDtlTubeItem;
import com.yzm.property.materials.pojo.InventorySimpleExcel;
import com.yzm.property.materials.pojo.IssueTempDtlTubeItemExcel;
import com.yzm.property.materials.service.IssueTempDtlService;
import com.yzm.property.materials.service.IssueTempDtlTubeItemService;
import com.yzm.property.materials.service.IssueTempDtlTubeService;
import com.yzm.property.system.service.impl.SysUserServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

@Controller
@RequestMapping("materials/issueTempDtlTubeOver")
@Slf4j
public class IssueTempDtlTubeOverController extends BaseController {
    private String urlPrefix = "materials/issueTempDtlTube";

    @Autowired
    private IssueTempDtlTubeService issueTempDtlService;

    @Autowired
    private IssueTempDtlTubeItemService issueTempDtlTubeItemService;

    @Autowired
    private IssueTempDtlService tempDtlService;

    @Autowired
    private SysUserServiceImpl sysUserService;

    @GetMapping
    public String list(ModelMap map) {

        map.put("lineCList", new ArrayList<>());
        return urlPrefix + "/listOver";
    }

    @RequestMapping("/deliveryStorageTube")
    public String deliveryStorage(ModelMap mmap, Long id) {
        IssueTempDtlTube byId = issueTempDtlService.getById(id);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String formattedDate = sdf.format(byId.getPlanIssDate());
        String formattedDate1 = sdf.format(byId.getPlanIssDate1());
        mmap.put("formattedPlannedIssueDt", formattedDate);
        mmap.put("formattedPlannedIssueDt1", formattedDate1);
        mmap.put("issueTempDtl", byId);
        return urlPrefix + "/deliveryStorage";
    }


    /**
     * 根据ID查询详细
     *
     * @param id
     * @return
     */
    @RequestMapping("/findDetailsData")
    @ResponseBody
    public ResponseData findDetailsData(@RequestParam Long id) {
        List<IssueTempDtlTubeItem> allBy = issueTempDtlTubeItemService.findByPrimaryId(id);
        return success().put("issueTempDtlList", allBy);
    }






    @RequestMapping("/scanSelect")
    public String scanSelect(ModelMap mmap,String id,String type) {
        mmap.put("id", id);
        mmap.put("type", type);
        return urlPrefix + "/selectType";
    }
    @RequestMapping("/inputScan")
    public String inputScan(ModelMap mmap,String id,String type) {
        mmap.put("id", id);
        mmap.put("type", type);
        return urlPrefix + "/inputScan";
    }
    @RequestMapping("/allocation")
    public String allocation(ModelMap mmap,String ids) {
        mmap.put("ids", ids);
        mmap.put("userList", sysUserService.findAll());
        return urlPrefix + "/allocation";
    }
    @BussinessLog(title = "分配发料员", businessType = BusinessType.UPDATE)
    @RequestMapping(value = "/allocationByIds", method = RequestMethod.POST)
    @ResponseBody
    public ResponseData allocation(@RequestParam("ids") Long[] ids,@RequestParam("salesmanId") Long salesmanId){
        return issueTempDtlService.allocationByIds(ids,salesmanId)?success():error("分配失败");
    }
    /**
     * 根据ID查询详细
     *
     * @param id
     * @return
     */
    @RequestMapping("/findDetails")
    @ResponseBody
    public ResponseData findDetail(@RequestParam Long id) {
        List<IssueTempDtlTubeItem> allBy = issueTempDtlTubeItemService.findByPrimaryId(id);

        return success().put("issueTempDtlList", allBy);
    }
 /* @ResponseBody
    @PostMapping(value = "/list")*/
   /* public ResponseData page(PageBean pageBean, BorrowReturnCriteria area) {
        Map<String, Object> datas = borrowReturnService.findAllByPage(area, pageBean.getPagable());
        return success(datas);
    }*/


    @ResponseBody
    @PostMapping(value = "/list2")
    public ResponseData page(PageBean pageBean, IssueTempDtlTubeItemCriteria issueTempDtlTubeItem) {
//        area.setStatusData(2L);
//        LoginUser userInfo = ShiroUtils.getUserInfo();
//        if(!userInfo.getUsername().equals("admin")){
//            area.setIssuerId(userInfo.getId());
//        }
//        Map<String, Object> datas = issueTempDtlTubeItemService.findAllByPageGroup(issueTempDtlTubeItem, pageBean.getPagable());
        Map<String, Object> datas = issueTempDtlTubeItemService.findAllByPage(issueTempDtlTubeItem, pageBean.getPagable());
        return success(datas);
    }
//
//    /**
//     * 提交审核
//     */
//    @RepeatSubmit
//    @RequestMapping(value = "/scanOk", method = RequestMethod.POST)
//    @ResponseBody
//    public ResponseData scanOk(Long id) {
//        IssueTempDtlItem one = issueTempDtlItemRepository.getOne(id);
//        one.setSucceed(1L);
//
//
//        IssueTempDtlItem issueTempDtlItem = issueTempDtlItemRepository.saveAndFlush(one);
//
//
//        List<IssueTempDtlItem> allOrderByPrimaryId = issueTempDtlItemRepository.findAllOrderByPrimaryId(one.getPrimaryId());
//        for (IssueTempDtlItem item : allOrderByPrimaryId) {
//            item.setSucceed(1L);
//            issueTempDtlItemRepository.saveAndFlush(item);
//        }
//
//
//        IssueTempDtl one1 = issueTempDtlRepository.getOne(one.getPrimaryId());
//
//        int count = issueTempDtlItemRepository.findByPrimaryIdAndCountComparison(one1.getId());
//        if (count == 0) {
//            one1.setStatus(2L);
//        } else {
//            one1.setStatus(1L);
//        }
//        issueTempDtlRepository.saveAndFlush(one1);
//
//
//        return ResponseData.success(issueTempDtlItem);
//    }

    /**
     * 出库操作
     *
     * @return
     */
    @PostMapping("/deliveryStorageTube")
    @ResponseBody
    public ResponseData deliveryStorageTube(IssueTempDtlTube issueTempDtlTubeItem) {
        return issueTempDtlService.deliveryStorage(issueTempDtlTubeItem);
    }


    /**
     * 同步数据
     *
     * @return
     */
    @RequestMapping("/synchronizationData")
    @ResponseBody
    public ResponseData synchronizationData() {
        return tempDtlService.synchronizationDataTube();
    }

    /**
     * 根据单据号查询详细
     *
     * @return
     */
    @GetMapping("/view/{id}")
    public String viewPage(@PathVariable("id") Long id, ModelMap mmap) {
        IssueTempDtlTube byId = issueTempDtlService.getById(id);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String formattedDate = sdf.format(byId.getPlanIssDate());
        mmap.put("formattedPlannedIssueDt", formattedDate);
        mmap.put("issueTempDtl", byId);
        return urlPrefix + "/show";

    }

    @BussinessLog(title = "胶管出库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public ResponseData export(IssueTempDtlTubeItemCriteria issueTempDtlTubeItemCriteria) throws Exception {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String fileName = "切割胶管作业日报";
        fileName = encodingExcelFilename(fileName);
        String folder = Global.getTempPath() + File.separator + "pio" + File.separator;
        FileUtil.touch(folder + fileName);
        List<IssueTempDtlTubeItem> listData = issueTempDtlTubeItemService.findAll(issueTempDtlTubeItemCriteria);
       List<IssueTempDtlTubeItemExcel> issueTempDtlTubeItemExcels = new ArrayList<>();
        for (IssueTempDtlTubeItem listDatum : listData) {
            IssueTempDtlTubeItemExcel issueTempDtlTubeItemExcel = new IssueTempDtlTubeItemExcel();
            BeanUtils.copyProperties(listDatum,issueTempDtlTubeItemExcel);
            issueTempDtlTubeItemExcel.setPlanIssDate1(simpleDateFormat.format(listDatum.getPlanIssDate1()));
            issueTempDtlTubeItemExcel.setOperatingTime(simpleDateFormat2.format(listDatum.getOperatingTime()));
            issueTempDtlTubeItemExcel.setCutingQty(listDatum.getCutingQty().toString());
            issueTempDtlTubeItemExcel.setCutLen(listDatum.getCutLen().toString());
            issueTempDtlTubeItemExcel.setFirstLen(listDatum.getFirstLen().toString());
            issueTempDtlTubeItemExcel.setEndLen(listDatum.getEndLen().toString());
            issueTempDtlTubeItemExcel.setQuantityDelivered(listDatum.getQuantityDelivered().toString());
            issueTempDtlTubeItemExcel.setQuantityDeliveredLen(listDatum.getQuantityDeliveredLen().toString());
            issueTempDtlTubeItemExcels.add(issueTempDtlTubeItemExcel);
        }

        EasyExcel.write(folder + fileName, IssueTempDtlTubeItemExcel.class).sheet("作业日报").doWrite(issueTempDtlTubeItemExcels);
        return success(fileName);
    }

    String encodingExcelFilename(String filename) {
        filename = UUID.randomUUID().toString() + "_" + filename + ".xlsx";
        return filename;
    }
}
