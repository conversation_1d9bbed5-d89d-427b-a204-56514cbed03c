package com.yzm.property.materials.contorller;

import cn.hutool.core.io.FileUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yzm.common.annotation.BussinessLog;
import com.yzm.common.enums.BusinessType;
import com.yzm.common.utils.OrderUtils;
import com.yzm.common.utils.ShiroUtils;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.framework.freemark.DictUtil;
import com.yzm.framework.freemark.Global;
import com.yzm.framework.shiro.LoginUser;
import com.yzm.property.basis.entity.InventorySimple;
import com.yzm.property.basis.entity.InventorySimpleItem;
import com.yzm.property.business.entity.BorrowReturnItem;
import com.yzm.property.business.repository.BorrowReturnItemRepository;
import com.yzm.property.materials.criteria.MaterialsRepertoryCriteria;
import com.yzm.property.materials.entity.*;
import com.yzm.property.materials.importHeadListener.MaterialsInfoHeadDataListener;
import com.yzm.property.materials.importHeadListener.MaterialsRepertoryHeadDataListener;
import com.yzm.property.materials.pojo.InventorySimpleExcel;
import com.yzm.property.materials.pojo.MaterialsRepertoryAllExcel;
import com.yzm.property.materials.pojo.MaterialsRepertoryExcel;
import com.yzm.property.materials.service.MaterialsInfoService;
import com.yzm.property.materials.service.MaterialsRepertoryFifoService;
import com.yzm.property.materials.service.MaterialsRepertoryService;
import com.yzm.property.system.entity.SysDictData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 材料库存列表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:46
 */
@Controller
@RequestMapping("/materials_repertory")
public class MaterialsRepertoryController extends BaseController {
    private Logger logger = LoggerFactory.getLogger(MaterialsRepertoryController.class);

    private String urlPrefix = "materials/consumableRepertory";


    @Autowired
    private MaterialsInfoService materialsInfoService;
    @Autowired
    private MaterialsRepertoryService materialsRepertoryService;
    @Autowired
    private MaterialsRepertoryFifoService materialsRepertoryFifoService;

    @Autowired
    private BorrowReturnItemRepository borrowReturnItemRepository;


    /**
     * 跳转至list页面
     *
     * @param mmap
     * @return
     */
    @RequestMapping
    public String assets(ModelMap mmap) {
        return urlPrefix + "/list";
    }

    /**
     * 跳转至list页面
     *
     * @param mmap
     * @return
     */
    @RequestMapping("/listSummary")
    public String listSummary(ModelMap mmap) {
        return urlPrefix + "/listSummary";
    }

    /**
     * 根据关键字和分页查询资产
     *
     * @param keyword
     * @param pageBean
     * @return
     */
    @RequestMapping("/list")
    @ResponseBody
    public ResponseData showAllMaterialsRepository(MaterialsRepertoryCriteria repertoryCriteria, PageBean pageBean) {
        return success(materialsRepertoryService.findAllByPage(repertoryCriteria, pageBean.getPagable()));
    }

    /**
     * 根据关键字和分页查询资产
     *
     * @param keyword
     * @param pageBean
     * @return
     */
    @PostMapping("/listSummaryInfo")
    @ResponseBody
    public ResponseData listSummaryInfo(
            @RequestParam(value = "keyword", required = false)
                    String keyword,@RequestParam(value = "dept", required = false)
            String dept,
            PageBean pageBean
    ) {
        if(StringUtils.isEmpty(dept)){
            dept = "";
        }
        return success(materialsRepertoryService.showAllMaterialsSummaryInfo(keyword,dept, pageBean));
    }

    /**
     * 删除配置
     */
    @RequestMapping(value = "/del", method = RequestMethod.POST)
    @ResponseBody
    @BussinessLog(businessType = BusinessType.DELETE, title = "删除库存信息")
    public ResponseData delete(Long[] ids) {
        for (Long id : ids) {
            materialsRepertoryService.deleteById(id);
        }

        return success();
    }

    /**
     * 删除配置
     */
    @RequestMapping(value = "/delAll", method = RequestMethod.POST)
    @ResponseBody
    @BussinessLog(businessType = BusinessType.DELETE, title = "删除库存汇总信息")
    public ResponseData delAll(Long[] ids) {
        for (Long id : ids) {
            MaterialsRepertory byId = materialsRepertoryService.getById(id);
            List<MaterialsRepertory> byAssetsNumber = materialsRepertoryService.findByAssetsNumber(byId.getAssetsNumber());
            for (MaterialsRepertory materialsRepertory : byAssetsNumber) {
                materialsRepertoryService.delete(materialsRepertory);
            }
        }
        return success();
    }


    /**
     * 查询材料信息根据条码和rfid
     *
     * @param rfid
     * @param code
     * @return
     */
    @GetMapping("/findDetailByRfidOrCodeDB")
    @ResponseBody
    public ResponseData findDetailByRfidOrCodeDB(@RequestParam String rfid, @RequestParam String code, @RequestParam String assetsNumber) {
        MaterialsRepertory consumableRepertory = materialsRepertoryService.findByAssetsRfidAndAssetsNumber(rfid, assetsNumber);
        if (StringUtils.isEmpty(consumableRepertory)) {
            return error("当前条码/RFID未在库存中查询到材料！");
        }
        return success().put("consumableInfo", consumableRepertory);
    }

    /**
     * 查询材料信息根据条码和rfid
     *
     * @param rfid
     * @param code
     * @return
     */
    @GetMapping("/findDetailByRfidOrCode")
    @ResponseBody
    public ResponseData findDetailByRfidOrCode(@RequestParam String rfid, @RequestParam String code) {
        MaterialsRepertory consumableRepertory = materialsRepertoryService.findByAssetsCodeOrAssetsRfid(rfid, code);
        if (StringUtils.isEmpty(consumableRepertory)) {
            return error("当前条码/RFID未查询到库存！");
        }
        return success().put("consumableInfo", consumableRepertory);
    }

    /**
     * 查询材料信息根据条码和rfid
     *
     * @param rfid
     * @param code
     * @return
     */
    @GetMapping("/findDetailByRfidOrCodeCheck")
    @ResponseBody
    public ResponseData findDetailByRfidOrCodeCheck(@RequestParam String rfid, @RequestParam String code) {
        MaterialsRepertory consumableRepertory = materialsRepertoryService.findByAssetsCodeOrAssetsRfid(rfid, code);
        BorrowReturnItem byAssetsRfidAndBiReturnStandby = borrowReturnItemRepository.findByAssetsRfidAndBiReturnStandby(rfid, 0);
        if (StringUtils.isNotEmpty(byAssetsRfidAndBiReturnStandby)) {
            return ResponseData.error(rfid + ":当前RFID数据存在未归还信息！");
        }
        if (StringUtils.isEmpty(consumableRepertory)) {
            return success("OK");
        } else {
            return error("当前RFID号已经入库!");
        }
    }


    /**
     * 导入库存
     *
     * @param excelFile 库存导入
     * @return
     */
    @RequestMapping("/ReadFileText")
    @ResponseBody
    public ResponseData readFileText2(@RequestParam(value = "file", required = false) MultipartFile excelFile) {
        //============================使用模板======================================================
        InputStream inputStream = null;
        try {
            // 使用模板导入——接收上传文件
            inputStream = excelFile.getInputStream();
            // 读取表格数据
            EasyExcel.read(inputStream, MaterialsRepertory.class, new MaterialsRepertoryHeadDataListener()).headRowNumber(1).doReadAll();

            // TODO 根据业务处理objectList……
        } catch (IOException e) {
            e.printStackTrace();
        } catch (ExcelAnalysisException e) {
            return ResponseData.error(e.getMessage());
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return success("导入成功");
    }

    private static final String REGEX = "^([^,]*,){7}[^,]*$";

    public static boolean isValidFormat(String text) {
        Pattern pattern = Pattern.compile(REGEX);
        Matcher matcher = pattern.matcher(text);
        return matcher.matches();
    }

    public static String[] parseFormat(String text) {
        // 使用下划线分隔字符串
        String[] parts = text.split(",");

        // 检查是否有3部分
        if (parts.length == 8) {
            return parts;
        } else {
            return null;
        }
    }

    /**
     * 申请先进先出
     *
     * @param code
     * @return
     */
    @PostMapping("/uploadFIFO")
    @ResponseBody
    public ResponseData uploadFIFO(@RequestParam String code) {
        MaterialsRepertory byAssetsCodeOrAssetsRfid = materialsRepertoryService.findByAssetsCodeOrAssetsRfid(code, code);
        List<MaterialsRepertory> byAssetsNumberAndFifoStatusOrderByDateArrivalAsc = materialsRepertoryService.findByAssetsNumberAndFifoStatusOrderByDateArrivalAsc(byAssetsCodeOrAssetsRfid.getAssetsNumber(), 1);
        if (byAssetsNumberAndFifoStatusOrderByDateArrivalAsc.size() > 0) {
//            MaterialsRepertory materialsRepertory = byAssetsNumberAndFifoStatusOrderByDateArrivalAsc.get(0);
            MaterialsRepertoryFifo materialsRepertoryFifo = materialsRepertoryFifoService.findByAssetsNumberAndBatchNumber(byAssetsCodeOrAssetsRfid.getAssetsNumber(), byAssetsCodeOrAssetsRfid.getBatchNumber());
            if (materialsRepertoryFifo != null) {
//                if(materialsRepertoryFifo.getStatus()==2L){
                return ResponseData.error("材料号码" + materialsRepertoryFifo.getAssetsNumber() + "批号" + materialsRepertoryFifo.getBatchNumber() + "已经提交");
//                }
            }
            MaterialsRepertoryFifo fifo = new MaterialsRepertoryFifo();
            BeanUtils.copyProperties(byAssetsCodeOrAssetsRfid, fifo);
            fifo.setId(null);
            fifo.setCreateBy(null);
            fifo.setCreateTime(null);
            fifo.setUpdateBy(null);
            fifo.setUpdateTime(null);
            fifo.setStatus(2L);
            String parameterNo = OrderUtils.getFifoCode();
            fifo.setRecordNumber(parameterNo);
            fifo.setApplicant(ShiroUtils.getUserInfo().getName());//出库人
            materialsRepertoryFifoService.save(fifo);
        }


        return ResponseData.success("提交成功");
    }


    /**
     * 校验库存条码内容
     *
     * @param code
     * @return
     */
    @GetMapping("/verifyCode")
    @ResponseBody
    public ResponseData verifyCode(@RequestParam String code) {
        if (!isValidFormat(code)) {
            return ResponseData.error("条码格式错误请重新扫描");
        }
        // 解析
        String[] parts = parseFormat(code);

        if (parts != null) {
            System.out.println("编号: " + parts[0]);
            System.out.println("名称: " + parts[1]);
            System.out.println("批号: " + parts[2]);
            System.out.println("批次数量: " + parts[3]);
            System.out.println("总数量: " + parts[4]);
            System.out.println("来料日期: " + parts[5]);
            System.out.println("包装数量: " + parts[6]);
            System.out.println("流水号: " + parts[7]);
        } else {
            System.out.println(code + "-格式无效");
        }
        if (StringUtils.isEmpty(parts)) {
            return error(code + "-条码格式错误！");
        }
        String number = parts[0].trim();
        number = StringUtils.replaceFifthCharacter(number);

        MaterialsRepertory byAssetsCodeOrAssetsRfid = materialsRepertoryService.findByAssetsCodeOrAssetsRfid(code, code);
        if (byAssetsCodeOrAssetsRfid == null) {
            byAssetsCodeOrAssetsRfid = materialsRepertoryService.findByAssetsNumberAndAssetsCodeIsNull(number);
            if (byAssetsCodeOrAssetsRfid == null) {
                return error(number + "未查询到库存信息");
            }
            BigDecimal decimal = new BigDecimal(1);

            MaterialsInfo materialsInfo = materialsInfoService.findByAssetsNumber(number);
            if (StringUtils.isNotEmpty(materialsInfo)) {
                if (StringUtils.isNotEmpty(materialsInfo.getMinLot())) {
                    decimal = materialsInfo.getMinLot();
                }
            }
            BigDecimal decimal1 = new BigDecimal(parts[4]);
            byAssetsCodeOrAssetsRfid.setNowRepertory(decimal1.multiply(decimal));
            byAssetsCodeOrAssetsRfid.setAssetsCode(code);
            byAssetsCodeOrAssetsRfid.setBatchNumber(parts[2]);
        }

        List<MaterialsRepertory> materialsList = materialsRepertoryService.findByAssetsNumberAndFifoStatusOrderByDateArrivalAsc(byAssetsCodeOrAssetsRfid.getAssetsNumber(), 1L);
        if (StringUtils.isEmpty(byAssetsCodeOrAssetsRfid.getInDataType())) {
            byAssetsCodeOrAssetsRfid.setInDataType(0L);
        }

        if (!StringUtils.isEmpty(byAssetsCodeOrAssetsRfid.getPriority()) && byAssetsCodeOrAssetsRfid.getPriority() == 1L || byAssetsCodeOrAssetsRfid.getInDataType() == 1L) {

        } else {
            try {
// 获取入库最早的物料
                MaterialsRepertory oldestMaterial = materialsList.get(0);
                // 获取当前扫描的物料
                MaterialsRepertory currentMaterial = byAssetsCodeOrAssetsRfid; // 假设最后一个是当前的


// 获取最早物料和当前物料的入库时间
                LocalDate oldestDate = oldestMaterial.getDateArrival().toInstant()
                        .atZone(ZoneId.systemDefault()).toLocalDate();
                LocalDate currentDate = currentMaterial.getDateArrival().toInstant()
                        .atZone(ZoneId.systemDefault()).toLocalDate();

// 比较年份和月份
//                boolean sameMonthAndYear = oldestDate.getYear() >= currentDate.getYear()
//                        && oldestDate.getMonth().getValue() >= currentDate.getMonth().getValue();
                boolean sameMonthAndYear = oldestDate.getYear() > currentDate.getYear()
                        || (oldestDate.getYear() == currentDate.getYear()
                        && oldestDate.getMonth().getValue() >= currentDate.getMonth().getValue());

                boolean statusInfo = false;

// 如果年份和月份相同，则不严格要求FIFO
                if (sameMonthAndYear) {
                    statusInfo = true;
                    // return success("入库时间在同一个月份，物料无需判断先进先出(FIFO)。");
                }
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
// 如果年份和月份不同，且两个物料不相同，则需要判断FIFO
                if (!statusInfo) {
                    return error("50002", "<div>当前物料未满足先进先出原则！最早来料日期:" + simpleDateFormat.format(oldestMaterial.getDateArrival()) + "最早库存批号: <span id='batchNumber'>" + oldestMaterial.getBatchNumber() + "</span></div><div style='color:red'>点击确定提交排除申请，取消则不提交！</div>");
                }
            } catch (Exception e) {
                logger.error("先进先出计算有误排除--------------------------");
            }

// 如果满足条件，返回成功
//            return success("物料出库符合先进先出原则。");


//
//            // 计算最早物料与当前物料的入库时间差
//            Duration duration = Duration.between(oldestMaterial.getDateArrival().toInstant(), currentMaterial.getDateArrival().toInstant());
//            long daysDifference = duration.toDays();
//            boolean statusInfo = false;
//
//            // 如果两个物料的入库时间差小于或等于30天，则不严格要求FIFO
//            if (daysDifference <= 30) {
//                statusInfo = true;
////                    return success("入库时间相差小于或等于30天，物料无需判断先进先出(FIFO)。");
//            }
//
//            // 如果入库时间差超过30天，则需要判断FIFO
//            if (!oldestMaterial.equals(currentMaterial) && statusInfo == false) {
//                return error("当前物料未满足先进先出原则！最早库存批号" + oldestMaterial.getBatchNumber());
//            }

            // 如果符合FIFO规则
//                return success("物料符合先进先出(FIFO)原则");
        }
        if (byAssetsCodeOrAssetsRfid == null) {
            return error(code + "未查询到库存信息");
        }
        if (StringUtils.isEmpty(byAssetsCodeOrAssetsRfid.getNowRepertory()) || byAssetsCodeOrAssetsRfid.getNowRepertory().compareTo(new BigDecimal(0)) == 0) {
            return error(byAssetsCodeOrAssetsRfid.getAssetsCode() + "库存数量为0无法扫码，请删除后重新入库");

        }
        return success("扫码成功").put("materialsRepertory", byAssetsCodeOrAssetsRfid);
    }

    public static void main(String[] args) {
        String a = "1242-1241 ";
        String number = a;
        if (number.length() == 9) {
            number = number.replaceAll("-", "");
        }
        System.out.println(number);
    }

    @Autowired
    private DictUtil dictUtil;

    @BussinessLog(title = "库存明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public ResponseData export(MaterialsRepertoryCriteria materialsRepertoryCriteria) throws Exception {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String fileName = "库存明细";
        fileName = encodingExcelFilename(fileName);
        String folder = Global.getTempPath() + File.separator + "pio" + File.separator;
        FileUtil.touch(folder + fileName);
        List<MaterialsRepertoryExcel> list = new ArrayList<>();
        String mode_of_trade_type = dictUtil.getDictListJson("mode_of_trade_type");
        List<JSONObject> jsonObjects = JSONArray.parseArray(mode_of_trade_type, JSONObject.class);
        Map<String, String> map = new HashMap<>();
        for (JSONObject sysDictData : jsonObjects) {
            map.put(sysDictData.getString("dictValue"), sysDictData.getString("dictLabel"));
        }

        List<MaterialsRepertory> materialsRepertoryList = materialsRepertoryService.findAll(materialsRepertoryCriteria);
        for (MaterialsRepertory materialsRepertory : materialsRepertoryList) {
            MaterialsRepertoryExcel materialsRepertoryExcel = new MaterialsRepertoryExcel();
            BeanUtils.copyProperties(materialsRepertory, materialsRepertoryExcel);
            materialsRepertoryExcel.setModeOfTrade(map.get(materialsRepertory.getModeOfTrade() + ""));
            materialsRepertoryExcel.setNowRepertory(materialsRepertory.getNowRepertory().toString());
            list.add(materialsRepertoryExcel);
        }
        EasyExcel.write(folder + fileName, MaterialsRepertoryExcel.class).sheet("库存明细").doWrite(list);
        return success(fileName);
    }

    @BussinessLog(title = "库存汇总", businessType = BusinessType.EXPORT)
    @PostMapping("/exportAll")
    @ResponseBody
    public ResponseData exportAll(MaterialsRepertoryCriteria materialsRepertoryCriteria) throws Exception {
        String fileName = "库存汇总";
        fileName = encodingExcelFilename(fileName);
        String folder = Global.getTempPath() + File.separator + "pio" + File.separator;
        FileUtil.touch(folder + fileName);
        List<MaterialsRepertoryAllExcel> list = new ArrayList<>();

        List<MaterialsRepertory> byNowRepertoryAll = materialsRepertoryService.findByNowRepertoryAll(materialsRepertoryCriteria.getKeyword());
        for (MaterialsRepertory materialsRepertory : byNowRepertoryAll) {
            MaterialsRepertoryAllExcel materialsRepertoryExcel = new MaterialsRepertoryAllExcel();
            BeanUtils.copyProperties(materialsRepertory, materialsRepertoryExcel);
            materialsRepertoryExcel.setNowRepertory(materialsRepertory.getNowCount().toString());
            list.add(materialsRepertoryExcel);
        }
        EasyExcel.write(folder + fileName, MaterialsRepertoryAllExcel.class).sheet("库存汇总").doWrite(list);
        return success(fileName);
    }

    String encodingExcelFilename(String filename) {
        filename = UUID.randomUUID().toString() + "_" + filename + ".xlsx";
        return filename;
    }
}
