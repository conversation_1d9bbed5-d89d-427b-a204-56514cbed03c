package com.yzm.property.materials.contorller;

import cn.hutool.core.io.FileUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.alibaba.fastjson.JSONObject;
import com.yzm.common.annotation.BussinessLog;
import com.yzm.common.enums.BusinessType;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.framework.freemark.Global;
import com.yzm.property.basis.entity.InventorySimple;
import com.yzm.property.basis.entity.InventorySimpleItem;
import com.yzm.property.basis.service.BrandService;
import com.yzm.property.basis.service.UnitService;
import com.yzm.property.materials.criteria.MaterialsBomCriteria;
import com.yzm.property.materials.entity.MaterialsInfo;
import com.yzm.property.materials.entity.MaterialsInfoBom;
import com.yzm.property.materials.importHeadListener.DynamicHeadListener;
import com.yzm.property.materials.importHeadListener.MaterialsInfoHeadDataListener;
import com.yzm.property.materials.importHeadListener.MaterialsInfoTransitHeadDataListener;
import com.yzm.property.materials.pojo.InventorySimpleExcel;
import com.yzm.property.materials.pojo.MaterialsInfoBomExcel;
import com.yzm.property.materials.service.IssueTempDtlService;
import com.yzm.property.materials.service.MaterialsInfoBomService;
import com.yzm.property.materials.service.MaterialsInfoService;
import com.yzm.property.materials.service.MaterialsRepertoryService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:46
 */
@Controller
@RequestMapping("/materials_info_bom")
public class MaterialsInfoBomController extends BaseController {

    private String urlPrefix = "materials/materialsInfoBom";

    @Autowired
    private MaterialsInfoService materialsInfoService;
    @Autowired
    private MaterialsInfoBomService materialsInfoBomService;
    @Autowired
    private MaterialsRepertoryService materialsInfoBomRepertoryService;
    @Autowired
    private BrandService brandService;
    @Autowired
    private UnitService unitService;//在途计量单位
    @Autowired
    private IssueTempDtlService issueTempDtlService;//在途计量单位

    /**
     * 跳转至list页面
     *
     * @param mmap
     * @return
     */
    @RequestMapping
    public String assets(ModelMap mmap) {
        return urlPrefix + "/list";
    }

    /**
     * bom选择在途物资
     */
    @GetMapping("/addMaterialsInfo")
    public String addMaterialsInfo() {
        return urlPrefix + "/addMaterialsInfo";
    }

    /**
     * bom明细
     */
    @GetMapping("/addBomItem")
    public String addBomItem() {
        return urlPrefix + "/addBomItem";
    }


    /**
     * 跳转至新增页面，携带在途类型以及在途分类数据
     *
     * @param mmap
     * @return
     */
    @GetMapping("/add")
    public String addMaterials(ModelMap mmap) {
        mmap.put("unit", unitService.findAll());
        mmap.put("brandList", brandService.findAll());
        return urlPrefix + "/add";
    }

    /**
     * 修改跳转页面
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        mmap.put("materialsInfoBomInfo", materialsInfoBomService.getById(id));
        mmap.put("unit", unitService.findAll());
        mmap.put("brandList", brandService.findAll());
        return urlPrefix + "/edit";
    }

    /**
     * 修改跳转页面
     */
    @GetMapping("/handleData/{id}")
    public String handleData(@PathVariable("id") Long id, ModelMap mmap) {
        mmap.put("materialsInfoBomInfo", materialsInfoBomService.getById(id));
        return urlPrefix + "/handleData";
    }


    /**
     * 新增在途物资
     *
     * @param materialsInfoBomInfo
     * @return
     */
    @PostMapping("/add")
    @ResponseBody
    @BussinessLog(businessType = BusinessType.INSERT, title = "新增在途物资")
    public ResponseData addMaterialsInfo(MaterialsInfo materialsInfoBomInfo) {
        return materialsInfoBomService.addMaterialsInfoBom(materialsInfoBomInfo);
    }


    /**
     * 修改在途
     */
    @RequestMapping(value = "/edit", method = RequestMethod.POST)
    @ResponseBody
    @BussinessLog(businessType = BusinessType.UPDATE, title = "修改在途物资信息")
    public ResponseData updateMaterialsInfo(MaterialsInfo materialsInfoBomInfo) {
        return materialsInfoBomService.updateMaterialsInfo(materialsInfoBomInfo);
    }

    /**
     * 审核
     */
    @BussinessLog(title = "在途状态修改", businessType = BusinessType.UPDATE)
    @RequestMapping(value = "/handleDataSave", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public ResponseData handleDataSave(MaterialsInfoBom materialsInfoBom) {

        return materialsInfoBomService.handleDataSave(materialsInfoBom) ? success() : error("在途状态修改失败!");
    }

    /**
     * 根据id列表删除在途
     *
     * @param ids
     * @return
     */
    @PostMapping("/delete")
    @ResponseBody
    @BussinessLog(businessType = BusinessType.DELETE, title = "删除在途物资")
    public ResponseData deleteMaterialsInfo(Long[] ids) {
        materialsInfoBomService.deleteBatchByIds(ids);
        return success();
    }

    /**
     * 根据id列表删除在途
     *
     * @param ids
     * @return
     */
    @PostMapping("/deleteAll")
    @ResponseBody
    @BussinessLog(businessType = BusinessType.DELETE, title = "删除在途物资集装箱")
    public ResponseData deleteAll(Long[] ids) {
        for (Long id : ids) {
            MaterialsInfoBom byId = materialsInfoBomService.getById(id);
            List<MaterialsInfoBom> byContainerNumber = materialsInfoBomService.findByContainerNumberAndInvoiceNumber(byId.getContainerNumber(),byId.getInvoiceNumber());
            for (MaterialsInfoBom materialsInfoBom : byContainerNumber) {
                materialsInfoBomService.delete(materialsInfoBom);
            }
        }
        return success();
    }

    /**
     * 根据关键字和分页查询在途
     *
     * @param pageBean
     * @return
     */
    @PostMapping("/listGroup")
    @ResponseBody
    public ResponseData showAllMaterialsInfoGroup(MaterialsBomCriteria materialsBomCriteria, PageBean pageBean) {
//        materialsBomCriteria.setBomNumber("1");
        Map<String, Object> allByPage = materialsInfoBomService.findAllByPageGroup(materialsBomCriteria, pageBean.getPagable());
        return success(allByPage);
    }

    /**
     * 根据关键字和分页查询在途
     *
     * @param pageBean
     * @return
     */
    @PostMapping("/list")
    @ResponseBody
    public ResponseData showAllMaterialsInfo(MaterialsBomCriteria materialsBomCriteria, PageBean pageBean) {
        if (StringUtils.isEmpty(materialsBomCriteria.getContainerNumber())) {
            materialsBomCriteria.setContainerNumberInfo("1");
        }
//        materialsBomCriteria.setBomNumber("1");
        Map<String, Object> allByPage = materialsInfoBomService.findAllByPage(materialsBomCriteria, pageBean.getPagable());
        return success(allByPage);
    }
    /**
     * 同步数据
     *
     * @return
     */
    @RequestMapping("/synchronizationData")
    @ResponseBody
    public ResponseData synchronizationData() {
        return issueTempDtlService.synchronizationData3();
    }

    /**
     * 查看详情
     *
     * @param id
     * @return
     */
    @RequestMapping("/show/{id}")
    public ModelAndView showMaterialsInfoById(@PathVariable("id") Long id) {
        MaterialsInfoBom materialsInfoBomInfo = materialsInfoBomService.getById(id);
        ModelAndView modelAndView = new ModelAndView(urlPrefix + "/show");
        modelAndView.addObject("unit", unitService.findAll());
        modelAndView.addObject("containerNumber", materialsInfoBomInfo.getContainerNumber());
        modelAndView.addObject("invoiceNumber", materialsInfoBomInfo.getInvoiceNumber());
        modelAndView.addObject("materialsInfoBomInfo", materialsInfoBomInfo);
        return modelAndView;
    }

    /*获取子表信息*/
    @RequestMapping("/getData")
    @ResponseBody
    public ResponseData getInfo(Long id) {
        MaterialsInfoBom byId = materialsInfoBomService.getById(id);
        List<MaterialsInfoBom> list = materialsInfoBomService.findByContainerNumber(byId.getContainerNumber());
        return success().put("itemJson", list);
    }
//    /**
//     * 查询在途物资信息根据条码和rfid
//     *
//     * @param rfid
//     * @param code
//     * @return
//     */
//    @GetMapping("/findDetailByRfidOrCode")
//    @ResponseBody
//    public ResponseData findDetailByRfidOrCode(@RequestParam String rfid, @RequestParam String code) {
//        MaterialsInfo materialsInfoBomInfo = materialsInfoBomInfoService.findByAssetsCodeOrAssetsRfid(rfid, code);
//        if (StringUtils.isEmpty(materialsInfoBomInfo)) {
//            return error("当前条码/RFID未绑定在途物资！");
//        }
//        MaterialsRepertory byAssetsCodeOrAssetsRfid = materialsInfoBomRepertoryService.findByAssetsCodeOrAssetsRfid(rfid, code);
//        if (StringUtils.isNotEmpty(byAssetsCodeOrAssetsRfid)) {
//            return error("当前条码/RFID:"+rfid+"-无法重复入库！");
//        }
//        return success().put("materialsInfoBomInfo", materialsInfoBomInfo);
//    }

    @BussinessLog(title = "在途数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public ResponseData export(Long ids) throws Exception {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String fileName = "在途数据";
        fileName = encodingExcelFilename(fileName);
        String folder = Global.getTempPath() + File.separator + "pio" + File.separator;
        FileUtil.touch(folder + fileName);
        List<MaterialsInfoBomExcel> materialsInfoBomExcels = new ArrayList<>();
        List<MaterialsInfoBom> listData = materialsInfoBomService.findAll();
        for (MaterialsInfoBom listDatum : listData) {
            MaterialsInfoBomExcel materialsInfoBomExcel = new MaterialsInfoBomExcel();
            BeanUtils.copyProperties(listDatum,materialsInfoBomExcel);
            materialsInfoBomExcels.add(materialsInfoBomExcel);
        }

        EasyExcel.write(folder + fileName, MaterialsInfoBomExcel.class).sheet("在途数据").doWrite(materialsInfoBomExcels);
        return success(fileName);
    }
    String encodingExcelFilename(String filename) {
        filename = UUID.randomUUID().toString() + "_" + filename + ".xlsx";
        return filename;
    }
    /**
     * 导入物料
     *
     * @param excelFile 物料导入
     * @return
     */
    @RequestMapping("/ReadFileText")
    @ResponseBody
    public ResponseData readFileText2(@RequestParam(value = "file", required = false) MultipartFile excelFile) {
        //============================使用模板======================================================
        InputStream inputStream = null;
        try {
            // 使用模板导入——接收上传文件
            inputStream = excelFile.getInputStream();
            DynamicHeadListener materialsInfoTransitHeadDataListener = new DynamicHeadListener();
            // 读取表格数据
            try {

                EasyExcel.read(inputStream, materialsInfoTransitHeadDataListener).headRowNumber(1).doReadAll();
            } catch (ExcelAnalysisException e) {
                e.printStackTrace();
            }
            // TODO 根据业务处理objectList……
            return ResponseData.success("共" + materialsInfoTransitHeadDataListener.getCount() + "条信息导入成功");

        } catch (IOException e) {
            e.printStackTrace();
            return new ResponseData("500", "物料编码" + "'" + "'" + "不正确", null);
        } catch (ExcelAnalysisException e) {
            if (e.getCause() instanceof ExcelDataConvertException) {
                ExcelDataConvertException excelDataConvertException = (ExcelDataConvertException) e.getCause();
                return ResponseData.error(e.getMessage());
            } else {
                return ResponseData.error(e.getMessage());
            }
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }
}
