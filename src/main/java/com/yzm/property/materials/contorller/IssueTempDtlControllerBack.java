//package com.yzm.property.materials.contorller;
//
//import com.yzm.common.annotation.BussinessLog;
//import com.yzm.common.annotation.RepeatSubmit;
//import com.yzm.common.enums.BusinessType;
//import com.yzm.common.utils.PageUtil;
//import com.yzm.common.utils.QueryHelp;
//import com.yzm.common.utils.ShiroUtils;
//import com.yzm.common.utils.StringUtils;
//import com.yzm.framework.base.BaseController;
//import com.yzm.framework.bean.PageBean;
//import com.yzm.framework.bean.ResponseData;
//import com.yzm.framework.shiro.LoginUser;
//import com.yzm.property.materials.criteria.IssueTempDtlCriteria;
//import com.yzm.property.materials.criteria.IssueTempDtlItemCriteria;
//import com.yzm.property.materials.entity.IssueTempDtl;
//import com.yzm.property.materials.entity.IssueTempDtlItem;
//import com.yzm.property.materials.repository.IssueTempDtlItemRepository;
//import com.yzm.property.materials.repository.IssueTempDtlRepository;
//import com.yzm.property.materials.service.BindingCarService;
//import com.yzm.property.materials.service.IssueTempDtlItemService;
//import com.yzm.property.materials.service.IssueTempDtlService;
//import com.yzm.property.system.entity.SysDictData;
//import com.yzm.property.system.service.ISysDictDataService;
//import com.yzm.property.system.service.impl.SysUserServiceImpl;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.data.domain.Page;
//import org.springframework.data.jpa.domain.Specification;
//import org.springframework.stereotype.Controller;
//import org.springframework.ui.ModelMap;
//import org.springframework.web.bind.annotation.*;
//
//import javax.persistence.criteria.CriteriaBuilder;
//import javax.persistence.criteria.CriteriaQuery;
//import javax.persistence.criteria.Predicate;
//import javax.persistence.criteria.Root;
//import java.text.SimpleDateFormat;
//import java.util.*;
//
//@Controller
//@RequestMapping("materials/issueTempDtl")
//@Slf4j
//public class IssueTempDtlControllerBack extends BaseController {
//    private String urlPrefix = "materials/issueTempDtl";
//
//    @Autowired
//    private IssueTempDtlService issueTempDtlService;
//    @Autowired
//    private IssueTempDtlRepository issueTempDtlRepository;
//    @Autowired
//    private IssueTempDtlItemService issueTempDtlItemService;
//    @Autowired
//    private IssueTempDtlItemRepository issueTempDtlItemRepository;
//    @Autowired
//    private SysUserServiceImpl sysUserService;
//    @Autowired
//    private ISysDictDataService sysDictDataService;
//    @Autowired
//    private BindingCarService bindingCarService;
//
//    @GetMapping
//    public String list(ModelMap map) {
//
//        List<IssueTempDtl> issueTempDtlItems = issueTempDtlService.findAllGroupBySectionC();
//        map.put("sectionCList", issueTempDtlItems);
//        map.put("lineCList", new ArrayList<>());
//        return urlPrefix + "/list";
//    }
//
//    @RequestMapping("/deliveryStorage")
//    public String deliveryStorage(ModelMap mmap, Long id) {
//        IssueTempDtl byId = issueTempDtlRepository.findById(id).get();
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
//        String formattedDate = sdf.format(byId.getPlannedIssueDt());
//        mmap.put("formattedPlannedIssueDt", formattedDate);
//        mmap.put("issueTempDtl", byId);
//        return urlPrefix + "/deliveryStorage";
//    }
//
//
//    /**
//     * 根据ID查询详细
//     *
//     * @param id
//     * @return
//     */
//    @RequestMapping("/findDetailsData")
//    @ResponseBody
//    public ResponseData findDetailsData(@RequestParam Long id) {
//        List<IssueTempDtlItem> allBy = issueTempDtlItemRepository.findByPrimaryIdOrderByLocationCDesc(id);
//        List<IssueTempDtlItem> list = new ArrayList<>();
//        for (IssueTempDtlItem issueTempDtlItem : allBy) {
//            if (StringUtils.isNotEmpty(issueTempDtlItem.getSucceed())) {
//                if (issueTempDtlItem.getSucceed() == 1L) {
//                    continue;
//                }
//            }
//            list.add(issueTempDtlItem);
//            if (StringUtils.isNotEmpty(issueTempDtlItem.getLotNo())) {
//                issueTempDtlItem.setLotNo(issueTempDtlItem.getLotNo().trim());
//            }
//
//            if (StringUtils.isNotEmpty(issueTempDtlItem.getProductNo())) {
//                issueTempDtlItem.setProductNo(issueTempDtlItem.getProductNo().trim());
//            }
//
//            if (StringUtils.isNotEmpty(issueTempDtlItem.getMaterialNo())) {
//                issueTempDtlItem.setMaterialNo(issueTempDtlItem.getMaterialNo().trim());
//            }
//
//            if (StringUtils.isNotEmpty(issueTempDtlItem.getMaterialNm())) {
//                issueTempDtlItem.setMaterialNm(issueTempDtlItem.getMaterialNm().trim());
//            }
//        }
//        return success().put("issueTempDtlList", list);
//    }
//
//
//    /**
//     * 根据部门代码查询生产线
//     *
//     * @return
//     */
//    @RequestMapping("/getLineCBySectionC")
//    @ResponseBody
//    public ResponseData getLineCBySectionC(String sectionC) {
//        return ResponseData.success(issueTempDtlService.findAllGroupLineCBySectionC(sectionC));
//    }
//
//
//    /**
//     * 同步数据
//     *
//     * @return
//     */
//    @RequestMapping("/synchronizationData")
//    @ResponseBody
//    public ResponseData synchronizationData() {
//        return issueTempDtlService.synchronizationData();
//    }
//
//
//    @RequestMapping("/scanSelect")
//    public String scanSelect(ModelMap mmap, String id, String type) {
//        mmap.put("id", id);
//        mmap.put("type", type);
//        return urlPrefix + "/selectType";
//    }
//
//    @RequestMapping("/inputScan")
//    public String inputScan(ModelMap mmap, String id, String type) {
//        mmap.put("id", id);
//        mmap.put("type", type);
//        return urlPrefix + "/inputScan";
//    }
//
//    @RequestMapping("/addTrolley")
//    public String addTrolley(ModelMap mmap, Long id) {
//        IssueTempDtl byId = issueTempDtlService.getById(id);
//        String floor_mapping_dictionary = sysDictDataService.selectDictLabel("floor_mapping_dictionary", byId.getLineC());
//        if (!StringUtils.isEmpty(byId.getKitNo()) && byId.getKitNo().indexOf("F") != -1 && byId.getLineC().equals("6-00")) {
//            mmap.put("floor", "835");
//        }else{
//            mmap.put("floor", floor_mapping_dictionary);
//        }
//        mmap.put("id", id);
//        return urlPrefix + "/addTrolley";
//    }
//
//    @RequestMapping(value = "/addTrolleyData", method = RequestMethod.POST)
//    @ResponseBody
//    public ResponseData addTrolleyData(@RequestParam("code") String code, @RequestParam("floor") String floor, @RequestParam("id") Long id) {
//        ResponseData responseData = bindingCarService.addTrolleyData(code, floor, id);
//        return responseData;
//    }
//
//    @RequestMapping(value = "/checkTrolleyData", method = RequestMethod.POST)
//    @ResponseBody
//    public ResponseData checkTrolleyData(@RequestParam("code") String code, @RequestParam("floor") String floor, @RequestParam("id") Long id) {
//        ResponseData responseData = bindingCarService.checkTrolleyData(code, floor, id);
//        return responseData;
//    }
//    @RequestMapping("/allocation")
//    public String allocation(ModelMap mmap, String ids) {
//        mmap.put("ids", ids);
//        mmap.put("userList", sysUserService.findAll());
//        return urlPrefix + "/allocation";
//    }
//
//    @RequestMapping("/lineCheck")
//    public String lineCheck(ModelMap mmap, String ids) {
//        mmap.put("ids", ids);
//        List<SysDictData> floor_mapping_dictionary = sysDictDataService.selectDictDataByType("floor_mapping_dictionary");
//        mmap.put("floor_mapping_dictionary_list", floor_mapping_dictionary);
//        return urlPrefix + "/lineCheck";
//    }
//
//    @RequestMapping("/issueDate")
//    public String issueDate(ModelMap mmap, String ids) {
//        mmap.put("ids", ids);
//        return urlPrefix + "/issueDate";
//    }
//
//    @BussinessLog(title = "分配发料员", businessType = BusinessType.UPDATE)
//    @RequestMapping(value = "/allocationByIds", method = RequestMethod.POST)
//    @ResponseBody
//    public ResponseData allocation(@RequestParam("ids") Long[] ids, @RequestParam("salesmanId") Long salesmanId) {
//        return issueTempDtlService.allocationByIds(ids, salesmanId) ? success() : error("分配失败");
//    }
//
//    @BussinessLog(title = "更改产线", businessType = BusinessType.UPDATE)
//    @RequestMapping(value = "/lineCheckByIds", method = RequestMethod.POST)
//    @ResponseBody
//    public ResponseData lineCheckByIds(@RequestParam("ids") Long[] ids, @RequestParam("lineC") String lineC) {
//        return issueTempDtlService.lineCheckByIds(ids, lineC) ? success() : error("更改失败");
//    }
//
//    @BussinessLog(title = "变更发料日", businessType = BusinessType.UPDATE)
//    @RequestMapping(value = "/issueDateCheckByIds", method = RequestMethod.POST)
//    @ResponseBody
//    public ResponseData issueDateCheckByIds(@RequestParam("ids") Long[] ids, @RequestParam("plannedIssueDt") Date plannedIssueDt) {
//        return issueTempDtlService.issueDateCheckByIds(ids, plannedIssueDt) ? success() : error("更改失败");
//    }
//
//    /**
//     * 根据ID查询详细
//     *
//     * @param id
//     * @return
//     */
//    @RequestMapping("/findDetails")
//    @ResponseBody
//    public ResponseData findDetail(@RequestParam Long id) {
//        List<IssueTempDtlItem> allBy = issueTempDtlItemRepository.findByPrimaryId(id);
//        for (IssueTempDtlItem issueTempDtlItem : allBy) {
//            issueTempDtlItem.setLotNo(issueTempDtlItem.getLotNo().trim());
//            issueTempDtlItem.setProductNo(issueTempDtlItem.getProductNo().trim());
//            issueTempDtlItem.setMaterialNo(issueTempDtlItem.getMaterialNo().trim());
//            issueTempDtlItem.setMaterialNm(issueTempDtlItem.getMaterialNm().trim());
//        }
//        return success().put("issueTempDtlList", allBy);
//    }
// /* @ResponseBody
//    @PostMapping(value = "/list")*/
//   /* public ResponseData page(PageBean pageBean, BorrowReturnCriteria area) {
//        Map<String, Object> datas = borrowReturnService.findAllByPage(area, pageBean.getPagable());
//        return success(datas);
//    }*/
//
//    /**
//     * 根据关键字和分页查询资产
//     *
//     * @param keyword
//     * @param pageBean
//     * @return
//     */
//    @RequestMapping("/findDetail")
//    @ResponseBody
//    public ResponseData page(PageBean pageBean, IssueTempDtlItemCriteria issueTempDtlItemCriteria) {
//
//        Page<IssueTempDtlItem> datas = issueTempDtlItemRepository.findAll(new Specification<IssueTempDtlItem>() {
//            @Override
//            public Predicate toPredicate(Root<IssueTempDtlItem> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
//                return QueryHelp.getPredicate(root, issueTempDtlItemCriteria, criteriaBuilder);
//            }
//        }, pageBean.getPagable());
//        return success(PageUtil.toPage(datas));
//    }
//
//    /**
//     * 根据关键字和分页查询资产
//     *
//     * @param keyword
//     * @param pageBean
//     * @return
//     */
//    @PostMapping("/list")
//    @ResponseBody
//    public ResponseData showAllMaterialsInfo(
//            @RequestParam(value = "keyword", required = false)
//                    String keyword,
//            PageBean pageBean
//    ) {
//        return success(issueTempDtlService.showAllIssueTempDtl(keyword, pageBean));
//    }
//
//
//    /**
//     * 材料流水
//     */
//    @ResponseBody
//    @RequestMapping("listItemPage")
//    public ResponseData listItemPage(PageBean pageBean, IssueTempDtlCriteria criteria) {
//        if (criteria.getStartTime() != null) {
//            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
//            Date endTime = criteria.getEndTime();
//            Calendar c = Calendar.getInstance();
//            c.setTime(endTime);
//            c.add(Calendar.DATE, 1);
//            criteria.setEndTime(c.getTime());
//            System.out.println(criteria);
//        }
//        Map<String, Object> datas = issueTempDtlItemService.findAllByPage(criteria, pageBean.getPagable());
//        return success(datas);
//    }
//
//
//    @ResponseBody
//    @PostMapping(value = "/list2")
//    public ResponseData page(PageBean pageBean, IssueTempDtlCriteria area) {
//        if (StringUtils.isEmpty(area.getStatus())) {
//            List<Long> list = new ArrayList<>();
//            list.add(0L);
//            list.add(1L);
////            list.add("");
//            area.setStatus(list);
//        }
//        LoginUser userInfo = ShiroUtils.getUserInfo();
//
//
//        if (!userInfo.getUsername().equals("admin") && !userInfo.getPermissions().contains("materials:issueTempDtl:distribution")) {
//            area.setIssuerId(userInfo.getId());
//        }
//        Map<String, Object> datas = issueTempDtlService.findAllByPage(area, pageBean.getPagable());
//        return success(datas);
//    }
//
//    /**
//     * 提交审核
//     */
//    @RepeatSubmit
//    @RequestMapping(value = "/scanOk", method = RequestMethod.POST)
//    @ResponseBody
//    public ResponseData scanOk(Long id) {
//        IssueTempDtlItem one = issueTempDtlItemRepository.getOne(id);
//        one.setSucceed(1L);
//
//
//        IssueTempDtlItem issueTempDtlItem = issueTempDtlItemRepository.saveAndFlush(one);
//
//
//        List<IssueTempDtlItem> allOrderByPrimaryId = issueTempDtlItemRepository.findAllOrderByPrimaryId(one.getPrimaryId());
//        for (IssueTempDtlItem item : allOrderByPrimaryId) {
//            item.setSucceed(1L);
//            issueTempDtlItemRepository.saveAndFlush(item);
//        }
//
//
//        IssueTempDtl one1 = issueTempDtlRepository.getOne(one.getPrimaryId());
//
//        int count = issueTempDtlItemRepository.findByPrimaryIdAndCountComparison(one1.getId());
//        if (count == 0) {
//            one1.setStatus(2L);
//        } else {
//            one1.setStatus(1L);
//        }
//        issueTempDtlRepository.saveAndFlush(one1);
//
//
//        return ResponseData.success(issueTempDtlItem);
//    }
//
//    /**
//     * 出库操作
//     *
//     * @return
//     */
//    @PostMapping("/deliveryStorage")
//    @ResponseBody
//    public ResponseData deliveryStorage(IssueTempDtl issueTempDtl) {
//
//
//        return issueTempDtlService.deliveryStorage(issueTempDtl);
//    }
//
//
//    /**
//     * 根据单据号查询详细
//     *
//     * @return
//     */
//    @GetMapping("/view/{id}")
//    public String viewPage(@PathVariable("id") Long id, ModelMap mmap) {
//        IssueTempDtl byId = issueTempDtlRepository.findById(id).get();
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
//        String formattedDate = sdf.format(byId.getPlannedIssueDt());
//        mmap.put("formattedPlannedIssueDt", formattedDate);
//        mmap.put("issueTempDtl", byId);
//        return urlPrefix + "/show";
//
//    }
//
//
//}
