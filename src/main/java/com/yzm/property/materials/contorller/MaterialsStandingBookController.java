package com.yzm.property.materials.contorller;

import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.materials.service.MaterialsStandingBookService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

@Controller
@RequestMapping("materials/materialsStandingBook")
public class MaterialsStandingBookController extends BaseController {
    private String urlPrefix="materials/materialsStandingBook";

    @Autowired
    private MaterialsStandingBookService materialsStandingBookService;

    @GetMapping
    public String materialsStandingBook(ModelMap map){
        return urlPrefix+"/list";
    }

    /**
     * 材料台账流水
     */
    @PostMapping("/list")
    @ResponseBody
    public ResponseData showAllMaterialsStandingBook(
            @RequestParam(value = "keyword", required = false)
                    String keyword,
            PageBean pageBean
    ) {
        return success(materialsStandingBookService.showAllMaterialsStandingBook(keyword, pageBean));
    }
}
