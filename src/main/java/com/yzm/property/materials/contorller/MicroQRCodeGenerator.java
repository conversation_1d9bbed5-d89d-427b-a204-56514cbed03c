package com.yzm.property.materials.contorller;

public class MicroQRCodeGenerator {

    // Method to generate the Micro QR Code setup part command
    public static String generateSetupCommand(char errorCorrectionLevel, int cellSize, int dataSetupMode) {
        // Validate the inputs
        if (errorCorrectionLevel != 'L' && errorCorrectionLevel != 'M' && errorCorrectionLevel != 'Q') {
            throw new IllegalArgumentException("Invalid error correction level. Use 'L', 'M', or 'Q'.");
        }
        if (cellSize < 1 || cellSize > 99) {
            throw new IllegalArgumentException("Cell size must be between 01 and 99 dots.");
        }
        if (dataSetupMode != 0 && dataSetupMode != 1) {
            throw new IllegalArgumentException("Data setup mode must be 0 (Manual) or 1 (Automatic).");
        }

        // Construct the ESC/POS command for Micro QR Code setup
        return String.format("<2D32>,%s,%02d,%d", errorCorrectionLevel, cellSize, dataSetupMode);
    }

    // Method to generate the data part for manual setup
    public static String generateManualData(String inputData, int inputMode) {
        // Validate input mode
        if (inputMode < 1 || inputMode > 3) {
            throw new IllegalArgumentException("Input mode must be 1 (Numeric), 2 (Alphanumeric), or 3 (Kanji).");
        }

        // Construct the data part for manual setup
        return String.format("<DS>%d,%s", inputMode, inputData);
    }

    // Method to generate the data part for automatic setup
    public static String generateAutomaticData(int numberOfData, String printData) {
        if (numberOfData < 1 || numberOfData > 15) {
            throw new IllegalArgumentException("Number of data must be between 1 and 15.");
        }

        // Construct the data part for automatic setup
        return String.format("<DN>%d,%s", numberOfData, printData);
    }

    public static void main(String[] args) {
        // Example usage
        try {
            // Generate the setup command
            String setupCommand = generateSetupCommand('L', 4, 0); // Error correction: 7%, Cell size: 04, Manual setup
            System.out.println("Setup Command: " + setupCommand);

            // Generate the data part for manual setup
            String manualData = generateManualData("012345", 1); // Input data: "012345", Numeric mode
            System.out.println("Manual Data: " + manualData);

            // Generate the data part for automatic setup
            String automaticData = generateAutomaticData(6, "233"); // Number of data: 6, Print data: "233"
            System.out.println("Automatic Data: " + automaticData);
        } catch (IllegalArgumentException e) {
            System.err.println("Error: " + e.getMessage());
        }
    }
}
