package com.yzm.property.materials.contorller;

import com.yzm.common.annotation.BussinessLog;
import com.yzm.common.annotation.RepeatSubmit;
import com.yzm.common.enums.BusinessType;
import com.yzm.common.utils.PDFTemplateUtil;
import com.yzm.common.utils.ShiroUtils;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.framework.freemark.DictUtil;
import com.yzm.framework.shiro.LoginUser;
import com.yzm.property.basis.service.PersonnelService;
import com.yzm.property.materials.criteria.IssueTempDtlCriteria;
import com.yzm.property.materials.criteria.MaterialsListCriteria;
import com.yzm.property.materials.criteria.OutWarehouseCriteria;
import com.yzm.property.materials.entity.*;
import com.yzm.property.materials.service.MaterialsListService;
import com.yzm.property.materials.service.OutWarehouseItemService;
import com.yzm.property.materials.service.OutWarehouseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Controller(value = "materialsOutWarehouseContorller")
@RequestMapping("materials/outWarehouse")
public class OutWarehouseContorller extends BaseController {
    private String urlPrefix = "materials/outWarehouse";
    @Autowired
    private OutWarehouseService outWarehouseService;
    @Autowired
    private MaterialsListService materialsListService;
    @Autowired
    private OutWarehouseItemService outWarehouseItemService;
    @Autowired
    private PersonnelService personnelService;

    @GetMapping
    public String client(ModelMap map) {
        return urlPrefix + "/list";
    }


    @RequestMapping("/other")
    public String inStorageDysList(ModelMap mmap) {
        return "materials/outWarehouseOther" + "/list";
    }

    @GetMapping("addOutStorage")
    public String addOutStorage(ModelMap mmap) {
        LoginUser userInfo = ShiroUtils.getUserInfo();
        mmap.put("userInfo", userInfo);

        return "materials/outWarehouseOther" + "/addOutStorage";
    }

    /**
     * 材料出库
     */
    @ResponseBody
    @RequestMapping("/list")
    public ResponseData getOutWarehouse(@RequestParam(value = "keyword", required = false)
                                                String keyword,
                                        @RequestParam(value = "type", required = false)
                                                List<String> type,
                                        @RequestParam(value = "dept", required = false)
                                                String dept,
                                        PageBean pageBean) {
        Map<String, Object> datas = outWarehouseService.showAllOutWarehouseInfo(keyword, type, dept, pageBean);
        return success(datas);
    }

    /**
     * 材料出库
     */
    @ResponseBody
    @RequestMapping("/listOther")
    public ResponseData listOther(@RequestParam(value = "keyword", required = false)
                                          String keyword,
                                  @RequestParam(value = "type", required = false)
                                          List<String> type,
                                  PageBean pageBean) {
        Map<String, Object> datas = outWarehouseService.showAllOutWarehouseInfoOther(keyword, type, pageBean);
        return success(datas);
    }

    @ResponseBody
    @PostMapping(value = "/list2")
    public ResponseData page(PageBean pageBean, OutWarehouseCriteria area) {
        Map<String, Object> datas = outWarehouseService.findAllByPage(area, pageBean.getPagable());
        return success(datas);
    }

    @ResponseBody
    @PostMapping(value = "/listItem")
    public ResponseData listItem(PageBean pageBean, OutWarehouseCriteria area) {
        Map<String, Object> datas = outWarehouseItemService.findAllByPage(area, pageBean.getPagable());
        return success(datas);
    }

    /**
     * 提交审核
     */
    @RepeatSubmit
    @BussinessLog(title = "材料损耗出库", businessType = BusinessType.RUN)
    @RequestMapping(value = "/submit", method = RequestMethod.POST)
    @ResponseBody
    public ResponseData submit(Long[] ids) {
        return outWarehouseService.submitByIds(ids) ? success() : error("提交失败!");
    }

//    /**
//     * 送审、同意、拒绝、取消(借用)
//     */
//    @RequestMapping("/audit/{id}/{status}")
//    @BussinessLog(businessType = BusinessType.UPDATE, title = "审核退库")
//    public String audit(@PathVariable("id") Long id, @PathVariable("status") Integer status, ModelMap mmap) {
//
//        outWarehouseService.audit(id, status);
//        return "materials/outWarehouseOther" + "/list";
//    }


    /**
     * 送审、同意、拒绝、取消(借用)
     */
    @RequestMapping("/audit")
    @ResponseBody
    @BussinessLog(businessType = BusinessType.UPDATE, title = "审核退库")
    public ResponseData audit(Long id, Integer status) {
        outWarehouseService.audit(id, status);
        return ResponseData.success();
    }
    /*public ResponseData showAllOutWarehouseInfo(
            @RequestParam(value = "keyword", required = false)
                    String keyword,
            @RequestParam(value = "type", required = false)
                    String type,
            PageBean pageBean
    ) {
        return success(outWarehouseService.showAllOutWarehouseInfo(keyword, pageBean));

    }*/

    @GetMapping("add")
    public String add(ModelMap mmap) {
        mmap.put("personnelList", personnelService.findAll());
        return urlPrefix + "/addOutWarehouse";
    }


    @RequestMapping("/add")
    @ResponseBody
    @BussinessLog(businessType = BusinessType.INSERT, title = "新增材料待出库")
    public ResponseData save(OutWarehouseMaterialsEntity outWarehouseEntity) {
        Map<String, String> map = outWarehouseService.saveOutWarehouse(outWarehouseEntity);
        if (map.get("code").equals("500")) {
            return error("材料出库失败");
        } else if (map.get("code").equals("200")) {
            return error("库存数量不足");
        } else {
            return success();
        }
    }

    @RequestMapping("/addOutStorageSub")
    @ResponseBody
    @BussinessLog(businessType = BusinessType.INSERT, title = "新增材料退库/不良")
    public ResponseData addOutStorageSub(OutWarehouseMaterialsEntity outWarehouseEntity) {
        return outWarehouseService.addOutStorage(outWarehouseEntity);
    }

    @RequestMapping("/deliveryStorageSave")
    @ResponseBody
    @BussinessLog(businessType = BusinessType.INSERT, title = "出库")
    public ResponseData deliveryStorageSave(OutWarehouseMaterialsEntity outWarehouseEntity) {
        return outWarehouseService.deliveryStorageSave(outWarehouseEntity);
    }

    /**
     * 入库(选择材料信息)页面
     */
    @GetMapping("/addOutWarehouseItem")
    public String addInStorageItem() {
        return "materials/outWarehouseOther/addOutWarehouseItem";
    }

    /**
     * 入库(选择材料信息)页面
     */
    @GetMapping("/addInStorageItemDtl")
    public String addInStorageItemDtl() {
        return "materials/outWarehouseOther/addInStorageItemDtl";
    }

    @RequestMapping("/deliveryStorage")
    public String deliveryStorage(ModelMap mmap, Long id) {
        OutWarehouseMaterialsEntity byId = outWarehouseService.getById(id);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        mmap.put("outWarehouseMaterials", byId);
        return "materials/outWarehouseOther/deliveryStorage";
    }

    /**
     * 根据ID查询详细
     *
     * @param id
     * @return
     */
    @RequestMapping("/findDetailsData")
    @ResponseBody
    public ResponseData findDetailsData(@RequestParam Long id) {
        OutWarehouseMaterialsEntity byId = outWarehouseService.getById(id);
        List<OutWarehouseMaterialsItem> byRecordNumber = outWarehouseItemService.findByRecordNumber(byId.getRecordNumber());

        List<OutWarehouseMaterialsItem> list = new ArrayList<>();

        for (OutWarehouseMaterialsItem outWarehouseMaterialsItem : byRecordNumber) {
            if (StringUtils.isNotEmpty(outWarehouseMaterialsItem.getSucceed())) {
                if (outWarehouseMaterialsItem.getSucceed() == 1L) {
                    continue;
                }
            }
            list.add(outWarehouseMaterialsItem);
        }
        return success().put("issueTempDtlList", list);
    }

    /**
     * 根据ID查询详细
     *
     * @param id
     * @return
     */
    @RequestMapping("/findDetailsDataShow")
    @ResponseBody
    public ResponseData findDetailsDataShow(@RequestParam Long id) {
        OutWarehouseMaterialsEntity byId = outWarehouseService.getById(id);
        List<OutWarehouseMaterialsItem> byRecordNumber = outWarehouseItemService.findByRecordNumber(byId.getRecordNumber());

        List<OutWarehouseMaterialsItem> list = new ArrayList<>();

        return success().put("issueTempDtlList", byRecordNumber);
    }

    /**
     * 根据ID查询详细
     *
     * @param id
     * @return
     */
    @RequestMapping("/findDetailsDataShowItem")
    @ResponseBody
    public ResponseData findDetailsDataShowItem(@RequestParam Long id) {
        OutWarehouseMaterialsItem byId = outWarehouseItemService.getById(id);
        MaterialsListCriteria materialsListCriteria = new MaterialsListCriteria();
        materialsListCriteria.setOperationOtherIdEq(byId.getRecordNumber());
        materialsListCriteria.setAssetsNumberEq(byId.getAssetsNumber());
        List<MaterialsListEntity> all = materialsListService.findAll(materialsListCriteria);


//        List<OutWarehouseMaterialsItem> list = new ArrayList<>();

        return success().put("issueTempDtlList", all);
    }

    /**
     * 根据ID查询详细
     *
     * @param id
     * @return
     */
    @RequestMapping("/findDetails")
    @ResponseBody
    public ResponseData findDetail(@RequestParam Long id) {
        OutWarehouseMaterialsEntity byId = outWarehouseService.getById(id);
        List<OutWarehouseMaterialsItem> byRecordNumber = outWarehouseItemService.findByRecordNumber(byId.getRecordNumber());
        return success().put("issueTempDtlList", byRecordNumber);
    }

    /**
     * 根据单据号查询详细
     *
     * @return
     */
    @GetMapping("/view/{id}")
    public String viewPage(@PathVariable("id") Long id, ModelMap mmap) {
        OutWarehouseMaterialsEntity outWarehouseEntity = outWarehouseService.getById(id);
        List<OutWarehouseMaterialsItem> list = outWarehouseItemService.findByKitNo(outWarehouseEntity.getKitNo());
        mmap.put("outWarehouseEntity", outWarehouseEntity);
        mmap.put("outWarehouseEntityList", list);
        return urlPrefix + "/show";

    }

    /**
     * 根据单据号查询详细
     *
     * @return
     */
    @GetMapping("/viewItem/{id}")
    public String viewItem(@PathVariable("id") Long id, ModelMap mmap) {
        OutWarehouseMaterialsItem byId = outWarehouseItemService.getById(id);
        OutWarehouseMaterialsEntity byRecordNumber = outWarehouseService.findByRecordNumber(byId.getRecordNumber());
//        OutWarehouseMaterialsEntity outWarehouseEntity = outWarehouseService.getById(id);
//        List<OutWarehouseMaterialsItem> list = outWarehouseItemService.findByKitNo(outWarehouseEntity.getKitNo());
        mmap.put("outWarehouseEntity", byRecordNumber);
        mmap.put("item", byId);
//        mmap.put("outWarehouseEntityList", list);
        return urlPrefix + "/showItem";

    }

    /**
     * 根据单据号查询详细
     *
     * @return
     */
    @GetMapping("/viewOther/{id}")
    public String viewOther(@PathVariable("id") Long id, ModelMap mmap) {
        OutWarehouseMaterialsEntity outWarehouseEntity = outWarehouseService.getById(id);
        mmap.put("outWarehouseEntity", outWarehouseEntity);
        return "materials/outWarehouseOther" + "/show";

    }

    /**
     * 根据单据号查询详细
     *
     * @return
     */
    @GetMapping("/viewOtherShow/{id}")
    public String viewOtherShow(@PathVariable("id") Long id, ModelMap mmap) {
        OutWarehouseMaterialsEntity outWarehouseEntity = outWarehouseService.getById(id);
        mmap.put("outWarehouseEntity", outWarehouseEntity);
        return "materials/outWarehouseOther" + "/showItem";

    }

    /**
     * 删除报废信息
     *
     * @param ids
     * @return
     */
    @PostMapping("/del")
    @ResponseBody
    @BussinessLog(businessType = BusinessType.DELETE, title = "删除出库信息")
    public ResponseData deleteScrap(Long[] ids) {

        return outWarehouseService.deleteInfo(ids);
    }

    /**
     * 提交审核
     */
    @RepeatSubmit
    @RequestMapping(value = "/scanOk", method = RequestMethod.POST)
    @ResponseBody
    public ResponseData scanOk(Long id) {
        OutWarehouseMaterialsItem one = outWarehouseItemService.getById(id);
        one.setSucceed(1L);


        OutWarehouseMaterialsItem issueTempDtlItem = outWarehouseItemService.update(one);


        OutWarehouseMaterialsEntity byRecordNumber = outWarehouseService.findByRecordNumber(one.getRecordNumber());

        int count = outWarehouseItemService.findByPrimaryIdAndCountComparison(one.getRecordNumber());
        if (count == 0) {
            byRecordNumber.setInStatus(2L);
        } else {
            byRecordNumber.setInStatus(1L);
        }
        outWarehouseService.update(byRecordNumber);


        return ResponseData.success(issueTempDtlItem);
    }

    /**
     * 根据单据号查询详细
     *
     * @param recordNumber
     * @return
     */
    @GetMapping("/findDetailByRecordNumber")
    @ResponseBody
    public ResponseData findDetailByRecordNumber(Long id) {
        OutWarehouseMaterialsEntity outWarehouseEntity = outWarehouseService.getById(id);
        List<OutWarehouseMaterialsItem> list = outWarehouseItemService.findByKitNo(outWarehouseEntity.getKitNo());
        Map<String, OutWarehouseMaterialsItem> map = new HashMap<>();
        for (OutWarehouseMaterialsItem outWarehouseMaterialsItem : list) {
            if (outWarehouseMaterialsItem.getRecordNumber().indexOf("QTCK") != -1) {
                continue;
            }
            if (map.containsKey(outWarehouseMaterialsItem.getAssetsNumber())) {
                // 如果map中已存在相同的assetsNumber，累加consumableNum
                OutWarehouseMaterialsItem existingItem = map.get(outWarehouseMaterialsItem.getAssetsNumber());
                existingItem.setConsumableNum(existingItem.getConsumableNum().add(outWarehouseMaterialsItem.getConsumableNum()));
            } else {
                // 如果map中没有该assetsNumber，则直接放入map
                map.put(outWarehouseMaterialsItem.getAssetsNumber(), outWarehouseMaterialsItem);
            }
        }
        List<OutWarehouseMaterialsItem> arrayList = new ArrayList<>(map.values());
//        List<OutWarehouseMaterialsItem> list = outWarehouseItemService.findByRecordNumber(recordNumber);
        // 1. 根据 number
//        Map<String, List<OutWarehouseMaterialsItem>> map = list.stream().collect(Collectors.groupingBy(user -> user.getAssetsNumber()));
//
//// 2. 对于每个分组内的用户，将其 rfid 字段进行拼接
//        List<OutWarehouseMaterialsItem> result = new ArrayList<>();
//        for (Map.Entry<String, List<OutWarehouseMaterialsItem>> entry : map.entrySet()) {
//            List<OutWarehouseMaterialsItem> userList = entry.getValue();
//            OutWarehouseMaterialsItem user1 = userList.get(0);
//            StringBuilder builder = new StringBuilder();
////            BigDecimal count = new BigDecimal(0);
////            for (OutWarehouseMaterialsEntityItem user : userList) {
////                builder.append(user.getAssetsRfid()).append(",");
////                count++;
////            }
////            user1.setNowRepertory(count);
////            user1.setConsumableNum(count);
//            user1.setAssetsRfid(builder.toString());
//            result.add(user1);
//        }
        return success().put("OutWarehouseItemList", arrayList);
    }

    /**
     * 根据单据号查询详细
     *
     * @param
     * @return
     */
    @GetMapping("/findDetailByRecordNumberItem")
    @ResponseBody
    public ResponseData findDetailByRecordNumberItem(Long id) {
        OutWarehouseMaterialsItem outWarehouseEntity = outWarehouseItemService.getById(id);
        List<OutWarehouseMaterialsItem> list = outWarehouseItemService.findByKitNoAndAssetsNumber(outWarehouseEntity.getKitNo(), outWarehouseEntity.getAssetsNumber());

        Iterator<OutWarehouseMaterialsItem> iterator = list.iterator();
        while (iterator.hasNext()) {
            OutWarehouseMaterialsItem item = iterator.next();
            if (item.getRecordNumber().indexOf("QTCK") != -1) {
                // 删除包含"QTCK"的元素
                iterator.remove();
            }
        }
        return success().put("OutWarehouseItemList", list);
    }

    @Autowired
    DictUtil dictUtil;


    @GetMapping("exportInformation")
//    @BussinessLog(title = "出库单", businessType = BusinessType.EXPORT)
    public void downloadFile(HttpServletResponse response, Long id) throws Exception {
        ByteArrayOutputStream baos = null;
        OutputStream out = null;
        try {
            OutWarehouseMaterialsEntity inStorage = outWarehouseService.getById(id);
            Map<String, Object> data = new HashMap<>();
            data.put("title", "预览PDF");
            //申领编号
//            data.put("recordNumber", inStorage.getRecordNumber());
//            data.put("inStoragePeople", inStorage.getUserName());
//            data.put("inStorageWarehouse",inStorage.getInStorageWarehouse());
//            data.put("inStorageArea",inStorage.getInStorageArea());
            data.put("inStorageReson", dictUtil.getLabel("materials_list_type", inStorage.getOutReson()));
//            data.put("materialsCompany", inStorage.getConsumableCompany());
            data.put("out", inStorage);
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            data.put("outTime", simpleDateFormat.format(inStorage.getOutDate()));

            data.put("custom1", inStorage.getCustom1());
            data.put("outTime", inStorage.getCreateTime());
            data.put("user", ShiroUtils.getUserInfo().getName());
            data.put("now", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy.MM.dd HH:mm:ss")));
            List<OutWarehouseMaterialsItem> overList = outWarehouseItemService.findByKitNo(inStorage.getKitNo());
            BigDecimal sum = new BigDecimal(0);
            for (OutWarehouseMaterialsItem outWarehouseMaterialsItem : overList) {
                sum = sum.add(outWarehouseMaterialsItem.getConsumableNum());
            }
            data.put("detailList", overList);
            data.put("count", sum.toString());
            baos = PDFTemplateUtil.createPDF(data, "出库单.ftl");


            // 设置响应消息头，告诉浏览器当前响应是一个下载文件
            response.setContentType("application/pdf;charset=UTF-8");
            // 告诉浏览器，当前响应数据要求用户干预保存到文件中，以及文件名是什么 如果文件名有中文，必须URL编码
            String fileName = URLEncoder.encode(inStorage.getRecordNumber() + ".pdf", "UTF-8");
            response.setHeader("Content-Disposition", "inline;filename=" + fileName);
            out = response.getOutputStream();
            baos.writeTo(out);
            baos.close();
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("导出失败：" + e.getMessage());
        } finally {
            if (baos != null) {
                baos.close();
            }
            if (out != null) {
                out.close();
            }
        }
    }

}
