package com.yzm.property.materials.contorller;

import cn.hutool.core.io.FileUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.yzm.common.annotation.BussinessLog;
import com.yzm.common.enums.BusinessType;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.framework.freemark.Global;
import com.yzm.property.basis.entity.InventorySimple;
import com.yzm.property.basis.entity.InventorySimpleItem;
import com.yzm.property.materials.criteria.DelinquentMaterialCriteria;
import com.yzm.property.materials.entity.*;
import com.yzm.property.materials.importHeadListener.DelinquentMaterialHeadDataListener;
import com.yzm.property.materials.importHeadListener.MaterialsInfoHeadDataListener;
import com.yzm.property.materials.pojo.DelinquentMaterialExcel;
import com.yzm.property.materials.pojo.InventorySimpleExcel;
import com.yzm.property.materials.repository.*;
import com.yzm.property.materials.service.DelinquentMaterialService;
import com.yzm.property.materials.service.MaterialsInfoBomService;
import com.yzm.property.materials.utils.DateParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;

@Controller
@RequestMapping("materials/delinquentMaterial")
@Slf4j
public class DelinquentMaterialController extends BaseController {
    private String urlPrefix = "materials/delinquentMaterial";

    @Autowired
    private DelinquentMaterialService delinquentMaterialService;

    @Autowired
    private MaterialsInfoBomService materialsInfoBomService;
    @Autowired
    private DelinquentMaterialRepository delinquentMaterialRepository;
    @Autowired
    private MaterialsInfoRepository materialsInfoRepository;

    @Autowired
    private IssueTempDtlRepository issueTempDtlRepository;
    @Autowired
    private IssueTempDtlItemRepository issueTempDtlItemRepository;
    @Autowired
    private IssueTempDtlTubeRepository issueTempDtlTubeRepository;


    /**
     * 新增参数配置
     */
    @GetMapping("/add")
    public String add() {
        return urlPrefix + "/add";
    }

    @GetMapping
    public String client(ModelMap map) {
        return urlPrefix + "/delinquentMaterial";
    }

    /**
     * 保存配置
     */
    @RequestMapping("/add")
    @ResponseBody
    @BussinessLog(businessType = BusinessType.INSERT, title = "新增绑定小车信息")
    public ResponseData save(DelinquentMaterial delinquentMaterial) {
        delinquentMaterial.setStatus(0L);
        DelinquentMaterial basisUnit = null;
        basisUnit = delinquentMaterialService.save(delinquentMaterial);

        return StringUtils.isNotEmpty(basisUnit) ? success() : error("新增失败!");
    }

    /**
     * 修改参数配置
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        mmap.put("unit", delinquentMaterialService.getById(id));
        return urlPrefix + "/edit";
    }

    /**
     * 修改配置
     */
    @RequestMapping(value = "/edit", method = RequestMethod.POST)
    @ResponseBody
    public ResponseData update(DelinquentMaterial config) {
        return StringUtils.isNotEmpty(delinquentMaterialService.update(config)) ? success() : error("修改失败!");
    }
//    /**
//     * 处理提醒
//     */
//    @RequestMapping(value = "/disposeSave", method = RequestMethod.POST)
//    @ResponseBody
//    public ResponseData disposeSave(DelinquentMaterial config) {
//        DelinquentMaterial material = delinquentMaterialRepository.getOne(config.getId());
//        if (!StringUtils.isEmpty(config.getEstimatedTime())) {
//            material.setEstimatedTime(config.getEstimatedTime());
//        }
//        if (StringUtils.isEmpty(material.getKitNo())) {
//            Date plannedIssueDt = material.getPlannedIssueDt();
//            Date estimatedTime = material.getEstimatedTime();
//
//            if (plannedIssueDt.compareTo(estimatedTime) < 0) {
//                // plannedIssueDt 在 estimatedTime 之前
//                IssueTempDtlTube byKitNo = issueTempDtlTubeRepository.findByWorkOrdNoAndMatlNo(material.getLotNo(), material.getAssetsNumber());
//                byKitNo.setShortFeed("欠料");
//                issueTempDtlTubeRepository.saveAndFlush(byKitNo);
//            } else {
//                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
//// 将 Date 转换为 LocalDate
//                LocalDate localDate = plannedIssueDt.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
//
//// 获取前一天的日期
//                LocalDate previousDay = localDate.minusDays(1);
//
//// 将前一天的 LocalDate 转回 Date
//                Date previousDayDate = Date.from(previousDay.atStartOfDay(ZoneId.systemDefault()).toInstant());
//
//                IssueTempDtlTube byKitNo = issueTempDtlTubeRepository.findByWorkOrdNoAndMatlNo(material.getLotNo(), material.getAssetsNumber());
//                byKitNo.setShortFeed(simpleDateFormat.format(previousDayDate));
//                issueTempDtlTubeRepository.saveAndFlush(byKitNo);
//            }
//        } else {
//
//            Date plannedIssueDt = material.getPlannedIssueDt();
//            Date estimatedTime = material.getEstimatedTime();
//
//
//            if (plannedIssueDt.compareTo(estimatedTime) < 0) {
//                // plannedIssueDt 在 estimatedTime 之前
//                IssueTempDtl byKitNo = issueTempDtlRepository.findByKitNo(material.getKitNo());
//                byKitNo.setShortFeed("欠料");
//                issueTempDtlRepository.saveAndFlush(byKitNo);
//
//                List<IssueTempDtlItem> byKitNoAndMaterialNo = issueTempDtlItemRepository.findByKitNoAndMaterialNo(material.getKitNo(), material.getAssetsNumber());
//                for (IssueTempDtlItem item : byKitNoAndMaterialNo) {
//                    item.setShortFeed("欠料");
//                    issueTempDtlItemRepository.saveAndFlush(item);
//                }
//            } else {
//                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
//// 将 Date 转换为 LocalDate
//                LocalDate localDate = plannedIssueDt.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
//
//// 获取前一天的日期
//                LocalDate previousDay = localDate.minusDays(1);
//
//// 将前一天的 LocalDate 转回 Date
//                Date previousDayDate = Date.from(previousDay.atStartOfDay(ZoneId.systemDefault()).toInstant());
//
//                IssueTempDtl byKitNo = issueTempDtlRepository.findByKitNo(material.getKitNo());
//                byKitNo.setShortFeed(simpleDateFormat.format(previousDayDate));
//                issueTempDtlRepository.saveAndFlush(byKitNo);
//
//                List<IssueTempDtlItem> byKitNoAndMaterialNo = issueTempDtlItemRepository.findByKitNoAndMaterialNo(material.getKitNo(), material.getAssetsNumber());
//                for (IssueTempDtlItem item : byKitNoAndMaterialNo) {
//                    item.setShortFeed("欠料");
//                    item.setShortFeed(simpleDateFormat.format(previousDayDate));
//                    issueTempDtlItemRepository.saveAndFlush(item);
//                }
//            }
//
//        }
//
//
//        material.setStatus(1L);
//
//
//        return StringUtils.isNotEmpty(delinquentMaterialService.update(material)) ? success() : error("处理失败!");
//    }

    /**
     * 处理提醒
     */
    @RequestMapping(value = "/disposeSave", method = RequestMethod.POST)
    @ResponseBody
    public ResponseData disposeSave(DelinquentMaterial config) {
        DelinquentMaterial material = delinquentMaterialRepository.getOne(config.getId());
        if (!StringUtils.isEmpty(config.getEstimatedTime())) {
            material.setEstimatedTime(config.getEstimatedTime());
        }
        Date plannedIssueDt = material.getPlannedIssueDt();
        Date estimatedTime = material.getEstimatedTime();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        List<IssueTempDtl> byKitNoList = issueTempDtlRepository.findByLotNo(material.getLotNo());
        for (IssueTempDtl issueTempDtl : byKitNoList) {
            issueTempDtl.setShortFeed(simpleDateFormat.format(estimatedTime));
            issueTempDtlRepository.saveAndFlush(issueTempDtl);
        }
        List<IssueTempDtlItem> byKitNoAndMaterialNo = issueTempDtlItemRepository.findByLotNoAndMaterialNo(material.getLotNo(), material.getAssetsNumber());
        for (IssueTempDtlItem item : byKitNoAndMaterialNo) {
            item.setShortFeed(simpleDateFormat.format(estimatedTime));
            issueTempDtlItemRepository.saveAndFlush(item);
        }
        material.setStatus(1L);
        return StringUtils.isNotEmpty(delinquentMaterialService.update(material)) ? success() : error("处理失败!");
    }

    /**
     * 材料流水
     */
    @ResponseBody
    @RequestMapping("list")
    public ResponseData getDelinquentMaterial(PageBean pageBean, DelinquentMaterialCriteria criteria) {
        if (criteria.getStartTime() != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date endTime = criteria.getEndTime();
            Calendar c = Calendar.getInstance();
            c.setTime(endTime);
            c.add(Calendar.DATE, 1);
            criteria.setEndTime(c.getTime());
            System.out.println(criteria);
        }
        Map<String, Object> datas = delinquentMaterialService.findAllByPage(criteria, pageBean.getPagable());
        return success(datas);
    }

    @RequestMapping("/dispose")
    public String dispose(ModelMap mmap, Long id) {
        DelinquentMaterial byId = delinquentMaterialService.getById(id);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String formattedDate = sdf.format(byId.getPlannedIssueDt());

        mmap.put("formattedPlannedIssueDt", formattedDate);

        mmap.put("delinquentMaterial", byId);
        List<MaterialsInfoBom> materialsInfoBoms = materialsInfoBomService.findByAssetsNumber(byId.getAssetsNumber());
        mmap.put("materialsInfoBoms", materialsInfoBoms);
        return urlPrefix + "/dispose";
    }

    /**
     * 根据ID查询详细
     *
     * @param id
     * @return
     */
    @RequestMapping("/findDetails")
    @ResponseBody
    public ResponseData findDetail(@RequestParam Long id) {
        DelinquentMaterial byId = delinquentMaterialService.getById(id);
        List<MaterialsInfoBom> materialsInfoBoms = materialsInfoBomService.findByAssetsNumber(byId.getAssetsNumber());
        return success().put("materialsInfoBoms", materialsInfoBoms);
    }


    @BussinessLog(title = "欠料提醒", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public ResponseData export() throws Exception {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String fileName = "欠料跟进表";
        fileName = encodingExcelFilename(fileName);
        String folder = Global.getTempPath() + File.separator + "pio" + File.separator;
        FileUtil.touch(folder + fileName);
        List<DelinquentMaterialExcel> list = new ArrayList<>();

        List<DelinquentMaterial> all = delinquentMaterialService.findAll();
        for (DelinquentMaterial entity : all) {
            DelinquentMaterialExcel excel = new DelinquentMaterialExcel();
            BeanUtils.copyProperties(entity, excel);
            list.add(excel);
        }
        EasyExcel.write(folder + fileName, DelinquentMaterialExcel.class).sheet("欠料明细").doWrite(list);
        return success(fileName);
    }

    String encodingExcelFilename(String filename) {
        filename = UUID.randomUUID().toString() + "_" + filename + ".xlsx";
        return filename;
    }

    /**
     * 导入物料
     *
     * @param excelFile 物料导入
     * @return
     */
    @RequestMapping("/ReadFileText")
    @ResponseBody
    public ResponseData readFileText2(@RequestParam(value = "file", required = false) MultipartFile excelFile) {
        //============================使用模板======================================================
        InputStream inputStream = null;
        DelinquentMaterialHeadDataListener delinquentMaterialHeadDataListener = new DelinquentMaterialHeadDataListener();

        try {
            // 使用模板导入——接收上传文件
            inputStream = excelFile.getInputStream();
            // 读取表格数据
            EasyExcel.read(inputStream, DelinquentMaterialExcel.class, delinquentMaterialHeadDataListener).headRowNumber(1).doReadAll();

            return ResponseData.success("导入成功");

            // TODO 根据业务处理objectList……
        } catch (IOException e) {
            e.printStackTrace();
        } catch (ExcelAnalysisException e) {
            return ResponseData.error(e.getMessage());
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return success("导入成功");
    }
}
