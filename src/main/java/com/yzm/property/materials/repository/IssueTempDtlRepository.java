package com.yzm.property.materials.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.materials.entity.IssueTempDtl;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 *
 */
public interface IssueTempDtlRepository extends BaseRepository<IssueTempDtl, Long> {


    @Query(value = "SELECT * FROM issue_temp_dtl WHERE product_no   like '%' ?1 '%' GROUP BY kit_no ORDER BY planned_issue_dt desc LIMIT ?2,?3 ", nativeQuery = true)
    List<IssueTempDtl> findGroupPage(String keyword, int i, Integer limit);


    @Query(value = "SELECT * " +
            "FROM issue_temp_dtl " +
            "WHERE product_no LIKE %:keyword% " +
            "GROUP BY kit_no " +
            "ORDER BY planned_issue_dt DESC",
            countQuery = "SELECT COUNT(DISTINCT kit_no) " +
                    "FROM issue_temp_dtl " +
                    "WHERE product_no LIKE %:keyword%",
            nativeQuery = true)
    Page<IssueTempDtl> findByProductNoWithPagination(String keyword, Pageable pageable);


    @Query(value = "SELECT count(*) FROM issue_temp_dtl WHERE product_no   like '%' ?1 '%' GROUP BY kit_no  ", nativeQuery = true)
    int findGroupPageCount(String keyword);

    @Query(value = "SELECT *,SUM(issue_qty) as issueQty  FROM issue_temp_dtl WHERE kit_no  = ?1 GROUP BY material_no ", nativeQuery = true)
    List<IssueTempDtl> findAllByKitNoAndGroupByMaterialNo(String kitNo);

    IssueTempDtl findByKitNo(String key);

    @Query(value = "select * from issue_temp_dtl group by line_c",nativeQuery = true)
    List<IssueTempDtl> findAllGroupByLineC();

   @Query(value = "select * from issue_temp_dtl group by section_c",nativeQuery = true)
    List<IssueTempDtl> findAllGroupBySectionC();

    @Query(value = "select * from issue_temp_dtl where section_c = ?1 group by line_c",nativeQuery = true)
    List<IssueTempDtl> findAllGroupLineCBySectionC(String sectionC);

    List<IssueTempDtl> findByLotNo(String kitNo);
}
