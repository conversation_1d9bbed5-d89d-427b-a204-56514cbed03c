package com.yzm.property.materials.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.materials.entity.InStorageMaterials;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:49
 */


public interface InStorageMaterialsRepository extends BaseRepository<InStorageMaterials, Long> {
    @Query(nativeQuery = true, value = "SELECT a.* \n" +
            "FROM materials_in_storage a\n" +
            "WHERE a.record_number IN (\n" +
            "    SELECT DISTINCT a.record_number\n" +
            "    FROM materials_in_storage a\n" +
            "    LEFT JOIN materials_in_storage_item b ON a.record_number = b.record_number\n" +
            "    WHERE (a.invoice_number LIKE '%' ?1 '%' \n" +
            "           OR a.department_code LIKE '%' ?1 '%' \n" +
            "           OR a.record_number LIKE '%' ?1 '%' \n" +
            "           OR b.assets_number LIKE '%' ?1 '%')\n" +
            "    AND a.in_storage_reson IN (?2)\n" +
            ")\n" +
            "ORDER BY a.create_time DESC\n" +
            "LIMIT ?3, ?4 ")
    List<InStorageMaterials> findByRecordNumberContainsOrderByCreateTimeDesc(String keyword, List<String> type, int start, Integer size);


    @Query(nativeQuery = true, value = "SELECT * \n" +
            "FROM materials_in_storage \n" +
            "WHERE record_number IN (\n" +
            "    SELECT DISTINCT b.record_number\n" +
            "    FROM materials_in_storage b\n" +
            "    LEFT JOIN materials_in_storage_item c ON b.record_number = c.record_number\n" +
            "    WHERE (b.invoice_number LIKE CONCAT('%', ?1, '%') \n" +
            "           OR b.department_code LIKE CONCAT('%', ?1, '%') \n" +
            "           OR b.record_number LIKE CONCAT('%', ?1, '%') \n" +
            "           OR c.assets_number LIKE CONCAT('%', ?1, '%'))\n" +
            "    AND b.in_storage_reson = 0 and b.department_code LIKE CONCAT('%', ?2, '%') \n" +
            ")\n" +
            "ORDER BY create_time DESC",  // 必须手动排序
            countQuery = "SELECT COUNT(*) \n" +
                    "FROM materials_in_storage \n" +
                    "WHERE record_number IN (\n" +
                    "    SELECT DISTINCT b.record_number\n" +
                    "    FROM materials_in_storage b\n" +
                    "    LEFT JOIN materials_in_storage_item c ON b.record_number = c.record_number\n" +
                    "    WHERE (b.invoice_number LIKE CONCAT('%', ?1, '%') \n" +
                    "           OR b.department_code LIKE CONCAT('%', ?1, '%') \n" +
                    "           OR b.record_number LIKE CONCAT('%', ?1, '%') \n" +
                    "           OR c.assets_number LIKE CONCAT('%', ?1, '%'))\n" +
                    "    AND b.in_storage_reson =0 and b.department_code LIKE CONCAT('%', ?2, '%') \n" +
                    ")")
    Page<InStorageMaterials> findByRecordNumberContainsOrderByCreateTimeDescPage(String keyword,String departmentCode, List<String> type, Pageable pageable);

    @Query(value = "select count(*) from materials_in_storage  where  (invoice_number like '%' ?1 '%' or department_code like '%' ?1 '%' or  record_number like '%' ?1 '%' ) and in_storage_reson in  ( ?2 )  ", nativeQuery = true)
    int countGroupedResults(String keyword, List<String> type);


    Page<InStorageMaterials> findByOrderByCreateTimeDesc(Pageable pagable);

    InStorageMaterials findByRecordNumber(String recordNumber);



    @Query(nativeQuery = true, value = "SELECT * \n" +
            "FROM materials_in_storage \n" +
            "WHERE record_number IN (\n" +
            "    SELECT DISTINCT b.record_number\n" +
            "    FROM materials_in_storage b\n" +
            "    LEFT JOIN materials_in_storage_item c ON b.record_number = c.record_number\n" +
            "    WHERE (b.invoice_number LIKE CONCAT('%', ?1, '%') \n" +
            "           OR b.department_code LIKE CONCAT('%', ?1, '%') \n" +
            "           OR b.record_number LIKE CONCAT('%', ?1, '%') \n" +
            "           OR c.assets_number LIKE CONCAT('%', ?1, '%'))\n" +
            "    AND b.in_storage_reson != 0 \n" +
            ")\n" +
            "ORDER BY create_time DESC",  // 必须手动排序
            countQuery = "SELECT COUNT(*) \n" +
                    "FROM materials_in_storage \n" +
                    "WHERE record_number IN (\n" +
                    "    SELECT DISTINCT b.record_number\n" +
                    "    FROM materials_in_storage b\n" +
                    "    LEFT JOIN materials_in_storage_item c ON b.record_number = c.record_number\n" +
                    "    WHERE (b.invoice_number LIKE CONCAT('%', ?1, '%') \n" +
                    "           OR b.department_code LIKE CONCAT('%', ?1, '%') \n" +
                    "           OR b.record_number LIKE CONCAT('%', ?1, '%') \n" +
                    "           OR c.assets_number LIKE CONCAT('%', ?1, '%'))\n" +
                    "    AND b.in_storage_reson != 0 \n" +
                    ")")
    Page<InStorageMaterials> findByRecordNumberContainsOrderByCreateTimeDescPageDys(String keyword, List<String> type, Pageable pagable);
  /*  Page<InStorage> findAllByAssetsNameContainingOrOtherIdContaining(String keyword, String otherId, Pageable pageable);

    InStorage findByOtherId(String otherId);

    List<AssetsInfo> findAllByAssetsStatus(Integer status);

    Page<InStorage> findAllByAssetsNameContainingOrOtherIdContainingAndAssetsStatus(String keyword,String otherId,Integer status,Pageable pageable);

    Integer countAllByAssetsType(Integer assetsType);

    Integer countAllByAssetsArea(Integer type);

    Integer countAllByDepartment(Integer type);

    Integer countAllByAssetsUser(Integer type);
    //根据状态分页查询资产
    Page<AssetsInfo> findAllByAssetsStatus(Integer assetsStatus,Pageable pageable);
    //根据区域分页查询资产
    Page<AssetsInfo> findAllByAssetsArea(Integer assetsArea, Pageable pageable);
    //根据使用人分页查询资产
    Page<AssetsInfo> findAllByAssetsUser(Integer assetsUser, Pageable pageable);
    //根据部门分页查询资产
    Page<AssetsInfo> findAllByDepartment(Integer department, Pageable pageable);
    //根据类型分页查询资产
    Page<AssetsInfo> findAllByAssetsType(Integer assetsType, Pageable pageable);

//    Page<AssetsInfo> findAllByAssetsNameContainingAndMaintenanceStatusNotContainsAndMaintenanceStatusNotContains(String keyword, String repairs,String scrap,Pageable pageable);


    @Query(value="select COALESCE(SUM(purchase_price),0) from assets_info", nativeQuery = true)
    int findByPurchasePriceSum();
    @Query(value="select count(*) from assets_info where maintenance_status = '脱保'", nativeQuery = true)
    int findByMaintenanceStatusCount();
    @Query(value="SELECT count(*) AS sum FROM assets_info WHERE MONTH(create_time) = MONTH( NOW( ) )", nativeQuery = true)
    int findAssetsMonthCount();
    @Query(value="SELECT count(*) AS sum FROM assets_info WHERE YEAR(create_time) = YEAR( NOW( ) )", nativeQuery = true)
    int findAssetsYearCount();*/
}
