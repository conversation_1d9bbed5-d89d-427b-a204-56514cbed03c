package com.yzm.property.materials.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.materials.criteria.MaterialsBomCriteria;
import com.yzm.property.materials.entity.IssueTempDtlItem;
import com.yzm.property.materials.entity.MaterialsInfo;
import com.yzm.property.materials.entity.MaterialsInfoBom;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 *
 */
public interface MaterialsInfoBomRepository extends BaseRepository<MaterialsInfoBom, Long> {

    List<MaterialsInfoBom> findByParentId(Long id);

    List<MaterialsInfoBom> findByAssetsNumber(String assetsNumber);

    List<MaterialsInfoBom> findByAssetsNumberAndInvoiceNumberAndPltNo(String assetsNumber, String invoiceNumber, String pltNo);

    List<MaterialsInfoBom> findByContainerNumber(String containerNumber);

    @Query(value = "select * from  materials_info_bom group by invoice_number", nativeQuery = true)
    List<MaterialsInfoBom> findGroupByNumber();


    @Query(value = "SELECT * FROM materials_info_bom where  REPLACE(assets_number, ' ', '')   = ?2 and invoice_number=?1 ", nativeQuery = true)
    List<MaterialsInfoBom> findByInvoiceNumberAndAssetsNumber(String invoiceNumber, String assetsNumber);

    List<MaterialsInfoBom> findByAssetsNumberAndInvoiceNumber(String assetsNumber, String invoiceNumber);

    List<MaterialsInfoBom> findByAssetsNumberAndInvoiceNumberAndContainerNumberAndPoNoAndPltNo(String assetsNumber, String invoiceNumber, String containerNumber, String poNo, String pltNo);

    @Query(value = "select * from materials_info_bom where assets_number like '%' ?1 '%' and assets_name  like '%' ?2 '%' and invoice_number  like '%' ?3 '%' and container_number  like '%' ?4 '%'  group by invoice_number,container_number", nativeQuery = true)
    List<MaterialsInfoBom> findGroupedResults(String assets_number, String assets_name, String invoice_number, String container_number, Pageable page);

    @Query(value = "SELECT COUNT(*) FROM ( " +
            "SELECT container_number FROM materials_info_bom " +
            "WHERE assets_number LIKE CONCAT('%', ?1, '%') " +
            "AND assets_name LIKE CONCAT('%', ?2, '%') " +
            "AND invoice_number LIKE CONCAT('%', ?3, '%') " +
            "AND container_number LIKE CONCAT('%', ?4, '%') " +
            "GROUP BY invoice_number,container_number) AS subquery", nativeQuery = true)
    int countGroupedResults(String assets_number, String assets_name, String invoice_number, String container_number);


    @Query(value = "select * from materials_info_bom where dosage - quantity_arrived <> 0 and invoice_number = ?1 and assets_number = ?2", nativeQuery = true)
    List<MaterialsInfoBom> findAllIsNotZ(String invoice_number, String assets_number);

    List<MaterialsInfoBom> findByInvoiceNumberAndAssetsNumberAndPltNo(String invoiceNumber, String assetsNumber, String pltNo);

    MaterialsInfoBom findByInvoiceNumberAndAssetsNumberAndMaterialLotNo(String invoiceNumber, String assetsNumber, String materialLotNo);

    List<MaterialsInfoBom> findByContainerNumberAndInvoiceNumber(String containerNumber, String invoiceNumber);

    List<MaterialsInfoBom> findByInvoiceNumber(String invoiceNumber);

    @Query(value = "select sum(dosage) from materials_info_bom where assets_number = ?1", nativeQuery = true)
    BigDecimal findByAssetsNumberSum(String assets_number);

    @Query(nativeQuery = true, value = "select sum(dosage) - quantity_arrived AS now_count , assets_number from materials_info_bom  GROUP BY assets_number  ")
    List<Map<String,Object>> findByAssetsNumberGroupByAssetsNumberList();
}
