package com.yzm.property.materials.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.materials.entity.BindingCar;
import com.yzm.property.materials.entity.IssueTempDtlTubeItem;
import com.yzm.property.materials.entity.MaterialsInfoBom;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface IssueTempDtlTubeItemRepository extends BaseRepository<IssueTempDtlTubeItem, Long> {
    List<IssueTempDtlTubeItem> findByPrimaryId(Long id);







    @Query(value = "SELECT * FROM issue_temp_dtl_tube_item " +
            "WHERE sect_cd LIKE CONCAT('%', ?1, '%') " +
            "AND line_cd LIKE CONCAT('%', ?2, '%') " +
            "AND work_ord_no LIKE CONCAT('%', ?3, '%') " +
            "AND assets_number LIKE CONCAT('%', ?4, '%') " +
            "AND prod_no LIKE CONCAT('%', ?5, '%') " +
            "GROUP BY batch_number, assets_number", nativeQuery = true)
    List<IssueTempDtlTubeItem> findGroupedResults(String sectCd, String lineCd, String workOrdNo, String assetsNumber, String prodNo, Pageable page);
    @Query(value = "SELECT COUNT(*) FROM ( " +
            "SELECT batch_number, assets_number FROM issue_temp_dtl_tube_item " +
            "WHERE sect_cd LIKE CONCAT('%', ?1, '%') " +
            "AND line_cd LIKE CONCAT('%', ?2, '%') " +
            "AND work_ord_no LIKE CONCAT('%', ?3, '%') " +
            "AND assets_number LIKE CONCAT('%', ?4, '%') " +
            "AND prod_no LIKE CONCAT('%', ?5, '%') " +
            "GROUP BY batch_number, assets_number) AS subquery", nativeQuery = true)
    int countGroupedResults(String sectCd, String lineCd, String workOrdNo, String assetsNumber, String prodNo);
}
