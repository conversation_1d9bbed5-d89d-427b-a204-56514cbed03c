package com.yzm.property.materials.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.materials.entity.IssueTempDtl;
import com.yzm.property.materials.entity.IssueTempDtlItem;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 *
 */
public interface IssueTempDtlItemRepository extends BaseRepository<IssueTempDtlItem, Long> {

    List<IssueTempDtlItem> findByPrimaryIdOrderByLocationCDesc(Long id);

    List<IssueTempDtlItem> findByPrimaryId(Long id);

    IssueTempDtlItem findByPrimaryIdAndMaterialNo(Long id, String assetsCode);

    @Query(value = "select COUNT(*) FROM issue_temp_dtl_item where primary_id = ?1 and issue_qty!=quantity_delivered and (succeed != 1 or succeed is null) ", nativeQuery = true)
    int findByPrimaryIdAndCountComparison(Long id);

    @Query(value = "select * from issue_temp_dtl_item where issue_qty - quantity_delivered <> 0  order by planned_issue_dt desc", nativeQuery = true)
    List<IssueTempDtlItem> findAllOrderByPlannedIssueDt();

    @Query(value = "select * from issue_temp_dtl_item where issue_qty = 0 and primary_id = ?1 ", nativeQuery = true)
    List<IssueTempDtlItem> findAllOrderByPrimaryId(Long primaryId);

    List<IssueTempDtlItem> findByKitNoAndMaterialNo(String kitNo, String assetsNumber);

    List<IssueTempDtlItem> findByLotNoAndMaterialNo(String kitNo, String assetsNumber);

    @Modifying
    @Query("UPDATE MaterialsRepertory m " +
            "SET m.nowRepertory = m.nowRepertory - :num " +
            "WHERE m.id = :id " +
            "AND m.nowRepertory >= :num")
        // 保证库存不扣为负
    int atomicReduceStock(@Param("id") Long id, @Param("num") BigDecimal num);

    @Modifying
    @Query("UPDATE IssueTempDtlItem i " +
            "SET  i.succeed = CASE WHEN (i.quantityDelivered + :num) >= i.issueQty THEN 1 ELSE 0 END ," +
            "    i.quantityDelivered = i.quantityDelivered + :num " +
            "WHERE i.id = :id " +
            "AND (i.quantityDelivered + :num) <= i.issueQty")
        // 防止超发
    int atomicAddQuantity(@Param("id") Long id, @Param("num") BigDecimal num);
}
