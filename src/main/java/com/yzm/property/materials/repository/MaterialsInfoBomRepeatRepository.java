package com.yzm.property.materials.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.materials.entity.MaterialsInfoBom;
import com.yzm.property.materials.entity.MaterialsInfoBomRepeat;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 *
 */
public interface MaterialsInfoBomRepeatRepository extends BaseRepository<MaterialsInfoBomRepeat, Long> {

    List<MaterialsInfoBom> findByParentId(Long id);

    List<MaterialsInfoBom> findByAssetsNumber(String assetsNumber);

    List<MaterialsInfoBom> findByAssetsNumberAndInvoiceNumberAndPltNo(String assetsNumber, String invoiceNumber, String pltNo);

    List<MaterialsInfoBom> findByContainerNumber(String containerNumber);

    @Query(value = "select * from  materials_info_bom group by invoice_number", nativeQuery = true)
    List<MaterialsInfoBom> findGroupByNumber();

    List<MaterialsInfoBom> findByInvoiceNumberAndAssetsNumber(String invoiceNumber, String assetsNumber);

    List<MaterialsInfoBom> findByAssetsNumberAndInvoiceNumber(String assetsNumber, String invoiceNumber);

    List<MaterialsInfoBom> findByAssetsNumberAndInvoiceNumberAndContainerNumberAndPoNoAndPltNo(String assetsNumber, String invoiceNumber, String containerNumber, String poNo,String pltNo);

    @Query(value = "select * from materials_info_bom where assets_number like '%' ?1 '%' and assets_name  like '%' ?2 '%' and invoice_number  like '%' ?3 '%' and container_number  like '%' ?4 '%'  group by invoice_number,container_number", nativeQuery = true)
    List<MaterialsInfoBom> findGroupedResults(String assets_number, String assets_name, String invoice_number, String container_number, Pageable page);

    @Query(value = "SELECT COUNT(*) FROM ( " +
            "SELECT container_number FROM materials_info_bom " +
            "WHERE assets_number LIKE CONCAT('%', ?1, '%') " +
            "AND assets_name LIKE CONCAT('%', ?2, '%') " +
            "AND invoice_number LIKE CONCAT('%', ?3, '%') " +
            "AND container_number LIKE CONCAT('%', ?4, '%') " +
            "GROUP BY invoice_number,container_number) AS subquery", nativeQuery = true)
    int countGroupedResults(String assets_number, String assets_name, String invoice_number, String container_number);


    @Query(value = "select * from materials_info_bom where dosage - quantity_arrived <> 0 and invoice_number = ?1 and assets_number = ?2", nativeQuery = true)
    List<MaterialsInfoBom> findAllIsNotZ(String invoice_number,String assets_number);

    List<MaterialsInfoBom> findByInvoiceNumberAndAssetsNumberAndPltNo(String invoiceNumber, String assetsNumber, String pltNo);

    List<MaterialsInfoBomRepeat> findByInvoiceNumberAndAssetsNumberAndMaterialLotNo(String invoiceNumber, String assetsNumber, String materialLotNo);

    List<MaterialsInfoBomRepeat> findByInvoiceAuditId(String invoiceAuditId);
}
