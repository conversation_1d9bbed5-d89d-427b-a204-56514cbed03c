package com.yzm.property.materials.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.materials.entity.MaterialsInfoBom;
import com.yzm.property.materials.entity.OutWarehouseMaterialsEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface OutWarehouseMaterialsRepository extends BaseRepository<OutWarehouseMaterialsEntity,Long> {
    @Query(nativeQuery=true,value = "select  o.* from materials_out_warehouse o  where   (o.department_code like '%' ?1 '%' or   o.lot_no like '%' ?1 '%' or o.product_no like '%' ?1 '%' or o.kit_no like '%' ?1 '%' or  o.line_code like '%' ?1 '%' or o.record_number like '%' ?1 '%' ) and o.department_code like '%' ?4 '%' group by o.kit_no ORDER by o.create_time desc LIMIT ?2 , ?3")

    List<OutWarehouseMaterialsEntity> findByRecordNumberContainsOrderByCreateTimeDesc(String keyword,int start, Integer size,String dept);



    @Query(value = "select      COUNT(DISTINCT o.kit_no)  from materials_out_warehouse o  where   (o.department_code like '%' ?1 '%' or   o.lot_no like '%' ?1 '%' or o.product_no like '%' ?1 '%' or o.kit_no like '%' ?1 '%' or  o.line_code like '%' ?1 '%' or o.record_number like '%' ?1 '%' )  and o.department_code like '%' ?2 '%'  ", nativeQuery = true)
    int countGroupedResults(String keyword,String dept);


//
//    @Query(nativeQuery = true, value = "SELECT *\n" +
//            "FROM (\n" +
//            "    SELECT \n" +
//            "        o.*,\n" +
//            "        ROW_NUMBER() OVER (PARTITION BY o.kit_no ORDER BY o.create_time DESC) AS rn\n" +
//            "    FROM materials_out_warehouse o\n" +
//            "    WHERE \n" +
//            "        o.out_reson = '3'\n" +
//            "        AND (\n" +
//            "            o.kit_no LIKE CONCAT('%', ?1, '%')\n" +
//            "            OR o.line_code LIKE CONCAT('%', ?1, '%')\n" +
//            "            OR o.record_number LIKE CONCAT('%', ?1, '%')\n" +
//            "            OR EXISTS (\n" +
//            "                SELECT 1\n" +
//            "                FROM materials_out_warehouse_item i\n" +
//            "                WHERE \n" +
//            "                    i.record_number = o.record_number\n" +
//            "                    AND (\n" +
//            "                        i.assets_number LIKE CONCAT('%', ?1, '%')\n" +
//            "                        OR i.assets_name LIKE CONCAT('%', ?1, '%')\n" +
//            "                    )\n" +
//            "            )\n" +
//            "        )\n" +
//            ") AS ranked\n" +
//            "WHERE rn = 1\n" +
//            "ORDER BY create_time DESC\n" +
//            "LIMIT ?2, ?3")
//    List<OutWarehouseMaterialsEntity> findByRecordNumberContainsOrderByCreateTimeDesc(String keyword, int start, Integer size);
//
//
//    @Query(nativeQuery = true, value = "SELECT COUNT(*)\n" +
//            "FROM (\n" +
//            "    SELECT o.kit_no\n" +
//            "    FROM materials_out_warehouse o\n" +
//            "    WHERE \n" +
//            "        o.out_reson = '3'\n" +
//            "        AND (\n" +
//            "            o.kit_no LIKE CONCAT('%', ?1, '%')\n" +
//            "            OR o.line_code LIKE CONCAT('%', ?1, '%')\n" +
//            "            OR o.record_number LIKE CONCAT('%', ?1, '%')\n" +
//            "            OR EXISTS (\n" +
//            "                SELECT 1\n" +
//            "                FROM materials_out_warehouse_item i\n" +
//            "                WHERE \n" +
//            "                    i.record_number = o.record_number\n" +
//            "                    AND (\n" +
//            "                        i.assets_number LIKE CONCAT('%', ?1, '%')\n" +
//            "                        OR i.assets_name LIKE CONCAT('%', ?1, '%')\n" +
//            "                    )\n" +
//            "            )\n" +
//            "        )\n" +
//            "    GROUP BY o.kit_no\n" +
//            ") AS grouped")
//    int countGroupedResults(String keyword);
//





    @Query(nativeQuery = true, value = "SELECT o.* \n" +
            "FROM materials_out_warehouse o WHERE \n" +
            "    o.out_reson = '4'\n" +
            "    AND (\n" +
            "        o.kit_no LIKE CONCAT('%', ?1, '%')\n" +
            "        OR o.line_code LIKE CONCAT('%', ?1, '%')\n" +
            "        OR o.record_number LIKE CONCAT('%', ?1, '%')\n" +
            "        OR EXISTS (\n" +
            "            SELECT 1\n" +
            "            FROM materials_out_warehouse_item i\n" +
            "            WHERE \n" +
            "                i.record_number = o.record_number\n" +
            "                AND (\n" +
            "                    i.assets_number LIKE CONCAT('%', ?1, '%')\n" +
            "                    OR i.assets_name LIKE CONCAT('%', ?1, '%')\n" +
            "                )\n" +
            "        )\n" +
            "    )\n" +
            "ORDER BY o.create_time DESC\n" +
            "LIMIT ?2, ?3")
    List<OutWarehouseMaterialsEntity> findByRecordNumberContainsOrderByCreateTimeDescOther(String keyword, int start, Integer size);


    @Query(nativeQuery = true, value = "SELECT COUNT(*) \n" +
            "FROM materials_out_warehouse o\n" +
            "WHERE \n" +
            "    o.out_reson = '4'\n" +
            "    AND (\n" +
            "        o.kit_no LIKE CONCAT('%', ?1, '%')\n" +
            "        OR o.line_code LIKE CONCAT('%', ?1, '%')\n" +
            "        OR o.record_number LIKE CONCAT('%', ?1, '%')\n" +
            "        OR EXISTS (\n" +
            "            SELECT 1\n" +
            "            FROM materials_out_warehouse_item i\n" +
            "            WHERE \n" +
            "                i.record_number = o.record_number\n" +
            "                AND (\n" +
            "                    i.assets_number LIKE CONCAT('%', ?1, '%')\n" +
            "                    OR i.assets_name LIKE CONCAT('%', ?1, '%')\n" +
            "                )\n" +
            "        )\n" +
            "    )")
    int countGroupedResultsOther(String keyword);



    @Query(nativeQuery=true,value = "select DISTINCT o.* from materials_out_warehouse  o,materials_out_warehouse_item  i where  o.record_number = i.record_number and (o.kit_no like '%' ?1 '%' or  o.line_code like '%' ?1 '%' or i.assets_number like '%' ?1 '%' or  i.assets_name like '%' ?1 '%' or o.record_number like '%' ?1 '%' )  group by kit_no ")
    List<OutWarehouseMaterialsEntity> findByRecordNumberContainsOrderByCreateTimeDesc(String keyword);

    Page<OutWarehouseMaterialsEntity> findByOrderByCreateTimeDesc(Pageable pagable);

    OutWarehouseMaterialsEntity findByKitNo(String key);

    OutWarehouseMaterialsEntity findByRecordNumber(String recordNumber);
}
