package com.yzm.property.materials.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.materials.entity.MaterialsInfo;
import com.yzm.property.materials.entity.MaterialsRepertoryFifo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 *
 */
public interface MaterialsRepertoryFifoRepository extends BaseRepository<MaterialsRepertoryFifo, Long> {


    MaterialsRepertoryFifo findByAssetsNumberAndBatchNumber(String assetsNumber, String batchNumber);

    MaterialsRepertoryFifo findByAssetsNumberAndBatchNumberAndFifoStatus(String assetsNumber, String batchNumber, long l);
}
