package com.yzm.property.materials.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.materials.entity.MaterialsListEntity;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Map;

public interface MaterialsListRepository extends BaseRepository<MaterialsListEntity, Long> {


    @Query(value = "select * from materials_list where assets_number = ?1 order by create_time desc limit 0,1", nativeQuery = true)
    List<Map<String, Object>> findByAssetsNumberGruopByReason(String number);
}
