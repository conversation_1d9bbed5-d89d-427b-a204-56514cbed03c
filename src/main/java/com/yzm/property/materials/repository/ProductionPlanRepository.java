package com.yzm.property.materials.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.materials.entity.BindingCar;
import com.yzm.property.materials.entity.ProductionPlan;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface ProductionPlanRepository extends BaseRepository<ProductionPlan, Long> {

    ProductionPlan findByBatchNo(String productNo);
}
