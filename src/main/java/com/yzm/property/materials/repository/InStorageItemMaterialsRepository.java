package com.yzm.property.materials.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.materials.entity.InStorageItemMaterials;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:49*/


public interface InStorageItemMaterialsRepository extends BaseRepository<InStorageItemMaterials, Long> {
    List<InStorageItemMaterials> findByRecordNumber(String recordNumber);


    List<InStorageItemMaterials> findByRecordNumberAndAssetsNumber(String recordNumber, String assetsNumber);

    InStorageItemMaterials findByRecordNumberAndAssetsCode(String recordNumber, String assetsCode);
}
