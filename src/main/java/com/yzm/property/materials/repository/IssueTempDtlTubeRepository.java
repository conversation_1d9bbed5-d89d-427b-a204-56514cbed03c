package com.yzm.property.materials.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.materials.entity.BindingCar;
import com.yzm.property.materials.entity.IssueTempDtl;
import com.yzm.property.materials.entity.IssueTempDtlTube;
import org.springframework.data.jpa.repository.Query;

import java.util.Date;
import java.util.List;

public interface IssueTempDtlTubeRepository extends BaseRepository<IssueTempDtlTube, Long> {
    IssueTempDtlTube findByProdNoAndWorkOrdNoAndMatlNoAndPlanIssDate(String prodNo, String workOrdNo, String matlNo, Date planIssDate);

    @Query(value = "select * from issue_temp_dtl_tube where cuting_qty - quantity_delivered <> 0  order by plan_iss_date desc",nativeQuery = true)
    List<IssueTempDtlTube> findAllOrderByPlannedIssueDt();

    IssueTempDtlTube findByWorkOrdNoAndMatlNo(String lotNo, String assetsNumber);

    @Query(value = "select * from issue_temp_dtl_tube where sect_cd = ?1 group by line_cd",nativeQuery = true)
    List<IssueTempDtlTube> findAllGroupLineCBySectionC(String sectionC);


    @Query(value = "select * from issue_temp_dtl_tube group by sect_cd",nativeQuery = true)
    List<IssueTempDtlTube> findAllGroupBySectionC();
}
