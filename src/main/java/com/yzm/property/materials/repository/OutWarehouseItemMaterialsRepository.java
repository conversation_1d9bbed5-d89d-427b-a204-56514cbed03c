package com.yzm.property.materials.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.materials.entity.OutWarehouseMaterialsItem;
import org.springframework.data.jpa.repository.Query;

import java.util.Date;
import java.util.List;
import java.util.Map;


public interface OutWarehouseItemMaterialsRepository extends BaseRepository<OutWarehouseMaterialsItem, Long> {
    List<OutWarehouseMaterialsItem> findByRecordNumber(String recordNumber);//通过记录编号来查数据库的资表数据

    List<OutWarehouseMaterialsItem> findByRecordNumberAndAssetsNumber(String recordNumber, String assetsNumber);

    OutWarehouseMaterialsItem findFirstByAssetsCodeOrderByIdDesc(String code);


    @Query(value = "select *,sum(now_repertory) as now_repertory from materials_out_warehouse_item where record_number =?1 group by assets_number", nativeQuery = true)
    List<OutWarehouseMaterialsItem> findByRecordNumberGroupByAssetsNumber(String recordNumber);

//    @Query(value = "SELECT b.* from materials_out_warehouse a , materials_out_warehouse_item b where a.record_number = b.record_number and a.kit_no=?1", nativeQuery = true)
    @Query(value = "SELECT * from materials_out_warehouse_item  where kit_no=?1", nativeQuery = true)
    List<OutWarehouseMaterialsItem> findByKitNo(String kitNo);

    List<OutWarehouseMaterialsItem> findByKitNoAndSucceed(String kitNo, long l);

    @Query(value = "select COUNT(*) FROM materials_out_warehouse_item where record_number = ?1 and consumable_num!=quantity_delivered and (succeed != 1 or succeed is null) ",nativeQuery = true)
    int findByPrimaryIdAndCountComparison(String recordNumber);

    List<OutWarehouseMaterialsItem> findByKitNoAndAssetsNumber(String kitNo, String assetsNumber);
//
//    @Query(value = "select o.kit_no as  kit_no,o.lot_no as lot_no,o.product_no as product_no, i.*  from materials_out_warehouse  o,materials_out_warehouse_item  i where  o.record_number = i.record_number " +
//            " and o.create_time >= ?1 and o.create_time <= ?2",nativeQuery = true)
//    List<Map<String,Object>> findAllInfo(Date firstDayOfLastMonth, Date lastDayOfLastMonth);
}
