package com.yzm.property.materials.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.materials.entity.BindingCar;
import com.yzm.property.materials.entity.ProductionPlanBom;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface ProductionPlanBomRepository extends BaseRepository<ProductionPlanBom, Long> {

    @Query(value = "select * from production_plan_bom where issue_date >= ?1  order by issue_date Asc", nativeQuery = true)
    List<ProductionPlanBom> findOrderByIssueDateAsc(String data);

    List<ProductionPlanBom> findByBatchNoAndMaterialNo(String lotNo, String assetsNumber);
}
