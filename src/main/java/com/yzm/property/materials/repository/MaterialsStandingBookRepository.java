package com.yzm.property.materials.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.materials.entity.MaterialsStandingBook;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface MaterialsStandingBookRepository extends BaseRepository<MaterialsStandingBook,Long> {
    Page<MaterialsStandingBook> findAllByAssetsNameContaining(String keyword, Pageable pagable);

}
