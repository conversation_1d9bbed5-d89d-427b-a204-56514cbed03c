package com.yzm.property.materials.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.materials.entity.BindingCar;
import com.yzm.property.materials.entity.DelinquentMaterial;

import java.util.List;

public interface DelinquentMaterialRepository extends BaseRepository<DelinquentMaterial, Long> {
    List<DelinquentMaterial> findByAssetsNumberOrderByCreateTimeAsc(String assetsNumber);

    List<DelinquentMaterial> findByKitNoAndLotNoAndAssetsNumber(String kitNo, String lotNo, String assetsNumber);

    List<DelinquentMaterial> findByLotNoAndAssetsNumber(String lotNo, String assetsNumber);

    List<DelinquentMaterial> findByKitNo(String kitNo);

    List<DelinquentMaterial> findByKitNoAndAssetsNumber(String kitNo, String assetsNumber);

    DelinquentMaterial findByAssetsNumberAndLotNo(String assetsNumber, String lotNo);
}
