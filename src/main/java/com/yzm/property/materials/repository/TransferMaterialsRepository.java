package com.yzm.property.materials.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.materials.entity.TransferMaterials;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:49*/


public interface TransferMaterialsRepository extends BaseRepository<TransferMaterials, Long> {

    Page<TransferMaterials> findByOrderByCreateTimeDesc(Pageable pagable);
    @Query(nativeQuery=true,value = "select distinct c.* from transfer_item b ,transfer c where   b.record_number = c.record_number and (  b.record_number like '%' ?1 '%' or b.assets_number like '%' ?1 '%' or  b.assets_specifications like '%' ?1 '%' or b.assets_rfid = ?1 ) " +
            "order by c.create_time desc  limit ?2 , ?3 ")
    List<TransferMaterials> findByRecordNumberContainsOrderByCreateTimeDesc(String keyword, int i, Integer limit);
}
