package com.yzm.property.materials.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.materials.entity.BindingCar;
import com.yzm.property.materials.entity.MaterialsListEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface BindingCarRepository extends BaseRepository<BindingCar, Long> {

    @Query(value = "select * from binding_car where (car1=?1 or car2 =?1 or car3 = ?1 or car4 = ?1) and `status`= ?2 ", nativeQuery = true)
    BindingCar findByCar1AndStatus(String rfidData, long l);

    @Query(value = "select count(*) from binding_car where product2 like %:product%", nativeQuery = true)
    long countByProduct2Like(@Param("product") String product);
}
