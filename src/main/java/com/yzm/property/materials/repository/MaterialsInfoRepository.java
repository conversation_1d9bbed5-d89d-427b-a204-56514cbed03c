package com.yzm.property.materials.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.materials.entity.MaterialsInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 *
 */
public interface MaterialsInfoRepository extends BaseRepository<MaterialsInfo, Long> {



    Page<MaterialsInfo> findAllByAssetsNameContainingOrAssetsNumberContaining(String keyword, String assetsNumber, Pageable pageable);

    MaterialsInfo findByAssetsNumber(String assetsNumber);

    MaterialsInfo getByStorageWarehouseAndStorageArea(String storageWarehouse, String storageArea);

//    ConsumableInfo findByAssetsCodeOrAssetsRfid(String rfid,String code);
}
