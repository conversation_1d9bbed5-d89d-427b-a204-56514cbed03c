package com.yzm.property.materials.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.materials.entity.MaterialsRepertory;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:49
 */
public interface MaterialsRepertoryRepository extends BaseRepository<MaterialsRepertory, Long> {

    MaterialsRepertory findByMaterialsInfoId(Long consumableInfoId);

    MaterialsRepertory findByMaterialsInfoIdAndWarehouseInfo(Long consumableInfoId, String warehouseInfo);

    @Query(nativeQuery = true, value = "select *,sum(now_repertory) AS now_count from materials_repertory GROUP BY assets_number,warehouse_info,area_info order by create_time desc  limit ?1 , ?2 ")
    List<Map<String, Object>> findByMaterialsRepertory(int i, int limit);

    @Query(nativeQuery = true, value = "select *,sum(now_repertory) AS now_count from materials_repertory where (assets_number  like '%' ?1 '%'  and  department_code  like '%' ?2 '%') GROUP BY assets_number order by create_time desc  limit ?3 , ?4 ")
    List<Map<String, Object>> findByAssetsNumberOrderByCreateTimeDesc(String keyword,String dept, int i, int limit);

    @Query(nativeQuery = true, value = "select *,sum(now_repertory) AS now_count from materials_repertory where (assets_number  like '%' ?1 '%' ) GROUP BY assets_number order by create_time  ")
    List<Map<String, Object>> findByNowRepertoryAll(String keyword);

    @Query(value = "SELECT COUNT(*) FROM (select assets_number from materials_repertory where (assets_number  like '%' ?1 '%'  and  department_code  like '%' ?2 '%') GROUP BY assets_number ) AS subquery", nativeQuery = true)
    int countGroupedResults(String keyword,String dept);


    @Query(nativeQuery = true, value = "select * from materials_repertory where (assets_number  like '%' ?1 '%' or assets_name like '%' ?1 '%' or assets_specifications  like '%' ?1 '%') GROUP BY assets_number order by create_time desc  limit ?2 , ?3 ")
    List<Map<String, Object>> findByListAssetsNumberOrderByCreateTimeDesc(String keyword, int i, int limit);

    MaterialsRepertory findByAssetsCodeOrAssetsRfid(String rfid, String code);


    @Query(nativeQuery = true, value = "select *,sum(now_repertory) AS now_count from materials_repertory where assets_number = ?1  GROUP BY assets_number  ")
    Map<String, Object> findByAssetsNumberGroupByAssetsNumber(String keyword);

    @Query(nativeQuery = true, value = "select *,sum(now_repertory) AS now_count from materials_repertory  GROUP BY assets_number  ")
    List<Map<String, Object>> findByAssetsNumberGroupByAssetsNumberList();

    List<MaterialsRepertory> findByAssetsNumberOrderByCreateTimeDesc(String assetsNumber);


    List<MaterialsRepertory> findByAssetsNumber(String assetsNumber);

    List<MaterialsRepertory> findByAssetsNumberAndWarehouseInfoAndAreaInfo(String assetsNumber, String warehouseInfo, String areaInfo);

    List<MaterialsRepertory> findByWarehouseInfoAndAreaInfo(String warehouseInfo, String areaInfo);

    List<MaterialsRepertory> findByWarehouseInfo(String warehouseInfo);

    List<MaterialsRepertory> findByDepartmentCode(String departmentCode);

    MaterialsRepertory findByAssetsRfidAndAssetsNumber(String rfid, String assetsNumber);

    List<MaterialsRepertory> findByAssetsNumberOrderByDateArrivalDesc(String assetsNumber);

    List<MaterialsRepertory> findByAssetsNumberOrderByDateArrivalAsc(String assetsNumber);



    @Query(value = "select * from materials_repertory where assets_number =?1 and  (fifo_status != ?2 or fifo_status is null) and now_repertory !=0 and date_arrival is not null order by date_arrival asc",nativeQuery = true)
    List<MaterialsRepertory> findByAssetsNumberAndFifoStatusOrderByDateArrivalAsc(String assetsNumber,long fifoStatus);



    MaterialsRepertory findByAssetsNumberAndAssetsCodeIsNull(String assetsNumber);

    @Query(value = "select * from materials_repertory group by department_code",nativeQuery = true)
    List<MaterialsRepertory> findGroupByDepartmentCode();

    @Query(value = "select assets_number,SUM(now_repertory) now_repertory_sum from materials_repertory where department_code =  '%' ?1 '%' GROUP BY assets_number",nativeQuery = true)
    List<Map<String, Object>> findSumByDepartmentCode(String departmentCode);

    List<MaterialsRepertory> findByAssetsNumberAndBatchNumber(String assetsNumber, String batchNumber);

    List<MaterialsRepertory> findByAssetsCodeInOrAssetsRfidIn(List<String> assetCodes, List<String> assetRfids);

//    Page<MaterialsRepertory> findAllByAssetsNameContainingOrAssetsBrandContaining(String keyword, String assetsBrand, Pageable pagable);


    @Modifying
    @Query("UPDATE MaterialsRepertory m " +
            "SET m.nowRepertory = m.nowRepertory - :num " +
            "WHERE m.id = :id " +
            "AND m.nowRepertory >= :num")  // 保证库存不扣为负
    int atomicReduceStock(@Param("id") Long id, @Param("num") BigDecimal num);

    @Modifying
    @Query(nativeQuery = true,
            value = "UPDATE materials_repertory " +
                    "SET now_repertory = now_repertory - :num " +
                    "WHERE id = :id " +
                    "AND now_repertory >= :num")
    int atomicReduce(@Param("id") Long id, @Param("num") BigDecimal num);

    @Modifying
    @Query(nativeQuery = true,
            value = "DELETE FROM materials_repertory " +
                    "WHERE id = :id " +
                    "AND now_repertory = 0")
    int deleteIfZero(@Param("id") Long id);

    @Query(value = "select *,sum(now_repertory) as nowRepertorySum from materials_repertory group by assets_number", nativeQuery = true)
    List<Map<String,Object>> groupByAssetsNumber();

}
