package com.yzm.property.materials.entity;

import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;

public class WordReplace {
    public static void main(String[] args) throws IOException {
        String inputFilePath = "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/1359952b98e24ea5c8a1ff2c026cf6b9/Message/MessageTemp/9a68383a7f11efd2714937d112d6cd68/File/一些模板/01.诉讼案件接谈笔录.docx";
        String outputFilePath = "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/1359952b98e24ea5c8a1ff2c026cf6b9/Message/MessageTemp/9a68383a7f11efd2714937d112d6cd68/File/一些模板/01.诉讼案件接谈笔录-back.docx";

        String placeholder = "{name}";
        String replacement = "John Doe";

        try (FileInputStream fis = new FileInputStream(inputFilePath);
             XWPFDocument document = new XWPFDocument(fis)) {

            // 遍历所有段落
            for (XWPFParagraph paragraph : document.getParagraphs()) {
                List<XWPFRun> runs = paragraph.getRuns();
                if (runs != null) {
                    StringBuilder paragraphText = new StringBuilder();

                    // 记录占位符的起始和结束run索引
                    int startRun = -1;
                    int endRun = -1;

                    // 将所有run的文本拼接起来
                    for (int i = 0; i < runs.size(); i++) {
                        String runText = runs.get(i).getText(0);
                        if (runText != null && runText.contains(placeholder)) {
                            startRun = i; // 记录第一个包含占位符的run索引
                            endRun = i;   // 记录最后一个包含占位符的run索引
                            break;
                        }
                    }

                    // 如果找到了占位符，替换文本并更新原有run
                    if (startRun != -1) {
                        // 找到占位符所在的 run，并替换该 run 的文本
                        XWPFRun targetRun = runs.get(startRun);
                        String runText = targetRun.getText(0);
                        if (runText != null) {
                            // 替换文本
                            String newText = runText.replace(placeholder, replacement);
                            targetRun.setText(newText, 0);
                        }
                    }
                }
            }

            // 保存到新的文件
            try (FileOutputStream fos = new FileOutputStream(outputFilePath)) {
                document.write(fos);
            }

            System.out.println("替换完成，文件已保存为：" + outputFilePath);
        }
    }
}
