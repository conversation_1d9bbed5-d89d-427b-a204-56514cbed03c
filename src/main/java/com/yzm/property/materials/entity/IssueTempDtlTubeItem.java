package com.yzm.property.materials.entity;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Entity
@Table(name = "issue_temp_dtl_tube_item")
@AllArgsConstructor
@NoArgsConstructor
public class IssueTempDtlTubeItem {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @GenericGenerator(name = "persistenceGenerator", strategy = "increment")
    @Column(name = "id", unique = true, nullable = false, length = 20)
    protected Long id;
    private Long primaryId;
    private String recordNumber; //记录编号

    private String sectCd;         // 部门代码 (varchar 6)
    private String lineCd;         // 生成线代码 (varchar 6)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat("yyyy-MM-dd")
    private Date planIssDate;      // 计划发料日 (datetime)
    private String prodNo;         // 品番 (varchar 24)
    private String cusdeschCd1;    // 客户设变 (varchar 4)
    private String cusdeschCd2;    // 指定仕样 (varchar 2)
    private String intdeschCd;     // 社内设变 (varchar 2)
    private BigDecimal planIssQty;        // 计划数量 (int)
    private String matlNo;         // 物料号码 (varchar 32)
    private BigDecimal bomQty;         // BOM数量 (decimal)
    private BigDecimal totQty;        // 总数量 (int)
    private String matlNm;         // 物料名称 (nvarchar 15)
    private BigDecimal cutLen;        // 切断长 (int)
    private BigDecimal cutingQty;     // 切断数量 (int)

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat("yyyy-MM-dd")
    private Date planIssDate1;     // 计划日期 (datetime)
    private String workOrdNo;      // 生产批号 (nvarchar 20)

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat("yyyy-MM-dd")
    private Date operatingTime;     // 操作时间 (datetime)

    //状态 0 未出库，部分出库 ，已出库
    private Long status;
    //已出数量
    private BigDecimal quantityDelivered;
    //已出总数
    private BigDecimal quantityDeliveredLen;
    //首长度
    private BigDecimal firstLen;
    //末长度
    private BigDecimal endLen;
    //条码
    private String assetsCode;

    @Transient
    private String itemJson;

    private Long issuerId;//发料人id
    private String issuerName;//发料员

    @Column(name = "assets_number", columnDefinition = "varchar(100) COMMENT '材料编号'")
    private String assetsNumber; //材料编号
    @Column(name = "assets_name", columnDefinition = "varchar(100) COMMENT '材料名称'")
    private String assetsName; //材料名称
    @Column(name = "assets_unit_name", columnDefinition = "varchar(20) COMMENT '材料单位'")
    private String assetsUnitName; //材料单位
    @Column(name = "assets_type_name", columnDefinition = "varchar(255) COMMENT '材料类型'")
    private String assetsTypeName; //材料类型
    @Column(name = "consumable_num", columnDefinition = "int(20) COMMENT '出库数量'")
    private BigDecimal consumableNum;//数量
    private BigDecimal nowRepertory; //当前库存
    private String areaInfo; //库位
    private String batchNumber; //材料批号
    /** 操作人员 */
    private String operName;
}
