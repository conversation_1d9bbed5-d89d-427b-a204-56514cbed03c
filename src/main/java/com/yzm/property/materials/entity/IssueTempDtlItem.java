package com.yzm.property.materials.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Entity
@Table(name = "issue_temp_dtl_item")
public class IssueTempDtlItem {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @GenericGenerator(name = "persistenceGenerator", strategy = "increment")
    @Column(name = "id", unique = true, nullable = false, length = 20)
    protected Long id;

    private Long primaryId;

    @Column(name = "section_c")
    private String sectionC;
    @Column(name = "line_c")
    private String lineC;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date plannedIssueDt;//发料时间
    private int shift;
    private String kitNo;//发料指示书号
    private String lotNo;
    private String productNo;
    private String cusdeschC1;
    private String cusdeschC2;
    private String intdeschC;
    private int planIssueQty;
    private String materialNo;
    private BigDecimal bomQty;
    private String bomUnit;
    private String materialNm;
    private String issueUnit;
    private String locationC;//发料地址
    private int issueMinLot;
    private int unitConfacIssue;
    private BigDecimal reqdQtyBom;
    private BigDecimal reqdQtyIssue; //需求数量

    private BigDecimal issueQty; //发料数量

    @Transient
    private BigDecimal reqdQtyIssueSum;
    private String type;
    //状态 0 未出库，部分出库 ，已出库
    private Long status;
    //已出数量
    private BigDecimal quantityDelivered; //已发数量
    private String matlPrcsCd;//楼层
    private String shortFeed;


    private Long fillBatchNumber; //是否填充批号
    private Long storageWarehouseId;//仓库id
    private String storageWarehouse;//仓库名称
    private Long storageAreaId;//库位id
    private String storageArea;//库位名称

    private Long taskStatus;//欠料提醒状态

    private Long succeed;//是否完成
}
