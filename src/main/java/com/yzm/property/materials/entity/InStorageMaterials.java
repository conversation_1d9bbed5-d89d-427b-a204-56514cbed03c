package com.yzm.property.materials.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yzm.framework.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "materials_in_storage")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InStorageMaterials extends BaseEntity {
    @Column(name = "record_number", columnDefinition = "varchar(100) COMMENT '记录编号'")
    private String recordNumber; //记录编号
    @Column(name = "in_storage_date", columnDefinition = "date COMMENT '入库时间'")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date inStorageDate;//入库时间
    @Column(name = "in_storage_reson", columnDefinition = "varchar(100) COMMENT '记录原由'")
    private String inStorageReson; //记录原由
    @Column(name = "in_storage_people", columnDefinition = "varchar(100) COMMENT '入库人'")
    private String inStoragePeople;//入库人

    private String applicant;//申请人

    @Column(name = "in_storage_warehouse", columnDefinition = "varchar(100) COMMENT '收入仓库'")
    private String inStorageWarehouse;//收入仓库
    @Column(name = "in_storage_area", columnDefinition = "varchar(100) COMMENT '收入库位'")
    private String inStorageArea;//收入库位

    private Long modeOfTrade;//贸易方式

    private Long status;//状态

    private Long inStatus;//状态

    private String departmentCode;//部门代码

    private String auditor;//审核人
    private String invoiceNumber;//发票号码

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date auditorDate;//审核时间



//    @Column(name = "in_storage_total", columnDefinition = "(10) COMMENT '入库数量'")
    private BigDecimal inStorageTotal; //入库总数

    /**
     * 入库子表json
     */
    @Transient
    private String itemJson;


}
