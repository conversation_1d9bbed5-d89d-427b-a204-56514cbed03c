package com.yzm.property.materials.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yzm.framework.base.BaseEntity;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 欠料信息
 */
@Data
@Entity
@Table(name = "delinquent_material")
public class DelinquentMaterial extends BaseEntity {
    @Column(name = "assets_number", columnDefinition = "varchar(100) COMMENT '产品编号'")
    private String assetsNumber; //编号
    @Column(name = "assets_name", columnDefinition = "varchar(100) COMMENT '材料名称'")
    private String assetsName; //部品名称
    @Column(name = "assets_unit", columnDefinition = "int(20) COMMENT '材料单位ID'")
    private Long assetsUnit; //材料单位ID
    @Column(name = "assets_unit_name", columnDefinition = "varchar(20) COMMENT '材料单位'")
    private String assetsUnitName; //材料单位

    private BigDecimal sumData; //欠料数量
    private BigDecimal delinquentCount; //欠料数量

    private BigDecimal quantityArrived;//已到数量
    private BigDecimal transitCount;//在途数量
    private String kitNo;//发料指示书号
    private String productNo;//品番
    private String lotNo;//批号
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat("yyyy-MM-dd")
    private Date plannedIssueDt; //计划日期
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat("yyyy-MM-dd")
    private Date naageDate; //纳期


    private Long status; //欠料状态

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat("yyyy-MM-dd")
    private Date estimatedTime; //预计到货时间



}
