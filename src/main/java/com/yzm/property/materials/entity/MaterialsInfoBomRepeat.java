package com.yzm.property.materials.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yzm.framework.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;

/**
 * 部品总览
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:33
 */
@Entity
@Table(name = "materials_info_bom_repeat")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MaterialsInfoBomRepeat extends BaseEntity {
    private Long parentId; //上级id

    @Column(name = "assets_number", columnDefinition = "varchar(100) COMMENT '产品编号'")
    @ExcelProperty(value = "产品编号", index = 0)

    private String assetsNumber; //编号
    @Column(name = "assets_name", columnDefinition = "varchar(100) COMMENT '部品名称'")
    @ExcelProperty(value = "部品名称", index = 1)
    private String assetsName; //部品名称
    @Column(name = "assets_specifications", columnDefinition = "varchar(100) COMMENT '规格型号'")
    @ExcelProperty(value = "规格型号")
    private String assetsSpecifications;//规格型号
    @Column(name = "assets_unit", columnDefinition = "int(20) COMMENT '部品单位ID'")
    private Long assetsUnit; //部品单位ID
    @ExcelProperty(value = "部品数量", index = 2)
    private BigDecimal dosage; //用量


    @Column(name = "assets_unit_name", columnDefinition = "varchar(20) COMMENT '部品单位'")
    @ExcelProperty(value = "部品单位", index = 3)
    private String assetsUnitName; //部品单位
    private String poNo;
    private String pltNo;
    //    @Column(name = "assets_code", columnDefinition = "varchar(255) COMMENT '部品条码'")
//    private String assetsCode; //部品条码
//    @Column(name = "assets_rfid", columnDefinition = "varchar(255) COMMENT '部品RFID'")
//    private String assetsRfid; //部品RFID
    @Column(name = "assets_type_name", columnDefinition = "varchar(255) COMMENT '部品类型'")
    @ExcelProperty(value = "部品类型")
    private String assetsTypeName; //部品类型
    @ExcelProperty(value = "部品类型上级")
    @Transient
    private String assetsTypeNameSuperior; //部品类型
    @Column(name = "assets_type", columnDefinition = "varchar(255) COMMENT '部品类型Id'")
    private Long assetsType; //部品类型
    @Column(name = "assets_position", columnDefinition = "varchar(255) COMMENT '常用货位'")
    @ExcelProperty(value = "常用货位")
    private String assetsPosition; //常用货位

    private Long fillBatchNumber; //是否填充批号
    @ExcelProperty(value = "集装箱号", index = 4)

    private String containerNumber; //集装箱号
    @ExcelProperty(value = "发票号码", index = 5)

    private String invoiceNumber; //发票号码
    @ExcelProperty(value = "贸易方式", index = 6)
    private String modeOfTrade;//贸易方式

    private BigDecimal quantityArrived;//已到数量

    private String materialLotNo;//批号

    private String invoiceAuditId;//批号







    private Long minLot; //最小包装
    private String assetsStatus; //在途地点
}
