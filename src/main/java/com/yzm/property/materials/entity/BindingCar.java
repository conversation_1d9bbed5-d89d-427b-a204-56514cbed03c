package com.yzm.property.materials.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yzm.framework.base.BaseEntity;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Data
@Entity
@Table(name = "binding_car")
public class BindingCar extends BaseEntity {


    private String matlPrcsCd1;

    private String car1;
    private String car2;
    private String car3;
    private String car4;
    private String product1;
    private String product2;
    private String product3;
    private String product4;



    private Long status; //状态


}
