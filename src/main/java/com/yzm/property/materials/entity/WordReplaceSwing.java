import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;

import javax.swing.*;
import java.awt.*;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class WordReplaceSwing extends JFrame {
    private JTextArea selectedFilesArea;
    private JTextField outputDirField;
    private File[] selectedFiles;
    private File outputDir;
    private JPanel placeholderPanel;
    private List<JTextField> placeholderFields = new ArrayList<>();
    private List<JTextField> replacementFields = new ArrayList<>();

    public WordReplaceSwing() {
        setTitle("Word 文档批量占位符替换");
        setSize(600, 500);  // 增大窗口尺寸
        setDefaultCloseOperation(EXIT_ON_CLOSE);
        setLocationRelativeTo(null);
        setLayout(new BorderLayout());

        JPanel topPanel = new JPanel(new GridLayout(2, 1));

        // 文件选择按钮
        JButton selectFilesButton = new JButton("选择模板文件");
        selectedFilesArea = new JTextArea(3, 20);
        selectedFilesArea.setEditable(false);
        JScrollPane fileScrollPane = new JScrollPane(selectedFilesArea);
        selectFilesButton.addActionListener(e -> chooseFiles());

        // 输出目录选择按钮
        JButton selectOutputDirButton = new JButton("选择输出目录");
        outputDirField = new JTextField();
        outputDirField.setEditable(false);
        selectOutputDirButton.addActionListener(e -> chooseOutputDirectory());

        topPanel.add(selectFilesButton);
        topPanel.add(fileScrollPane);
        topPanel.add(selectOutputDirButton);
        topPanel.add(outputDirField);
        add(topPanel, BorderLayout.NORTH);

        // 占位符输入区域
        placeholderPanel = new JPanel();
        placeholderPanel.setLayout(new BoxLayout(placeholderPanel, BoxLayout.Y_AXIS));
        JScrollPane placeholderScrollPane = new JScrollPane(placeholderPanel);
        add(placeholderScrollPane, BorderLayout.CENTER);

        // 添加按钮面板
        JPanel buttonPanel = new JPanel(new FlowLayout());

        // 增加占位符输入框按钮
        JButton addPlaceholderButton = new JButton("添加占位符");
        addPlaceholderButton.addActionListener(e -> addPlaceholderField());
        buttonPanel.add(addPlaceholderButton);

        // 执行替换按钮
        JButton executeButton = new JButton("替换并保存");
        executeButton.addActionListener(e -> replacePlaceholders());
        buttonPanel.add(executeButton);

        add(buttonPanel, BorderLayout.SOUTH);  // 添加到底部

        // 初始化第一个占位符输入框
        addPlaceholderField();
    }

    // 动态添加占位符和替换内容的输入字段
    private void addPlaceholderField() {
        JPanel rowPanel = new JPanel(new GridLayout(1, 4, 5, 5));
        JTextField placeholderField = new JTextField();
        JTextField replacementField = new JTextField();
        placeholderFields.add(placeholderField);
        replacementFields.add(replacementField);

        rowPanel.add(new JLabel("占位符:"));
        rowPanel.add(placeholderField);
        rowPanel.add(new JLabel("替换内容:"));
        rowPanel.add(replacementField);
        placeholderPanel.add(rowPanel);

        placeholderPanel.revalidate();
        placeholderPanel.repaint();
    }

    // 选择多个文件
    private void chooseFiles() {
        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setMultiSelectionEnabled(true);
        fileChooser.setFileSelectionMode(JFileChooser.FILES_ONLY);
        int result = fileChooser.showOpenDialog(this);
        if (result == JFileChooser.APPROVE_OPTION) {
            selectedFiles = fileChooser.getSelectedFiles();
            StringBuilder sb = new StringBuilder();
            for (File file : selectedFiles) {
                sb.append(file.getAbsolutePath()).append("\n");
            }
            selectedFilesArea.setText(sb.toString());
        }
    }

    // 选择输出目录
    private void chooseOutputDirectory() {
        JFileChooser dirChooser = new JFileChooser();
        dirChooser.setFileSelectionMode(JFileChooser.DIRECTORIES_ONLY);
        int result = dirChooser.showOpenDialog(this);
        if (result == JFileChooser.APPROVE_OPTION) {
            outputDir = dirChooser.getSelectedFile();
            outputDirField.setText(outputDir.getAbsolutePath());
        }
    }

    // 替换 Word 文件中的占位符
    private void replacePlaceholders() {
        if (selectedFiles == null || outputDir == null || placeholderFields.isEmpty()) {
            JOptionPane.showMessageDialog(this, "请填写所有字段并选择文件和输出目录。");
            return;
        }

        // 获取占位符和对应的替换内容
        List<String> placeholders = new ArrayList<>();
        List<String> replacements = new ArrayList<>();
        for (int i = 0; i < placeholderFields.size(); i++) {
            String placeholder = placeholderFields.get(i).getText();
            String replacement = replacementFields.get(i).getText();
            if (!placeholder.isEmpty() && !replacement.isEmpty()) {
                placeholders.add(placeholder);
                replacements.add(replacement);
            }
        }

        if (placeholders.isEmpty()) {
            JOptionPane.showMessageDialog(this, "请填写至少一个占位符和替换内容。");
            return;
        }

        for (File file : selectedFiles) {
            try (FileInputStream fis = new FileInputStream(file);
                 XWPFDocument document = new XWPFDocument(fis)) {

                // 遍历段落并替换占位符
                for (XWPFParagraph paragraph : document.getParagraphs()) {
                    for (XWPFRun run : paragraph.getRuns()) {
                        String text = run.getText(0);
                        if (text != null) {
                            // 逐个替换所有占位符
                            for (int i = 0; i < placeholders.size(); i++) {
                                if (text.contains(placeholders.get(i))) {
                                    text = text.replace(placeholders.get(i), replacements.get(i));
                                }
                            }
                            run.setText(text, 0); // 更新文本
                        }
                    }
                }

                // 保存修改后的文档到输出目录
                String outputFilePath = outputDir.getAbsolutePath() + File.separator + file.getName();
                try (FileOutputStream fos = new FileOutputStream(outputFilePath)) {
                    document.write(fos);
                }

                System.out.println("文件已保存到: " + outputFilePath);

            } catch (IOException e) {
                e.printStackTrace();
                JOptionPane.showMessageDialog(this, "处理文件时出错: " + file.getName());
            }
        }

        JOptionPane.showMessageDialog(this, "替换完成！");
    }

    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            WordReplaceSwing app = new WordReplaceSwing();
            app.setVisible(true);
        });
    }
}
