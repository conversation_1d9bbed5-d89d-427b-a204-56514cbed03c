package com.yzm.property.materials.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yzm.framework.base.BaseEntity;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Entity
@Table(name = "production_plan")
public class ProductionPlan extends BaseEntity {

    private String productionArea;  // 生产区域

    private String carModel;  // 车种

    private String productNo;  // 品番

    private String designChange;  // 设变

    private String batchNo;  // 批号

    private BigDecimal quantity;  // 数量

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date issueDate;  // 发料日

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deliveryDate;  // 纳期

    private String remarks;  // 备注

}
