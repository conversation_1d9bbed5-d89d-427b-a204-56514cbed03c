package com.yzm.property.materials.entity;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Entity
@Table(name = "issue_temp_dtl_tube")
@AllArgsConstructor
@NoArgsConstructor
public class IssueTempDtlTube {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @GenericGenerator(name = "persistenceGenerator", strategy = "increment")
    @Column(name = "id", unique = true, nullable = false, length = 20)
    protected Long id;
    private String sectCd;         // 部门代码 (varchar 6)
    private String lineCd;         // 生成线代码 (varchar 6)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat("yyyy-MM-dd")
    private Date planIssDate;      // 计划发料日 (datetime)
    private String prodNo;         // 品番 (varchar 24)
    private String cusdeschCd1;    // 客户设变 (varchar 4)
    private String cusdeschCd2;    // 指定仕样 (varchar 2)
    private String intdeschCd;     // 社内设变 (varchar 2)
    private BigDecimal planIssQty;        // 计划数量 (int)
    private String matlNo;         // 物料号码 (varchar 32)
    private BigDecimal bomQty;         // BOM数量 (decimal)
    private BigDecimal totQty;        // 总数量 (int)
    private String matlNm;         // 物料名称 (nvarchar 15)
    private BigDecimal cutLen;        // 切断长 (int)
    private BigDecimal cutingQty;     // 切断数量 (int)

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat("yyyy-MM-dd")
    private Date planIssDate1;     // 计划日期 (datetime)
    private String workOrdNo;      // 生产批号 (nvarchar 20)

    //状态 0 未出库，部分出库 ，已出库
    private Long status;

    private Long taskStatus;//欠料提醒状态


    //已出数量
    private BigDecimal quantityDelivered;
    //已出数量长度
    private BigDecimal quantityDeliveredLen;
    private String shortFeed;

    @Transient
    private String itemJson;

    private Long issuerId;//发料人id
    private String issuerName;//发料员



}
