package com.yzm.property.materials.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yzm.framework.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name="materials_out_warehouse")
public class OutWarehouseMaterialsEntity extends BaseEntity {
    @Column(name = "out_reson", columnDefinition = "varchar(100) COMMENT '出库原由'")
    private String outReson; //记录原由
    @Column(name = "out_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private Date outDate;//出库时间
    @Column(name = "record_number", columnDefinition = "varchar(100) COMMENT '记录编号'")
    private String recordNumber; //记录编号
    @Column(name = "department_code", columnDefinition = "varchar(100) COMMENT '部门代码'")
    private String departmentCode; //部门代码
    @Column(name = "line_code", columnDefinition = "varchar(100) COMMENT '生产线代码'")
    private String lineCode; //生产线代码
    @Column(name = "shift", columnDefinition = "int(20)  COMMENT '班次'")
    private int shift; //班次


    private String applicant;//申请人

    private String auditor;//审核人
    private Long modeOfTrade;//贸易方式
    private String kitNo;//发料指示书号
    private String lotNo;//批号
    private BigDecimal planIssueQty;//手配数
    private String productNo;//品番
    private String matlPrcsCd;//发往楼层


    //未提交  待审核 审核通过 审核不通过
    private Long status;//状态
    private Long inStatus;//状态


    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date auditorDate;//审核时间





    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date intendedDate; //计划发料日期





    @Column(name="consumable_num",columnDefinition = "int(20) COMMENT '出库数量'")
    private BigDecimal consumableNum;//出库数量
    @Column(name="user_name",columnDefinition = "varchar(20) COMMENT '出库人'")
    private String userName;//出库人
    @Column(name="consumable_price",columnDefinition = "varchar(20) COMMENT '单价'")
    private String consumablePrice;//单价
     @Column(name="consumable_company",columnDefinition = "varchar(20) COMMENT '往来单位'")
    private String consumableCompany;//往来单位
     @Column(name="consumable_department",columnDefinition = "varchar(20) COMMENT '部门'")
    private String consumableDepartment;//部门

    /** 入库子表json */
    @Transient
    private String itemJson;
}
