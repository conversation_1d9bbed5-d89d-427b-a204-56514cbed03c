package com.yzm.property.materials.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yzm.framework.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.List;

/**
 * 材料总览
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:33
 */
@Entity
@Table(name = "materials_info")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MaterialsInfo extends BaseEntity {

    @Column(name = "assets_number", columnDefinition = "varchar(100) COMMENT '产品编号'")
    @ExcelProperty(value = "产品编号", index = 0)
    private String assetsNumber; //编号
    @Column(name = "assets_name", columnDefinition = "varchar(100) COMMENT '材料名称'")
    @ExcelProperty(value = "部品名称", index = 1)
    private String assetsName; //部品名称
    @Column(name = "assets_specifications", columnDefinition = "varchar(100) COMMENT '规格型号'")
    @ExcelProperty(value = "规格型号")
    private String assetsSpecifications;//规格型号
    @Column(name = "assets_unit", columnDefinition = "int(20) COMMENT '材料单位ID'")
    private Long assetsUnit; //材料单位ID
    @Column(name = "assets_unit_name", columnDefinition = "varchar(20) COMMENT '材料单位'")
    @ExcelProperty(value = "材料单位", index = 2)
    private String assetsUnitName; //材料单位
    //    @Column(name = "assets_code", columnDefinition = "varchar(255) COMMENT '材料条码'")
//    private String assetsCode; //材料条码
//    @Column(name = "assets_rfid", columnDefinition = "varchar(255) COMMENT '材料RFID'")
//    private String assetsRfid; //材料RFID
    @Column(name = "assets_type_name", columnDefinition = "varchar(255) COMMENT '材料类型'")
    @ExcelProperty(value = "材料类型", index = 3)
    private String assetsTypeName; //材料类型
    //    @ExcelProperty(value = "材料类型上级")
//    @Transient
//    private String assetsTypeNameSuperior; //材料类型
    @Column(name = "assets_type", columnDefinition = "varchar(255) COMMENT '材料类型Id'")
    private Long assetsType; //材料类型
    @Column(name = "assets_position", columnDefinition = "varchar(255) COMMENT '常用货位'")
    @ExcelProperty(value = "常用货位")
    private String assetsPosition; //常用货位
    @Column(name = "bom_number", columnDefinition = "varchar(255) COMMENT 'BOM编码'")
    @ExcelProperty(value = "BOM编码")
    private String bomNumber; //BOM编码


    @ExcelProperty(value = "系数", index = 4)
    private BigDecimal minLot; //最小包装
    @ExcelProperty(value = "是否填充批号", index = 5)
    private Long fillBatchNumber; //是否填充批号
    private Long storageWarehouseId;//仓库id
    @ExcelProperty(value = "仓库名称", index = 6)
    private String storageWarehouse;//仓库名称
    private Long storageAreaId;//库位id
    @ExcelProperty(value = "库位名称", index = 7)
    private String storageArea;//库位名称

    @Transient
    private String itemJson;

    @Transient
    private String assetsCode; //材料条码

    @Transient
    private String count; //材料数量
    @Transient
    private String batchNumber; //材料批号
    @Transient
    private String dateArrival; //来料日期


    @Transient
    private List<MaterialsInfoBom> bomList;
}
