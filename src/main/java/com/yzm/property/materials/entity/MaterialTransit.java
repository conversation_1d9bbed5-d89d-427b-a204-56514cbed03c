package com.yzm.property.materials.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yzm.framework.base.BaseEntity;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.List;

/**
 * 在途物料信息维护
 */
@Data
@Entity
@Table(name = "material_transit")
public class MaterialTransit extends BaseEntity {
    @Column(name = "assets_number", columnDefinition = "varchar(100) COMMENT '产品编号'")
    @ExcelProperty(value = "产品编号", index = 0)
    private String assetsNumber; //编号
    @Column(name = "assets_name", columnDefinition = "varchar(100) COMMENT '材料名称'")
    @ExcelProperty(value = "部品名称", index = 1)
    private String assetsName; //部品名称
    @Column(name = "assets_unit", columnDefinition = "int(20) COMMENT '材料单位ID'")
    private Long assetsUnit; //材料单位ID
    @Column(name = "assets_unit_name", columnDefinition = "varchar(20) COMMENT '材料单位'")
    @ExcelProperty(value = "材料单位", index = 2)
    private String assetsUnitName; //材料单位
    @ExcelProperty(value = "材料数量", index = 3)
    private String assetsCount; //材料数量
    @ExcelProperty(value = "当前状态", index = 5)
    private String assetsStatus; //当前状态

}
