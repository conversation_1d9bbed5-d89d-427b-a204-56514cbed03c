package com.yzm.property.materials.entity;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yzm.framework.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Entity
@Table(name = "issue_temp_dtl")
@AllArgsConstructor
@NoArgsConstructor
public class IssueTempDtl  {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @GenericGenerator(name = "persistenceGenerator", strategy = "increment")
    @Column(name = "id", unique = true, nullable = false, length = 20)
    protected Long id;
    @Column(name = "section_c")
    private String sectionC;
    @Column(name = "line_c")
    private String lineC;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat("yyyy-MM-dd")
    private Date plannedIssueDt;
    private int shift;
    private String kitNo;//发料指示书号
    private String lotNo;//批号
    private String productNo;//品番
    private String cusdeschC1;
    private String cusdeschC2;
    private String intdeschC;
    private BigDecimal planIssueQty;
    private BigDecimal issueQty;
    private String materialNo;
    private BigDecimal bomQty;
    private String bomUnit;
    private String materialNm;
    private String issueUnit;
    private String locationC;//发料地址

    private int issueMinLot;
    private int unitConfacIssue;
    private BigDecimal reqdQtyBom;
    private BigDecimal reqdQtyIssue;
    @Transient
    private BigDecimal reqdQtyIssueSum;
    private String type;
    private String matlPrcsCd;//楼层
    //状态 0 未出库，部分出库 ，已出库
    private Long status;
    //已出数量
    private BigDecimal quantityDelivered;

    private String shortFeed;

    @Transient
    private String itemJson;

    private Long issuerId;//发料人id

    private Long trolleySum;//小车数

    private String issuerName;//发料员



}
