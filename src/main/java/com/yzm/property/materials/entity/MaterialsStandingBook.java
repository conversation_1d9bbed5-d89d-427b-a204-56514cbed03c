package com.yzm.property.materials.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yzm.framework.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * 耗材台账
 * @version 1.0
 * @date 2024/4/26 9:33
 */
@Entity
@Table(name = "materials_standing_book")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MaterialsStandingBook extends BaseEntity {
        @Column(name = "standing_book_number", columnDefinition = "varchar(100) COMMENT '批次号'")
        private String standingBookNumber; //批次号
        @Column(name = "in_storage_count", columnDefinition = "int(10) COMMENT '数量'")
        private Integer inStorageCount; //总数
        @Column(name = "in_storage_date", columnDefinition = "date COMMENT '时间'")
        @JsonFormat(pattern = "yyyy-MM-dd" , timezone = "GMT+8")
        private Date inStorageDate;//时间


        @Column(name = "assets_number", columnDefinition = "varchar(100) COMMENT '材料编号'")
        private String assetsNumber; //材料编号
        @Column(name = "assets_name", columnDefinition = "varchar(100) COMMENT '材料名称'")
        private String assetsName; //材料名称
        @Column(name = "assets_specifications", columnDefinition = "varchar(100) COMMENT '规格型号'")
        private String assetsSpecifications;//规格型号
        @Column(name = "assets_unit", columnDefinition = "int(20) COMMENT '材料单位ID'")
        private Long assetsUnit; //材料单位ID
        @Column(name = "assets_unit_name", columnDefinition = "varchar(20) COMMENT '材料单位'")
        private String assetsUnitName; //材料单位
        @Column(name = "assets_code", columnDefinition = "varchar(255) COMMENT '材料条码'")
        private String assetsCode; //材料条码
        @Column(name = "assets_rfid", columnDefinition = "varchar(255) COMMENT '材料RFID'")
        private String assetsRfid; //材料RFID
        @Column(name = "assets_type", columnDefinition = "varchar(255) COMMENT '材料类型'")
        private String assetsType; //材料类型
        @Column(name = "assets_position", columnDefinition = "varchar(255) COMMENT '常用货位'")
        private String assetsPosition; //常用货位
        @Column(name = "assets_id", columnDefinition = "bigint COMMENT '材料ID'")
        private Long assetsId;

        @Column(name = "now_repertory", columnDefinition = "int(20) COMMENT '当前库存'")
        private Integer nowRepertory; //当前库存



}
