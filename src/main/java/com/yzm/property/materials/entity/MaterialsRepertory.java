package com.yzm.property.materials.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yzm.framework.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 材料库存
 */
@Entity
@Table(name = "materials_repertory")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MaterialsRepertory extends BaseEntity {
    @Column(name = "assets_number", columnDefinition = "varchar(100) COMMENT '材料编号'")
    @ExcelProperty(value = "产品编号", index = 0)
    private String assetsNumber; //材料编号
    @Column(name = "assets_name", columnDefinition = "varchar(100) COMMENT '材料名称'")
    @ExcelProperty(value = "产品名称", index = 1)
    private String assetsName; //材料名称
    @Column(name = "assets_specifications", columnDefinition = "varchar(100) COMMENT '规格型号'")
    private String assetsSpecifications;//规格型号
    @Column(name = "assets_unit", columnDefinition = "int(20) COMMENT '材料单位ID'")
    private Long assetsUnit; //材料单位ID
    @Column(name = "assets_unit_name", columnDefinition = "varchar(20) COMMENT '材料单位'")
    @ExcelProperty(value = "材料单位", index = 2)
    private String assetsUnitName; //材料单位
    @Column(name = "assets_code", columnDefinition = "varchar(255) COMMENT '材料条码'")
    private String assetsCode; //材料条码
    @Column(name = "assets_rfid", columnDefinition = "varchar(255) COMMENT '材料RFID'")
    private String assetsRfid; //材料RFID
    @Column(name = "assets_type_name", columnDefinition = "varchar(255) COMMENT '材料类型'")
    private String assetsTypeName; //材料类型
    @Column(name = "assets_type", columnDefinition = "varchar(255) COMMENT '材料类型Id'")
    private Long assetsType; //材料类型
    @Column(name = "assets_position", columnDefinition = "varchar(255) COMMENT '常用货位'")
    private String assetsPosition; //常用货位
    @Column(name = "now_repertory", columnDefinition = "int(20) COMMENT '当前库存'")
    @ExcelProperty(value = "库存数量", index = 3)
    private BigDecimal nowRepertory; //当前库存
    @Column(name = "warehouse_info", columnDefinition = "varchar(255) COMMENT '库存仓库'")
    @ExcelProperty(value = "仓库信息", index = 4)
    private String warehouseInfo; //库存仓库
    @Column(name = "area_info", columnDefinition = "varchar(255) COMMENT '库区'")
    @ExcelProperty(value = "库位信息", index = 5)
    private String areaInfo; //库位
    @Column(name = "area_info_id", columnDefinition = "bigint(20) COMMENT '库位ID'")
    private Long areaInfoId; //库位id
    @Column(name = "location_info", columnDefinition = "varchar(255) COMMENT '库位'")
    private String locationInfo; //库位
    @Column(name = "operation_number", columnDefinition = "varchar(255) COMMENT '操作单号'")
    private String operationNumber; //操作单号
  /*  @Column(name = "plan_im_repertory", columnDefinition = "int(20) COMMENT '待入库'")
    private Integer planImRepertory; //待入库
    @Column(name = "plan_ex_repertory", columnDefinition = "int(20) COMMENT '待出库'")
    private Integer planExRepertory; //待出库*/
    @Column(name = "consumable_info_id", columnDefinition = "bigint(20) COMMENT '材料ID'")
    private Long consumableInfoId; //材料ID

    private Long materialsInfoId; //操作单号

    @ExcelProperty(value = "贸易方式", index = 6)
    private Long modeOfTrade;//贸易方式
    private String invoiceNumber;//发票号码

    private Long priority;//是否优先出库

    private Long inDataType;//入库类型  0 正常 1 不良 2退库

    @JsonFormat(pattern = "yyyyMMdd", timezone = "GMT+8")
    private Date dateArrival;//来料日期

    private String departmentCode;//部门代码
    @ExcelProperty(value = "材料批号", index = 8)
    private String batchNumber; //材料批号

    private Long fifoStatus; //先进先出
    /**
     * 入库子表json
     */
    @Transient
    private BigDecimal nowCount;
    /**
     * 入库子表json
     */
    @Transient
    @ExcelProperty(value = "来料日期", index = 7)
    private String dateArrivalString;

}
