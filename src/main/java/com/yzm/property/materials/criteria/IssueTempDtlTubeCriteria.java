package com.yzm.property.materials.criteria;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yzm.common.annotation.Query;
import com.yzm.framework.base.CriteriaBean;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class IssueTempDtlTubeCriteria extends CriteriaBean {
    @Query(blurry = "matlNo,matlNm,workOrdNo,prodNo")
    private String keyword;
    @Query(type = Query.Type.EQUAL)
    private String sectCd;
    @Query(type = Query.Type.EQUAL)
    private String lineCd;
    @Query(type = Query.Type.NOT_EQUAL)
    private Long status;

    @Query(type = Query.Type.EQUAL, propName = "status")
    private Long statusData;
    @Query(type = Query.Type.EQUAL)
    private Long issuerId;

    @Query(type = Query.Type.GREATER_THAN, propName = "plannedIssueDt")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Date startTime;
    @Query(type = Query.Type.LESS_THAN, propName = "plannedIssueDt")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Date endTime;
}
