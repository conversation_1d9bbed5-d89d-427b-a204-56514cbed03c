package com.yzm.property.materials.criteria;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yzm.common.annotation.Query;
import com.yzm.framework.base.CriteriaBean;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
public class IssueTempDtlCriteria extends CriteriaBean {
    @Query(blurry = "lotNo,kitNo,productNo")
    private String keyword;
    @Query(type = Query.Type.INNER_LIKE)
    private String kitNo;
    @Query(type = Query.Type.INNER_LIKE)
    private String productNo;
    @Query(type = Query.Type.INNER_LIKE)
    private String lotNo;
    @Query(type = Query.Type.INNER_LIKE)
    private String materialNo;
    @Query(type = Query.Type.EQUAL)
    private String sectionC;
    @Query(type = Query.Type.INNER_LIKE)
    private String lineC;
    @Query(type = Query.Type.IN)
    private List<Long> status;

    @Query(type = Query.Type.EQUAL, propName = "status")
    private Long statusData;

    @Query(type = Query.Type.EQUAL)
    private Long issuerId;

    @Query(type = Query.Type.GREATER_THAN, propName = "plannedIssueDt")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Date startTime;
    @Query(type = Query.Type.LESS_THAN, propName = "plannedIssueDt")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Date endTime;
}
