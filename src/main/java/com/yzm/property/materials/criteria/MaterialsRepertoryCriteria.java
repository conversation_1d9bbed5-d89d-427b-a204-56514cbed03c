package com.yzm.property.materials.criteria;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yzm.common.annotation.Query;
import com.yzm.framework.base.CriteriaBean;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import java.util.Date;
import java.util.List;

@Getter
@Setter
public class MaterialsRepertoryCriteria extends CriteriaBean {
    @Query(blurry = "assetsNumber,assetsName,areaInfo,assetsCode")
    private String keyword;

    @Query(type = Query.Type.IN,propName = "inDataType")
    private List<String> dataType;

    @Query(type = Query.Type.EQUAL)
    private String departmentCode;

}
