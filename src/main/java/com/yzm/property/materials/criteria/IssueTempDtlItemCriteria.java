package com.yzm.property.materials.criteria;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yzm.common.annotation.Query;
import com.yzm.framework.base.CriteriaBean;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class IssueTempDtlItemCriteria extends CriteriaBean {
    @Query(blurry = "lotNo,kitNo,productNo,materialNo,materialNm")
    private String keyword;
    @Query(type = Query.Type.EQUAL)
    private String sectionC;
    @Query(type = Query.Type.INNER_LIKE)
    private String lineC;
    @Query(type = Query.Type.NOT_EQUAL)
    private Long status;


}
