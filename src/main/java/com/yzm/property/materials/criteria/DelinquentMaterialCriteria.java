package com.yzm.property.materials.criteria;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yzm.common.annotation.Query;
import com.yzm.framework.base.CriteriaBean;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import java.util.Date;

@Getter
@Setter
public class DelinquentMaterialCriteria extends CriteriaBean {

    @Query(type = Query.Type.INNER_LIKE)
    private String assetsNumber; //编号
    @Query(type = Query.Type.INNER_LIKE)
    private String assetsName; //部品名称
    @Query(type = Query.Type.INNER_LIKE)
    private String operationName;
    @Query(type = Query.Type.INNER_LIKE)
    private String consumableOtherId;
    @Query(type = Query.Type.INNER_LIKE)
    private String consumableName;
    @Query(type = Query.Type.GREATER_THAN, propName = "createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Date startTime;
    @Query(type = Query.Type.LESS_THAN, propName = "createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Date endTime;
}
