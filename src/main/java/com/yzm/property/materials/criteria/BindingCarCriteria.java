package com.yzm.property.materials.criteria;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yzm.common.annotation.Query;
import com.yzm.framework.base.CriteriaBean;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class BindingCarCriteria extends CriteriaBean {
    @Query(blurry = "car1,car2,car3,car4")
    private String rfidCode;
    @Query(blurry = "product1,product2,product3,product4")
    private String assetsCode;
    @Query(type = Query.Type.INNER_LIKE)
    private String product2;
    @Query(type = Query.Type.INNER_LIKE)
    private String product1;
    @Query(type = Query.Type.INNER_LIKE)
    private String operationName;
    @Query(type = Query.Type.INNER_LIKE)
    private String consumableOtherId;
    @Query(type = Query.Type.INNER_LIKE)
    private String consumableName;
    @Query(type = Query.Type.INNER_LIKE)
    private String assetsNumber;
    @Query(type = Query.Type.GREATER_THAN, propName = "createTime")
    @JsonFormat(shape= JsonFormat.Shape.STRING, pattern="yyyy-MM-dd")
    private Date startTime;
    @Query(type = Query.Type.LESS_THAN, propName = "createTime")
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd")
    private Date endTime;
}
