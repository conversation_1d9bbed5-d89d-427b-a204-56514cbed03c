package com.yzm.property.materials.criteria;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yzm.common.annotation.Query;
import com.yzm.framework.base.CriteriaBean;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class OutWarehouseCriteria extends CriteriaBean {


    @Query(blurry = "assetsNumber,assetsName,lotNo,kitNo,productNo")
    private String keywordItem;

    @Query(blurry = "lotNo,kitNo,productNo")
    private String keyword;
    @Query(type = Query.Type.EQUAL)
    private String sectionC;
    @Query(type = Query.Type.INNER_LIKE)
    private String lineCode;
    @Query(type = Query.Type.EQUAL)
    private String status;

    @Query(type = Query.Type.GREATER_THAN, propName = "createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Date startTime;
    @Query(type = Query.Type.LESS_THAN, propName = "createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Date endTime;

}
