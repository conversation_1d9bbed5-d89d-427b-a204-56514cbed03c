package com.yzm.property.materials.criteria;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yzm.common.annotation.Query;
import com.yzm.framework.base.CriteriaBean;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
public class InStorageItemCriteria extends CriteriaBean {


    @Query(type = Query.Type.IN,propName = "inStorageReson")
    private List<String> type;
    @Query(type = Query.Type.GREATER_THAN, propName = "succeedDate")
    @JsonFormat(shape= JsonFormat.Shape.STRING, pattern="yyyy-MM-dd")
    private Date startTime;
    @Query(type = Query.Type.LESS_THAN, propName = "succeedDate")
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd")
    private Date endTime;
}
