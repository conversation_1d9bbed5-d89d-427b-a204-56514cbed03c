//package com.yzm.property.materials.service;
//
//import com.yzm.framework.base.IBaseService;
//import com.yzm.framework.bean.ResponseData;
//import com.yzm.property.materials.entity.BindingCar;
//
//public interface BindingCarServiceBack extends IBaseService<BindingCar,Long>{
//
//
//    BindingCar findByCar1AndStatus(String rfidData, long l);
//
//    ResponseData addTrolleyData(String code, String floor,Long id);
//    ResponseData checkTrolleyData(String code, String floor,Long id);
//}
