package com.yzm.property.materials.service;

import com.yzm.framework.base.IBaseService;
import com.yzm.property.materials.entity.InStorageItemMaterials;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:47
 */
public interface InStorageItemService extends IBaseService<InStorageItemMaterials,Long> {


    List<InStorageItemMaterials> findByRecordNumber(String recordNumber);

    List<InStorageItemMaterials> findByRecordNumberAndAssetsNumber(String recordNumber, String assetsNumber);
}
