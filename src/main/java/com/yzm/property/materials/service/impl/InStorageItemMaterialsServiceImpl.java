package com.yzm.property.materials.service.impl;

import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.property.materials.entity.InStorageItemMaterials;
import com.yzm.property.materials.repository.InStorageItemMaterialsRepository;
import com.yzm.property.materials.service.InStorageItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:47
 */
@Service
public class InStorageItemMaterialsServiceImpl extends BaseService<InStorageItemMaterials, Long> implements InStorageItemService {
    @Autowired
    private InStorageItemMaterialsRepository inStorageItemRepository;

    @Override
    public BaseRepository<InStorageItemMaterials, Long> getRepository() {
        return inStorageItemRepository;
    }


    @Override
    public List<InStorageItemMaterials> findByRecordNumber(String recordNumber) {
        return inStorageItemRepository.findByRecordNumber(recordNumber);
    }

    @Override
    public List<InStorageItemMaterials> findByRecordNumberAndAssetsNumber(String recordNumber, String assetsNumber) {
        return inStorageItemRepository.findByRecordNumberAndAssetsNumber(recordNumber,assetsNumber);
    }
}
