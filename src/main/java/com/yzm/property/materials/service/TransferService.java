package com.yzm.property.materials.service;

import com.yzm.framework.base.IBaseService;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.materials.entity.TransferMaterials;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:47
 */
public interface TransferService extends IBaseService<TransferMaterials, Long> {

    Map showAllTransferInfo(String keyword, PageBean pageBean);

    ResponseData saveTransfer(TransferMaterials transfer);
}
