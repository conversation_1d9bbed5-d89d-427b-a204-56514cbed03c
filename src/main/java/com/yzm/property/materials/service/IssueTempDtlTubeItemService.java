package com.yzm.property.materials.service;

import com.yzm.framework.base.IBaseService;
import com.yzm.property.materials.entity.BindingCar;
import com.yzm.property.materials.entity.IssueTempDtlTubeItem;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

public interface IssueTempDtlTubeItemService extends IBaseService<IssueTempDtlTubeItem,Long>{


    List<IssueTempDtlTubeItem> findByPrimaryId(Long id);

    Map<String, Object> findAllByPageGroup(IssueTempDtlTubeItem issueTempDtlTubeItem, Pageable pagable);
}
