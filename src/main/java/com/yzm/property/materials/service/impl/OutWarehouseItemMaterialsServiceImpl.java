package com.yzm.property.materials.service.impl;

import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.property.materials.entity.OutWarehouseMaterialsItem;
import com.yzm.property.materials.repository.OutWarehouseItemMaterialsRepository;
import com.yzm.property.materials.service.OutWarehouseItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
public class OutWarehouseItemMaterialsServiceImpl extends BaseService<OutWarehouseMaterialsItem, Long> implements OutWarehouseItemService {
    @Autowired
    private OutWarehouseItemMaterialsRepository outWarehouseItemRepository;

    @Override
    public BaseRepository<OutWarehouseMaterialsItem, Long> getRepository() {
        return outWarehouseItemRepository;
    }


    @Override
    //通过记录编号查询字表信息
    public List<OutWarehouseMaterialsItem> findByRecordNumber(String recordNumber) {
        return outWarehouseItemRepository.findByRecordNumber(recordNumber);
    }

    @Override
    public List<OutWarehouseMaterialsItem> findByRecordNumberGroupByAssetsNumber(String recordNumber) {
        return outWarehouseItemRepository.findByRecordNumberGroupByAssetsNumber(recordNumber);
    }

    @Override
    public List<OutWarehouseMaterialsItem> findByRecordNumberAndAssetsNumber(String recordNumber, String assetsNumber) {
        return outWarehouseItemRepository.findByRecordNumberAndAssetsNumber(recordNumber, assetsNumber);
    }

    @Override
    public List<OutWarehouseMaterialsItem> findByKitNo(String kitNo) {
        return outWarehouseItemRepository.findByKitNo(kitNo);
    }

    @Override
    public List<OutWarehouseMaterialsItem> findByKitNoAndAssetsNumber(String kitNo, String assetsNumber) {
        return outWarehouseItemRepository.findByKitNoAndAssetsNumber(kitNo, assetsNumber);
    }

    @Override
    public int findByPrimaryIdAndCountComparison(String recordNumber) {
        return outWarehouseItemRepository.findByPrimaryIdAndCountComparison(recordNumber);
    }

//    @Override
//    public List<OutWarehouseMaterialsItem> findAllInfo(Date firstDayOfLastMonth, Date lastDayOfLastMonth) {
//        List<Map<String, Object>> allInfo = outWarehouseItemRepository.findAllInfo(firstDayOfLastMonth, lastDayOfLastMonth);
//        return ;
//    }

}
