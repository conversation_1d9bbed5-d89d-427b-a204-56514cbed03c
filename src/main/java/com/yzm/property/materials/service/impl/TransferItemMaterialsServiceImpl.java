package com.yzm.property.materials.service.impl;

import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.property.materials.entity.TransferMaterialsItem;
import com.yzm.property.materials.repository.TransferItemMaterialsRepository;
import com.yzm.property.materials.service.TransferItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:47
 */
@Service
public class TransferItemMaterialsServiceImpl extends BaseService<TransferMaterialsItem, Long> implements TransferItemService {
    @Autowired
    private TransferItemMaterialsRepository transferItemRepository;

    @Override
    public BaseRepository<TransferMaterialsItem, Long> getRepository() {
        return transferItemRepository;
    }


    @Override
    public List<TransferMaterialsItem> findByRecordNumber(String recordNumber) {
        return transferItemRepository.findByRecordNumber(recordNumber);
    }
}
