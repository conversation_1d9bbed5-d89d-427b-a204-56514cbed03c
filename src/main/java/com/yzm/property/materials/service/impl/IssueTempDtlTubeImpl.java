package com.yzm.property.materials.service.impl;

import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.fastjson.JSONArray;
import com.yzm.common.utils.OrderUtils;
import com.yzm.common.utils.ShiroUtils;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.consumable.repository.ConsumableListRepository;
import com.yzm.property.materials.entity.*;
import com.yzm.property.materials.repository.BindingCarRepository;
import com.yzm.property.materials.repository.IssueTempDtlTubeItemRepository;
import com.yzm.property.materials.repository.IssueTempDtlTubeRepository;
import com.yzm.property.materials.repository.MaterialsListRepository;
import com.yzm.property.materials.service.BindingCarService;
import com.yzm.property.materials.service.IssueTempDtlTubeService;
import com.yzm.property.materials.service.MaterialsRepertoryService;
import com.yzm.property.materials.utils.MaterialsUtil;
import com.yzm.property.system.entity.SysUser;
import com.yzm.property.system.repository.SysUserRepository;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Service
public class IssueTempDtlTubeImpl extends BaseService<IssueTempDtlTube, Long> implements IssueTempDtlTubeService {
    @Autowired
    private IssueTempDtlTubeRepository issueTempDtlTubeRepository;
    @Autowired
    private IssueTempDtlTubeItemRepository issueTempDtlTubeItemRepository;

    @Autowired
    private SysUserRepository sysUserRepository;
    @Autowired
    private MaterialsRepertoryService materialsRepertoryService;

    @Autowired
    private MaterialsListRepository materialsListRepository;

    public BaseRepository<IssueTempDtlTube, Long> getRepository() {
        return issueTempDtlTubeRepository;
    }

    @Override
    public boolean allocationByIds(Long[] ids, Long salesmanId) {
        for (Long id : ids) {
            IssueTempDtlTube one = issueTempDtlTubeRepository.getOne(id);
            one.setIssuerId(salesmanId);
            SysUser one1 = sysUserRepository.getOne(salesmanId);
            one.setIssuerName(one1.getName());
            issueTempDtlTubeRepository.saveAndFlush(one);
        }
        return true;
    }

    @Override
    @Transactional
    public ResponseData deliveryStorage(IssueTempDtlTube issueTempDtl) {
        IssueTempDtlTube one = issueTempDtlTubeRepository.getOne(issueTempDtl.getId());
        String itemJson = issueTempDtl.getItemJson();
        List<IssueTempDtlTubeItem> issueTempDtlTubeItems = JSONArray.parseArray(itemJson, IssueTempDtlTubeItem.class);
        BigDecimal sum = new BigDecimal(0);
        BigDecimal sumLen = new BigDecimal(0);
        String parameterNo = OrderUtils.getJiaoGuanCuKuCode();

        for (IssueTempDtlTubeItem issueTempDtlTubeItem : issueTempDtlTubeItems) {
            BigDecimal quantityDelivered = issueTempDtlTubeItem.getQuantityDelivered(); // 切割数量

            BeanUtils.copyProperties(one, issueTempDtlTubeItem);
            issueTempDtlTubeItem.setId(null);
            issueTempDtlTubeItem.setRecordNumber(parameterNo);
            issueTempDtlTubeItem.setQuantityDelivered(quantityDelivered);
            BigDecimal cutLen = one.getCutLen(); // 切割长度
            BigDecimal firstLen = issueTempDtlTubeItem.getFirstLen(); // 首段长度
            BigDecimal endLen = issueTempDtlTubeItem.getEndLen(); // 末尾长度

            BigDecimal totalLength = BigDecimal.ZERO; // 总长度初始化

            if (quantityDelivered.compareTo(BigDecimal.ONE) == 0) {
                // 如果切割数量为1，直接取首段长度
                totalLength = firstLen;
            } else if (quantityDelivered.compareTo(new BigDecimal(2)) == 0) {
                // 如果切割数量为2，则总长度为首段 + 末尾长度
                totalLength = firstLen.add(endLen);
            } else if (quantityDelivered.compareTo(new BigDecimal(3)) >= 0) {
                // 如果切割数量大于等于3，按照公式计算
                totalLength = cutLen.multiply(quantityDelivered.subtract(new BigDecimal(2))) // 中间段长度
                        .add(firstLen) // 加首段长度
                        .add(endLen); // 加末尾长度
            }
            sum = sum.add(issueTempDtlTubeItem.getQuantityDelivered());
            sumLen = sumLen.add(totalLength);


            totalLength = totalLength.divide(new BigDecimal(1000));


            issueTempDtlTubeItem.setQuantityDeliveredLen(totalLength);

            MaterialsRepertory materialsRepertory = materialsRepertoryService.findByAssetsCodeOrAssetsRfid(issueTempDtlTubeItem.getAssetsCode(), issueTempDtlTubeItem.getAssetsCode());
            if (materialsRepertory == null) {
                throw new ExcelAnalysisException(issueTempDtlTubeItem.getAssetsCode() + "-条码没有库存");
            }
            materialsRepertory.setNowRepertory(materialsRepertory.getNowRepertory().subtract(totalLength));//当前库存
            if (materialsRepertory.getNowRepertory().compareTo(new BigDecimal(0)) == 0) {
                materialsRepertoryService.delete(materialsRepertory);
            } else if (materialsRepertory.getNowRepertory().compareTo(new BigDecimal(0)) == -1) {
                throw new ExcelAnalysisException("库存不足无法出库");
            } else {
                materialsRepertoryService.update(materialsRepertory);
            }
            issueTempDtlTubeItem.setOperatingTime(new Date());
            issueTempDtlTubeItem.setOperName(ShiroUtils.getUserInfo().getName());
            issueTempDtlTubeItemRepository.save(issueTempDtlTubeItem);
            MaterialsListEntity consumableListEntity = new MaterialsListEntity();
            BeanUtils.copyProperties(materialsRepertory, consumableListEntity);
            consumableListEntity.setNowRepertory(totalLength);
            consumableListEntity.setId(null);
            consumableListEntity.setOperationName(MaterialsUtil.OPERATION_CHUKU);
            consumableListEntity.setReason("9");
            consumableListEntity.setOperationOtherId(issueTempDtlTubeItem.getRecordNumber());
            consumableListEntity.setOperationId(issueTempDtlTubeItem.getId());
            consumableListEntity.setUserName(ShiroUtils.getUserInfo().getName());
            consumableListEntity.setOutDate(new Date());
            consumableListEntity.setInvoiceNumber(materialsRepertory.getInvoiceNumber());
            consumableListEntity.setDateArrival(materialsRepertory.getDateArrival());
            consumableListEntity.setModeOfTrade(materialsRepertory.getModeOfTrade());
            consumableListEntity.setDepartmentCode(materialsRepertory.getDepartmentCode());
            consumableListEntity.setCreateTime(new Date());
            materialsListRepository.save(consumableListEntity);
        }


        if (StringUtils.isEmpty(one.getQuantityDelivered())) {
            one.setQuantityDelivered(sum);
        } else {
            one.setQuantityDelivered(one.getQuantityDelivered().add(sum));
        }

        if (StringUtils.isEmpty(one.getQuantityDeliveredLen())) {
            one.setQuantityDeliveredLen(sumLen);
        } else {
            one.setQuantityDeliveredLen(one.getQuantityDeliveredLen().add(sumLen));
        }
        if (one.getQuantityDelivered().compareTo(one.getCutingQty()) == 1) {
            throw new ExcelAnalysisException("切割数量超出胶管任务数量，需求数量为（"+one.getCutingQty()+"）提交数量总数为：（"+one.getQuantityDelivered()+"）请修改后提交");
        }
        if (one.getQuantityDelivered().compareTo(one.getCutingQty()) != -1) {
            one.setStatus(2L);
        } else {
            one.setStatus(1L);
        }
        return ResponseData.success();
    }

    @Override
    public ResponseData deliveryStorageTube(IssueTempDtlTubeItem issueTempDtlTubeItem) {
        return null;
    }

    @Override
    public List<IssueTempDtlTube> findAllGroupLineCBySectionC(String sectionC) {
        return issueTempDtlTubeRepository.findAllGroupLineCBySectionC(sectionC);
    }


    @Override
    public List<IssueTempDtlTube> findAllGroupBySectionC() {
        return issueTempDtlTubeRepository.findAllGroupBySectionC();
    }
}
