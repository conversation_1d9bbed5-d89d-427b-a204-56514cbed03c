package com.yzm.property.materials.service;

import com.yzm.framework.base.IBaseService;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.materials.entity.IssueTempDtl;
import com.yzm.property.materials.entity.MaterialsListEntity;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface IssueTempDtlService extends IBaseService<IssueTempDtl,Long>{

    Map showAllIssueTempDtl(String keyword,  PageBean pageBean);

    List<IssueTempDtl> findAllByKitNoAndGroupByMaterialNo(String kitNo);

    ResponseData deliveryStorage(IssueTempDtl issueTempDtl);

    ResponseData synchronizationData();

    List<IssueTempDtl> findAllGroupByLineC();

    List<IssueTempDtl> findAllGroupBySectionC();

    List<IssueTempDtl> findAllGroupLineCBySectionC(String sectionC);

    boolean allocationByIds(Long[] ids, Long salesmanId);

    IssueTempDtl findByKitNo(String kitNo);

    ResponseData synchronizationDataTube();


    ResponseData synchronizationData3();

    boolean lineCheckByIds(Long[] ids, String lineC);

    boolean issueDateCheckByIds(Long[] ids, Date plannedIssueDt);
}
