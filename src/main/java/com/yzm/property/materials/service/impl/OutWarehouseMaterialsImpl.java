package com.yzm.property.materials.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.yzm.common.utils.OrderUtils;
import com.yzm.common.utils.ShiroUtils;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.basis.repository.DepartmentRepository;
import com.yzm.property.materials.entity.*;
import com.yzm.property.materials.repository.*;
import com.yzm.property.materials.service.OutWarehouseService;
import com.yzm.property.materials.utils.MaterialsUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class OutWarehouseMaterialsImpl extends BaseService<OutWarehouseMaterialsEntity, Long> implements OutWarehouseService {
    @Autowired
    private MaterialsInfoRepository materialsInfoRepository;
    @Autowired
    private OutWarehouseMaterialsRepository outWarehouseRepository;
    @Autowired
    private OutWarehouseItemMaterialsRepository outWarehouseItemRepository;

    @Autowired
    private MaterialsRepertoryRepository materialsRepertoryRepository;
    @Autowired
    private MaterialsStandingBookRepository consumableStandingBookRepository;
    @Autowired
    private MaterialsListRepository consumableListRepository;

    @Autowired
    private DepartmentRepository departmentRepository;

    @Autowired

    @Override
    public BaseRepository<OutWarehouseMaterialsEntity, Long> getRepository() {
        return outWarehouseRepository;
    }

    @Override
    @Transactional
    public OutWarehouseMaterialsEntity save(OutWarehouseMaterialsEntity outWarehouseEntity) {
        outWarehouseEntity = outWarehouseRepository.save(outWarehouseEntity);
        // AssetsLogUtil.createAssetsLog(null,null,inStorage.getId(),inStorage.getOtherId(),inStorage.getAssetsName(),consumableInfo.getAssetsSource());
        return outWarehouseEntity;
    }

    @Override
    public Map showAllOutWarehouseInfo(String keyword, List<String> type, String dept, PageBean pageBean) {
        HashMap<String, Object> map = new HashMap<>();
//        if (keyword == null || "".equals(keyword)) {
//            Page<OutWarehouseMaterialsEntity> result = outWarehouseRepository.findByOrderByCreateTimeDesc(pageBean.getPagable());
//            map.put("list", result.getContent());
//            map.put("totalCount", result.getTotalElements());
//        } else {                                                                                                            //从第几页开始                                 //显示几个
        List<OutWarehouseMaterialsEntity> result = outWarehouseRepository.findByRecordNumberContainsOrderByCreateTimeDesc(keyword, (pageBean.getPage() - 1) * pageBean.getLimit(), pageBean.getLimit(), dept);
        //List<InStorage>          result = inStorageRepository.findByRecordNumberContainsOrderByCreateTimeDesc   (keyword,(pageBean.getPage() - 1 )      *pageBean.getLimit(), pageBean.getLimit());

        map.put("list", result);
        map.put("totalCount", outWarehouseRepository.countGroupedResults(keyword, dept));
//        }
        return map;
    }

    @Override
    public Map showAllOutWarehouseInfoOther(String keyword, List<String> type, PageBean pageBean) {
        HashMap<String, Object> map = new HashMap<>();
//        if (keyword == null || "".equals(keyword)) {
//            Page<OutWarehouseMaterialsEntity> result = outWarehouseRepository.findByOrderByCreateTimeDesc(pageBean.getPagable());
//            map.put("list", result.getContent());
//            map.put("totalCount", result.getTotalElements());
//        } else {                                                                                                            //从第几页开始                                 //显示几个
        List<OutWarehouseMaterialsEntity> result = outWarehouseRepository.findByRecordNumberContainsOrderByCreateTimeDescOther(keyword, (pageBean.getPage() - 1) * pageBean.getLimit(), pageBean.getLimit());
        //List<InStorage>          result = inStorageRepository.findByRecordNumberContainsOrderByCreateTimeDesc   (keyword,(pageBean.getPage() - 1 )      *pageBean.getLimit(), pageBean.getLimit());
        map.put("list", result);
        map.put("totalCount", outWarehouseRepository.countGroupedResultsOther(keyword));
//        }
        return map;
    }

    //    @Override
//    public  List<OutWarehouseMaterialsEntity> showAllOutWarehouseInfoList(String keyword, List<String> type, PageBean pageBean) {
//        List<OutWarehouseMaterialsEntity> result = outWarehouseRepository.findByRecordNumberContainsOrderByCreateTimeDesc(keyword, type, (pageBean.getPage() - 1) * pageBean.getLimit(), pageBean.getLimit());
//        //List<InStorage>          result = inStorageRepository.findByRecordNumberContainsOrderByCreateTimeDesc   (keyword,(pageBean.getPage() - 1 )      *pageBean.getLimit(), pageBean.getLimit());
//        return result;
//    }
    @Override
    public Map<String, String> saveOutWarehouse(OutWarehouseMaterialsEntity outWarehouseEntity) {
        //单据编号
        Map<String, String> map = new HashMap<>();
        String parameterNo = OrderUtils.getCuKuCode();
        outWarehouseEntity.setRecordNumber(parameterNo);//出库编号
        outWarehouseEntity.setOutDate(outWarehouseEntity.getOutDate());//出库时间
        if (StringUtils.isNotEmpty(outWarehouseEntity.getConsumableDepartment())) {
            outWarehouseEntity.setConsumableDepartment(departmentRepository.findById(Long.parseLong(outWarehouseEntity.getConsumableDepartment())).get().getBasisName());
        }
        outWarehouseEntity.setUserName(ShiroUtils.getUserInfo().getName());//出库人
        outWarehouseEntity.setConsumableNum(outWarehouseEntity.getConsumableNum());//出库数量
        outWarehouseRepository.save(outWarehouseEntity);
        BigDecimal sum = new BigDecimal(0);
        //获取前台传来的表格信息
        List<OutWarehouseMaterialsItem> outWarehouseEntityItem = JSONArray.parseArray(outWarehouseEntity.getItemJson(), OutWarehouseMaterialsItem.class);
        boolean code = true;
        for (OutWarehouseMaterialsItem storageItem : outWarehouseEntityItem) {
//            storageItem.setConsumableNum(new BigDecimal(1));
            /*更新库存的当前库存、待入库*/
            //通过出库子表查库存
            MaterialsRepertory consumableRepertory = materialsRepertoryRepository.getOne(storageItem.getId());
            storageItem.setOperationNumber(consumableRepertory.getOperationNumber());
            /*MaterialsRepertory consumableRepertory = consumableRepertoryRepository.getOne(storageItem.getId());*/
//            if(consumableRepertory.getNowRepertory()<storageItem.getMaterialsNum()){
//                code=false;
//                break;
//            }
            /*if(consumableRepertory.getNowRepertory()<storageItem.getMaterialsNum()){
                code =false;
                break;
            }*/
          /*  //通过耗材查库存
            MaterialsRepertory consumableRepertory =  consumableRepertoryRepository.findByMaterialsInfoId(consumableInfo.getId());*/
//            consumableRepertory.setNowRepertory(consumableRepertory.getNowRepertory()-storageItem.getMaterialsNum());//当前库存
//            if(consumableRepertory.getNowRepertory()==0){
            materialsRepertoryRepository.delete(consumableRepertory);
//            }else{
//                consumableRepertoryRepository.saveAndFlush(consumableRepertory);
//            }
            sum.add(storageItem.getConsumableNum());
            //新建入库子表
            OutWarehouseMaterialsItem o = new OutWarehouseMaterialsItem();
            BeanUtils.copyProperties(storageItem, o);

            o.setConsumableNum(storageItem.getConsumableNum());//数量
//            o.setPrice(storageItem.getPrice());//单价
//            o.setAssetsName(storageItem.getAssetsName());//资产名称
            o.setRecordNumber(outWarehouseEntity.getRecordNumber());//记录编号主表
//            o.setAssetsSpecifications(storageItem.getAssetsSpecifications());//规格型号
//            o.setAssetsUnit(consumableStandingBook.getAssetsUnit());//单位
            outWarehouseItemRepository.save(o);
            MaterialsListEntity consumableListEntity = new MaterialsListEntity();
            BeanUtils.copyProperties(o, consumableListEntity);
            consumableListEntity.setId(null);
            consumableListEntity.setOperationName(MaterialsUtil.OPERATION_CHUKU);
            consumableListEntity.setOperationOtherId(o.getRecordNumber());
            consumableListEntity.setOperationId(outWarehouseEntity.getId());
            consumableListEntity.setUserName(outWarehouseEntity.getUserName());
            consumableListEntity.setOutDate(outWarehouseEntity.getCreateTime());
            consumableListEntity.setCreateTime(new Date());
            consumableListRepository.save(consumableListEntity);
        }
        outWarehouseEntity.setConsumableNum(sum);//入库总数

        if (code) {
            if (StringUtils.isNotEmpty(outWarehouseRepository.saveAndFlush(outWarehouseEntity))) {
                map.put("code", "000");
            } else {
                map.put("code", "500");
            }
            ;
        } else {
            map.put("code", "200");
        }
        return map;
    }

    @Override
    @Transactional
    public ResponseData deleteInfo(Long[] ids) {
        for (Long id : ids) {
            OutWarehouseMaterialsEntity one = outWarehouseRepository.getOne(id);
            List<OutWarehouseMaterialsItem> byRecordNumber = outWarehouseItemRepository.findByRecordNumber(one.getRecordNumber());
            for (OutWarehouseMaterialsItem outWarehouseEntityItem : byRecordNumber) {
//                MaterialsRepertory consumableRepertory = new MaterialsRepertory();
//                BeanUtils.copyProperties(outWarehouseEntityItem, consumableRepertory);
//                consumableRepertory.setNowRepertory(outWarehouseEntityItem.getNowRepertory());//当前库存
//                consumableRepertory.setWarehouseInfo(outWarehouseEntityItem.getWarehouseInfo());
//                consumableRepertory.setAreaInfo(outWarehouseEntityItem.getAreaInfo());
////                consumableRepertory.setMaterialsInfoId(outWarehouseEntityItem.getMaterialsNum());
//                consumableRepertory.setNowCount(new BigDecimal(1));
//                consumableRepertory.setAssetsRfid(outWarehouseEntityItem.getAssetsRfid());
//                consumableRepertory.setCreateTime(new Date());
//                consumableRepertory.setId(null);
//                consumableRepertory.setOperationNumber(outWarehouseEntityItem.getOperationNumber());
//                //通过耗材查库存
//                materialsRepertoryRepository.saveAndFlush(consumableRepertory);
                outWarehouseItemRepository.delete(outWarehouseEntityItem);
            }
            outWarehouseRepository.delete(one);
        }
        return ResponseData.success("删除成功！");
    }

    @Override
    public ResponseData audit(Long id, Integer status) {
        OutWarehouseMaterialsEntity inStorage = outWarehouseRepository.getOne(id);
        if (status == 3) {//审核通过
//            List<OutWarehouseMaterialsItem> inStorageItemMaterialsList = outWarehouseItemRepository.findByRecordNumber(inStorage.getRecordNumber());
//            for (OutWarehouseMaterialsItem inStorageItemMaterials : inStorageItemMaterialsList) {
//                MaterialsInfo materialsInfo = materialsInfoRepository.findByAssetsNumber(inStorageItemMaterials.getAssetsNumber());
//                MaterialsRepertory byAssetsCodeOrAssetsRfid = materialsRepertoryRepository.findByAssetsCodeOrAssetsRfid(inStorageItemMaterials.getAssetsCode(), inStorageItemMaterials.getAssetsCode());
//                if (byAssetsCodeOrAssetsRfid == null) {
//                    return ResponseData.error("库存无当前条码信息");
//
//                } else {
//                    BigDecimal subtract = byAssetsCodeOrAssetsRfid.getNowRepertory().subtract(inStorageItemMaterials.getConsumableNum());
//                    if (subtract.compareTo(new BigDecimal(0)) == -1) {
//                        return ResponseData.error("库存不足，无法出库");
//                    }
//                    if (subtract.compareTo(new BigDecimal(0)) == 0) {
//                        materialsRepertoryRepository.delete(byAssetsCodeOrAssetsRfid);
//                    }
//                    byAssetsCodeOrAssetsRfid.setNowRepertory(subtract);
//                    materialsRepertoryRepository.saveAndFlush(byAssetsCodeOrAssetsRfid);
//                }
//                MaterialsListEntity materialsListEntity = new MaterialsListEntity();
//                BeanUtils.copyProperties(materialsInfo, materialsListEntity);
//                materialsListEntity.setCreateTime(new Date());
//                materialsListEntity.setId(null);
//                materialsListEntity.setOperationName(MaterialsUtil.OPERATION_RUKU);
//                materialsListEntity.setOperationOtherId(inStorage.getRecordNumber());
//                materialsListEntity.setReason(inStorage.getOutReson());
//                materialsListEntity.setOperationId(inStorage.getId());
//                materialsListEntity.setAssetsRfid(inStorageItemMaterials.getAssetsRfid());
//                materialsListEntity.setCountInfo(inStorageItemMaterials.getConsumableNum());
//                materialsListEntity.setNowRepertory(inStorageItemMaterials.getConsumableNum());
//                materialsListEntity.setUserName(inStorage.getUserName());
//                materialsListEntity.setOutDate(inStorage.getOutDate());
//                materialsListEntity.setAreaInfo(materialsInfo.getStorageArea());
//                materialsListEntity.setWarehouseInfo(materialsInfo.getStorageWarehouse());
//                materialsListEntity.setBatchNumber(inStorageItemMaterials.getBatchNumber());
//                materialsListEntity.setModeOfTrade(inStorage.getModeOfTrade());
//                consumableListRepository.save(materialsListEntity);
//            }
            inStorage.setStatus(3L);
        } else {//不通过
            inStorage.setStatus(4L);
        }
        inStorage.setAuditor(ShiroUtils.getUserInfo().getName());
        inStorage.setAuditorDate(new Date());
        outWarehouseRepository.saveAndFlush(inStorage);
        return ResponseData.success("审核成功");
    }

    @Override
    public ResponseData addOutStorage(OutWarehouseMaterialsEntity outWarehouseEntity) {
        //单据编号
        String parameterNo = OrderUtils.getQTCuKuCode();
        outWarehouseEntity.setRecordNumber(parameterNo);//入库编号
        outWarehouseEntity.setApplicant(ShiroUtils.getUserInfo().getName());//申请人
        outWarehouseEntity.setStatus(1L);
        outWarehouseEntity.setInStatus(0L);
        outWarehouseEntity.setOutDate(new Date());
        outWarehouseEntity.setUserName(ShiroUtils.getUserInfo().getName());
        //获取前台传来的表格信息
        List<OutWarehouseMaterialsItem> inStorageItem = JSONArray.parseArray(outWarehouseEntity.getItemJson(), OutWarehouseMaterialsItem.class);
        BigDecimal sum = new BigDecimal(0);
        for (OutWarehouseMaterialsItem storageItem : inStorageItem) {
//            MaterialsInfo materialsInfo = materialsInfoRepository.getOne(storageItem.getId());
//            MaterialsRepertory consumableRepertory = new MaterialsRepertory();
//            BeanUtils.copyProperties(materialsInfo, consumableRepertory);
//            consumableRepertory.setNowRepertory(storageItem.getInStorageCount());//当前库存
//            consumableRepertory.setWarehouseInfo(materialsInfo.getStorageWarehouse());
//            consumableRepertory.setAreaInfo(materialsInfo.getStorageArea());
//            consumableRepertory.setAreaInfoId(materialsInfo.getStorageAreaId());
//            consumableRepertory.setMaterialsInfoId(materialsInfo.getId());
//            consumableRepertory.setNowCount(1L);
//            consumableRepertory.setCreateTime(new Date());
//            consumableRepertory.setId(null);
//            consumableRepertory.setOperationNumber(inStorage.getRecordNumber());
//            consumableRepertory.setAssetsCode(storageItem.getAssetsCode());
//            consumableRepertory.setBatchNumber(storageItem.getBatchNumber());
//            consumableRepertory.setModeOfTrade(inStorage.getModeOfTrade());
//            //通过耗材查库存
//            materialsRepertoryRepository.saveAndFlush(consumableRepertory);
            sum = sum.add(storageItem.getConsumableNum());
            //新建入库子表
            OutWarehouseMaterialsItem s = new OutWarehouseMaterialsItem();
            BeanUtils.copyProperties(storageItem, s);
            s.setId(null);
            s.setCreateTime(new Date());
            s.setAssetsCode(storageItem.getAssetsCode());
            s.setAssetsRfid(storageItem.getAssetsRfid());
            s.setConsumableNum(storageItem.getConsumableNum());//数量
            s.setRecordNumber(outWarehouseEntity.getRecordNumber());//记录编号主表
            s.setWarehouseInfo(storageItem.getWarehouseInfo());
            s.setAreaInfo(storageItem.getAreaInfo());
            s.setAssetsId(storageItem.getId());//耗材ID
            s.setBatchNumber(storageItem.getBatchNumber());
            s.setQuantityDelivered(new BigDecimal(0));
            s.setModeOfTrade(outWarehouseEntity.getModeOfTrade());
            s.setSucceed(0L);
            outWarehouseItemRepository.save(s);
//            MaterialsListEntity materialsListEntity = new MaterialsListEntity();
//            BeanUtils.copyProperties(materialsInfo, materialsListEntity);
//            materialsListEntity.setId(null);
//            materialsListEntity.setOperationName(MaterialsUtil.OPERATION_RUKU);
//            materialsListEntity.setOperationOtherId(inStorage.getRecordNumber());
//            materialsListEntity.setReason(inStorage.getInStorageReson());
//            materialsListEntity.setOperationId(inStorage.getId());
//            materialsListEntity.setAssetsRfid(storageItem.getAssetsRfid());
//            materialsListEntity.setCountInfo(storageItem.getInStorageCount());
//            materialsListEntity.setNowRepertory(storageItem.getInStorageCount());
//            materialsListEntity.setUserName(inStorage.getInStoragePeople());
//            materialsListEntity.setOutDate(inStorage.getInStorageDate());
//            materialsListEntity.setAreaInfo(materialsInfo.getStorageArea());
//            materialsListEntity.setWarehouseInfo(materialsInfo.getStorageWarehouse());
//            materialsListEntity.setBatchNumber(storageItem.getBatchNumber());
//            materialsListEntity.setModeOfTrade(inStorage.getModeOfTrade());
//            consumableListRepository.save(materialsListEntity);
        }
        outWarehouseEntity.setConsumableNum(sum);//入库总数
        return ResponseData.success(outWarehouseRepository.saveAndFlush(outWarehouseEntity));
    }

    @Override
    public boolean submitByIds(Long[] ids) {
        Boolean code = true;
        for (Long id : ids) {
            OutWarehouseMaterialsEntity outWarehouseMaterialsEntity = this.getById(id);
            outWarehouseMaterialsEntity.setStatus(2L);//办理状态-审核中
            outWarehouseRepository.save(outWarehouseMaterialsEntity);
        }
        return code;
    }

    @Override
    @Transactional
    public ResponseData deliveryStorageSave(OutWarehouseMaterialsEntity one) {
        OutWarehouseMaterialsEntity outWarehouseMaterialsEntity = outWarehouseRepository.getOne(one.getId());
        outWarehouseMaterialsEntity.setUserName(ShiroUtils.getUserInfo().getName());
        //单据编号
        Map<String, String> map = new HashMap<>();
        BigDecimal sum = new BigDecimal(0);
        //获取前台传来的表格信息
        boolean code = true;

        List<OutWarehouseMaterialsItem> list = JSONArray.parseArray(one.getItemJson(), OutWarehouseMaterialsItem.class);
        for (OutWarehouseMaterialsItem item : list) {
            if (StringUtils.isNotEmpty(item.getSucceed())) {
                if (item.getSucceed() == 1L) {
                    continue;
                }
            }

//
//            BigDecimal decimal = new BigDecimal(1);
//            MaterialsInfo byAssetsNumber = materialsInfoRepository.findByAssetsNumber(item.getAssetsNumber().trim());
//            if (StringUtils.isNotEmpty(byAssetsNumber)) {
//                if (StringUtils.isNotEmpty(byAssetsNumber.getMinLot())) {
//                    decimal = byAssetsNumber.getMinLot();
//                }
//            }
//            item.setConsumableNum(item.getConsumableNum().multiply(decimal));


            if (item.getReplaceInfo().equals("是")) {
                OutWarehouseMaterialsItem issueTempDtlItem = outWarehouseItemRepository.getOne(item.getReplaceId());
                if (issueTempDtlItem.getSucceed() == 1L) {
                    continue;
                }
                issueTempDtlItem.setQuantityDelivered(issueTempDtlItem.getQuantityDelivered().add(item.getConsumableNum()));

                if (issueTempDtlItem.getQuantityDelivered().compareTo(issueTempDtlItem.getConsumableNum()) == 0) {
                    issueTempDtlItem.setSucceed(1L);
                    issueTempDtlItem.setSucceedDate(new Date());
                }
                outWarehouseItemRepository.saveAndFlush(issueTempDtlItem);
            } else {
                List<OutWarehouseMaterialsItem> issueTempDtlItemList = outWarehouseItemRepository.findByRecordNumberAndAssetsNumber(outWarehouseMaterialsEntity.getRecordNumber(), item.getAssetsNumber());
                for (OutWarehouseMaterialsItem outWarehouseMaterialsItem : issueTempDtlItemList) {
                    outWarehouseMaterialsItem.setQuantityDelivered(outWarehouseMaterialsItem.getQuantityDelivered().add(item.getConsumableNum()));
                    int compareTo = outWarehouseMaterialsItem.getQuantityDelivered().compareTo(outWarehouseMaterialsItem.getConsumableNum());
                    if (compareTo == 0) {
                        outWarehouseMaterialsItem.setSucceed(1L);
                        outWarehouseMaterialsItem.setSucceedDate(new Date());
                    }
                    if (compareTo == 1) {
                        throw new RuntimeException(outWarehouseMaterialsItem.getAssetsNumber()+"-超出需要出库的数量,请刷新后重新扫描");
                    }
                    outWarehouseItemRepository.saveAndFlush(outWarehouseMaterialsItem);
                }

            }


//通过出库子表查库存
//            MaterialsRepertory consumableRepertory = materialsRepertoryRepository.getOne(item.getId());
//            storageItem.setOperationNumber(consumableRepertory.getOperationNumber());
            /*MaterialsRepertory consumableRepertory = consumableRepertoryRepository.getOne(storageItem.getId());*/
//            if(consumableRepertory.getNowRepertory()<storageItem.getMaterialsNum()){
//                code=false;
//                break;
//            }
            /*if(consumableRepertory.getNowRepertory()<storageItem.getMaterialsNum()){
                code =false;
                break;
            }*/

            //通过耗材查库存
            MaterialsRepertory materialsRepertory = materialsRepertoryRepository.findByAssetsCodeOrAssetsRfid(item.getAssetsCode(), item.getAssetsCode());
            if (materialsRepertory == null) {
                materialsRepertory = materialsRepertoryRepository.findByAssetsNumberAndAssetsCodeIsNull(item.getAssetsNumber());
            }
            BigDecimal subtract = new BigDecimal(0);
            try {
                subtract = materialsRepertory.getNowRepertory().subtract(item.getConsumableNum());
                if (subtract.compareTo(new BigDecimal(0)) == -1) {
                    throw new RuntimeException(materialsRepertory.getAssetsNumber() + "-库存不足无法出库");
                }
            } catch (NullPointerException e) {
                throw new RuntimeException(item.getAssetsNumber() + "-库存不足无法出库");
            }
//            MaterialsRepertory materialsRepertory = materialsRepertoryRepository.findByMaterialsInfoId(item.getId());
            materialsRepertory.setNowRepertory(subtract);//当前库存
            if (materialsRepertory.getNowRepertory().compareTo(new BigDecimal(0)) == 0) {
                materialsRepertoryRepository.delete(materialsRepertory);
            } else {
                materialsRepertoryRepository.saveAndFlush(materialsRepertory);
            }
            sum = sum.add(item.getConsumableNum());
            //新建入库子表
            MaterialsListEntity consumableListEntity = new MaterialsListEntity();
            BeanUtils.copyProperties(item, consumableListEntity);
            consumableListEntity.setNowRepertory(item.getConsumableNum());
            consumableListEntity.setId(null);
            consumableListEntity.setOperationName(MaterialsUtil.OPERATION_CHUKU);
            consumableListEntity.setReason(outWarehouseMaterialsEntity.getOutReson());
            consumableListEntity.setOperationOtherId(outWarehouseMaterialsEntity.getRecordNumber());
            consumableListEntity.setOperationId(outWarehouseMaterialsEntity.getId());
            consumableListEntity.setUserName(outWarehouseMaterialsEntity.getUserName());
            consumableListEntity.setOutDate(outWarehouseMaterialsEntity.getCreateTime());
            consumableListEntity.setCreateTime(new Date());
            consumableListRepository.save(consumableListEntity);
        }

//        outWarehouseMaterialsEntity.setConsumableNum(sum);//出库总数

        boolean status = true;
        List<OutWarehouseMaterialsItem> byRecordNumber = outWarehouseItemRepository.findByRecordNumber(outWarehouseMaterialsEntity.getRecordNumber());
        for (OutWarehouseMaterialsItem inStorageItemMaterials : byRecordNumber) {
            if (StringUtils.isEmpty(inStorageItemMaterials.getSucceed())) {
                status = false;
            }
            if (StringUtils.isNotEmpty(inStorageItemMaterials.getSucceed()) && inStorageItemMaterials.getSucceed() != 1L) {
                status = false;
            }
        }
        if (status) {
            outWarehouseMaterialsEntity.setInStatus(2L);
        } else {
            outWarehouseMaterialsEntity.setInStatus(1L);
        }

        outWarehouseRepository.saveAndFlush(outWarehouseMaterialsEntity);
        return ResponseData.success("发料成功");
    }

    @Override
    public OutWarehouseMaterialsEntity findByKitNo(String key) {
        return outWarehouseRepository.findByKitNo(key);
    }

    @Override
    public OutWarehouseMaterialsEntity findByRecordNumber(String recordNumber) {
        return outWarehouseRepository.findByRecordNumber(recordNumber);
    }
}
