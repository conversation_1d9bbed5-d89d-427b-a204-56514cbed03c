package com.yzm.property.materials.service;

import com.yzm.framework.base.CriteriaBean;
import com.yzm.framework.base.IBaseService;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.materials.criteria.MaterialsBomCriteria;
import com.yzm.property.materials.entity.MaterialsInfo;
import com.yzm.property.materials.entity.MaterialsInfoBom;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:47
 */
public interface MaterialsInfoBomService extends IBaseService<MaterialsInfoBom,Long> {


    ResponseData addMaterialsInfoBom(MaterialsInfo materialsInfoBomInfo);

    ResponseData updateMaterialsInfo(MaterialsInfo materialsInfoBomInfo);

    List<MaterialsInfoBom> findByParentId(Long id);

    List<MaterialsInfoBom> findByAssetsNumber(String assetsNumber);

    boolean handleDataSave(MaterialsInfoBom dataJson);


    Map<String, Object> findAllByPageGroup(MaterialsBomCriteria bean, Pageable page);

    List<MaterialsInfoBom> findByContainerNumber(String containerNumber);

    List<MaterialsInfoBom> findGroupByNumber();

    List<MaterialsInfoBom> findByInvoiceNumberAndAssetsNumber(String invoiceNumber, String assetsNumber);

   MaterialsInfoBom findByInvoiceNumberAndAssetsNumberAndMaterialLotNo(String invoiceNumber, String assetsNumber, String materialLotNo);

    List<MaterialsInfoBom> findByContainerNumberAndInvoiceNumber(String containerNumber, String invoiceNumber);
}
