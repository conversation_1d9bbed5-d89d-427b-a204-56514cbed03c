package com.yzm.property.materials.service.impl;

import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yzm.common.utils.OrderUtils;
import com.yzm.common.utils.ShiroUtils;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.framework.exception.RxcException;
import com.yzm.property.materials.entity.*;
import com.yzm.property.materials.repository.*;
import com.yzm.property.materials.service.IssueTempDtlService;
import com.yzm.property.materials.service.IssueTempDtlTubeService;
import com.yzm.property.materials.service.MaterialsInfoBomService;
import com.yzm.property.materials.service.MaterialsInfoService;
import com.yzm.property.materials.utils.MaterialsUtil;
import com.yzm.property.system.entity.SysUser;
import com.yzm.property.system.repository.SysUserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Date;

@Service
public class IssueTempDtlImpl extends BaseService<IssueTempDtl, Long> implements IssueTempDtlService {
    private static Logger log = LoggerFactory.getLogger(IssueTempDtlImpl.class);

    @Value("${sqlserver.url}")
    private String URL;
    @Value("${sqlserver.username}")
    private String USER;
    @Value("${sqlserver.password}")
    private String PASSWORD;
    @Value("${sqlserver.storeIssueTable}")
    private String storeIssueTable;
    @Autowired
    private IssueTempDtlRepository issueTempDtlRepository;
    @Autowired
    private IssueTempDtlItemRepository issueTempDtlItemRepository;
    @Autowired
    private OutWarehouseMaterialsRepository outWarehouseRepository;

    @Autowired
    private OutWarehouseItemMaterialsRepository outWarehouseItemRepository;
    @Autowired
    private MaterialsRepertoryRepository materialsRepertoryRepository;

    @Autowired
    private MaterialsListRepository consumableListRepository;
    @Autowired
    private MaterialsInfoService materialsInfoService;
    @Autowired
    private MaterialsInfoRepository materialsInfoRepository;
    @Autowired
    private MaterialsInfoBomService materialsInfoBomService;
    @Autowired
    private MaterialsInfoBomRepeatRepository materialsInfoBomRepeatRepository;

    @Autowired
    private SysUserRepository sysUserRepository;
    @Autowired
    private IssueTempDtlTubeRepository issueTempDtlTubeRepository;
    @Autowired
    private DelinquentMaterialRepository delinquentMaterialRepository;
    @Autowired
    private ProductionPlanBomRepository productionPlanBomRepository;

    public BaseRepository<IssueTempDtl, Long> getRepository() {
        return issueTempDtlRepository;
    }

    @Override
    public Map showAllIssueTempDtl(String keyword, PageBean pageBean) {
        HashMap<String, Object> map = new HashMap<>();
//        if (keyword == null || "".equals(keyword)) {
//            Page<InStorageMaterials> result = inStorageRepository.findByOrderByCreateTimeDesc(pageBean.getPagable());
//            map.put("list", result.getContent());
//            map.put("totalCount", result.getTotalElements());
//        } else {


        Page<IssueTempDtl> byProductNoWithPagination = issueTempDtlRepository.findByProductNoWithPagination(keyword, pageBean.getPagable());
        map.put("list", byProductNoWithPagination.getContent());
        map.put("totalCount", byProductNoWithPagination.getTotalElements());
//        List<IssueTempDtl> result = issueTempDtlRepository.findGroupPage(keyword, (pageBean.getPage() - 1) * pageBean.getLimit(), pageBean.getLimit());
//        map.put("list", result);
//        map.put("totalCount", issueTempDtlRepository.findGroupPageCount(keyword));
//        }


        return map;
    }

    @Override
    public List<IssueTempDtl> findAllByKitNoAndGroupByMaterialNo(String kitNo) {
        return issueTempDtlRepository.findAllByKitNoAndGroupByMaterialNo(kitNo);
    }

    @Override
    @Transactional
    public ResponseData deliveryStorage(IssueTempDtl issueTempDtl) {
        IssueTempDtl issueTempDtlOne = issueTempDtlRepository.getOne(issueTempDtl.getId());
        //单据编号
        Map<String, String> map = new HashMap<>();
        String parameterNo = OrderUtils.getCuKuCode();
        OutWarehouseMaterialsEntity outWarehouseEntity = new OutWarehouseMaterialsEntity();

        outWarehouseEntity.setOutDate(new Date());
        outWarehouseEntity.setDepartmentCode(issueTempDtlOne.getSectionC());
        outWarehouseEntity.setLineCode(issueTempDtlOne.getLineC());
        outWarehouseEntity.setShift(issueTempDtlOne.getShift());
        outWarehouseEntity.setKitNo(issueTempDtlOne.getKitNo());
        outWarehouseEntity.setLotNo(issueTempDtlOne.getLotNo());
        outWarehouseEntity.setPlanIssueQty(issueTempDtlOne.getPlanIssueQty());
        if (StringUtils.isNotEmpty(issueTempDtlOne.getProductNo())) {
            outWarehouseEntity.setProductNo(issueTempDtlOne.getProductNo());
        }

        outWarehouseEntity.setStatus(2L);//审核通过
        outWarehouseEntity.setOutReson("3");//计划出库
        outWarehouseEntity.setRecordNumber(parameterNo);//出库编号
        outWarehouseEntity.setOutDate(outWarehouseEntity.getOutDate());//出库时间
//        if(StringUtils.isNotEmpty(outWarehouseEntity.getConsumableDepartment())){
//            outWarehouseEntity.setConsumableDepartment(departmentRepository.findById(Long.parseLong(outWarehouseEntity.getConsumableDepartment())).get().getBasisName());
//        }
        outWarehouseEntity.setUserName(ShiroUtils.getUserInfo().getName());//出库人
        outWarehouseEntity.setConsumableNum(outWarehouseEntity.getConsumableNum());//出库数量
        outWarehouseRepository.save(outWarehouseEntity);
        BigDecimal sum = new BigDecimal(0);
        //获取前台传来的表格信息
        boolean code = true;

        List<OutWarehouseMaterialsItem> list = JSONArray.parseArray(issueTempDtl.getItemJson(), OutWarehouseMaterialsItem.class);
        for (OutWarehouseMaterialsItem item : list) {
            if ("是".equals(item.getReplaceInfo())) {


                int updated = issueTempDtlItemRepository.atomicAddQuantity(
                        item.getReplaceId(),
                        item.getConsumableNum()
                );
                if (updated == 0) {
                    throw new ExcelAnalysisException("当前材料无法出库（可能已出库完成或超量）");
                }
//                IssueTempDtlItem issueTempDtlItem = issueTempDtlItemRepository.getOne(item.getReplaceId());
//                if (StringUtils.isNotEmpty(issueTempDtlItem.getSucceed()) && issueTempDtlItem.getSucceed() == 1L) {
//                    throw new ExcelAnalysisException("当前材料已经出库完成，无法重复出库,请刷新后再试");
//                }
//                issueTempDtlItem.setQuantityDelivered(issueTempDtlItem.getQuantityDelivered().add(item.getConsumableNum()));
//
//                if (issueTempDtlItem.getQuantityDelivered().compareTo(issueTempDtlItem.getIssueQty()) == 0) {
//                    issueTempDtlItem.setSucceed(1L);
//                }
//                issueTempDtlItemRepository.save(issueTempDtlItem);
            } else {
                IssueTempDtlItem issueTempDtlItem = issueTempDtlItemRepository.findByPrimaryIdAndMaterialNo(issueTempDtl.getId(), item.getAssetsNumber());
                int updated = issueTempDtlItemRepository.atomicAddQuantity(
                        issueTempDtlItem.getId(),
                        item.getConsumableNum()
                );
                if (updated == 0) {
                    throw new ExcelAnalysisException("当前材料无法出库（可能已出库完成或超量）");
                }

//
//                if (StringUtils.isNotEmpty(issueTempDtlItem.getSucceed()) && issueTempDtlItem.getSucceed() == 1L) {
//                    throw new ExcelAnalysisException("当前材料已经出库完成，无法重复出库,请刷新后再试");
//                }
//                issueTempDtlItem.setQuantityDelivered(issueTempDtlItem.getQuantityDelivered().add(item.getConsumableNum()));
//                if (issueTempDtlItem.getQuantityDelivered().compareTo(issueTempDtlItem.getIssueQty()) == 0) {
//                    issueTempDtlItem.setSucceed(1L);
//                } else if (issueTempDtlItem.getQuantityDelivered().compareTo(issueTempDtlItem.getIssueQty()) == 1) {
//                    throw new ExcelAnalysisException("当前材料数量超出计划，无法重复出库,请刷新页面后重新尝试");
//                }
//                issueTempDtlItemRepository.save(issueTempDtlItem);
            }


//通过出库子表查库存
//            MaterialsRepertory consumableRepertory = materialsRepertoryRepository.getOne(item.getId());
//            storageItem.setOperationNumber(consumableRepertory.getOperationNumber());
            /*MaterialsRepertory consumableRepertory = consumableRepertoryRepository.getOne(storageItem.getId());*/
//            if(consumableRepertory.getNowRepertory()<storageItem.getMaterialsNum()){
//                code=false;
//                break;
//            }
            /*if(consumableRepertory.getNowRepertory()<storageItem.getMaterialsNum()){
                code =false;
                break;
            }*/

            MaterialsRepertory materialsRepertory = null;

            if (StringUtils.isNotEmpty(item.getAssetsCode())) {
                //通过耗材查库存
                materialsRepertory = materialsRepertoryRepository.findByAssetsCodeOrAssetsRfid(item.getAssetsCode(), item.getAssetsCode());
                if (materialsRepertory == null) {
                    materialsRepertory = materialsRepertoryRepository.findByAssetsNumberAndAssetsCodeIsNull(item.getAssetsNumber());
                }

//            MaterialsRepertory materialsRepertory = materialsRepertoryRepository.findByMaterialsInfoId(item.getId());


                int stockUpdated = materialsRepertoryRepository.atomicReduce(
                        materialsRepertory.getId(),
                        item.getConsumableNum()
                );
                if (stockUpdated == 0) {
                    throw new ExcelAnalysisException("库存不足，无法出库");
                } else {
                    materialsRepertoryRepository.deleteIfZero(materialsRepertory.getId());
                }

//                materialsRepertory.setNowRepertory(materialsRepertory.getNowRepertory().subtract(item.getConsumableNum()));//当前库存
//                if (materialsRepertory.getNowRepertory().compareTo(new BigDecimal(0)) == 0) {
//                    materialsRepertoryRepository.delete(materialsRepertory);
//                } else if (materialsRepertory.getNowRepertory().compareTo(new BigDecimal(0)) == -1) {
//                    throw new ExcelAnalysisException("库存不足无法出库");
//                } else {
//                    materialsRepertoryRepository.save(materialsRepertory);
//                }


                sum = sum.add(item.getConsumableNum());
                //新建入库子表
                item.setRecordNumber(outWarehouseEntity.getRecordNumber());//记录编号主表
//            o.setAssetsSpecifications(storageItem.getAssetsSpecifications());//规格型号
//            o.setAssetsUnit(consumableStandingBook.getAssetsUnit());//单位
                item.setKitNo(issueTempDtlOne.getKitNo());
                item.setLotNo(issueTempDtlOne.getLotNo());
                item.setProductNo(issueTempDtlOne.getProductNo());
                item.setCreateTime(new Date());
                item.setUpdateTime(null);
                item.setId(null);
                outWarehouseItemRepository.save(item);
                MaterialsListEntity consumableListEntity = new MaterialsListEntity();
                BeanUtils.copyProperties(item, consumableListEntity);
                consumableListEntity.setNowRepertory(item.getConsumableNum());
                consumableListEntity.setId(null);
                consumableListEntity.setOperationName(MaterialsUtil.OPERATION_CHUKU);
                consumableListEntity.setReason(outWarehouseEntity.getOutReson());
                consumableListEntity.setOperationOtherId(item.getRecordNumber());
                consumableListEntity.setOperationId(outWarehouseEntity.getId());
                consumableListEntity.setUserName(outWarehouseEntity.getUserName());
                consumableListEntity.setOutDate(outWarehouseEntity.getCreateTime());
                consumableListEntity.setInvoiceNumber(materialsRepertory.getInvoiceNumber());
                consumableListEntity.setDateArrival(materialsRepertory.getDateArrival());
                consumableListEntity.setModeOfTrade(materialsRepertory.getModeOfTrade());
                consumableListEntity.setDepartmentCode(materialsRepertory.getDepartmentCode());
                consumableListEntity.setCreateTime(new Date());
                consumableListRepository.save(consumableListEntity);

                try {
                    String assetsNumber = consumableListEntity.getAssetsNumber();

                    String lotNo = issueTempDtlOne.getLotNo();

                    List<ProductionPlanBom> list1 = productionPlanBomRepository.findByBatchNoAndMaterialNo(lotNo, assetsNumber);
                    for (ProductionPlanBom productionPlanBom : list1) {
                        BigDecimal bomQty = productionPlanBom.getBomQty();
                        BigDecimal consumableNum = item.getConsumableNum();
                        BigDecimal subtract = bomQty.subtract(consumableNum);

                        if (subtract.compareTo(new BigDecimal(0)) != 1) {
                            productionPlanBomRepository.delete(productionPlanBom);
                        } else {
                            productionPlanBom.setBomQty(subtract);
                            productionPlanBomRepository.save(productionPlanBom);
                        }
                    }
                } catch (Exception e) {
                    log.error("生产计划扣减失败" + consumableListEntity.getAssetsNumber());
                }


            }
        }

        outWarehouseEntity.setConsumableNum(sum);//出库总数
//
//        List<IssueTempDtlItem> allOrderByPrimaryId = issueTempDtlItemRepository.findAllOrderByPrimaryId(issueTempDtlOne.getId());
//        for (IssueTempDtlItem issueTempDtlItem : allOrderByPrimaryId) {
//            issueTempDtlItem.setSucceed(1L);
//            issueTempDtlItemRepository.save(issueTempDtlItem);
//        }

        if (code) {
            if (StringUtils.isNotEmpty(outWarehouseRepository.save(outWarehouseEntity))) {
                map.put("code", "000");
            } else {
                map.put("code", "500");
            }
        } else {
            map.put("code", "200");
        }
        int count = issueTempDtlItemRepository.findByPrimaryIdAndCountComparison(issueTempDtlOne.getId());
        if (count == 0) {
            issueTempDtlOne.setStatus(2L);
        } else {
            issueTempDtlOne.setStatus(1L);
        }
        issueTempDtlRepository.save(issueTempDtlOne);


        return ResponseData.success("发料成功");
    }

    public List<IssueTempDtlTube> getIssueTempDtlTube(String query) throws SQLException {
        List<IssueTempDtlTube> list = new ArrayList<>();
        Connection conn = null;
        log.info("getIssueTempDtlTube==============" + URL + "------------" + USER + "--------" + PASSWORD);

        try {
            conn = DriverManager.getConnection(URL, USER, PASSWORD);
            if (conn != null) {
                log.info("Connection established successfully.");
            } else {
                log.error("Failed to establish connection.");
            }

            try (PreparedStatement stmt = conn.prepareStatement(query);
                 ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    IssueTempDtlTube item = new IssueTempDtlTube();
                    item.setSectCd(rs.getString("sect_cd"));           // 部门代码
                    item.setLineCd(rs.getString("line_cd"));           // 生成线代码
                    item.setPlanIssDate(rs.getTimestamp("plan_iss_date")); // 计划发料日
                    item.setProdNo(rs.getString("prod_no"));           // 品番
                    item.setCusdeschCd1(rs.getString("cusdesch_cd1"));   // 客户设变
                    item.setCusdeschCd2(rs.getString("cusdesch_cd2"));   // 指定仕样
                    item.setIntdeschCd(rs.getString("intdesch_cd"));     // 社内设变
                    item.setPlanIssQty(rs.getBigDecimal("plan_iss_qty"));     // 计划数量
                    item.setMatlNo(rs.getString("matl_no"));           // 物料号码
                    item.setBomQty(rs.getBigDecimal("bom_qty"));       // BOM数量
                    item.setTotQty(rs.getBigDecimal("tot_qty"));             // 总数量
                    item.setMatlNm(rs.getString("matl_nm"));           // 物料名称
                    item.setCutLen(rs.getBigDecimal("cut_len"));             // 切断长
                    item.setCutingQty(rs.getBigDecimal("cuting_qty"));       // 切断数量
                    item.setPlanIssDate1(rs.getTimestamp("plan_iss_date1")); // 计划日期
                    item.setWorkOrdNo(rs.getString("work_ord_no"));     // 生产批号
                    list.add(item);
                }
            }

        } catch (SQLException e) {
            log.error("Error while executing query: {}", e.getMessage());
        } finally {
            if (conn != null) {
                try {
                    conn.close();
                    log.info("Connection closed successfully.");
                } catch (SQLException e) {
                    log.error("Error while closing connection: {}", e.getMessage());
                }
            }
        }

        return list;
    }

    public List<IssueTempDtl> getIssueTempDtlList(String query) throws SQLException {
        List<IssueTempDtl> list = new ArrayList<>();
        Connection conn = null;
        log.info(URL + "------------" + USER + "--------" + PASSWORD);

        try {
            conn = DriverManager.getConnection(URL, USER, PASSWORD);
            if (conn != null) {
                log.info("Connection established successfully.");
            } else {
                log.error("Failed to establish connection.");
            }

            try (PreparedStatement stmt = conn.prepareStatement(query);
                 ResultSet rs = stmt.executeQuery()) {

                while (rs.next()) {
                    IssueTempDtl item = new IssueTempDtl();
                    item.setSectionC(rs.getString("section_c"));
                    item.setLineC(rs.getString("line_c"));
                    item.setPlannedIssueDt(rs.getTimestamp("planned_dt"));
//                    item.setShift(rs.getInt("shift"));
                    item.setKitNo(rs.getString("kit_no"));
                    item.setLotNo(rs.getString("lot_no"));
                    item.setProductNo(rs.getString("product_no"));
//                    item.setCusdeschC1(rs.getString("cusdesch_c1"));
//                    item.setCusdeschC2(rs.getString("cusdesch_c2"));
//                    item.setIntdeschC(rs.getString("intdesch_c"));
                    item.setPlanIssueQty(rs.getBigDecimal("plan_issue_qty"));
                    item.setIssueQty(rs.getBigDecimal("issued_qty"));
                    item.setMaterialNo(rs.getString("material_no"));
//                    item.setBomQty(rs.getBigDecimal("bom_qty"));
//                    item.setBomUnit(rs.getString("bom_unit"));
                    item.setLocationC(rs.getString("location_c"));
                    item.setMaterialNm(rs.getString("material_nm"));
                    item.setIssueUnit(rs.getString("issue_unit"));
                    item.setIssueMinLot(rs.getInt("issue_min_lot"));
                    item.setUnitConfacIssue(rs.getInt("unit_confac_issue"));
                    item.setReqdQtyBom(rs.getBigDecimal("reqd_qty_bom"));
//                    item.setReqdQtyIssue(rs.getBigDecimal("reqd_qty_issue"));
                    item.setType(rs.getString("type"));
                    item.setMatlPrcsCd(rs.getString("line_c"));//楼层数据
                    // item.setAssyC(rs.getString("assy_c"));
                    String s = JSONObject.toJSONString(item);
//                    log.info("-----------发料单据信息---------------" + s);
                    list.add(item);
                }
            }

        } catch (SQLException e) {
            log.error("Error while executing query: {}", e.getMessage());
        } finally {
            if (conn != null) {
                try {
                    conn.close();
                    log.info("Connection closed successfully.");
                } catch (SQLException e) {
                    log.error("Error while closing connection: {}", e.getMessage());
                }
            }
        }

        return list;
    }

    public void data2() {
        Map<String, MaterialsInfo> materialsInfoHashMap = new HashMap<>();


        List<MaterialsInfo> materialsInfoList = materialsInfoRepository.findAll();
        for (MaterialsInfo materialsInfo : materialsInfoList) {

            if (!materialsInfoHashMap.containsKey(materialsInfo.getAssetsNumber())) {
                materialsInfoHashMap.put(materialsInfo.getAssetsNumber(), materialsInfo);
            }
        }

        String query = "select * from b3e_bom_temp_tube WHERE plan_iss_date BETWEEN DATEADD(DAY, -20, GETDATE()) AND DATEADD(DAY, 20, GETDATE())"; // 修改为你的查询语句
        List<IssueTempDtlTube> results = null;
        try {
            results = getIssueTempDtlTube(query);
        } catch (SQLException e) {
            e.printStackTrace();
        }
        log.info("==========查询到单据胶管数据==================" + results.size());
        Map<String, List<IssueTempDtl>> map = new HashMap<>();

        for (IssueTempDtlTube result : results) {
            IssueTempDtlTube issueTempDtlTube = issueTempDtlTubeRepository.findByProdNoAndWorkOrdNoAndMatlNoAndPlanIssDate(result.getProdNo(), result.getWorkOrdNo(), result.getMatlNo(), result.getPlanIssDate());
            if (issueTempDtlTube == null) {
                result.setQuantityDelivered(new BigDecimal(0));
                result.setStatus(0L);
                issueTempDtlTubeRepository.save(result);
            }
            // 获取 kit_no
//            String kitNo = result.getPlanIssDate();  // 假设 getKitNo() 是 IssueTempDtl 类中的方法
//
//            result.setLotNo(result.getLotNo().trim());
//            result.setProductNo(result.getProductNo().trim());
//            result.setMaterialNo(result.getMaterialNo().trim());
//            result.setMaterialNm(result.getMaterialNm().trim());
//
//            // 如果 map 中已经存在该 kit_no 的条目
//            if (!map.containsKey(kitNo)) {
//                map.put(kitNo, new ArrayList<>());
//            }
//
//            // 将结果添加到对应的列表中
//            map.get(kitNo).add(result);
        }

// 打印结果
//        for (Map.Entry<String, List<IssueTempDtl>> entry : map.entrySet()) {
//
//            IssueTempDtl issueTempDtlFind = issueTempDtlRepository.findByKitNo(entry.getKey());
//            if (issueTempDtlFind != null) {
//                continue;
//            }
//
//            IssueTempDtl issueTempDtl = entry.getValue().get(0);
//            issueTempDtl.setStatus(0L);
//            IssueTempDtl save = issueTempDtlRepository.save(issueTempDtl);
//            Map<String, IssueTempDtlItem> mergedMap = new HashMap<>();
//
//            System.out.println("Kit No: " + entry.getKey());
//            for (IssueTempDtl detail : entry.getValue()) {
//                String materialNo = detail.getMaterialNo();
//
//                if (!mergedMap.containsKey(materialNo)) {
//                    IssueTempDtlItem item = new IssueTempDtlItem();
//                    BeanUtils.copyProperties(detail, item);
//                    item.setStorageWarehouse("");
//                    item.setStorageArea(detail.getLocationC());
//                    item.setId(null);
//                    item.setPrimaryId(save.getId());
//                    item.setQuantityDelivered(new BigDecimal(0));
//                    item.setStatus(0L);
//                    // If materialNo not yet in map, add it
//                    mergedMap.put(materialNo, item);
//                } else {
//                    continue;
//                }
//            }
//            List<IssueTempDtlItem> mergedList = new ArrayList<>(mergedMap.values());
//            issueTempDtlItemRepository.saveAll(mergedList);
//
//        }
    }

    public static void main(String[] args) {
        String ss = "F3619AWG26A<GR            ";
        IssueTempDtl issueTempDtl = new IssueTempDtl();
        System.out.println(StringUtils.trimToEmpty(ss));
    }

    public synchronized void data() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Map<String, MaterialsInfo> materialsInfoHashMap = new HashMap<>();
        List<MaterialsInfo> materialsInfoList = materialsInfoRepository.findAll();
        for (MaterialsInfo materialsInfo : materialsInfoList) {
            materialsInfoHashMap.putIfAbsent(materialsInfo.getAssetsNumber(), materialsInfo);
        }

        String query = "SELECT a.*,b.product_no as product_no,b.lot_no as lot_no , b.plan_issue_qty as plan_issue_qty FROM isscons_temp a LEFT JOIN issue_temp b ON a.kit_no = b.kit_no AND a.material_no = b.material_no";
        List<IssueTempDtl> results = null;
        try {
            results = getIssueTempDtlList(query);
        } catch (SQLException e) {
            log.error("查询数据失败", e);
            return; // 终止方法执行
        }
        log.info("查询到单据数量: {}", results.size());

        Map<String, List<IssueTempDtl>> map = new HashMap<>();

        for (IssueTempDtl result : results) {
            // 处理sectionC为null的情况
            String sectionC = result.getSectionC();
//            if (sectionC == null || !"983C".equals(sectionC.trim())) {
//                log.debug("跳过sectionC为 {} 的记录", sectionC);
//                continue;
//            }

            // 处理字段trim并检查异常
            try {
                result.setLotNo(StringUtils.trimToEmpty(result.getLotNo()));
                result.setProductNo(StringUtils.trimToEmpty(result.getProductNo()));
                result.setMaterialNo(StringUtils.trimToEmpty(result.getMaterialNo()));
                result.setMaterialNm(StringUtils.trimToEmpty(result.getMaterialNm()));
            } catch (Exception e) {
                log.error(result.getKitNo() + "处理字段时发生异常", e);
                continue;
            }

            String kitNo = result.getKitNo();
            map.computeIfAbsent(kitNo, k -> new ArrayList<>()).add(result);

            // 欠料处理逻辑
            try {
                List<DelinquentMaterial> delinquents = delinquentMaterialRepository.findByLotNoAndAssetsNumber(result.getLotNo(), result.getMaterialNo());
                if (!delinquents.isEmpty()) {
                    result.setShortFeed("欠料");
                    delinquents.stream()
                            .filter(d -> d.getEstimatedTime() != null)
                            .findFirst()
                            .ifPresent(d -> result.setShortFeed(simpleDateFormat.format(d.getEstimatedTime())));
                }
            } catch (Exception e) {
                log.error("处理欠料信息失败", e);
            }
        }

        // 保存处理逻辑
        for (Map.Entry<String, List<IssueTempDtl>> entry : map.entrySet()) {
            String kitNo = entry.getKey();
            List<IssueTempDtl> details = entry.getValue();

            IssueTempDtl issueTempDtlFind = issueTempDtlRepository.findByKitNo(kitNo);
            if (issueTempDtlFind == null && !details.isEmpty()) {
                IssueTempDtl firstDetail = details.get(0);
                firstDetail.setStatus(0L);
                firstDetail.setShortFeed(StringUtils.defaultIfEmpty(firstDetail.getShortFeed(), "可发料"));
                issueTempDtlFind = issueTempDtlRepository.save(firstDetail);
                log.info("新增主记录，kitNo: {}", kitNo);
            }

            if (issueTempDtlFind == null) {
                log.warn("未找到或创建主记录，跳过kitNo: {}", kitNo);
                continue;
            }

            List<IssueTempDtlItem> itemsToSave = new ArrayList<>();
            for (IssueTempDtl detail : details) {
                String materialNo = detail.getMaterialNo();
                if (materialNo == null || materialNo.isEmpty()) {
                    log.warn("materialNo为空，跳过");
                    continue;
                }

                List<IssueTempDtlItem> existingItems = issueTempDtlItemRepository.findByKitNoAndMaterialNo(kitNo, materialNo);
                if (!existingItems.isEmpty()) {
                    log.debug("已存在记录，kitNo: {}, materialNo: {}", kitNo, materialNo);
                    continue;
                }

                IssueTempDtlItem newItem = new IssueTempDtlItem();
                BeanUtils.copyProperties(detail, newItem);
                newItem.setStorageWarehouse("");
                newItem.setStorageArea(detail.getLocationC());
                newItem.setPrimaryId(issueTempDtlFind.getId());
                newItem.setQuantityDelivered(BigDecimal.ZERO);
                newItem.setStatus(0L);
                newItem.setShortFeed(StringUtils.defaultIfEmpty(detail.getShortFeed(), "可发料"));
                itemsToSave.add(newItem);
            }

            if (!itemsToSave.isEmpty()) {
                for (IssueTempDtlItem item : itemsToSave) {
                    try {
                        issueTempDtlItemRepository.saveAndFlush(item);
                    } catch (Exception e) {
                        log.error("保存明细记录失败", e);
                    }
                }
                log.info("成功保存 {} 条明细记录，kitNo: {}", itemsToSave.size(), kitNo);


            }
        }
    }

    // 数据库连接配置
    private static final String URL2 = "************************************************************"; // 修改为你的数据库URL
//    private static final String URL = "*****************************************************************************;"; // 修改为你的数据库URL


    private static final String USER2 = "sdms_rd"; // 修改为你的数据库用户名
    private static final String PASSWORD2 = "sdms-sdms"; // 修改为你的数据库密码

    /**
     * 判断对象是否为空 true 不为空
     *
     * <AUTHOR>
     * @date 2020-03-11 15:07
     */
    public static String replaceFifthCharacter(String str) {
        // 检查字符串长度是否大于或等于5
        if (str.length() >= 5 && str.charAt(4) == '-') {
            // 替换第五位字符
            return str.substring(0, 4) + str.substring(5);
        }
        return str; // 如果条件不满足，返回原字符串
    }

    public List<MaterialsInfoBom> getIssueTempDtlHoseList(String query) throws SQLException {
        List<MaterialsInfoBom> list = new ArrayList<>();
        Connection conn = null;
        log.info(URL2 + "------------" + USER2 + "--------" + PASSWORD2);

        try {
            conn = DriverManager.getConnection(URL2, USER2, PASSWORD2);
            if (conn != null) {
                log.info("Connection established successfully.");
            } else {
                log.error("Failed to establish connection.");
            }

            try (PreparedStatement stmt = conn.prepareStatement(query);
                 ResultSet rs = stmt.executeQuery()) {

                while (rs.next()) {
                    MaterialsInfoBom item = new MaterialsInfoBom();
                    item.setInvoiceNumber(rs.getString("invoice_cd"));
                    item.setAssetsNumber(replaceFifthCharacter(rs.getString("pc_cd")));
                    item.setAssetsName(rs.getString("material_nm"));
//                    item.setMaterialLotNo(rs.getString("material_lot_no"));
                    item.setQuantityArrived(new BigDecimal(0));
                    item.setInvoiceAuditId(rs.getString("invoice_upload_id"));
                    item.setContainerNumber("0");
//                    item.setShift(rs.getInt("shift"));
                    item.setDosage(rs.getBigDecimal("instorage_qty"));
                    item.setCustom1("系统");
                    // item.setAssyC(rs.getString("assy_c"));
                    if (item.getInvoiceNumber().indexOf("ECS") != -1) {
//                        log.info("---------------剔除" + item.getInvoiceNumber());
                    } else {
                        list.add(item);
                    }
                }
            }

        } catch (SQLException e) {
            log.error("Error while executing query: {}", e.getMessage());
        } finally {
            if (conn != null) {
                try {
                    conn.close();
                    log.info("Connection closed successfully.");
                } catch (SQLException e) {
                    log.error("Error while closing connection: {}", e.getMessage());
                }
            }
        }

        return list;
    }

    @Override
    public ResponseData synchronizationData() {
        Thread thread = new MyTh();
        thread.start();
        return ResponseData.success("发送同步任务成功！");
    }

    class MyTh extends Thread {
        @Override
        public void run() {
            data();
        }
    }

    @Override
    public List<IssueTempDtl> findAllGroupByLineC() {
        return issueTempDtlRepository.findAllGroupByLineC();
    }

    @Override
    public List<IssueTempDtl> findAllGroupBySectionC() {
        return issueTempDtlRepository.findAllGroupBySectionC();
    }

    @Override
    public List<IssueTempDtl> findAllGroupLineCBySectionC(String sectionC) {
        return issueTempDtlRepository.findAllGroupLineCBySectionC(sectionC);
    }

    @Override
    public boolean allocationByIds(Long[] ids, Long salesmanId) {
        for (Long id : ids) {
            IssueTempDtl one = issueTempDtlRepository.getOne(id);
            one.setIssuerId(salesmanId);
            SysUser one1 = sysUserRepository.getOne(salesmanId);
            one.setIssuerName(one1.getName());
            issueTempDtlRepository.saveAndFlush(one);
        }
        return true;
    }

    @Override
    public IssueTempDtl findByKitNo(String kitNo) {
        return issueTempDtlRepository.findByKitNo(kitNo);
    }

    @Override
    public ResponseData synchronizationDataTube() {
        try {
            data2();
        } catch (Exception e) {
            log.error("胶管数据错误" + e.getMessage());
        }
        return ResponseData.success();
    }

    @Override
    public ResponseData synchronizationData3() {
        log.info("---------------开始运行发票----------------");
        List<MaterialsInfoBom> issueTempDtlHoseList = null;
        try {
            issueTempDtlHoseList = getIssueTempDtlHoseList("select * from invoice_upload where instorage_dt  BETWEEN DATEADD(DAY, -7, CAST(GETDATE() AS DATE))  AND DATEADD(DAY, 1, CAST(GETDATE() AS DATE))");
            for (MaterialsInfoBom materialsInfoBom : issueTempDtlHoseList) {
                List<MaterialsInfoBomRepeat> byInvoiceNumberAndAssetsNumberAndMaterialLotNo = materialsInfoBomRepeatRepository.findByInvoiceAuditId(materialsInfoBom.getInvoiceAuditId());
                if (byInvoiceNumberAndAssetsNumberAndMaterialLotNo.size() == 0) {
                    List<MaterialsInfoBom> list = materialsInfoBomService.findByInvoiceNumberAndAssetsNumber(materialsInfoBom.getInvoiceNumber(), materialsInfoBom.getAssetsNumber());
                    if (list.size() == 0) {
                        MaterialsInfo materialsInfo = materialsInfoService.findByAssetsNumber(materialsInfoBom.getAssetsNumber());
                        BigDecimal decimal = new BigDecimal(1);
                        if (materialsInfo != null) {
                            if (StringUtils.isNotEmpty(materialsInfo.getMinLot())) {
                                decimal = materialsInfo.getMinLot();
                            }
                        }

                        materialsInfoBom.setDosage(materialsInfoBom.getDosage().multiply(decimal));
                        materialsInfoBom.setCreateTime(new Date());
                        if (materialsInfoBom.getInvoiceNumber().indexOf("退仓") != -1) {
                            log.info("---------------发票信息退仓去除----------------" + JSONObject.toJSONString(materialsInfoBom));
                            continue;
                        }
                        materialsInfoBom.setId(null);
                        materialsInfoBomService.save(materialsInfoBom);
                        log.info(issueTempDtlHoseList.size() + "---------------发票信息增加----------------" + JSONObject.toJSONString(materialsInfoBom));
                    } else {
                        MaterialsInfo materialsInfo = materialsInfoService.findByAssetsNumber(materialsInfoBom.getAssetsNumber());
                        BigDecimal decimal = new BigDecimal(1);
                        if (materialsInfo != null) {
                            if (StringUtils.isNotEmpty(materialsInfo.getMinLot())) {
                                decimal = materialsInfo.getMinLot();
                            }
                        }
                        list.get(0).setDosage(list.get(0).getDosage().add(materialsInfoBom.getDosage().multiply(decimal)));
                        materialsInfoBomService.update(list.get(0));
                    }
                    MaterialsInfoBomRepeat materialsInfoBomRepeat = new MaterialsInfoBomRepeat();
                    BeanUtils.copyProperties(materialsInfoBom, materialsInfoBomRepeat);
                    materialsInfoBomRepeat.setId(null);
                    materialsInfoBomRepeatRepository.save(materialsInfoBomRepeat);
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return ResponseData.success();

    }

    @Override
    public boolean lineCheckByIds(Long[] ids, String lineC) {
        for (Long id : ids) {
            IssueTempDtl one = issueTempDtlRepository.getOne(id);
            one.setLineC(lineC);
            issueTempDtlRepository.saveAndFlush(one);
        }
        return true;
    }

    @Override
    public boolean issueDateCheckByIds(Long[] ids, Date plannedIssueDt) {
        for (Long id : ids) {
            IssueTempDtl one = issueTempDtlRepository.getOne(id);
            one.setPlannedIssueDt(plannedIssueDt);
            issueTempDtlRepository.saveAndFlush(one);
        }
        return true;
    }
}
