package com.yzm.property.materials.service;

import com.yzm.framework.base.IBaseService;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.materials.entity.BindingCar;
import com.yzm.property.materials.entity.MaterialsListEntity;

public interface BindingCarService extends IBaseService<BindingCar,Long>{


    BindingCar findByCar1AndStatus(String rfidData, long l);

    ResponseData addTrolleyData(String code, String floor,Long id);
}
