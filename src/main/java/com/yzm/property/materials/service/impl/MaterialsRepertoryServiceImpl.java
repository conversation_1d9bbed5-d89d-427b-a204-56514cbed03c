package com.yzm.property.materials.service.impl;

import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.framework.bean.PageBean;
import com.yzm.property.basis.utils.BeanMapUtils;
import com.yzm.property.materials.criteria.MaterialsRepertoryCriteria;
import com.yzm.property.materials.entity.MaterialsInfo;
import com.yzm.property.materials.entity.MaterialsRepertory;
import com.yzm.property.materials.repository.MaterialsInfoRepository;
import com.yzm.property.materials.repository.MaterialsRepertoryRepository;
import com.yzm.property.materials.service.MaterialsRepertoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:47
 */
@Service
public class MaterialsRepertoryServiceImpl extends BaseService<MaterialsRepertory, Long> implements MaterialsRepertoryService {
    @Autowired
    private MaterialsRepertoryRepository materialsRepertoryRepository;
    @Autowired
    private MaterialsInfoRepository consumableInfoRepository;

    @Override
    public BaseRepository<MaterialsRepertory, Long> getRepository() {
        return materialsRepertoryRepository;
    }

    @Override
    public Map showAllMaterialsRepository(String keyword, PageBean pageBean) {
        Map<String, Object> result;
//        if (keyword == null || "".equals(keyword)) {
        MaterialsRepertoryCriteria materialsRepertoryCriteria = new MaterialsRepertoryCriteria();
        materialsRepertoryCriteria.setKeyword(keyword);
        result = this.findAllByPage(materialsRepertoryCriteria, pageBean.getPagable());

//        HashMap<String, Object> map = new HashMap<>();
//        map.put("list", result.getContent());
//        map.put("totalCount", result.getTotalElements());
        return result;
    }

    @Override
    public Map showAllMaterialsSummaryInfo(String keyword,String dept, PageBean pageBean) {
        HashMap<String, Object> map = new HashMap<>();
        List<Map<String, Object>> result;
        result = materialsRepertoryRepository.findByAssetsNumberOrderByCreateTimeDesc(keyword,dept, (pageBean.getPage() - 1) * pageBean.getLimit(), pageBean.getLimit());
        int i = materialsRepertoryRepository.countGroupedResults(keyword,dept);
        List<MaterialsRepertory> list = new ArrayList<>();
        for (Map<String, Object> map1 : result) {
            try {
                MaterialsRepertory consumableRepertory = BeanMapUtils.mapToBean(map1, MaterialsRepertory.class);
                list.add(consumableRepertory);
            } catch (Exception e) {
                 e.printStackTrace();
            }
        }
        map.put("list", list);
        map.put("totalCount", i);
        return map;
    }

    @Override
    public List<MaterialsRepertory> findByNowRepertoryAll(String keyword) {
        List<MaterialsRepertory> list = new ArrayList<>();
        List<Map<String, Object>> byNowRepertoryAll = materialsRepertoryRepository.findByNowRepertoryAll(keyword);
        for (Map<String, Object> map1 : byNowRepertoryAll) {
            try {
                MaterialsRepertory consumableRepertory = BeanMapUtils.mapToBean(map1, MaterialsRepertory.class);
                list.add(consumableRepertory);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return list;
    }

    @Override
    public boolean findByMaterialsInfoId(Long id) {
        MaterialsInfo consumableInfo = consumableInfoRepository.getOne(id);
        MaterialsRepertory consumableRepertory = materialsRepertoryRepository.findByMaterialsInfoId(consumableInfo.getId());
        if (StringUtils.isEmpty(consumableRepertory)) {
            consumableInfoRepository.delete(consumableInfo);
            return true;
        }
        if (consumableRepertory.getNowRepertory().compareTo(new BigDecimal(0)) == 0) {
            consumableInfoRepository.delete(consumableInfo);
            materialsRepertoryRepository.delete(consumableRepertory);
            System.out.println("库存为0 可删除");
        } else {
            throw new RuntimeException("库存不为0,不可进行删除");
        }

        return true;
    }

    @Override
    public MaterialsRepertory findByAssetsCodeOrAssetsRfid(String rfid, String code) {
        return materialsRepertoryRepository.findByAssetsCodeOrAssetsRfid(rfid, code);
    }

    @Override
    public List<MaterialsRepertory> findByAssetsNumber(String assetsNumber) {
        return materialsRepertoryRepository.findByAssetsNumber(assetsNumber);
    }

    @Override
    public List<MaterialsRepertory> findByAssetsNumberAndWarehouseInfoAndAreaInfo(String assetsNumber, String warehouseInfo, String areaInfo) {
        return materialsRepertoryRepository.findByAssetsNumberAndWarehouseInfoAndAreaInfo(assetsNumber, warehouseInfo, areaInfo);
    }

    @Override
    public MaterialsRepertory findByAssetsRfidAndAssetsNumber(String rfid, String assetsNumber) {
        return materialsRepertoryRepository.findByAssetsRfidAndAssetsNumber(rfid, assetsNumber);
    }

    @Override
    public List<MaterialsRepertory> findByAssetsNumberOrderByDateArrivalAsc(String assetsNumber) {
        return materialsRepertoryRepository.findByAssetsNumberOrderByDateArrivalAsc(assetsNumber);
    }

    @Override
    public MaterialsRepertory findByAssetsNumberAndAssetsCodeIsNull(String part) {
        return materialsRepertoryRepository.findByAssetsNumberAndAssetsCodeIsNull(part);
    }

    @Override
    public List<MaterialsRepertory> findGroupByDepartmentCode() {
        return materialsRepertoryRepository.findGroupByDepartmentCode();
    }

    @Override
    public List<MaterialsRepertory> findByAssetsNumberAndFifoStatusOrderByDateArrivalAsc(String assetsNumber, long l) {
        return materialsRepertoryRepository.findByAssetsNumberAndFifoStatusOrderByDateArrivalAsc(assetsNumber, l);
    }
}
