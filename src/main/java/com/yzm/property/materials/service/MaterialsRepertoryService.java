package com.yzm.property.materials.service;

import com.yzm.framework.base.IBaseService;
import com.yzm.framework.bean.PageBean;
import com.yzm.property.materials.entity.MaterialsRepertory;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:47
 */
public interface MaterialsRepertoryService extends IBaseService<MaterialsRepertory, Long> {


    Map showAllMaterialsRepository(String keyword, PageBean pageBean);

    Map showAllMaterialsSummaryInfo(String keyword, String dept, PageBean pageBean);

    List<MaterialsRepertory> findByNowRepertoryAll(String keyword);

    boolean findByMaterialsInfoId(Long id);

    MaterialsRepertory findByAssetsCodeOrAssetsRfid(String rfid, String code);

    List<MaterialsRepertory> findByAssetsNumber(String assetsNumber);

    List<MaterialsRepertory> findByAssetsNumberAndWarehouseInfoAndAreaInfo(String assetsNumber, String warehouseInfo, String areaInfo);

    MaterialsRepertory findByAssetsRfidAndAssetsNumber(String rfid, String assetsNumber);

    List<MaterialsRepertory> findByAssetsNumberOrderByDateArrivalAsc(String assetsNumber);

    MaterialsRepertory findByAssetsNumberAndAssetsCodeIsNull(String part);

    List<MaterialsRepertory> findGroupByDepartmentCode();

    List<MaterialsRepertory> findByAssetsNumberAndFifoStatusOrderByDateArrivalAsc(String assetsNumber, long l);
}
