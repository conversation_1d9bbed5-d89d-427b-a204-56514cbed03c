package com.yzm.property.materials.service.impl;

import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.property.materials.entity.MaterialTransit;
import com.yzm.property.materials.entity.MaterialsListEntity;
import com.yzm.property.materials.repository.MaterialTransitRepository;
import com.yzm.property.materials.repository.MaterialsListRepository;
import com.yzm.property.materials.service.MaterialTransitService;
import com.yzm.property.materials.service.MaterialsListService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class MaterialTransitImpl extends BaseService<MaterialTransit,Long> implements MaterialTransitService {
    @Autowired
    private MaterialTransitRepository materialTransitRepository;

    public BaseRepository<MaterialTransit,Long> getRepository(){
        return materialTransitRepository;
    }


}
