package com.yzm.property.materials.service;

import com.yzm.framework.base.IBaseService;
import com.yzm.property.materials.entity.DelinquentMaterial;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface DelinquentMaterialService extends IBaseService<DelinquentMaterial, Long> {

    List<DelinquentMaterial> calculateMaterialShortage();

    List<DelinquentMaterial> findByAssetsNumber(String assetsNumber);

    List<DelinquentMaterial> findByKitNoAndAssetsNumber(String kitNo, String assetsNumber);

    List<DelinquentMaterial> calculateMaterialShortageNew();

    DelinquentMaterial findByAssetsNumberAndLotNo(String assetsNumber, String lotNo);
}
