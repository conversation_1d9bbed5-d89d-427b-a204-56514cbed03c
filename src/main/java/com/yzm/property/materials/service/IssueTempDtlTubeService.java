package com.yzm.property.materials.service;

import com.yzm.framework.base.IBaseService;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.materials.entity.BindingCar;
import com.yzm.property.materials.entity.IssueTempDtl;
import com.yzm.property.materials.entity.IssueTempDtlTube;
import com.yzm.property.materials.entity.IssueTempDtlTubeItem;

import java.util.List;

public interface IssueTempDtlTubeService extends IBaseService<IssueTempDtlTube,Long>{


    boolean allocationByIds(Long[] ids, Long salesmanId);

    ResponseData deliveryStorage(IssueTempDtlTube issueTempDtl);

    ResponseData deliveryStorageTube(IssueTempDtlTubeItem issueTempDtlTubeItem);

    List<IssueTempDtlTube> findAllGroupLineCBySectionC(String sectionC);

    List<IssueTempDtlTube> findAllGroupBySectionC();
}
