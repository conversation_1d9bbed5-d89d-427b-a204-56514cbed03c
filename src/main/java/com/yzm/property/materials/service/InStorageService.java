package com.yzm.property.materials.service;

import com.yzm.framework.base.IBaseService;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.materials.entity.InStorageMaterials;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:47
 */
public interface InStorageService extends IBaseService<InStorageMaterials,Long> {



   Map showAllInStorageInfo(String keyword, List<String> type, PageBean pageBean);

    ResponseData saveInStorage(InStorageMaterials inStorage);

    ResponseData deleteInfo(Long[] ids);

    ResponseData saveInStorageReturning(InStorageMaterials inStorage);

    boolean submitByIds(Long[] ids);

    void audit(Long id, Integer status);

    ResponseData inStorageSave(InStorageMaterials inStorage);

 ResponseData saveInStorageVerify(InStorageMaterials inStorage);

 InStorageMaterials findByRecordNumber(String recordNumber);

    Map<String, Object> showAllOutWarehouseInfo(String keyword, List<String> type, PageBean pageBean);

    Map<String, Object> showAllOutWarehouseInfoPage(String keyword, List<String> type, PageBean pageBean,String departmentCode);

    Map<String, Object> showAllOutWarehouseInfoPageDys(String keyword, List<String> type, PageBean pageBean);

 /*
    InStorage findById(Long id);*/
}
