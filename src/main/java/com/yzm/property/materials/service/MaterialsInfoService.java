package com.yzm.property.materials.service;

import com.yzm.framework.base.IBaseService;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.materials.entity.MaterialsInfo;
import com.yzm.property.materials.pojo.BomMst;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:47
 */
public interface MaterialsInfoService extends IBaseService<MaterialsInfo,Long> {



    Map showAllMaterialsInfo(String keyword, PageBean pageBean);

    MaterialsInfo findById(Long id);

    ResponseData addMaterialsInfo(MaterialsInfo consumableInfo);

    ResponseData updateMaterialsInfo(MaterialsInfo consumableInfo);

    MaterialsInfo findByAssetsNumber(String assetsNumber);

    List<String> getMaterialsInfo(String code,String deptCode);

    List<String> getMaterialsInfoData(String code);


    List<BomMst> getMaterialsInfoBom(String code, String deptCode);
//    MaterialsInfo findByAssetsCodeOrAssetsRfid(String rfid,String code);
}
