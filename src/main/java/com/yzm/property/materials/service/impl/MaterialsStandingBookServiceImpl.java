package com.yzm.property.materials.service.impl;

import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.framework.bean.PageBean;
import com.yzm.property.materials.entity.MaterialsStandingBook;
import com.yzm.property.materials.repository.MaterialsStandingBookRepository;
import com.yzm.property.materials.service.MaterialsStandingBookService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class MaterialsStandingBookServiceImpl extends BaseService<MaterialsStandingBook,Long> implements MaterialsStandingBookService {
    @Autowired
    private MaterialsStandingBookRepository consumableStandingBookRepository;
    @Override
    public BaseRepository<MaterialsStandingBook,Long> getRepository(){

        return consumableStandingBookRepository;
    }

    @Override
    public Map showAllMaterialsStandingBook(String keyword, PageBean pageBean) {
        Page<MaterialsStandingBook> result;
        if (keyword == null || "".equals(keyword)) {
            result = consumableStandingBookRepository.findAll(pageBean.getPagable());
        } else {
            result = consumableStandingBookRepository.findAllByAssetsNameContaining(keyword,pageBean.getPagable());
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("list", result.getContent());
        map.put("totalCount", result.getTotalElements());
        return map;
    }
}
