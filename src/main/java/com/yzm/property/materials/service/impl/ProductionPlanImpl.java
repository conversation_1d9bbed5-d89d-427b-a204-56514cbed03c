package com.yzm.property.materials.service.impl;

import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.property.materials.entity.ProductionPlan;
import com.yzm.property.materials.repository.ProductionPlanRepository;
import com.yzm.property.materials.service.ProductionPlanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ProductionPlanImpl extends BaseService<ProductionPlan, Long> implements ProductionPlanService {
    @Autowired
    private ProductionPlanRepository productionPlanRepository;


    public BaseRepository<ProductionPlan, Long> getRepository() {
        return productionPlanRepository;
    }

    @Override
    public ProductionPlan findByBatchNo(String productNo) {
        return productionPlanRepository.findByBatchNo(productNo);
    }
}
