//package com.yzm.property.materials.service.impl;
//
//import com.yzm.common.utils.StringUtils;
//import com.yzm.framework.base.BaseRepository;
//import com.yzm.framework.base.BaseService;
//import com.yzm.framework.bean.ResponseData;
//import com.yzm.property.materials.entity.BindingCar;
//import com.yzm.property.materials.entity.IssueTempDtl;
//import com.yzm.property.materials.repository.BindingCarRepository;
//import com.yzm.property.materials.repository.IssueTempDtlRepository;
//import com.yzm.property.materials.service.BindingCarService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.util.Arrays;
//import java.util.List;
//
//@Service
//public class BindingCarImplBack extends BaseService<BindingCar, Long> implements BindingCarService {
//    @Autowired
//    private BindingCarRepository bindingCarRepository;
//    @Autowired
//    private IssueTempDtlRepository issueTempDtlRepository;
//
//    public BaseRepository<BindingCar, Long> getRepository() {
//        return bindingCarRepository;
//    }
//
//    @Override
//    public BindingCar findByCar1AndStatus(String rfidData, long l) {
//        return bindingCarRepository.findByCar1AndStatus(rfidData, l);
//    }
//
//    @Override
//    public ResponseData addTrolleyData(String code, String floor, Long id) {
//        IssueTempDtl one = issueTempDtlRepository.getOne(id);
//        String[] split = code.split(",");
//        BindingCar byCar1AndStatus = null;
//        for (String s : split) {
//            byCar1AndStatus = bindingCarRepository.findByCar1AndStatus(s, 0);
//            break;
//        }
//
//        if (StringUtils.isEmpty(one.getProductNo())) {
//            return ResponseData.error("单据品番为空，请更换单据扫描绑定");
//        }
//        if (byCar1AndStatus != null) {
//            if (!floor.equals(byCar1AndStatus.getMatlPrcsCd1())) {
//                return ResponseData.error("当前小车已绑定品番，跟当前扫码去往楼层不同。请确认！");
//            }
//            String[] split2 = byCar1AndStatus.getProduct2().split(",");
//            List<String> list2 = Arrays.asList(split2);
//            if (list2.contains(one.getKitNo())) {
//                return ResponseData.error("当前发料单(" + one.getKitNo() + ")-小车条码(" + code + ")已经绑定");
//            }
//            byCar1AndStatus.setProduct1(byCar1AndStatus.getProduct1() + "," + one.getProductNo());
//            byCar1AndStatus.setProduct2(byCar1AndStatus.getProduct2() + "," + one.getKitNo());
//            bindingCarRepository.saveAndFlush(byCar1AndStatus);
//        } else {
//            BindingCar bindingCar = new BindingCar();
//            bindingCar.setCar1(split[0]);
//            bindingCar.setCar2(split[1]);
//            bindingCar.setCar3(split[2]);
//            bindingCar.setCar4(split[3]);
//            bindingCar.setStatus(0L);
//            bindingCar.setProduct1(one.getProductNo());
//            bindingCar.setProduct2(one.getKitNo());
//            bindingCar.setMatlPrcsCd1(floor);
//            bindingCarRepository.save(bindingCar);
//        }
//
//        Long aLong = bindingCarRepository.countByProduct2Like(one.getKitNo());
//        one.setTrolleySum(aLong);
//        issueTempDtlRepository.saveAndFlush(one);
//        return ResponseData.success("绑定成功", aLong);
//    }
//
//    @Override
//    public ResponseData checkTrolleyData(String code, String floor, Long id) {
//        BindingCar byCar1AndStatus = bindingCarRepository.findByCar1AndStatus(code, 0);
//        IssueTempDtl one = issueTempDtlRepository.getOne(id);
//
//        if (StringUtils.isEmpty(one.getProductNo())) {
//            return ResponseData.error("单据品番为空，请更换单据扫描绑定");
//        }
//        if (byCar1AndStatus != null) {
//            if (!floor.equals(byCar1AndStatus.getMatlPrcsCd1())) {
//                return ResponseData.error("当前小车已绑定品番，跟当前扫码去往楼层不同。请确认！");
//            }
//
//            String[] split = byCar1AndStatus.getProduct1().split(",");
//            String[] split2 = byCar1AndStatus.getProduct2().split(",");
//            List<String> list2 = Arrays.asList(split2);
//            if (list2.contains(one.getKitNo())) {
//                return ResponseData.error("当前发料单(" + one.getKitNo() + ")-小车条码(" + code + ")已经绑定");
//            }
////            byCar1AndStatus.setProduct1(byCar1AndStatus.getProduct1() + "," + one.getProductNo());
////            byCar1AndStatus.setProduct2(byCar1AndStatus.getProduct2() + "," + one.getKitNo());
////            bindingCarRepository.saveAndFlush(byCar1AndStatus);
//        } else {
//
//        }
////        Long aLong = bindingCarRepository.countByProduct2Like(one.getKitNo());
////        one.setTrolleySum(aLong);
////        issueTempDtlRepository.saveAndFlush(one);
//
//        return ResponseData.success("校验成功", code);
//    }
//
//    /*public Map showAllOutWarehouseInfo(String keyword, String operationName, Date startTime,Date endTime, PageBean pageBean){
//        Map<String,Object> map=new HashMap<>();
//        if("全部".equals(operationName))
//        return map;
//    }*/
//    /*public void select(String keyword,String operationName){
//        if ("全部".equals(operationName)){
//            return consumableListRepository.findByKeyword();
//        }else {
//            return consumableListRepository.findByKeywordAndOperation(operationName);
//        }
//    }*/
//}
