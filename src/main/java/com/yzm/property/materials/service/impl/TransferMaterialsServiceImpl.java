package com.yzm.property.materials.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.yzm.common.utils.OrderUtils;
import com.yzm.common.utils.ShiroUtils;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.basis.repository.PersonnelRepository;
import com.yzm.property.materials.entity.MaterialsRepertory;
import com.yzm.property.materials.entity.TransferMaterials;
import com.yzm.property.materials.entity.TransferMaterialsItem;
import com.yzm.property.materials.repository.MaterialsInfoRepository;
import com.yzm.property.materials.repository.MaterialsRepertoryRepository;
import com.yzm.property.materials.repository.TransferItemMaterialsRepository;
import com.yzm.property.materials.repository.TransferMaterialsRepository;
import com.yzm.property.materials.service.TransferService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:47
 */
@Service
public class TransferMaterialsServiceImpl extends BaseService<TransferMaterials, Long> implements TransferService {

    @Autowired
    private TransferMaterialsRepository transferRepository;

    @Autowired
    private TransferItemMaterialsRepository transferItemRepository;

    @Autowired
    private MaterialsInfoRepository materialsInfoRepository;
    @Autowired
    private MaterialsRepertoryRepository materialsRepertoryRepository;
    @Autowired
    private PersonnelRepository personnelRepository;

    @Override
    public BaseRepository<TransferMaterials, Long> getRepository() {
        return transferRepository;
    }


    @Override
    public Map showAllTransferInfo(String keyword, PageBean pageBean) {
        HashMap<String, Object> map = new HashMap<>();
        if (keyword == null || "".equals(keyword)) {
            Page<TransferMaterials> result = transferRepository.findByOrderByCreateTimeDesc(pageBean.getPagable());
            map.put("list", result.getContent());
            map.put("totalCount", result.getTotalElements());
        } else {
            List<TransferMaterials> result = transferRepository.findByRecordNumberContainsOrderByCreateTimeDesc(keyword, (pageBean.getPage() - 1) * pageBean.getLimit(), pageBean.getLimit());
            map.put("list", result);
            map.put("totalCount", result.size());
        }
        return map;
    }

    @Override
    public ResponseData saveTransfer(TransferMaterials transfer) {
        List<MaterialsRepertory> byWarehouseInfoAndAreaInfo = materialsRepertoryRepository.findByWarehouseInfoAndAreaInfo(transfer.getInStorageWarehouse(), transfer.getInStorageArea());
        if(byWarehouseInfoAndAreaInfo.size()>0){
            return ResponseData.error("转入库位已有物品存放");
        }
        //单据编号
        String parameterNo = OrderUtils.getDiaoBoCode();
        transfer.setRecordNumber(parameterNo);//入库编号
//        if(StringUtils.isNotEmpty(transfer.getInStoragePeople())){
        transfer.setInStoragePeople(ShiroUtils.getUserInfo().getName());//移库人
//        transfer.setInStoragePeople(personnelRepository.findById(Long.valueOf(transfer.getInStoragePeople())).get().getBasisName());//入库人
//        }
        transferRepository.save(transfer);
        //获取前台传来的表格信息
        List<TransferMaterialsItem> transferItem = JSONArray.parseArray(transfer.getItemJson(), TransferMaterialsItem.class);
        int sum = 0;
        for (TransferMaterialsItem storageItem : transferItem) {
            MaterialsRepertory materialsRepertory = materialsRepertoryRepository.findByAssetsCodeOrAssetsRfid(storageItem.getAssetsCode(), storageItem.getAssetsCode());
            materialsRepertory.setWarehouseInfo(transfer.getInStorageWarehouse());
            materialsRepertory.setAreaInfo(transfer.getInStorageArea());
            materialsRepertory.setOperationNumber(transfer.getRecordNumber());
            //通过耗材查库存
            materialsRepertoryRepository.saveAndFlush(materialsRepertory);

            sum += storageItem.getInStorageCount();
            transfer.setInStorageTotal(sum);//入库总数
            //新建入库子表
            TransferMaterialsItem sInfo = new TransferMaterialsItem();
            BeanUtils.copyProperties(storageItem, sInfo);
            sInfo.setId(null);
            sInfo.setInStorageAreaOriginal(storageItem.getAreaInfo());
            sInfo.setInStorageWarehouseOriginal(storageItem.getWarehouseInfo());
            sInfo.setAreaInfo(transfer.getInStorageArea());
            sInfo.setWarehouseInfo(transfer.getInStorageWarehouse());
            sInfo.setInStorageCount(storageItem.getInStorageCount());//数量
            sInfo.setInStoragePrice(storageItem.getInStoragePrice());//单价
            sInfo.setRecordNumber(transfer.getRecordNumber());//记录编号主表
            sInfo.setAssetsId(storageItem.getId());//耗材ID
            transferItemRepository.save(sInfo);
//            MaterialsListEntity materialsListEntity = new MaterialsListEntity();
//            BeanUtils.copyProperties(materialsRepertory, materialsListEntity);
//            materialsListEntity.setId(null);
//            materialsListEntity.setOperationName(MaterialsUtil.OPERATION_RUKU);
//            materialsListEntity.setOperationOtherId(inStorage.getRecordNumber());
//            materialsListEntity.setOperationId(inStorage.getId());
//            materialsListEntity.setUserName(inStorage.getInStoragePeople());
//            materialsListEntity.setOutDate(inStorage.getInStorageDate());
//            materialsListRepository.save(materialsListEntity);

        }
        transferRepository.saveAndFlush(transfer);
        return ResponseData.success("调拨成功");
    }
}
