package com.yzm.property.materials.service;

import com.yzm.framework.base.IBaseService;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.materials.entity.OutWarehouseMaterialsEntity;

import java.util.List;
import java.util.Map;

public interface OutWarehouseService  extends IBaseService<OutWarehouseMaterialsEntity,Long> {
    Map showAllOutWarehouseInfo(String keyword, List<String> type,String dept, PageBean pageBean);


    Map showAllOutWarehouseInfoOther(String keyword, List<String> type, PageBean pageBean);

    Map<String,String> saveOutWarehouse(OutWarehouseMaterialsEntity outWarehouseEntity);

    ResponseData deleteInfo(Long[] ids);

    ResponseData audit(Long id, Integer status);

    ResponseData addOutStorage(OutWarehouseMaterialsEntity outWarehouseEntity);

    boolean submitByIds(Long[] ids);

    ResponseData deliveryStorageSave(OutWarehouseMaterialsEntity outWarehouseEntity);

    OutWarehouseMaterialsEntity findByKitNo(String key);

    OutWarehouseMaterialsEntity findByRecordNumber(String recordNumber);
}
