package com.yzm.property.materials.service;

import com.yzm.framework.base.IBaseService;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.materials.entity.MaterialsInfo;
import com.yzm.property.materials.entity.MaterialsRepertoryFifo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:47
 */
public interface MaterialsRepertoryFifoService extends IBaseService<MaterialsRepertoryFifo,Long> {

    MaterialsRepertoryFifo findByAssetsNumberAndBatchNumber(String assetsNumber, String batchNumber);

    boolean submitByIds(Long[] ids);

    void audit(Long id, Integer status);
}
