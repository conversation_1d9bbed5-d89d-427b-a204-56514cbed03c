package com.yzm.property.materials.service.impl;

import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.basis.entity.BasisArea;
import com.yzm.property.basis.entity.BasisUnit;
import com.yzm.property.basis.entity.BasisWarehouse;
import com.yzm.property.basis.repository.*;
import com.yzm.property.materials.contorller.MaterialsInfoController;
import com.yzm.property.materials.entity.IssueTempDtl;
import com.yzm.property.materials.entity.MaterialsInfo;
import com.yzm.property.materials.entity.MaterialsInfoBom;
import com.yzm.property.materials.pojo.BomMst;
import com.yzm.property.materials.repository.MaterialsInfoRepository;
import com.yzm.property.materials.repository.MaterialsRepertoryRepository;
import com.yzm.property.materials.service.MaterialsInfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 */
@Service
public class MaterialsInfoServiceImpl extends BaseService<MaterialsInfo, Long> implements MaterialsInfoService {
    private static Logger log = LoggerFactory.getLogger(MaterialsInfoServiceImpl.class);

    @Value("${sqlserver.url}")
    private String URL;
    @Value("${sqlserver.username}")
    private String USER;
    @Value("${sqlserver.password}")
    private String PASSWORD;
    @Value("${sqlserver.storeIssueTable}")
    private String storeIssueTable;

    @Autowired
    private MaterialsInfoRepository materialsRepository;
    @Autowired
    private MaterialsRepertoryRepository consumableRepertoryRepository;
    @Autowired
    private UnitRepository unitRepository;
    @Autowired
    private BrandRepository brandRepository;
    @Autowired
    private TypeRepository typeRepository;
    @Autowired
    private WarehouseRepository warehouseRepository;
    @Autowired
    private AreaRepository areaRepository;


    @Override
    public BaseRepository<MaterialsInfo, Long> getRepository() {
        return materialsRepository;
    }

    @Override
    @Transactional
    public MaterialsInfo save(MaterialsInfo consumableInfo) {
        consumableInfo = materialsRepository.save(consumableInfo);
        //AssetsLogUtil.createAssetsLog(null,null,consumableInfo.getId(),consumableInfo.getOtherId(),consumableInfo.getAssetsName(),consumableInfo.getAssetsSource());
        return consumableInfo;
    }

    @Override
    public Map showAllMaterialsInfo(String keyword, PageBean pageBean) {
        Page<MaterialsInfo> result;
        if (keyword == null || "".equals(keyword)) {
            result = materialsRepository.findAll(pageBean.getPagable());
        } else {
            result = materialsRepository.findAllByAssetsNameContainingOrAssetsNumberContaining(keyword, keyword, pageBean.getPagable());
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("list", result.getContent());
        map.put("totalCount", result.getTotalElements());
        return map;
    }

    @Override
    public MaterialsInfo findById(Long id) {
        return materialsRepository.findById(id).get();
    }

    @Override
    public ResponseData addMaterialsInfo(MaterialsInfo consumableInfo) {
        MaterialsInfo consumableInfo1 = materialsRepository.findByAssetsNumber(consumableInfo.getAssetsNumber());
        if (StringUtils.isNotEmpty(consumableInfo1)) {
            return ResponseData.error("材料编号重复请重新输入！");
        }

        try {
            if (StringUtils.isNotEmpty(consumableInfo.getStorageWarehouseId())) {
                BasisWarehouse warehouse = warehouseRepository.getOne(consumableInfo.getStorageWarehouseId());
                consumableInfo.setStorageWarehouse(warehouse.getCkmc());
            }
            if (StringUtils.isNotEmpty(consumableInfo.getStorageAreaId())) {
                BasisArea area = areaRepository.getOne(consumableInfo.getStorageAreaId());
                consumableInfo.setStorageArea(area.getBasisName());
                area.setStatus(1L);
                areaRepository.saveAndFlush(area);
            }
        } catch (Exception e) {

        }

//        MaterialsInfo consumableInfo2 =   materialsRepository.findByAssetsCodeOrAssetsRfid(consumableInfo.getAssetsRfid(),consumableInfo.getAssetsRfid());
//        if(StringUtils.isNotEmpty(consumableInfo2)){
//            return ResponseData.error("RFID重复请重新输入！");
//        }
//        consumableInfo.setAssetsTypeName(typeRepository.findById(consumableInfo.getAssetsType()).get().getBasisName());
        BasisUnit unit = unitRepository.findById(consumableInfo.getAssetsUnit()).get();
        //耗材编号
//        String parameterNo = OrderUtils.getHaoCaiCode();
//        consumableInfo.setAssetsNumber(parameterNo);
//        consumableInfo.setAssetsUnitName(unit.getUnitName());

        //新建耗材库存信息
//        MaterialsRepertory consumableRepertory = new MaterialsRepertory();
//        BeanUtils.copyProperties(consumableInfo, consumableRepertory);
//        consumableRepertory.setId(null);
////        consumableRepertory.setAssetsNumber(consumableInfo.getAssetsNumber());//耗材编号
//        consumableRepertory.setMaterialsInfoId(consumableInfo.getId());//耗材ID
////        consumableRepertory.setAssetsName(consumableInfo.getAssetsName());//资产名称
////        consumableRepertory.setAssetsSpecifications(consumableInfo.getAssetsSpecifications());//规格型号
////        consumableRepertory.setAssetsUnit(consumableInfo.getAssetsUnit());//单位ID
        consumableInfo.setAssetsUnitName(unit.getUnitName());//单位名称
//        consumableRepertory.setNowRepertory(0); //当前库存库
       /* consumableRepertory.setPlanExRepertory(0); //待出库
        consumableRepertory.setPlanImRepertory(0);//待入库*/

        return materialsRepository.save(consumableInfo) != null ? ResponseData.success() : ResponseData.error("新增失败");
    }

    @Override
    public ResponseData updateMaterialsInfo(MaterialsInfo consumableInfo) {
        MaterialsInfo consumableInfo1 = materialsRepository.findByAssetsNumber(consumableInfo.getAssetsNumber());
        if (StringUtils.isNotEmpty(consumableInfo1) && !consumableInfo.getId().equals(consumableInfo1.getId())) {
            return ResponseData.error("材料编号重复请重新输入！");
        }
        BasisUnit unit = unitRepository.findById(consumableInfo.getAssetsUnit()).get();
        consumableInfo.setAssetsUnitName(unit.getUnitName());
//        consumableInfo.setAssetsTypeName(typeRepository.findById(consumableInfo.getAssetsType()).get().getBasisName());
//        Long consumableInfoId = consumableInfo.getId();
        //通过ID查耗材库存
//        MaterialsRepertory consumableRepertory = consumableRepertoryRepository.findByMaterialsInfoId(consumableInfoId);
//        consumableRepertory.setAssetsName(consumableInfo.getAssetsName());//资产名称
//        consumableRepertory.setAssetsSpecifications(consumableInfo.getAssetsSpecifications());//规格型号
//        consumableRepertory.setAssetsUnit(consumableInfo.getAssetsUnit());//单位ID
//        consumableRepertory.setAssetsUnitName(consumableInfo.getAssetsUnitName());//单位名称

//        consumableRepertoryRepository.saveAndFlush(consumableRepertory);
        return materialsRepository.saveAndFlush(consumableInfo) != null ? ResponseData.success() : ResponseData.error("新增失败");
    }

    @Override
    public MaterialsInfo findByAssetsNumber(String assetsNumber) {
        return materialsRepository.findByAssetsNumber(assetsNumber);
    }

    @Override
    public List<String> getMaterialsInfo(String code, String deptCode) {
        // 建议使用参数化查询以防止 SQL 注入
        String query = "SELECT location_c FROM matloc_mst WHERE  LTRIM(RTRIM(material_no))  = ? and section_c = ?";
        List<String> list = new ArrayList<>();

        try (Connection conn = DriverManager.getConnection(URL, USER, PASSWORD);
             PreparedStatement stmt = conn.prepareStatement(query)) {

            // 设置参数
            stmt.setString(1, code);
            stmt.setString(2, deptCode);

            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    log.info("======库位信息==============" + rs.getString("location_c"));
                    list.add(rs.getString("location_c"));  // 将 item 添加到列表中
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return list; // 返回包含结果的列表

    }
    @Override
    public List<String> getMaterialsInfoData(String code) {
        // 建议使用参数化查询以防止 SQL 注入
        String query = "SELECT material_nm FROM material_mst WHERE  LTRIM(RTRIM(material_no))  = ? ";
        List<String> list = new ArrayList<>();

        try (Connection conn = DriverManager.getConnection(URL, USER, PASSWORD);
             PreparedStatement stmt = conn.prepareStatement(query)) {

            // 设置参数
            stmt.setString(1, code);

            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    log.info("======物料名称==============" + rs.getString("material_nm"));
                    list.add(rs.getString("material_nm"));  // 将 item 添加到列表中
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return list; // 返回包含结果的列表

    }

    @Override
    public List<BomMst> getMaterialsInfoBom(String code, String deptCode) {
        // 建议使用参数化查询以防止 SQL 注入
        String query = "SELECT * FROM bom_mst WHERE  LTRIM(RTRIM(product_no))  = ? and section_c = ?";
        List<BomMst> list = new ArrayList<>();

        try (Connection conn = DriverManager.getConnection(URL, USER, PASSWORD);
             PreparedStatement stmt = conn.prepareStatement(query)) {

            // 设置参数
            stmt.setString(1, code);
            stmt.setString(2, deptCode);

            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {

                    BomMst item = new BomMst();
                    item.setProductNo(rs.getString("product_no"));
                    item.setMaterialNo(rs.getString("material_no"));
                    item.setBomQty(rs.getBigDecimal("bom_qty"));
                    item.setBomUnit(rs.getString("bom_unit"));
//                    item.setMaterialLotNo(rs.getString("material_lot_no"));
                    item.setSectionC(rs.getString("section_c"));
                    list.add(item);
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return list; // 返回包含结果的列表
    }
//
//    @Override
//    public MaterialsInfo findByAssetsCodeOrAssetsRfid(String rfid,String code) {
//        return materialsRepository.findByAssetsCodeOrAssetsRfid(rfid,code);
//    }
}
