package com.yzm.property.materials.service.impl;

import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.property.materials.entity.MaterialsListEntity;
import com.yzm.property.materials.repository.MaterialsListRepository;
import com.yzm.property.materials.service.MaterialsListService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class MaterialsListImpl extends BaseService<MaterialsListEntity, Long> implements MaterialsListService {
    @Autowired
    private MaterialsListRepository consumableListRepository;

    public BaseRepository<MaterialsListEntity, Long> getRepository() {
        return consumableListRepository;
    }

    @Override
    public List<Map<String, Object>> findByAssetsNumberGruopByReason(String number) {
        return consumableListRepository.findByAssetsNumberGruopByReason(number);
    }

    /*public Map showAllOutWarehouseInfo(String keyword, String operationName, Date startTime,Date endTime, PageBean pageBean){
        Map<String,Object> map=new HashMap<>();
        if("全部".equals(operationName))
        return map;
    }*/
    /*public void select(String keyword,String operationName){
        if ("全部".equals(operationName)){
            return consumableListRepository.findByKeyword();
        }else {
            return consumableListRepository.findByKeywordAndOperation(operationName);
        }
    }*/
}
