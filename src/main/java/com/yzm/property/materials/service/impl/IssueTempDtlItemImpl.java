package com.yzm.property.materials.service.impl;

import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yzm.common.utils.OrderUtils;
import com.yzm.common.utils.ShiroUtils;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.materials.entity.*;
import com.yzm.property.materials.repository.*;
import com.yzm.property.materials.service.IssueTempDtlItemService;
import com.yzm.property.materials.service.IssueTempDtlService;
import com.yzm.property.materials.service.MaterialsInfoBomService;
import com.yzm.property.materials.service.MaterialsInfoService;
import com.yzm.property.materials.utils.MaterialsUtil;
import com.yzm.property.system.entity.SysUser;
import com.yzm.property.system.repository.SysUserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.sql.*;
import java.util.Date;
import java.util.*;

@Service
public class IssueTempDtlItemImpl extends BaseService<IssueTempDtlItem, Long> implements IssueTempDtlItemService {

    @Autowired
    private IssueTempDtlItemRepository issueTempDtlItemRepository;

    @Override
    public BaseRepository<IssueTempDtlItem, Long> getRepository() {
        return issueTempDtlItemRepository;
    }
}
