package com.yzm.property.materials.service;

import com.yzm.framework.base.IBaseService;
import com.yzm.property.materials.entity.OutWarehouseMaterialsItem;
import org.springframework.data.jpa.repository.Query;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

public interface OutWarehouseItemService extends IBaseService<OutWarehouseMaterialsItem,Long> {
    List<OutWarehouseMaterialsItem> findByRecordNumber(String recordNumber);

    List<OutWarehouseMaterialsItem> findByRecordNumberGroupByAssetsNumber(String recordNumber);


    List<OutWarehouseMaterialsItem> findByRecordNumberAndAssetsNumber(String recordNumber, String assetsNumber);

    List<OutWarehouseMaterialsItem> findByKitNo(String kitNo);

    List<OutWarehouseMaterialsItem> findByKitNoAndAssetsNumber(String kitNo,String assetsNumber);

    int findByPrimaryIdAndCountComparison(String recordNumber);

//    List<OutWarehouseMaterialsItem> findAllInfo(Date firstDayOfLastMonth, Date lastDayOfLastMonth);
}
