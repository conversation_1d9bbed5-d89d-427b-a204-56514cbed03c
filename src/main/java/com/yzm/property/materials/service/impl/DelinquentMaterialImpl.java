package com.yzm.property.materials.service.impl;

import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.property.materials.entity.*;
import com.yzm.property.materials.repository.*;
import com.yzm.property.materials.service.BindingCarService;
import com.yzm.property.materials.service.DelinquentMaterialService;
import com.yzm.property.materials.task.DatabaseHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class DelinquentMaterialImpl extends BaseService<DelinquentMaterial, Long> implements DelinquentMaterialService {
    @Autowired
    private DelinquentMaterialRepository delinquentMaterialRepository;
    @Autowired
    private IssueTempDtlItemRepository issueTempDtlItemRepository;
    @Autowired
    private IssueTempDtlTubeRepository issueTempDtlTubeRepository;
    @Autowired
    private ProductionPlanBomRepository productionPlanBomRepository;
    @Autowired
    private MaterialsRepertoryRepository materialsRepertoryRepository;
    @Autowired
    private MaterialsInfoBomRepository materialsInfoBomRepository;
    private static Logger log = LoggerFactory.getLogger(DelinquentMaterialImpl.class);

    public BaseRepository<DelinquentMaterial, Long> getRepository() {
        return delinquentMaterialRepository;
    }

    public static void main(String[] args) {
        Map<String, BigDecimal> currentStockMap = new HashMap<>();
        Map<String, Object> inventory = new HashMap<>();
        inventory.put("now_count", "199");
        BigDecimal availableStock = currentStockMap.getOrDefault(1, new BigDecimal(inventory.get("now_count").toString()));
        System.out.println("aaaaa");
    }

    /**
     * 计算欠料信息，按日期处理发料计划，并逐日扣减库存。
     *
     * @return 按日期分组的欠料信息。
     */
    public List<DelinquentMaterial> calculateMaterialShortage() {
        List<DelinquentMaterial> shortageInfoByDate = new ArrayList<>();
        // 初始化库存
        Map<String, BigDecimal> currentStockMap = new HashMap<>();

        List<Map<String, Object>> byAssetsNumberGroupByAssetsNumberList = materialsRepertoryRepository.findByAssetsNumberGroupByAssetsNumberList();
        for (Map<String, Object> map : byAssetsNumberGroupByAssetsNumberList) {
            currentStockMap.put(map.get("assets_number").toString(), new BigDecimal(map.get("now_count").toString()));
        }

        Map<String, BigDecimal> currentStockMapTubes = new HashMap<>();
        List<IssueTempDtlTube> issueTempDtlTubes = issueTempDtlTubeRepository.findAllOrderByPlannedIssueDt();
        for (IssueTempDtlTube issuePlan : issueTempDtlTubes) {
            // 找到库存中相对应的材料
//            Map<String, Object> inventory = materialsRepertoryRepository.findByAssetsNumberGroupByAssetsNumber(issuePlan.getMatlNo());
//            log.error("-----------胶管欠料运行" + shortageInfoByDate.size());

            if (!StringUtils.isEmpty(currentStockMap.get(issuePlan.getMatlNo()))) {
                BigDecimal availableStock = currentStockMap.get(issuePlan.getMatlNo());
                BigDecimal requiredQty = issuePlan.getTotQty().subtract((issuePlan.getQuantityDelivered().multiply(issuePlan.getCutLen())));

                // 计算欠料量
                if (availableStock.compareTo(requiredQty) >= 0) {
                    // 库存足够，直接扣减
                    availableStock = availableStock.subtract(requiredQty);
                } else {
                    // 库存不足，计算欠料
                    BigDecimal shortageQty = requiredQty.subtract(availableStock);
                    availableStock = BigDecimal.ZERO;

                    DelinquentMaterial shortageInfo = new DelinquentMaterial();
                    shortageInfo.setAssetsNumber(issuePlan.getMatlNo());
                    shortageInfo.setAssetsName(issuePlan.getMatlNm());
//                    shortageInfo.setKitNo(issuePlan.getKitNo());
                    shortageInfo.setLotNo(issuePlan.getWorkOrdNo());
                    shortageInfo.setStatus(0L);
                    shortageInfo.setDelinquentCount(shortageQty);
                    shortageInfo.setPlannedIssueDt(issuePlan.getPlanIssDate());
//                    shortageInfo.setAssetsUnitName(issuePlan.getBomUnit());
                    shortageInfo.setCreateTime(new Date());
                    shortageInfoByDate.add(shortageInfo);
                    // 按发料时间分组欠料信息
//                    shortageInfoByDate
//                            .computeIfAbsent(issuePlan.getPlannedIssueDt(), k -> new ArrayList<>())
//                            .add(shortageInfo);
                }

                // 更新剩余库存，用于下一天的发料计划
                currentStockMapTubes.put(issuePlan.getMatlNo(), availableStock);
            } else {
                DelinquentMaterial shortageInfo = new DelinquentMaterial();
                shortageInfo.setAssetsNumber(issuePlan.getMatlNo());
                shortageInfo.setAssetsName(issuePlan.getMatlNm());
//                shortageInfo.setKitNo(issuePlan.getKitNo());
                shortageInfo.setLotNo(issuePlan.getWorkOrdNo());
                shortageInfo.setStatus(0L);
                shortageInfo.setDelinquentCount(issuePlan.getTotQty());
                shortageInfo.setPlannedIssueDt(issuePlan.getPlanIssDate());
//                shortageInfo.setAssetsUnitName(issuePlan.getBomUnit());
                shortageInfo.setCreateTime(new Date());
                // 按发料时间分组欠料信息
                if (StringUtils.isEmpty(issuePlan.getTaskStatus())) {
                    shortageInfoByDate.add(shortageInfo);
                } else {
                    if (issuePlan.getTaskStatus() != 1L) {
                        shortageInfoByDate.add(shortageInfo);
                    }
                }
//                shortageInfoByDate
//                        .computeIfAbsent(issuePlan.getPlannedIssueDt(), k -> new ArrayList<>())
//                        .add(shortageInfo);
            }

            issuePlan.setTaskStatus(1L);
            issueTempDtlTubeRepository.saveAndFlush(issuePlan);
        }

        List<IssueTempDtlItem> issuePlans = issueTempDtlItemRepository.findAllOrderByPlannedIssueDt();
//        Map<Date, List<DelinquentMaterial>> shortageInfoByDate = new HashMap<>();


        List<IssueTempDtlItem> updateList = new ArrayList<>();
        for (IssueTempDtlItem issuePlan : issuePlans) {
            // 找到库存中相对应的材料
//            Map<String, Object> inventory = materialsRepertoryRepository.findByAssetsNumberGroupByAssetsNumber(issuePlan.getMaterialNo());
//            log.error("-----------物料欠料运行" + shortageInfoByDate.size());
            if (!StringUtils.isEmpty(currentStockMap.get(issuePlan.getMaterialNo()))) {
                BigDecimal availableStock = currentStockMap.get(issuePlan.getMaterialNo());
                BigDecimal requiredQty = issuePlan.getIssueQty().subtract(issuePlan.getQuantityDelivered());

                // 计算欠料量
                if (availableStock.compareTo(requiredQty) >= 0) {
                    // 库存足够，直接扣减
                    availableStock = availableStock.subtract(requiredQty);
                } else {
                    // 库存不足，计算欠料
                    BigDecimal shortageQty = requiredQty.subtract(availableStock);
                    availableStock = BigDecimal.ZERO;

                    DelinquentMaterial shortageInfo = new DelinquentMaterial();
                    shortageInfo.setAssetsNumber(issuePlan.getMaterialNo());
                    shortageInfo.setAssetsName(issuePlan.getMaterialNm());
                    shortageInfo.setKitNo(issuePlan.getKitNo());
                    shortageInfo.setLotNo(issuePlan.getLotNo());
                    shortageInfo.setStatus(0L);
                    shortageInfo.setDelinquentCount(shortageQty);
                    shortageInfo.setPlannedIssueDt(issuePlan.getPlannedIssueDt());
                    shortageInfo.setAssetsUnitName(issuePlan.getBomUnit());
                    shortageInfo.setCreateTime(new Date());
                    shortageInfoByDate.add(shortageInfo);
                    // 按发料时间分组欠料信息
//                    shortageInfoByDate
//                            .computeIfAbsent(issuePlan.getPlannedIssueDt(), k -> new ArrayList<>())
//                            .add(shortageInfo);
                }

                // 更新剩余库存，用于下一天的发料计划
                currentStockMap.put(issuePlan.getMaterialNo(), availableStock);
            } else {
                DelinquentMaterial shortageInfo = new DelinquentMaterial();
                shortageInfo.setAssetsNumber(issuePlan.getMaterialNo());
                shortageInfo.setAssetsName(issuePlan.getMaterialNm());
                shortageInfo.setKitNo(issuePlan.getKitNo());
                shortageInfo.setLotNo(issuePlan.getLotNo());
                shortageInfo.setStatus(0L);
                shortageInfo.setDelinquentCount(issuePlan.getIssueQty());
                shortageInfo.setPlannedIssueDt(issuePlan.getPlannedIssueDt());
                shortageInfo.setAssetsUnitName(issuePlan.getBomUnit());
                shortageInfo.setCreateTime(new Date());
                // 按发料时间分组欠料信息
                if (issuePlan.getTaskStatus() != 1L) {
                    shortageInfoByDate.add(shortageInfo);
                }
//                shortageInfoByDate
//                        .computeIfAbsent(issuePlan.getPlannedIssueDt(), k -> new ArrayList<>())
//                        .add(shortageInfo);
            }

            issuePlan.setTaskStatus(1L);
            updateList.add(issuePlan);
            issueTempDtlItemRepository.saveAndFlush(issuePlan);
        }

        log.error("-----------欠料条数" + updateList.size());

        Thread thread = new updateTHe(updateList, issueTempDtlItemRepository);
        thread.start();
        return shortageInfoByDate;
    }

    class updateTHe extends Thread {
        private List<IssueTempDtlItem> updateList;
        private IssueTempDtlItemRepository issueTempDtlItemRepository;

        public updateTHe(List<IssueTempDtlItem> updateList, IssueTempDtlItemRepository issueTempDtlItemRepository) {
            this.updateList = updateList;
            this.issueTempDtlItemRepository = issueTempDtlItemRepository;
        }

        @Override
        public void run() {
            for (IssueTempDtlItem item : updateList) {
                issueTempDtlItemRepository.saveAndFlush(item);
            }
        }
    }

    class updateTHeNew extends Thread {
        private List<ProductionPlanBom> updateList;
        private ProductionPlanBomRepository issueTempDtlItemRepository;

        public updateTHeNew(List<ProductionPlanBom> updateList, ProductionPlanBomRepository issueTempDtlItemRepository) {
            this.updateList = updateList;
            this.issueTempDtlItemRepository = issueTempDtlItemRepository;
        }

        @Override
        public void run() {
            for (ProductionPlanBom item : updateList) {
                issueTempDtlItemRepository.saveAndFlush(item);
            }
        }
    }

    @Override
    public List<DelinquentMaterial> findByAssetsNumber(String assetsNumber) {
        return delinquentMaterialRepository.findByAssetsNumberOrderByCreateTimeAsc(assetsNumber);
    }

    @Override
    public List<DelinquentMaterial> findByKitNoAndAssetsNumber(String kitNo, String assetsNumber) {
        return delinquentMaterialRepository.findByKitNoAndAssetsNumber(kitNo, assetsNumber);
    }

    @Override
    public List<DelinquentMaterial> calculateMaterialShortageNew() {
        List<DelinquentMaterial> shortageInfoByDate = new ArrayList<>();
        // 初始化库存
        Map<String, BigDecimal> currentStockMap = new HashMap<>();
        Map<String, BigDecimal> currentStockMapBom = new HashMap<>();
        List<Map<String, Object>> materialsInfoBoms = materialsInfoBomRepository.findByAssetsNumberGroupByAssetsNumberList();
        for (Map<String, Object> materialsInfoBom : materialsInfoBoms) {
            BigDecimal bigDecimal = (BigDecimal) materialsInfoBom.get("now_count");
            String assets_number = String.valueOf(materialsInfoBom.get("assets_number"));
            currentStockMapBom.put(assets_number, bigDecimal);
        }
        List<Map<String, Object>> byAssetsNumberGroupByAssetsNumberList = materialsRepertoryRepository.findByAssetsNumberGroupByAssetsNumberList();
        for (Map<String, Object> map : byAssetsNumberGroupByAssetsNumberList) {
//            BigDecimal sum = materialsInfoBomRepository.findByAssetsNumberSum(map.get("assets_number").toString());
//            if (sum == null) {
                currentStockMap.put(map.get("assets_number").toString(), new BigDecimal(map.get("now_count").toString()));
//            } else {
//                currentStockMap.put(map.get("assets_number").toString(), new BigDecimal(map.get("now_count").toString()).add(sum));
//            }
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
        List<ProductionPlanBom> all = productionPlanBomRepository.findOrderByIssueDateAsc(simpleDateFormat.format(new Date()));
        List<ProductionPlanBom> updateList = new ArrayList<>();

        for (ProductionPlanBom issuePlan : all) {
            // 找到库存中相对应的材料
//            Map<String, Object> inventory = materialsRepertoryRepository.findByAssetsNumberGroupByAssetsNumber(issuePlan.getMaterialNo());
//            log.error("-----------物料欠料运行" + shortageInfoByDate.size());
            if (!StringUtils.isEmpty(currentStockMap.get(issuePlan.getMaterialNo()))) {
                BigDecimal availableStock = currentStockMap.get(issuePlan.getMaterialNo());
                BigDecimal requiredQty = issuePlan.getBomQty();

                // 计算欠料量
                if (availableStock.compareTo(requiredQty) >= 0) {
                    // 库存足够，直接扣减
                    availableStock = availableStock.subtract(requiredQty);
                } else {
                    // 库存不足，计算欠料
                    BigDecimal shortageQty = requiredQty.subtract(availableStock);
                    availableStock = BigDecimal.ZERO;

                    DelinquentMaterial shortageInfo = new DelinquentMaterial();
                    shortageInfo.setAssetsNumber(issuePlan.getMaterialNo());
                    shortageInfo.setLotNo(issuePlan.getBatchNo());
                    shortageInfo.setStatus(0L);
                    shortageInfo.setDelinquentCount(shortageQty);
                    shortageInfo.setSumData(requiredQty);
                    shortageInfo.setQuantityArrived(shortageInfo.getSumData().subtract(shortageInfo.getDelinquentCount()));
                    shortageInfo.setPlannedIssueDt(issuePlan.getIssueDate());
                    shortageInfo.setProductNo(issuePlan.getProductNo());
                    shortageInfo.setAssetsUnitName(issuePlan.getBomUnit());
                    shortageInfo.setCreateTime(new Date());
                    shortageInfo.setAssetsName(issuePlan.getMaterialNm());
                    shortageInfo.setTransitCount(currentStockMapBom.get(issuePlan.getMaterialNo()));
                    if (!StringUtils.isEmpty(issuePlan.getDeliveryDate())) {
                        shortageInfo.setNaageDate(issuePlan.getDeliveryDate());
                    }
                    shortageInfoByDate.add(shortageInfo);
                    // 按发料时间分组欠料信息
//                    shortageInfoByDate
//                            .computeIfAbsent(issuePlan.getPlannedIssueDt(), k -> new ArrayList<>())
//                            .add(shortageInfo);
                }

                // 更新剩余库存，用于下一天的发料计划
                currentStockMap.put(issuePlan.getMaterialNo(), availableStock);
            } else {
                DelinquentMaterial shortageInfo = new DelinquentMaterial();
                shortageInfo.setAssetsNumber(issuePlan.getMaterialNo());
//                shortageInfo.setAssetsName(issuePlan.getMaterialNm());
//                shortageInfo.setKitNo(issuePlan.getKitNo());
                shortageInfo.setAssetsNumber(issuePlan.getMaterialNo());
                shortageInfo.setLotNo(issuePlan.getBatchNo());
                shortageInfo.setProductNo(issuePlan.getProductNo());
                shortageInfo.setAssetsName(issuePlan.getMaterialNm());
                shortageInfo.setTransitCount(currentStockMapBom.get(issuePlan.getMaterialNo()));

                shortageInfo.setStatus(0L);
                shortageInfo.setSumData(issuePlan.getBomQty());
                shortageInfo.setQuantityArrived(new BigDecimal(0));
                shortageInfo.setProductNo(issuePlan.getProductNo());
                shortageInfo.setDelinquentCount(issuePlan.getBomQty());
                shortageInfo.setPlannedIssueDt(issuePlan.getIssueDate());
                shortageInfo.setAssetsUnitName(issuePlan.getBomUnit());
                shortageInfo.setCreateTime(new Date());
                if (!StringUtils.isEmpty(issuePlan.getDeliveryDate())) {
                    shortageInfo.setNaageDate(issuePlan.getDeliveryDate());
                }

                // 按发料时间分组欠料信息
//                if (issuePlan.getTaskStatus() != 1L) {
                shortageInfoByDate.add(shortageInfo);
//                }
//                shortageInfoByDate
//                        .computeIfAbsent(issuePlan.getPlannedIssueDt(), k -> new ArrayList<>())
//                        .add(shortageInfo);
            }

            updateList.add(issuePlan);
//            issueTempDtlItemRepository.saveAndFlush(issuePlan);
        }
//        List<IssueTempDtlItem> issuePlans = issueTempDtlItemRepository.findAllOrderByPlannedIssueDt();
//        Map<Date, List<DelinquentMaterial>> shortageInfoByDate = new HashMap<>();

        log.error("-----------欠料条数" + updateList.size());

        Thread thread = new updateTHeNew(updateList, productionPlanBomRepository);
        thread.start();
        return shortageInfoByDate;
    }

    @Override
    public DelinquentMaterial findByAssetsNumberAndLotNo(String assetsNumber, String lotNo) {
        return delinquentMaterialRepository.findByAssetsNumberAndLotNo(assetsNumber, lotNo);
    }
}
