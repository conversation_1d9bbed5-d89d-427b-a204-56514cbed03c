package com.yzm.property.materials.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yzm.common.utils.PageUtil;
import com.yzm.common.utils.QueryHelp;
import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.framework.base.CriteriaBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.materials.criteria.MaterialsBomCriteria;
import com.yzm.property.materials.entity.MaterialsInfo;
import com.yzm.property.materials.entity.MaterialsInfoBom;
import com.yzm.property.materials.repository.MaterialsInfoBomRepository;
import com.yzm.property.materials.repository.MaterialsInfoRepository;
import com.yzm.property.materials.service.MaterialsInfoBomService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 */
@Service
public class MaterialsInfoBomServiceImpl extends BaseService<MaterialsInfoBom, Long> implements MaterialsInfoBomService {

    @Autowired
    private MaterialsInfoBomRepository bomRepository;
    @Autowired
    private MaterialsInfoRepository materialsInfoRepository;

    @Override
    public BaseRepository<MaterialsInfoBom, Long> getRepository() {
        return bomRepository;
    }

    @Override
    public ResponseData addMaterialsInfoBom(MaterialsInfo materialsInfo) {
        MaterialsInfo oldMaterialsInfo = materialsInfoRepository.getOne(materialsInfo.getId());
        String itemJson = materialsInfo.getItemJson();
        oldMaterialsInfo.setBomNumber("BOM" + System.currentTimeMillis());
        List<MaterialsInfoBom> materialsInfoBoms = JSONArray.parseArray(itemJson, MaterialsInfoBom.class);
        for (MaterialsInfoBom materialsInfoBom : materialsInfoBoms) {
            materialsInfoBom.setParentId(materialsInfo.getId());
            bomRepository.save(materialsInfoBom);
        }
        materialsInfoRepository.saveAndFlush(oldMaterialsInfo);
        return ResponseData.success("新增成功");
    }

    @Override
    public ResponseData updateMaterialsInfo(MaterialsInfo materialsInfoBomInfo) {
        return null;
    }

    @Override
    public List<MaterialsInfoBom> findByParentId(Long id) {
        return bomRepository.findByParentId(id);
    }

    @Override
    public List<MaterialsInfoBom> findByAssetsNumber(String assetsNumber) {
        return bomRepository.findByAssetsNumber(assetsNumber);
    }

    @Override
    public boolean handleDataSave(MaterialsInfoBom dataJson) {
        List<MaterialsInfoBom> materialsInfoBoms = bomRepository.findByContainerNumber(dataJson.getContainerNumber());
        for (MaterialsInfoBom materialsInfoBom : materialsInfoBoms) {
            materialsInfoBom.setAssetsStatus(dataJson.getAssetsStatus());
            bomRepository.saveAndFlush(materialsInfoBom);
        }
        return true;

    }
    @Override
    public Map<String, Object> findAllByPageGroup(MaterialsBomCriteria bean, Pageable page) {

        List<MaterialsInfoBom> groupedResults = bomRepository.findGroupedResults(bean.getAssetsNumber(), bean.getAssetsName(), bean.getInvoiceNumber(), bean.getContainerNumber(), page);
        int l = bomRepository.countGroupedResults(bean.getAssetsNumber(), bean.getAssetsName(), bean.getInvoiceNumber(), bean.getContainerNumber());
        Map<String,Object> map = new HashMap<>();
        map.put("list",groupedResults);
        map.put("totalCount",l);
//
//        Page<MaterialsInfoBom> datas = getRepository().findAll(new Specification<MaterialsInfoBom>() {
//            @Override
//            public Predicate toPredicate(Root<MaterialsInfoBom> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
//                query.groupBy(root.get("containerNumber")); // 在这里替换 "yourGroupByField" 为您要分组的字段名
//                return QueryHelp.getPredicate(root, bean, criteriaBuilder);
//            }
//        }, page);
        return map;
    }

    @Override
    public List<MaterialsInfoBom> findByContainerNumber(String containerNumber) {
        return bomRepository.findByContainerNumber(containerNumber);
    }

    @Override
    public List<MaterialsInfoBom> findGroupByNumber() {
        return bomRepository.findGroupByNumber();
    }

    @Override
    public List<MaterialsInfoBom> findByInvoiceNumberAndAssetsNumber(String invoiceNumber, String assetsNumber) {
        return bomRepository.findByInvoiceNumberAndAssetsNumber(invoiceNumber,assetsNumber);
    }

    @Override
    public MaterialsInfoBom findByInvoiceNumberAndAssetsNumberAndMaterialLotNo(String invoiceNumber, String assetsNumber, String materialLotNo) {
        return bomRepository.findByInvoiceNumberAndAssetsNumberAndMaterialLotNo(invoiceNumber,assetsNumber,materialLotNo);
    }

    @Override
    public List<MaterialsInfoBom> findByContainerNumberAndInvoiceNumber(String containerNumber, String invoiceNumber) {
        return bomRepository.findByContainerNumberAndInvoiceNumber(containerNumber,invoiceNumber);
    }

}
