package com.yzm.property.materials.service.impl;

import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.fastjson.JSONArray;
import com.yzm.common.utils.OrderUtils;
import com.yzm.common.utils.PageUtil;
import com.yzm.common.utils.ShiroUtils;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.framework.exception.RxcException;
import com.yzm.property.basis.entity.AssetsInfo;
import com.yzm.property.business.entity.ReceiptNote;
import com.yzm.property.business.entity.ReceiptNoteItem;
import com.yzm.property.business.repository.BorrowReturnItemRepository;
import com.yzm.property.materials.entity.*;
import com.yzm.property.materials.repository.*;
import com.yzm.property.materials.service.DelinquentMaterialService;
import com.yzm.property.materials.service.InStorageService;
import com.yzm.property.materials.utils.MaterialsUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:47
 */
@Service
public class InStorageMaterialsServiceImpl extends BaseService<InStorageMaterials, Long> implements InStorageService {

    @Autowired
    private MaterialsInfoRepository materialsInfoRepository;
    @Autowired
    private MaterialsInfoBomRepository materialsInfoBomRepository;
    @Autowired
    private InStorageMaterialsRepository inStorageRepository;
    @Autowired
    private InStorageItemMaterialsRepository inStorageItemRepository;
    @Autowired
    private MaterialsStandingBookRepository consumableStandingBookRepository;
    @Autowired
    private BorrowReturnItemRepository borrowReturnItemRepository;

    @Autowired
    private MaterialsRepertoryRepository materialsRepertoryRepository;
    @Autowired
    private MaterialsListRepository consumableListRepository;

    @Autowired
    private DelinquentMaterialService delinquentMaterialService;
    @Autowired
    private EntityManager entityManager;

    @Override
    public BaseRepository<InStorageMaterials, Long> getRepository() {
        return inStorageRepository;
    }

    @Override
    @Transactional
    public InStorageMaterials save(InStorageMaterials inStorage) {
        inStorage = inStorageRepository.save(inStorage);
        // AssetsLogUtil.createAssetsLog(null,null,inStorage.getId(),inStorage.getOtherId(),inStorage.getAssetsName(),materialsInfo.getAssetsSource());
        return inStorage;
    }

    @Override
    public Map showAllInStorageInfo(String keyword, List<String> type, PageBean pageBean) {
        HashMap<String, Object> map = new HashMap<>();
//        if (keyword == null || "".equals(keyword)) {
//            Page<InStorageMaterials> result = inStorageRepository.findByOrderByCreateTimeDesc(pageBean.getPagable());
//            map.put("list", result.getContent());
//            map.put("totalCount", result.getTotalElements());
//        } else {
        List<InStorageMaterials> result = inStorageRepository.findByRecordNumberContainsOrderByCreateTimeDesc(keyword, type, (pageBean.getPage() - 1) * pageBean.getLimit(), pageBean.getLimit());
        map.put("list", result);
        map.put("totalCount", inStorageRepository.countGroupedResults(keyword, type));
//        }


        return map;
    }

    @Override
    @Transactional
    public ResponseData saveInStorage(InStorageMaterials inStorage) {
        //单据编号
        String parameterNo = OrderUtils.getRuKuCode();
        inStorage.setRecordNumber(parameterNo);//入库编号
        inStorage.setInStoragePeople(ShiroUtils.getUserInfo().getName());//入库人
        inStorage.setStatus(6L);
        inStorage.setInStorageDate(new Date());

        //获取前台传来的表格信息
        List<InStorageItemMaterials> inStorageItem = JSONArray.parseArray(inStorage.getItemJson(), InStorageItemMaterials.class);

        // 批量检查资产编码是否存在
        List<String> assetCodes = inStorageItem.stream()
                .map(item -> item.getAssetsCode())
                .collect(Collectors.toList());
        List<MaterialsRepertory> existingAssets = materialsRepertoryRepository.findByAssetsCodeInOrAssetsRfidIn(assetCodes, assetCodes);
        if (!existingAssets.isEmpty()) {
            throw new RxcException(existingAssets.get(0).getAssetsCode() + "：已经存在库存请删除后入库!");
        }

        // 检查重复条码
        Set<String> uniqueCodes = new HashSet<>();
        for (InStorageItemMaterials item : inStorageItem) {
            if (!uniqueCodes.add(item.getAssetsCode().trim())) {
                throw new RxcException("存在重复条码信息请重新扫描!");
            }
        }

        // 批量准备数据
        List<MaterialsRepertory> repertoryList = new ArrayList<>();
        List<InStorageItemMaterials> itemList = new ArrayList<>();
        List<MaterialsListEntity> materialsList = new ArrayList<>();
        BigDecimal sum = new BigDecimal(0);
        Date now = new Date();

        // 批量获取发票信息
        Map<String, List<MaterialsInfoBom>> invoiceBomMap = new HashMap<>();
        Map<String, List<DelinquentMaterial>> delinquentMaterialMap = new HashMap<>();

        for (InStorageItemMaterials storageItem : inStorageItem) {
            // 准备库存记录
            MaterialsRepertory consumableRepertory = new MaterialsRepertory();
            BeanUtils.copyProperties(storageItem, consumableRepertory);
            consumableRepertory.setNowRepertory(storageItem.getInStorageCount());
            consumableRepertory.setWarehouseInfo(storageItem.getStorageWarehouse());
            consumableRepertory.setAreaInfo(storageItem.getStorageArea());
            consumableRepertory.setAreaInfoId(storageItem.getStorageAreaId());
            consumableRepertory.setMaterialsInfoId(storageItem.getId());
            consumableRepertory.setNowCount(new BigDecimal(1));
            consumableRepertory.setCreateTime(now);
            consumableRepertory.setOperationNumber(parameterNo);
            consumableRepertory.setModeOfTrade(inStorage.getModeOfTrade());
            consumableRepertory.setDepartmentCode(inStorage.getDepartmentCode());
            consumableRepertory.setInvoiceNumber(inStorage.getInvoiceNumber());
            consumableRepertory.setId(null);
            repertoryList.add(consumableRepertory);

            // 准备入库子表记录
            storageItem.setRecordNumber(parameterNo);
            storageItem.setModeOfTrade(inStorage.getModeOfTrade());
            storageItem.setDepartmentCode(inStorage.getDepartmentCode());
            storageItem.setInvoiceNumber(inStorage.getInvoiceNumber());
            storageItem.setCreateTime(now);
            storageItem.setId(null);
            itemList.add(storageItem);

            // 准备材料清单记录
            MaterialsListEntity materialsListEntity = new MaterialsListEntity();
            BeanUtils.copyProperties(storageItem, materialsListEntity);
            materialsListEntity.setOperationName(MaterialsUtil.OPERATION_RUKU);
            materialsListEntity.setOperationOtherId(parameterNo);
            materialsListEntity.setReason(inStorage.getInStorageReson());
            materialsListEntity.setOperationId(inStorage.getId());
            materialsListEntity.setCountInfo(storageItem.getInStorageCount());
            materialsListEntity.setNowRepertory(storageItem.getInStorageCount());
            materialsListEntity.setUserName(inStorage.getInStoragePeople());
            materialsListEntity.setOutDate(inStorage.getInStorageDate());
            materialsListEntity.setAreaInfo(storageItem.getStorageArea());
            materialsListEntity.setWarehouseInfo(storageItem.getStorageWarehouse());
            materialsListEntity.setBatchNumber(storageItem.getBatchNumber());
            materialsListEntity.setModeOfTrade(inStorage.getModeOfTrade());
            materialsListEntity.setCreateTime(now);
            materialsListEntity.setId(null);
            materialsList.add(materialsListEntity);

            sum = sum.add(storageItem.getInStorageCount());

            // 收集发票和欠料信息
            if (!invoiceBomMap.containsKey(storageItem.getAssetsNumber())) {
                invoiceBomMap.put(storageItem.getAssetsNumber(),
                        materialsInfoBomRepository.findByInvoiceNumberAndAssetsNumber(inStorage.getInvoiceNumber(), storageItem.getAssetsNumber().replaceAll("\\s+", "")));
            }
            if (!delinquentMaterialMap.containsKey(storageItem.getAssetsNumber())) {
                delinquentMaterialMap.put(storageItem.getAssetsNumber(),
                        delinquentMaterialService.findByAssetsNumber(storageItem.getAssetsNumber()));
            }
        }

        // 批量保存数据
        materialsRepertoryRepository.saveAll(repertoryList);
        inStorageItemRepository.saveAll(itemList);
        consumableListRepository.saveAll(materialsList);

        // 处理发票和欠料信息
        for (InStorageItemMaterials storageItem : inStorageItem) {
            List<MaterialsInfoBom> bomList = invoiceBomMap.get(storageItem.getAssetsNumber());
            if (bomList.isEmpty()) {
                throw new ExcelAnalysisException(inStorage.getInvoiceNumber() + "发票中未找到" + storageItem.getAssetsNumber() + "材料。不可入库！");
            }

            BigDecimal inStorageCount = storageItem.getInStorageCount();
            BigDecimal remainingCount = inStorageCount;
// 处理发票信息
            Iterator<MaterialsInfoBom> iterator = bomList.iterator();
            while (iterator.hasNext() && remainingCount.compareTo(BigDecimal.ZERO) > 0) {
                MaterialsInfoBom bom = iterator.next();

                BigDecimal quantityArrived = bom.getQuantityArrived() != null ? bom.getQuantityArrived() : BigDecimal.ZERO;
                BigDecimal subtract = bom.getDosage().subtract(quantityArrived).subtract(remainingCount);

                if (subtract.compareTo(BigDecimal.ZERO) > 0) {
                    bom.setQuantityArrived(quantityArrived.add(remainingCount));
                    remainingCount = BigDecimal.ZERO;
                } else {
                    remainingCount = subtract.abs();
                    iterator.remove();  // Safe removal using iterator
                    materialsInfoBomRepository.delete(bom);
                }
            }
//            // 处理发票信息
//            for (MaterialsInfoBom bom : bomList) {
//                if (remainingCount.compareTo(BigDecimal.ZERO) <= 0) break;
//
//                BigDecimal quantityArrived = bom.getQuantityArrived() != null ? bom.getQuantityArrived() : BigDecimal.ZERO;
//                BigDecimal subtract = bom.getDosage().subtract(quantityArrived).subtract(remainingCount);
//
//                if (subtract.compareTo(BigDecimal.ZERO) > 0) {
//                    bom.setQuantityArrived(quantityArrived.add(remainingCount));
//                    remainingCount = BigDecimal.ZERO;
//                } else {
//                    remainingCount = subtract.abs();
//                    bomList.remove(bom);
//                    materialsInfoBomRepository.delete(bom);
//                    continue;
//                }
//            }

            if (remainingCount.compareTo(BigDecimal.ZERO) != 0) {
                throw new ExcelAnalysisException(storageItem.getAssetsNumber() + "材料在途发票数量不足-" + storageItem.getAssetsCode() + "条码材料。不可入库！");
            }

            // 处理欠料信息
            BigDecimal remainingDelinquentCount = inStorageCount;
            List<DelinquentMaterial> delinquentMaterials = delinquentMaterialMap.get(storageItem.getAssetsNumber());

            for (DelinquentMaterial delinquent : delinquentMaterials) {
                if (remainingDelinquentCount.compareTo(BigDecimal.ZERO) <= 0) break;

                BigDecimal quantityArrived = delinquent.getQuantityArrived() != null ? delinquent.getQuantityArrived() : BigDecimal.ZERO;
                BigDecimal subtract = delinquent.getDelinquentCount().subtract(quantityArrived).subtract(remainingDelinquentCount);

                if (subtract.compareTo(BigDecimal.ZERO) > 0) {
                    delinquent.setQuantityArrived(quantityArrived.add(remainingDelinquentCount));
                    remainingDelinquentCount = BigDecimal.ZERO;
                } else {
                    remainingDelinquentCount = subtract.abs();
                    delinquentMaterialService.delete(delinquent);
                }
            }
        }

        // 更新入库总数
        inStorage.setInStorageTotal(sum);
        inStorageRepository.saveAndFlush(inStorage);

        return ResponseData.success("入库成功");
    }

    @Override
    @Transactional
    public ResponseData deleteInfo(Long[] ids) {
        for (Long id : ids) {
            InStorageMaterials one = inStorageRepository.getOne(id);
            if (one.getStatus() != 1L) {
                return ResponseData.error(one.getRecordNumber() + "当前单据状态无法删除");
            }
            List<InStorageItemMaterials> byRecordNumber = inStorageItemRepository.findByRecordNumber(one.getRecordNumber());
            for (InStorageItemMaterials inStorageItem : byRecordNumber) {
//                MaterialsRepertory byAssetsCodeOrAssetsRfid = materialsRepertoryRepository.findByAssetsCodeOrAssetsRfid(inStorageItem.getAssetsCode(), inStorageItem.getAssetsCode());
//
//                if (!byAssetsCodeOrAssetsRfid.getOperationNumber().equals(one.getRecordNumber())) {
//                    return ResponseData.error(one.getRecordNumber() + "当前单据无法删除-物料已经变动");
//                }
//                materialsRepertoryRepository.delete(byAssetsCodeOrAssetsRfid);
                inStorageItemRepository.delete(inStorageItem);
            }
            inStorageRepository.delete(one);
        }
        return ResponseData.success("删除成功！");
    }

    @Override
    public ResponseData saveInStorageReturning(InStorageMaterials inStorage) {
        //单据编号
        String parameterNo = OrderUtils.getRuKuCode();
        inStorage.setRecordNumber(parameterNo);//入库编号
        inStorage.setApplicant(ShiroUtils.getUserInfo().getName());//申请人
        inStorage.setStatus(1L);
        inStorage.setInStatus(0L);
        inStorage.setInStorageDate(new Date());
        //获取前台传来的表格信息
        List<InStorageItemMaterials> inStorageItem = JSONArray.parseArray(inStorage.getItemJson(), InStorageItemMaterials.class);
        BigDecimal sum = new BigDecimal(0);
        for (InStorageItemMaterials storageItem : inStorageItem) {
//            MaterialsInfo materialsInfo = materialsInfoRepository.getOne(storageItem.getId());
//            MaterialsRepertory consumableRepertory = new MaterialsRepertory();
//            BeanUtils.copyProperties(materialsInfo, consumableRepertory);
//            consumableRepertory.setNowRepertory(storageItem.getInStorageCount());//当前库存
//            consumableRepertory.setWarehouseInfo(materialsInfo.getStorageWarehouse());
//            consumableRepertory.setAreaInfo(materialsInfo.getStorageArea());
//            consumableRepertory.setAreaInfoId(materialsInfo.getStorageAreaId());
//            consumableRepertory.setMaterialsInfoId(materialsInfo.getId());
//            consumableRepertory.setNowCount(1L);
//            consumableRepertory.setCreateTime(new Date());
//            consumableRepertory.setId(null);
//            consumableRepertory.setOperationNumber(inStorage.getRecordNumber());
//            consumableRepertory.setAssetsCode(storageItem.getAssetsCode());
//            consumableRepertory.setBatchNumber(storageItem.getBatchNumber());
//            consumableRepertory.setModeOfTrade(inStorage.getModeOfTrade());
//            //通过耗材查库存
//            materialsRepertoryRepository.saveAndFlush(consumableRepertory);
            sum = sum.add(storageItem.getInStorageCount());
            //新建入库子表
            InStorageItemMaterials s = new InStorageItemMaterials();
            BeanUtils.copyProperties(storageItem, s);
            s.setAssetsCode(storageItem.getAssetsCode());
            s.setAssetsRfid(storageItem.getAssetsRfid());
            s.setInStorageCount(storageItem.getInStorageCount());//数量
            s.setInStoragePrice(storageItem.getInStoragePrice());//单价
            s.setRecordNumber(inStorage.getRecordNumber());//记录编号主表
            s.setQuantityDelivered(new BigDecimal(0));
            s.setAssetsId(storageItem.getId());//耗材ID
            s.setBatchNumber(storageItem.getBatchNumber());
            s.setModeOfTrade(inStorage.getModeOfTrade());
            s.setSucceed(0L);
            s.setId(null);
            inStorageItemRepository.save(s);
//            MaterialsListEntity materialsListEntity = new MaterialsListEntity();
//            BeanUtils.copyProperties(materialsInfo, materialsListEntity);
//            materialsListEntity.setId(null);
//            materialsListEntity.setOperationName(MaterialsUtil.OPERATION_RUKU);
//            materialsListEntity.setOperationOtherId(inStorage.getRecordNumber());
//            materialsListEntity.setReason(inStorage.getInStorageReson());
//            materialsListEntity.setOperationId(inStorage.getId());
//            materialsListEntity.setAssetsRfid(storageItem.getAssetsRfid());
//            materialsListEntity.setCountInfo(storageItem.getInStorageCount());
//            materialsListEntity.setNowRepertory(storageItem.getInStorageCount());
//            materialsListEntity.setUserName(inStorage.getInStoragePeople());
//            materialsListEntity.setOutDate(inStorage.getInStorageDate());
//            materialsListEntity.setAreaInfo(materialsInfo.getStorageArea());
//            materialsListEntity.setWarehouseInfo(materialsInfo.getStorageWarehouse());
//            materialsListEntity.setBatchNumber(storageItem.getBatchNumber());
//            materialsListEntity.setModeOfTrade(inStorage.getModeOfTrade());
//            consumableListRepository.save(materialsListEntity);
        }
        inStorage.setInStorageTotal(sum);//入库总数
        return ResponseData.success(inStorageRepository.saveAndFlush(inStorage));
    }

    @Override
    public boolean submitByIds(Long[] ids) {
        Boolean code = true;
        for (Long id : ids) {
            InStorageMaterials inStorageMaterials = this.getById(id);
            inStorageMaterials.setStatus(2L);//办理状态-审核中
            inStorageRepository.save(inStorageMaterials);
        }
        return code;
    }

    @Override
    public void audit(Long id, Integer status) {
        InStorageMaterials inStorage = inStorageRepository.getOne(id);
        if (status == 3) {//审核通过
//            List<InStorageItemMaterials> inStorageItemMaterialsList = inStorageItemRepository.findByRecordNumber(inStorage.getRecordNumber());
//            for (InStorageItemMaterials inStorageItemMaterials : inStorageItemMaterialsList) {
//                MaterialsInfo materialsInfo = materialsInfoRepository.findByAssetsNumber(inStorageItemMaterials.getAssetsNumber());
//                MaterialsRepertory byAssetsCodeOrAssetsRfid = materialsRepertoryRepository.findByAssetsCodeOrAssetsRfid(inStorageItemMaterials.getAssetsCode(), inStorageItemMaterials.getAssetsCode());
//                if (byAssetsCodeOrAssetsRfid == null) {
//                    MaterialsRepertory consumableRepertory = new MaterialsRepertory();
//                    BeanUtils.copyProperties(materialsInfo, consumableRepertory);
//                    consumableRepertory.setNowRepertory(inStorageItemMaterials.getInStorageCount());//当前库存
//                    consumableRepertory.setWarehouseInfo(materialsInfo.getStorageWarehouse());
//                    consumableRepertory.setAreaInfo(materialsInfo.getStorageArea());
//                    consumableRepertory.setAreaInfoId(materialsInfo.getStorageAreaId());
//                    consumableRepertory.setMaterialsInfoId(materialsInfo.getId());
//                    consumableRepertory.setNowCount(1L);
//                    consumableRepertory.setCreateTime(new Date());
//                    consumableRepertory.setId(null);
//                    consumableRepertory.setOperationNumber(inStorage.getRecordNumber());
//                    consumableRepertory.setPriority(1L);
//                    consumableRepertory.setAssetsCode(inStorageItemMaterials.getAssetsCode());
//                    consumableRepertory.setBatchNumber(inStorageItemMaterials.getBatchNumber());
//                    consumableRepertory.setModeOfTrade(inStorage.getModeOfTrade());
//                    consumableRepertory.setDateArrival(inStorageItemMaterials.getDateArrival());
//                    //通过耗材查库存
//                    materialsRepertoryRepository.saveAndFlush(consumableRepertory);
//
//                } else {
//                    byAssetsCodeOrAssetsRfid.setNowRepertory(byAssetsCodeOrAssetsRfid.getNowRepertory().add(inStorageItemMaterials.getInStorageCount()));
//                    materialsRepertoryRepository.saveAndFlush(byAssetsCodeOrAssetsRfid);
//                }
//
//
//                MaterialsListEntity materialsListEntity = new MaterialsListEntity();
//                BeanUtils.copyProperties(materialsInfo, materialsListEntity);
//                materialsListEntity.setId(null);
//                materialsListEntity.setOperationName(MaterialsUtil.OPERATION_RUKU);
//                materialsListEntity.setOperationOtherId(inStorage.getRecordNumber());
//                materialsListEntity.setReason(inStorage.getInStorageReson());
//                materialsListEntity.setOperationId(inStorage.getId());
//                materialsListEntity.setAssetsRfid(inStorageItemMaterials.getAssetsRfid());
//                materialsListEntity.setCountInfo(inStorageItemMaterials.getInStorageCount());
//                materialsListEntity.setNowRepertory(inStorageItemMaterials.getInStorageCount());
//                materialsListEntity.setUserName(inStorage.getInStoragePeople());
//                materialsListEntity.setOutDate(inStorage.getInStorageDate());
//                materialsListEntity.setAreaInfo(materialsInfo.getStorageArea());
//                materialsListEntity.setWarehouseInfo(materialsInfo.getStorageWarehouse());
//                materialsListEntity.setBatchNumber(inStorageItemMaterials.getBatchNumber());
//                materialsListEntity.setModeOfTrade(inStorage.getModeOfTrade());
//                materialsListEntity.setDateArrival(inStorageItemMaterials.getDateArrival());
//                consumableListRepository.save(materialsListEntity);
//            }
            inStorage.setStatus(3L);
        } else {//不通过
            inStorage.setStatus(4L);
        }
        inStorage.setAuditor(ShiroUtils.getUserInfo().getName());
        inStorage.setAuditorDate(new Date());
        inStorageRepository.saveAndFlush(inStorage);

    }

    @Override
    @Transactional
    public ResponseData inStorageSave(InStorageMaterials one) {
        InStorageMaterials inStorage = inStorageRepository.getOne(one.getId());
        inStorage.setInStoragePeople(one.getInStoragePeople());
        List<InStorageItemMaterials> inStorageItemMaterialsList = JSONArray.parseArray(one.getItemJson(), InStorageItemMaterials.class);
        for (InStorageItemMaterials inStorageItemMaterials : inStorageItemMaterialsList) {
            MaterialsRepertory byAssetsCodeOrAssetsRfid = materialsRepertoryRepository.findByAssetsCodeOrAssetsRfid(inStorageItemMaterials.getAssetsCode(), inStorageItemMaterials.getAssetsCode());
            if (byAssetsCodeOrAssetsRfid != null) {
                throw new RxcException(inStorageItemMaterials.getAssetsCode() + "：已经存在库存请删除后入库!");
            }
        }
        for (InStorageItemMaterials inStorageItemMaterials : inStorageItemMaterialsList) {
//
//            BigDecimal decimal = new BigDecimal(1);
//            MaterialsInfo byAssetsNumber = materialsInfoRepository.findByAssetsNumber(inStorageItemMaterials.getAssetsNumber().trim());
//            if (StringUtils.isNotEmpty(byAssetsNumber)) {
//                if (StringUtils.isNotEmpty(byAssetsNumber.getMinLot())) {
//                    decimal = byAssetsNumber.getMinLot();
//                }
//            }
//            inStorageItemMaterials.setInStorageCount(inStorageItemMaterials.getInStorageCount().multiply(decimal));
//            List<InStorageItemMaterials> inStorageItemMaterials1 =  inStorageItemRepository.findByRecordNumberAndAssetsNumber(inStorage.getRecordNumber(),inStorageItemMaterials.getAssetsNumber());
//            InStorageItemMaterials one1 = inStorageItemRepository.getOne(inStorageItemMaterials.getId());
            List<InStorageItemMaterials> byRecordNumberAndAssetsNumber = inStorageItemRepository.findByRecordNumberAndAssetsNumber(inStorage.getRecordNumber(), inStorageItemMaterials.getAssetsNumber());
            for (InStorageItemMaterials storageItemMaterials : byRecordNumberAndAssetsNumber) {
                storageItemMaterials.setQuantityDelivered(storageItemMaterials.getQuantityDelivered().add(inStorageItemMaterials.getInStorageCount()));
                if (storageItemMaterials.getQuantityDelivered().compareTo(storageItemMaterials.getInStorageCount()) == 0) {
                    storageItemMaterials.setSucceed(1L);
                    storageItemMaterials.setSucceedDate(new Date());
                }
                inStorageItemRepository.saveAndFlush(storageItemMaterials);
            }

            MaterialsRepertory byAssetsCodeOrAssetsRfid = materialsRepertoryRepository.findByAssetsCodeOrAssetsRfid(inStorageItemMaterials.getAssetsCode().trim(), inStorageItemMaterials.getAssetsCode().trim());
            if (byAssetsCodeOrAssetsRfid == null) {
                MaterialsRepertory consumableRepertory = new MaterialsRepertory();
                BeanUtils.copyProperties(inStorageItemMaterials, consumableRepertory);
                consumableRepertory.setNowRepertory(inStorageItemMaterials.getInStorageCount());//当前库存
                consumableRepertory.setNowCount(new BigDecimal(1));
                consumableRepertory.setCreateTime(new Date());
                consumableRepertory.setId(null);
                consumableRepertory.setOperationNumber(inStorage.getRecordNumber());
//                consumableRepertory.setPriority(1L);
                consumableRepertory.setAreaInfo(inStorageItemMaterials.getStorageArea());
                consumableRepertory.setWarehouseInfo(inStorageItemMaterials.getStorageWarehouse());
                consumableRepertory.setAssetsCode(inStorageItemMaterials.getAssetsCode());
                consumableRepertory.setBatchNumber(inStorageItemMaterials.getBatchNumber());
                consumableRepertory.setModeOfTrade(inStorage.getModeOfTrade());
                consumableRepertory.setDateArrival(inStorageItemMaterials.getDateArrival());
                consumableRepertory.setDepartmentCode(inStorage.getDepartmentCode());
                if (!StringUtils.isEmpty(one.getInvoiceNumber())) {
                    consumableRepertory.setInvoiceNumber(one.getInvoiceNumber());
                }
                if (inStorage.getInStorageReson().equals("2")) {
                    consumableRepertory.setPriority(1L);
                }
                consumableRepertory.setInDataType(Long.parseLong(inStorage.getInStorageReson()));
                //通过耗材查库存
                materialsRepertoryRepository.save(consumableRepertory);

            } else {
                byAssetsCodeOrAssetsRfid.setNowRepertory(byAssetsCodeOrAssetsRfid.getNowRepertory().add(inStorageItemMaterials.getInStorageCount()));
                materialsRepertoryRepository.saveAndFlush(byAssetsCodeOrAssetsRfid);
            }

            MaterialsListEntity materialsListEntity = new MaterialsListEntity();
            BeanUtils.copyProperties(inStorageItemMaterials, materialsListEntity);
            materialsListEntity.setId(null);
            materialsListEntity.setOperationName(MaterialsUtil.OPERATION_RUKU);
            materialsListEntity.setOperationOtherId(inStorage.getRecordNumber());
            if (inStorage.getInStorageReson().equals("3")) {
                materialsListEntity.setReason("6");
            } else {
                materialsListEntity.setReason(inStorage.getInStorageReson());
            }
            materialsListEntity.setOperationId(inStorage.getId());
            materialsListEntity.setAssetsRfid(inStorageItemMaterials.getAssetsRfid());
            materialsListEntity.setCountInfo(inStorageItemMaterials.getInStorageCount());
            materialsListEntity.setNowRepertory(inStorageItemMaterials.getInStorageCount());
            materialsListEntity.setUserName(inStorage.getInStoragePeople());
            materialsListEntity.setOutDate(inStorage.getInStorageDate());
            materialsListEntity.setAreaInfo(inStorageItemMaterials.getStorageArea());
            materialsListEntity.setWarehouseInfo(inStorageItemMaterials.getStorageWarehouse());
            materialsListEntity.setBatchNumber(inStorageItemMaterials.getBatchNumber());
            materialsListEntity.setModeOfTrade(inStorage.getModeOfTrade());
            materialsListEntity.setDateArrival(inStorageItemMaterials.getDateArrival());
            materialsListEntity.setDepartmentCode(inStorage.getDepartmentCode());
            materialsListEntity.setCreateTime(new Date());
            consumableListRepository.save(materialsListEntity);
        }
//        inStorage.setInStorageTotal(sum);//入库总数

        boolean status = true;
        List<InStorageItemMaterials> byRecordNumber = inStorageItemRepository.findByRecordNumber(inStorage.getRecordNumber());
        for (InStorageItemMaterials inStorageItemMaterials : byRecordNumber) {
            if (StringUtils.isEmpty(inStorageItemMaterials.getSucceed())) {
                status = false;
            }
            if (StringUtils.isNotEmpty(inStorageItemMaterials.getSucceed()) && inStorageItemMaterials.getSucceed() != 1L) {
                status = false;
            }
            if (inStorageItemMaterials.getInStorageCount().compareTo(inStorageItemMaterials.getQuantityDelivered()) != 0) {
                status = false;
            }
        }
        if (status) {
            inStorage.setInStatus(2L);
        } else {
            inStorage.setInStatus(1L);
        }
        return ResponseData.success(inStorageRepository.saveAndFlush(inStorage));
    }

    @Override
    public ResponseData saveInStorageVerify(InStorageMaterials inStorage) {
        // 获取前台传来的表格信息
        List<MaterialsInfoBom> list = new ArrayList<>();
        List<InStorageItemMaterials> inStorageItem = JSONArray.parseArray(inStorage.getItemJson(), InStorageItemMaterials.class);

        // 按照assetsNumber合并InStorageItemMaterials
        Map<String, InStorageItemMaterials> mergedItems = new HashMap<>();
        for (InStorageItemMaterials storageItem : inStorageItem) {
            String assetsNumber = storageItem.getAssetsNumber();
            if (mergedItems.containsKey(assetsNumber)) {
                InStorageItemMaterials existingItem = mergedItems.get(assetsNumber);
                // 合并逻辑：累加相同assetsNumber的入库数量
                existingItem.setInStorageCount(existingItem.getInStorageCount().add(storageItem.getInStorageCount()));
            } else {
                mergedItems.put(assetsNumber, storageItem);
            }
        }

        // 对合并后的数据进行处理
        for (InStorageItemMaterials mergedItem : mergedItems.values()) {
            List<MaterialsInfoBom> list1 = materialsInfoBomRepository.findByInvoiceNumberAndAssetsNumber(inStorage.getInvoiceNumber(), mergedItem.getAssetsNumber().replaceAll("\\s+", ""));
            if (list1.size() == 0) {
                throw new ExcelAnalysisException(inStorage.getInvoiceNumber() + "发票中未找到" + mergedItem.getAssetsNumber() + "材料。不可入库！");
            }
//            list.addAll(list1);
            BigDecimal inStorageCount = mergedItem.getInStorageCount();
            BigDecimal remainingCount = inStorageCount; // 跟踪剩余入库数量

            for (MaterialsInfoBom materialsInfoBom : list1) {
                entityManager.detach(materialsInfoBom); // 将实体从持久化上下文中分离

                if (remainingCount.compareTo(new BigDecimal(0)) == 0) {
                    continue;
                }
                // 初始化 QuantityArrived
                if (StringUtils.isEmpty(materialsInfoBom.getQuantityArrived())) {
                    materialsInfoBom.setQuantityArrived(new BigDecimal(0));
                }
                BigDecimal subtract = materialsInfoBom.getDosage().subtract(materialsInfoBom.getQuantityArrived()).subtract(remainingCount);
                if (subtract.compareTo(new BigDecimal(0)) == 1) {
                    materialsInfoBom.setQuantityArrived(materialsInfoBom.getQuantityArrived().add(remainingCount));
                    remainingCount = new BigDecimal(0);
                } else {
                    remainingCount = subtract.abs();
                    continue;
                }
            }
            if (remainingCount.compareTo(new BigDecimal(0)) != 0) {
                return ResponseData.error(mergedItem.getAssetsNumber() + "材料在途发票数量不足-" + mergedItem.getAssetsCode() + "条码材料。不可入库！");
            }
        }
        return ResponseData.success();
    }

    @Override
    public InStorageMaterials findByRecordNumber(String recordNumber) {
        return inStorageRepository.findByRecordNumber(recordNumber);
    }

    @Override
    public Map<String, Object> showAllOutWarehouseInfo(String keyword, List<String> type, PageBean pageBean) {
        HashMap<String, Object> map = new HashMap<>();
//        if (keyword == null || "".equals(keyword)) {
//            Page<OutWarehouseMaterialsEntity> result = outWarehouseRepository.findByOrderByCreateTimeDesc(pageBean.getPagable());
//            map.put("list", result.getContent());
//            map.put("totalCount", result.getTotalElements());
//        } else {                                                                                                            //从第几页开始                                 //显示几个

        List<InStorageMaterials> result = inStorageRepository.findByRecordNumberContainsOrderByCreateTimeDesc(keyword, type, (pageBean.getPage() - 1) * pageBean.getLimit(), pageBean.getLimit());
        //List<InStorage>          result = inStorageRepository.findByRecordNumberContainsOrderByCreateTimeDesc   (keyword,(pageBean.getPage() - 1 )      *pageBean.getLimit(), pageBean.getLimit());

        map.put("list", result);
        map.put("totalCount", inStorageRepository.countGroupedResults(keyword, type));
//        }
        return map;
    }

    @Override
    public Map<String, Object> showAllOutWarehouseInfoPage(String keyword, List<String> type, PageBean pageBean,String departmentCode) {
        Page<InStorageMaterials> byRecordNumberContainsOrderByCreateTimeDescPage = inStorageRepository.findByRecordNumberContainsOrderByCreateTimeDescPage(keyword, departmentCode,type, pageBean.getPagable());

        return PageUtil.toPage(byRecordNumberContainsOrderByCreateTimeDescPage);
    }
    @Override
    public Map<String, Object> showAllOutWarehouseInfoPageDys(String keyword, List<String> type, PageBean pageBean) {
        Page<InStorageMaterials> byRecordNumberContainsOrderByCreateTimeDescPage = inStorageRepository.findByRecordNumberContainsOrderByCreateTimeDescPageDys(keyword, type, pageBean.getPagable());

        return PageUtil.toPage(byRecordNumberContainsOrderByCreateTimeDescPage);
    }

   /* @Override
    public Map showAllMaterialsInfo(String keyword, PageBean pageBean) {
        Page<MaterialsInfo> result;
        if (keyword == null || "".equals(keyword)) {
            result = materialsInfoRepository.findAll(pageBean.getPagable());
        } else {
            result = materialsInfoRepository.findAllByAssetsNameContainingOrOtherIdContaining(keyword, keyword, pageBean.getPagable());
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("list", result.getContent());
        map.put("totalCount", result.getTotalElements());
        return map;
    }

    @Override
    public InStorage findById(Long id) {
        return inStorageRepository.findById(id).get();
    }*/
}
