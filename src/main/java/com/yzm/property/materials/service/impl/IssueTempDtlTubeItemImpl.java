package com.yzm.property.materials.service.impl;

import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.property.materials.entity.BindingCar;
import com.yzm.property.materials.entity.IssueTempDtlTubeItem;
import com.yzm.property.materials.entity.MaterialsInfoBom;
import com.yzm.property.materials.repository.BindingCarRepository;
import com.yzm.property.materials.repository.IssueTempDtlTubeItemRepository;
import com.yzm.property.materials.service.BindingCarService;
import com.yzm.property.materials.service.IssueTempDtlTubeItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class IssueTempDtlTubeItemImpl extends BaseService<IssueTempDtlTubeItem,Long> implements IssueTempDtlTubeItemService {
    @Autowired
    private IssueTempDtlTubeItemRepository issueTempDtlTubeItemRepository;

    public BaseRepository<IssueTempDtlTubeItem,Long> getRepository(){
        return issueTempDtlTubeItemRepository;
    }

    @Override
    public List<IssueTempDtlTubeItem> findByPrimaryId(Long id) {
        return issueTempDtlTubeItemRepository.findByPrimaryId(id);
    }

    @Override
    public Map<String, Object> findAllByPageGroup(IssueTempDtlTubeItem issueTempDtlTubeItem, Pageable pagable) {
        List<IssueTempDtlTubeItem> groupedResults = issueTempDtlTubeItemRepository.findGroupedResults(issueTempDtlTubeItem.getSectCd(), issueTempDtlTubeItem.getLineCd(), issueTempDtlTubeItem.getWorkOrdNo(), issueTempDtlTubeItem.getAssetsNumber(),issueTempDtlTubeItem.getProdNo(), pagable);
        int l = issueTempDtlTubeItemRepository.countGroupedResults(issueTempDtlTubeItem.getSectCd(), issueTempDtlTubeItem.getLineCd(), issueTempDtlTubeItem.getWorkOrdNo(), issueTempDtlTubeItem.getAssetsNumber(),issueTempDtlTubeItem.getProdNo());
        Map<String,Object> map = new HashMap<>();
        map.put("list",groupedResults);
        map.put("totalCount",l);
//
//        Page<MaterialsInfoBom> datas = getRepository().findAll(new Specification<MaterialsInfoBom>() {
//            @Override
//            public Predicate toPredicate(Root<MaterialsInfoBom> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
//                query.groupBy(root.get("containerNumber")); // 在这里替换 "yourGroupByField" 为您要分组的字段名
//                return QueryHelp.getPredicate(root, bean, criteriaBuilder);
//            }
//        }, page);
        return map;
    }
}
