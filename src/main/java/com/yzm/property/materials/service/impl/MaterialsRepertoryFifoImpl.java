package com.yzm.property.materials.service.impl;

import com.yzm.common.utils.ShiroUtils;
import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.property.materials.entity.MaterialsRepertory;
import com.yzm.property.materials.entity.MaterialsRepertoryFifo;
import com.yzm.property.materials.repository.MaterialsRepertoryFifoRepository;
import com.yzm.property.materials.repository.MaterialsRepertoryRepository;
import com.yzm.property.materials.service.MaterialsRepertoryFifoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Service
public class MaterialsRepertoryFifoImpl extends BaseService<MaterialsRepertoryFifo, Long> implements MaterialsRepertoryFifoService {
    @Autowired
    private MaterialsRepertoryFifoRepository consumableListRepository;
    @Autowired
    private MaterialsRepertoryRepository materialsRepertoryRepository;

    public BaseRepository<MaterialsRepertoryFifo, Long> getRepository() {
        return consumableListRepository;
    }

    @Override
    public MaterialsRepertoryFifo findByAssetsNumberAndBatchNumber(String assetsNumber, String batchNumber) {
        return consumableListRepository.findByAssetsNumberAndBatchNumberAndFifoStatus(assetsNumber, batchNumber,2L);
    }

    @Override
    public boolean submitByIds(Long[] ids) {
        for (Long id : ids) {
            MaterialsRepertoryFifo one = consumableListRepository.getOne(id);
            one.setStatus(2L);
            consumableListRepository.saveAndFlush(one);
        }
        return true;
    }

    @Override
    public void audit(Long id, Integer status) {
        MaterialsRepertoryFifo one = consumableListRepository.getOne(id);
        if (status == 3) { //审核通过
            List<MaterialsRepertory> list = materialsRepertoryRepository.findByAssetsNumber(one.getAssetsNumber());
            for (MaterialsRepertory materialsRepertory : list) {
                if(materialsRepertory.getDateArrival().before(one.getDateArrival())){
                    materialsRepertory.setFifoStatus(1L);
                    materialsRepertoryRepository.saveAndFlush(materialsRepertory);
                }
//                if(areDatesInSameMonth(materialsRepertory.getDateArrival(), one.getDateArrival())){
//
//                }
            }
        }
        one.setAuditor(ShiroUtils.getUserInfo().getName());
        one.setAuditorDate(new Date());
        one.setStatus(status.longValue());
        consumableListRepository.saveAndFlush(one);
    }
    public static boolean areDatesInSameMonth(Date date1, Date date2) {
        Calendar calendar1 = Calendar.getInstance();
        calendar1.setTime(date1);

        Calendar calendar2 = Calendar.getInstance();
        calendar2.setTime(date2);

        // 比较年份和月份
        return calendar1.get(Calendar.YEAR) == calendar2.get(Calendar.YEAR) &&
                calendar1.get(Calendar.MONTH) == calendar2.get(Calendar.MONTH);
    }
}
