package com.yzm.property.system.criteria;

import com.yzm.common.annotation.Query;
import com.yzm.common.annotation.Query.Type;
import com.yzm.framework.base.CriteriaBean;

public class SysConfigCriteria extends CriteriaBean{

	@Query(type=Type.INNER_LIKE)
	private String paramKey;
	
	@Query(type=Type.INNER_LIKE)
	private String paramValue;

	public String getParamKey() {
		return paramKey;
	}

	public void setParamKey(String paramKey) {
		this.paramKey = paramKey;
	}

	public String getParamValue() {
		return paramValue;
	}

	public void setParamValue(String paramValue) {
		this.paramValue = paramValue;
	}
	
}
