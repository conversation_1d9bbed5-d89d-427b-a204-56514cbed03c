package com.yzm.property.system.criteria;

import com.yzm.common.annotation.Query;
import com.yzm.common.annotation.Query.Type;
import com.yzm.framework.base.CriteriaBean;

public class SysDictDataCriteria extends CriteriaBean{

	@Query(type=Type.EQUAL)
	private String dictType;
	
	@Query(type=Type.INNER_LIKE)
	private String dictLabel;

	public String getDictType() {
		return dictType;
	}

	public void setDictType(String dictType) {
		this.dictType = dictType;
	}

	public String getDictLabel() {
		return dictLabel;
	}

	public void setDictLabel(String dictLabel) {
		this.dictLabel = dictLabel;
	}
	
}
