package com.yzm.property.system.criteria;

import com.yzm.common.annotation.Query;
import com.yzm.common.annotation.Query.Type;
import com.yzm.framework.base.CriteriaBean;

import java.util.HashSet;
import java.util.Set;

public class SysUserCriteria extends CriteriaBean {
	
	private Long roleId;

	@Query(type = Type.INNER_LIKE)
	private String username;

	@Query(propName = "id", type = Query.Type.IN)
	private Set<Long> ids = new HashSet<>();

	@Query
	private Integer status;

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Long getRoleId() {
		return roleId;
	}

	public void setRoleId(Long roleId) {
		this.roleId = roleId;
	}

	public Set<Long> getIds() {
		return ids;
	}

	public void setIds(Set<Long> ids) {
		this.ids = ids;
	}
}
