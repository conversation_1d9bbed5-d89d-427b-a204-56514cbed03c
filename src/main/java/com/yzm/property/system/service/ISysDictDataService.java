package com.yzm.property.system.service;

import com.yzm.property.system.entity.SysDictData;
import com.yzm.framework.base.IBaseService;

import java.util.List;

public interface ISysDictDataService extends IBaseService<SysDictData,Long> {

	String selectDictLabel(String dictType, String dictValue);

	List<SysDictData> findAllByDictType(SysDictData dict);

	List<SysDictData> selectDictDataByType(String dictType);

}
