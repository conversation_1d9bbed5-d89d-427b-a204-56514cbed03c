package com.yzm.property.system.service.impl;

import com.yzm.property.system.entity.SysDictData;
import com.yzm.property.system.entity.SysDictType;
import com.yzm.property.system.repository.SysDictDataRepository;
import com.yzm.property.system.repository.SysDictTypeRepository;
import com.yzm.property.system.service.ISysDictTypeService;
import com.yzm.common.utils.RedisUtil;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.framework.redis.RedisKeys;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SysDictTypeServiceImpl extends BaseService<SysDictType, Long> implements ISysDictTypeService {

	@Autowired
	private SysDictTypeRepository sysDictTypeRepository;
	@Autowired
	private SysDictDataRepository sysDictDataRepository;
	@Autowired
	private RedisUtil redisUtil;
	
	@Override
	public BaseRepository<SysDictType, Long> getRepository() {
		return sysDictTypeRepository;
	}

	@Override
	public boolean checkDictTypeUnique(SysDictType dict) {
		Long dictId = StringUtils.isEmpty(dict.getId()) ? -1L : dict.getId();
		SysDictType dictType = sysDictTypeRepository.findByDictType(dict.getDictType());
		if (!StringUtils.isEmpty(dictType) && dictType.getId().longValue() != dictId.longValue()) {
			return false;
		}
		return true;
	}

	@Override
	public boolean deleteBatchByIds(Long[] ids) {
		for (Long id : ids) {
			SysDictType dictType = super.getById(id);
			List<SysDictData> datas = sysDictDataRepository.findAllByDictType(dictType.getDictType());
			if(datas.size() > 0) {
				return false;
			}
		}
		super.deleteBatchByIds(ids);
		return true;
	}

	@Override
	public boolean clearDictRedis() {
		return redisUtil.deletes(RedisKeys.getDictConfigKey("*"));
	}
}
