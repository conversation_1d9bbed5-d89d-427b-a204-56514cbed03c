package com.yzm.property.system.service.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.yzm.property.system.entity.SysDictData;
import com.yzm.property.system.repository.SysDictDataRepository;
import com.yzm.property.system.service.ISysDictDataService;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.framework.redis.SysConfigRedis;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SysDictDataServiceImpl extends BaseService<SysDictData, Long> implements ISysDictDataService {

	@Autowired
	private SysDictDataRepository sysDictDataRepository;
	@Autowired
	private SysConfigRedis sysConfigRedis;
	
	@Override
	public BaseRepository<SysDictData, Long> getRepository() {
		return sysDictDataRepository;
	}

	@Override
	public String selectDictLabel(String dictType, String dictValue) {
		List<SysDictData> list = sysConfigRedis.getRedisDict(dictType);
		if(StringUtils.isEmpty(list)) {
			SysDictData dictData = sysDictDataRepository.findByDictTypeAndDictValue(dictType,dictValue);
			if(dictData == null) {
				return "";
			}
			return dictData.getDictLabel();
		}else {
			String r = "";
			JSONArray jsonArray = JSONUtil.parseArray(list, false);
			List<SysDictData> list1 = jsonArray.toList(SysDictData.class);
			for (SysDictData dict : list1) {
				if (dict.getDictValue().equals(dictValue)) {
					r = dict.getDictLabel();
					break;
				}
			}
			return r;
		}
	}

	@Override
	public List<SysDictData> findAllByDictType(SysDictData dict) {
		return selectDictDataByType(dict.getDictType());
	}

	@Override
	public List<SysDictData> selectDictDataByType(String dictType) {
		List<SysDictData> list = sysConfigRedis.getRedisDict(dictType);
		if (StringUtils.isEmpty(list)) {
			list = sysDictDataRepository.findAllByDictTypeOrderByDictSortDesc(dictType);
			sysConfigRedis.saveOrUpdateDict(dictType, list);
		}
		return list;
	}

}
