package com.yzm.property.system.service;

import com.yzm.property.system.entity.SysUserRole;
import com.yzm.framework.base.IBaseService;

import java.util.List;

public interface ISysUserRoleService extends IBaseService<SysUserRole, Long>{

	void updateUserAndRole(Long userid, List<Long> roleIdList);

	List<SysUserRole> findAllByRoleIdIn(Long[] ids);

	List<Long> findUserIdsByRole(Long roleId);

	List<Long> findRoldIdsByUser(Long userId);

}
