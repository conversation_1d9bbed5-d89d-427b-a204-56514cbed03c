package com.yzm.property.system.service;

import com.yzm.property.system.entity.SysMenu;
import com.yzm.property.system.entity.SysRole;
import com.yzm.framework.base.IBaseService;
import com.yzm.framework.bean.Ztree;
import com.yzm.framework.shiro.LoginUser;

import java.util.List;

public interface ISysMenuService extends IBaseService<SysMenu,Long>{

	List<SysMenu> findMenuList(Long userId);

	boolean checkMenuNameUnique(SysMenu menu);

	List<SysMenu> findListParentId(Long menuId);

	boolean clearMenuRedis();

	List<Ztree> menuTreeData(LoginUser userInfo);

	List<Ztree> roleMenuTreeData(SysRole role, LoginUser userInfo);

}
