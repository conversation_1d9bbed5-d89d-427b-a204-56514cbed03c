package com.yzm.property.system.service.impl;

import com.yzm.framework.redis.RedisKeys;
import com.yzm.property.system.entity.SysMenu;
import com.yzm.property.system.entity.SysRole;
import com.yzm.property.system.repository.SysMneuRepository;
import com.yzm.property.system.service.ISysMenuService;
import com.yzm.property.system.service.ISysRoleMenuService;
import com.yzm.common.config.ConfigConstant;
import com.yzm.common.utils.RedisUtil;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.framework.bean.Ztree;
import com.yzm.framework.shiro.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Service
public class SysMenuServiceImpl extends BaseService<SysMenu, Long> implements ISysMenuService {

	@Autowired
	private SysMneuRepository sysMneuRepository;
	@Resource
	private RedisUtil redisUtil;
	@Autowired
	private ISysRoleMenuService sysRoleMenuService;

	@Override
	public BaseRepository<SysMenu, Long> getRepository() {
		return sysMneuRepository;
	}

	@Override
	public List<SysMenu> findMenuList(Long userId) {
		List<SysMenu> datas = null;
		if (ConfigConstant.SUPER_ADMIN.equals(userId)) {
			datas = sysMneuRepository.findAll(Sort.by(Direction.ASC, SysMenu.FIELD_ORDER_NUM));
		} else {
			Set<SysMenu> menus = sysMneuRepository.findMenuByUserId(userId);
			datas = new ArrayList<>(menus);
		}
		return datas;
	}

	@Override
	public boolean checkMenuNameUnique(SysMenu menu) {
		Long menuId = StringUtils.isEmpty(menu.getId()) ? -1L : menu.getId();
		SysMenu info = sysMneuRepository.findByNameAndParentId(menu.getName(), menu.getParentId());
		if (!StringUtils.isEmpty(info) && info.getId().longValue() != menuId.longValue()) {
			return false;
		}
		return true;
	}

	@Override
	public SysMenu save(SysMenu menu) {
		// 查询上级菜单
		if (menu.getParentId() != 0L) {
			SysMenu pMenu = super.getById(menu.getParentId());
			menu.setParentName(pMenu.getName());
		}
		SysMenu sysmenu = super.save(menu);
		return sysmenu;
	}

	@Override
	public List<SysMenu> findListParentId(Long menuId) {
		return sysMneuRepository.findAllByParentId(menuId);
	}

	@Override
	public boolean clearMenuRedis() {
		return redisUtil.deletes(RedisKeys.getDictConfigKey("*"));
	}

	@Override
	public List<Ztree> menuTreeData(LoginUser userInfo) {
		List<SysMenu> menuList = findMenuList(userInfo.getId());
		List<Ztree> ztrees = initZtree(menuList, null, false);
		return ztrees;
	}

	/**
	 * 对象转菜单树
	 *
	 * @param menuList     菜单列表
	 * @param roleMenuList 角色已存在菜单列表
	 * @param permsFlag    是否需要显示权限标识
	 * @return 树结构列表
	 */
	public List<Ztree> initZtree(List<SysMenu> menuList, List<Long> roleMenuList, boolean permsFlag) {
		List<Ztree> ztrees = new ArrayList<Ztree>();
		boolean isCheck = !StringUtils.isEmpty(roleMenuList);
		for (SysMenu menu : menuList) {
			Ztree ztree = new Ztree();
			ztree.setId(menu.getId());
			ztree.setpId(menu.getParentId());
			ztree.setName(menu.getName());
			ztree.setTitle(menu.getName());
			if (isCheck) {
				ztree.setChecked(roleMenuList.contains(menu.getId()));
			}
			ztrees.add(ztree);
		}
		return ztrees;
	}

	@Override
	public List<Ztree> roleMenuTreeData(SysRole role, LoginUser userInfo) {
		Long roleId = role.getId();
		List<Ztree> ztrees = new ArrayList<Ztree>();
		List<SysMenu> menuList = findMenuList(userInfo.getId());
		if (!StringUtils.isEmpty(roleId)) {
			List<Long> roleMenuList = sysRoleMenuService.selectRoleMenu(roleId);
			ztrees = initZtree(menuList, roleMenuList, true);
		} else {
			ztrees = initZtree(menuList, null, true);
		}
		return ztrees;
	}
}
