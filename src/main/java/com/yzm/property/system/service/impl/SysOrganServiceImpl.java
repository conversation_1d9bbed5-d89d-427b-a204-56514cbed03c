package com.yzm.property.system.service.impl;

import cn.hutool.core.collection.ListUtil;
import com.yzm.property.system.entity.SysOrgan;
import com.yzm.property.system.entity.SysUser;
import com.yzm.property.system.repository.SysOrganRepository;
import com.yzm.property.system.repository.SysUserRepository;
import com.yzm.property.system.service.ISysOrganService;
import com.yzm.common.config.ConfigConstant;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.framework.bean.Ztree;
import com.yzm.framework.shiro.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class SysOrganServiceImpl extends BaseService<SysOrgan, Long> implements ISysOrganService {

	@Autowired
	private SysOrganRepository sysOrganRepository;
	@Autowired
	private SysUserRepository sysUserRepository;

	@Override
	public BaseRepository<SysOrgan, Long> getRepository() {
		return sysOrganRepository;
	}

	@Override
	public List<SysOrgan> findOrganList(Long userId) {
		List<SysOrgan> datas = null;
		if (ConfigConstant.SUPER_ADMIN == userId) {
			datas = sysOrganRepository.findAll();
		} else {
			datas = sysOrganRepository.findOrganByUserId(userId);
		}
		return datas;
	}

	@Override
	public boolean checkOrganNameUnique(SysOrgan organ) {
		Long menuId = StringUtils.isEmpty(organ.getId()) ? -1L : organ.getId();
		SysOrgan info = sysOrganRepository.findByNameAndParentId(organ.getName(), organ.getParentId());
		if (!StringUtils.isEmpty(info) && info.getId().longValue() != menuId.longValue()) {
			return false;
		}
		return true;
	}

	@Override
	public List<Ztree> organTreeData(LoginUser userInfo) {
		List<SysOrgan> menuList = findOrganList(userInfo.getId());
		return initZtree(menuList,null);
	}

	/**
	 * 对象转菜单树
	 *
	 * @param menuList     菜单列表
	 * @param roleMenuList 角色已存在菜单列表
	 * @param permsFlag    是否需要显示权限标识
	 * @return 树结构列表
	 */
	public List<Ztree> initZtree(List<SysOrgan> organList, List<String> userOrganList) {
		List<Ztree> ztrees = new ArrayList<Ztree>();
		boolean isCheck = !StringUtils.isEmpty(userOrganList);
		for (SysOrgan organ : organList) {
			Ztree ztree = new Ztree();
			ztree.setId(organ.getId());
			ztree.setpId(organ.getParentId());
			ztree.setName(organ.getName());
			ztree.setTitle(organ.getName());
			if (isCheck) {
				ztree.setChecked(userOrganList.contains(organ.getId().toString()));
			}
			ztrees.add(ztree);
		}
		return ztrees;
	}

	@Override
	public List<SysOrgan> findListParentId(Long organId) {
		return sysOrganRepository.findAllByParentId(organId);
	}

	@Override
	public List<Long> findChildrenOrganIds(Long organId, List<SysOrgan> orgList) {
		List<Long> list = new ArrayList<>();
		orgList.forEach(organ -> {
			if (organ != null) {
				List<SysOrgan> organs = findListParentId(organ.getId());
				if (organs.size() != 0) {
					list.addAll(findChildrenOrganIds(organ.getId(), organs));
				}
				list.add(organ.getId());
			}
		});
		return list;
	}

	@Override
	public boolean addOrgan(SysOrgan organ) {
		if(StringUtils.isNotEmpty(super.save(organ))) {
			return true;
		}
		return false;
	}

	@Override
	public List<Ztree> userOrganTreeData(Long userId) {
		List<SysOrgan> organList = findOrganList(userId);
		SysUser sysUser = sysUserRepository.findById(userId).get();
		List<Ztree> initZtree = null;
		if(StringUtils.isEmpty(sysUser.getOrganIds())) {
			initZtree = initZtree(organList, null);
		}else {
			String[] userOrganList = sysUser.getOrganIds().split(",");
			initZtree = initZtree(organList, ListUtil.toList(userOrganList));
		}
		return initZtree;
	}

}
