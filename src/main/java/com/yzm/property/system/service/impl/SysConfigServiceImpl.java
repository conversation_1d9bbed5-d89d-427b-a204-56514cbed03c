package com.yzm.property.system.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ClassUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.yzm.property.system.entity.SysConfig;
import com.yzm.property.system.repository.SysConfigRepository;
import com.yzm.property.system.service.ISysConfigService;
import com.yzm.common.utils.RedisUtil;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.framework.exception.RxcException;
import com.yzm.framework.redis.RedisKeys;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SysConfigServiceImpl extends BaseService<SysConfig, Long> implements ISysConfigService {

	@Autowired
	private SysConfigRepository sysConfigRepository;
	@Autowired
	private RedisUtil redisUtil;

	@Override
	public BaseRepository<SysConfig, Long> getRepository() {
		return sysConfigRepository;
	}

	@Override
	public String getConfigObject(String key, Class<String> clazz) {
		String value = getParamValue(key);
		if (!StrUtil.isBlankOrUndefined(value) && JSONUtil.isJson(value) && !ClassUtil.equals(clazz, "String", false)) { // 判断获取值,是否转换Bean对象
			return JSONUtil.toBean(value, clazz);
		}
		if (ClassUtil.equals(clazz, "String", false)) { // String 转换
			return Convert.convert(clazz, value);
		}
		try {
			return clazz.newInstance();
		} catch (Exception e) {
			throw new RxcException("获取参数失败");
		}
	}

	/**
	 * 通过key 获取值
	 *
	 * @param key
	 * @return
	 */
	public String getParamValue(String key) {
		SysConfig config = redisUtil.get(RedisKeys.getSysConfigKey(key), SysConfig.class);
		if (config == null) {
			config = sysConfigRepository.findByParamKey(key);
			redisUtil.set(RedisKeys.getSysConfigKey(key), config);
		}
		return config == null ? null : config.getParamValue();
	}

	@Override
	public boolean checkConfigKeyUnique(SysConfig config) {
		Long configId = StringUtils.isEmpty(config.getId()) ? -1L : config.getId();
		SysConfig info = sysConfigRepository.findByParamKey(config.getParamKey());
		if (StringUtils.isNotEmpty(info) && info.getId().longValue() != configId.longValue()) {
			return false;
		}
		return true;
	}

	@Override
	public boolean clearConfigRedis() {
		return redisUtil.deletes(RedisKeys.getSysConfigKey("*"));
	}

}
