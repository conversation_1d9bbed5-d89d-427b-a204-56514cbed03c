package com.yzm.property.system.service;

import com.yzm.property.system.entity.SysMenu;
import com.yzm.property.system.entity.SysUser;
import com.yzm.framework.base.IBaseService;

import java.util.List;
import java.util.Set;

public interface ISysUserService extends IBaseService<SysUser,Long> {

	Set<String> findAllPermisByUserId(Long userid);

	List<SysMenu> findMenuByUserId(Long id);

	boolean addUser(SysUser user);

	boolean checkUserNameUnique(String username);

	boolean checkMobileUnique(SysUser user);

	boolean updateUser(SysUser user);

	boolean updateStatus(SysUser user);

	boolean delUser(Long[] ids);

	boolean updatePassWord(Long id, String newPassword, String salt, Integer pwdSecurityLevel);

	boolean updatePassWord(Long id, String newPassword, String salt);

	SysUser findUserByUserName(String username);
}
