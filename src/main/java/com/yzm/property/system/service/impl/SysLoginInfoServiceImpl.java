package com.yzm.property.system.service.impl;

import com.yzm.property.system.entity.SysLoginInfo;
import com.yzm.property.system.repository.SysLoginInfoRepository;
import com.yzm.property.system.service.ISysLoginInfoService;
import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SysLoginInfoServiceImpl extends BaseService<SysLoginInfo, Long> implements ISysLoginInfoService {

	@Autowired
	private SysLoginInfoRepository sysLoginInfoRepository;
	@Override
	public BaseRepository<SysLoginInfo, Long> getRepository() {
		return sysLoginInfoRepository;
	}
	
	@Override
	public boolean cleanLog() {
		sysLoginInfoRepository.deleteAll();
		return true;
	}

}