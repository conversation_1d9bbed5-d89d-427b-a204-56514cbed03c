package com.yzm.property.system.service.impl;

import com.yzm.property.system.entity.SysUserRole;
import com.yzm.property.system.repository.SysUserRoleRepository;
import com.yzm.property.system.service.ISysUserRoleService;
import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class SysUserRoleServiceImpl extends BaseService<SysUserRole,Long> implements ISysUserRoleService{

	@Autowired
	private SysUserRoleRepository sysUserRoleRepository;
	
	@Override
	public BaseRepository<SysUserRole, Long> getRepository() {
		return sysUserRoleRepository;
	}

	@Override
	public void updateUserAndRole(Long userid, List<Long> roleIdList) {
		//删除原绑定
		sysUserRoleRepository.deleteAllByUserId(userid);
		//增加新绑定
		for (Long roleId : roleIdList) {
			SysUserRole sysUserRole = new SysUserRole();
			sysUserRole.setUserId(userid);
			sysUserRole.setRoleId(roleId);
			sysUserRoleRepository.save(sysUserRole);
		}
	}

	@Override
	public List<SysUserRole> findAllByRoleIdIn(Long[] ids) {
		return sysUserRoleRepository.findAllByRoleIdIn(ids);
	}

	@Override
	public List<Long> findUserIdsByRole(Long roleId) {
		List<SysUserRole> userRoles = sysUserRoleRepository.findAllByRoleId(roleId);
		return userRoles.stream().map(r->r.getUserId()).collect(Collectors.toList());
	}

	@Override
	public List<Long> findRoldIdsByUser(Long userId) {
		List<SysUserRole> userRoles = sysUserRoleRepository.findAllByUserId(userId);
		return userRoles.stream().map(r->r.getRoleId()).collect(Collectors.toList());
	}
	
}
