package com.yzm.property.system.service.impl;

import com.yzm.property.system.entity.SysRoleMenu;
import com.yzm.property.system.repository.SysRoleMenuRepository;
import com.yzm.property.system.service.ISysRoleMenuService;
import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class SysRoleMenuServiceImpl extends BaseService<SysRoleMenu, Long> implements ISysRoleMenuService {

    @Autowired
    private SysRoleMenuRepository sysRoleMenuRepository;

    @Override
    public BaseRepository<SysRoleMenu, Long> getRepository() {
        return sysRoleMenuRepository;
    }

    @Override
    public List<Long> selectRoleMenu(Long roleId) {
        List<SysRoleMenu> roleMenus = sysRoleMenuRepository.findAllByRoleId(roleId);
        return roleMenus.stream().map(m -> m.getMenuId()).collect(Collectors.toList());
    }

}
