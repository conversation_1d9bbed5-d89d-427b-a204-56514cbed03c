package com.yzm.property.system.service.impl;

import com.yzm.property.system.entity.SysOperLog;
import com.yzm.property.system.repository.SysOperLogRepository;
import com.yzm.property.system.service.ISysOperLogService;
import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SysOperLogServiceImpl extends BaseService<SysOperLog, Long> implements ISysOperLogService {

	@Autowired
	private SysOperLogRepository sysOperLogRepository;
	@Override
	public BaseRepository<SysOperLog, Long> getRepository() {
		return sysOperLogRepository;
	}
	
	@Override
	public boolean cleanLog() {
		sysOperLogRepository.deleteAll();
		return true;
	}

}
