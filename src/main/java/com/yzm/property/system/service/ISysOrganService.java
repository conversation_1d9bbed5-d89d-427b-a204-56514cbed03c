package com.yzm.property.system.service;

import com.yzm.property.system.entity.SysOrgan;
import com.yzm.framework.base.IBaseService;
import com.yzm.framework.bean.Ztree;
import com.yzm.framework.shiro.LoginUser;

import java.util.List;

public interface ISysOrganService extends IBaseService<SysOrgan, Long>{

	List<SysOrgan> findOrganList(Long userid);

	boolean checkOrganNameUnique(SysOrgan organ);

	List<Ztree> organTreeData(LoginUser userInfo);

	List<SysOrgan> findListParentId(Long organId);

	List<Long> findChildrenOrganIds(Long organId, List<SysOrgan> list);

	boolean addOrgan(SysOrgan organ);

	List<Ztree> userOrganTreeData(Long userId);

}
