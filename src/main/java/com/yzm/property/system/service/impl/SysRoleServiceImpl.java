package com.yzm.property.system.service.impl;

import com.yzm.property.system.entity.SysRole;
import com.yzm.property.system.entity.SysRoleMenu;
import com.yzm.property.system.entity.SysUserRole;
import com.yzm.property.system.repository.SysRoleMenuRepository;
import com.yzm.property.system.repository.SysRoleRepository;
import com.yzm.property.system.service.ISysRoleService;
import com.yzm.property.system.service.ISysUserRoleService;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SysRoleServiceImpl extends BaseService<SysRole, Long> implements ISysRoleService {

	@Autowired
	private SysRoleRepository sysRoleRepository;
	@Autowired
	private ISysUserRoleService sysUserRoleService;
	@Autowired
	private SysRoleMenuRepository sysRoleMenuRepository;
	
	@Override
	public BaseRepository<SysRole, Long> getRepository() {
		return sysRoleRepository;
	}

	@Override
	public boolean deleteBatch(Long[] ids) {
		List<SysUserRole> userRoles = sysUserRoleService.findAllByRoleIdIn(ids);
		if(StringUtils.isNotEmpty(userRoles) && userRoles.size()>0) {
			return false;
		}
		for (Long roleId : ids) {
			//删除角色和菜单的关联
			sysRoleMenuRepository.deleteAllByRoleId(roleId);
			//删除角色
			sysRoleRepository.deleteById(roleId);
		}
		return true;
	}

	@Override
	public boolean addRole(SysRole role) {
		SysRole sysRole = super.save(role);
		if(StringUtils.isNotEmpty(sysRole)) {
			List<Long> menuIds = role.getMenuIds();
			for (Long menuId : menuIds) {
				SysRoleMenu sysRoleMenu = new SysRoleMenu();
				sysRoleMenu.setRoleId(sysRole.getId());
				sysRoleMenu.setMenuId(menuId);
				sysRoleMenuRepository.save(sysRoleMenu);
			}
			return true;
		}
		return false;
	}

	@Override
	public boolean updateRole(SysRole role) {
		SysRole sysRole = super.save(role);
		if(StringUtils.isNotEmpty(sysRole)) {
			//移除角色和菜单关系
			sysRoleMenuRepository.deleteAllByRoleId(role.getId());
			//添加角色和菜单关系
			List<Long> menuIds = role.getMenuIds();
			for (Long menuId : menuIds) {
				SysRoleMenu sysRoleMenu = new SysRoleMenu();
				sysRoleMenu.setRoleId(sysRole.getId());
				sysRoleMenu.setMenuId(menuId);
				sysRoleMenuRepository.save(sysRoleMenu);
			}
			return true;
		}
		return false;
	}

	@Override
	public boolean checkRoleNameUnique(SysRole role) {
		Long roleId = StringUtils.isEmpty(role.getId())?-1L:role.getId();
		SysRole info = sysRoleRepository.findByName(role.getName());
		if(!StringUtils.isEmpty(info) && !info.getId().equals(roleId)){
			return  false;
		}
		return true;
	}

	
}
