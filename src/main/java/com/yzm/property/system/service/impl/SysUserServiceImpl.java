package com.yzm.property.system.service.impl;

import com.yzm.property.system.entity.SysMenu;
import com.yzm.property.system.entity.SysUser;
import com.yzm.property.system.repository.SysMneuRepository;
import com.yzm.property.system.repository.SysUserRepository;
import com.yzm.property.system.repository.SysUserRoleRepository;
import com.yzm.property.system.service.ISysOrganService;
import com.yzm.property.system.service.ISysUserRoleService;
import com.yzm.property.system.service.ISysUserService;
import com.yzm.common.config.ConfigConstant;
import com.yzm.common.utils.ShiroUtils;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.framework.freemark.Global;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Service
public class SysUserServiceImpl extends BaseService<SysUser, Long> implements ISysUserService {

    @Autowired
    private SysUserRepository sysUserRepository;
    @Autowired
    private SysMneuRepository sysMneuRepository;
    @Autowired
    private ISysUserRoleService sysUserRoleService;
    @Autowired
    private SysUserRoleRepository sysUserRoleRepository;
    @Autowired
    private ISysOrganService sysOrganService;

    @Override
    public BaseRepository<SysUser, Long> getRepository() {
        return this.sysUserRepository;
    }

    @Override
    public Set<String> findAllPermisByUserId(Long userid) {
        return sysUserRepository.findAllPermisByUserId(userid);
    }

    @Override
    public List<SysMenu> findMenuByUserId(Long id) {
        List<SysMenu> menus = new ArrayList<SysMenu>();
        if (ConfigConstant.SUPER_ADMIN.equals(id)) {
            menus = sysMneuRepository.findAllByTypeLessThanOrderByOrderNumAsc(2);
        } else {
            Set<SysMenu> menuSet = sysUserRepository.findMenuByUserId(id);
            menus.addAll(menuSet);
        }
        return getMenuTreeList(menus, 0L);
    }

    /**
     * 递归
     */
    private List<SysMenu> getMenuTreeList(List<SysMenu> rootmenuList, Long parentId) {
        List<SysMenu> menuList = new ArrayList<SysMenu>();
        for (SysMenu entity : rootmenuList) {
            if (entity.getParentId().equals(parentId)) {
                entity.setChildren(getMenuTreeList(rootmenuList, entity.getId()));
                menuList.add(entity);
            }
        }
        return menuList;
    }

    @Override
    public boolean addUser(SysUser user) {
        // 检查密码安全级别
        if (user.getPassword().equals(Global.getDbKey("sys.user.initPassword"))) {
            user.setPwdSecurityLevel(0);
        } else {
            user.setPwdSecurityLevel(1);
        }
        user.setSalt(ShiroUtils.randomSalt());
        user.setPassword(ShiroUtils.sha256(user.getPassword(), user.getSalt()));

        if (StringUtils.isNotEmpty(super.save(user))) {
            // 保存用户与角色关系
            sysUserRoleService.updateUserAndRole(user.getId(), user.getRoleIdList());
            return true;
        }
        return false;
    }

    @Override
    public boolean checkUserNameUnique(String username) {
        SysUser user = sysUserRepository.findByUsername(username);
        if (StringUtils.isEmpty(user)) {
            return true;
        }
        return false;
    }

    @Override
    public boolean checkMobileUnique(SysUser user) {
        Long userId = StringUtils.isEmpty(user.getId()) ? -1L : user.getId();
        SysUser info = sysUserRepository.findByMobile(user.getMobile());
        if (StringUtils.isNotEmpty(info) && !info.getId().equals(userId)) {
            return false;
        }
        return true;
    }

    @Override
    public boolean updateUser(SysUser user) {
        if (StringUtils.isNotEmpty(super.save(user))) {
            // 保存用户与角色关系
            sysUserRoleService.updateUserAndRole(user.getId(), user.getRoleIdList());
            return true;
        }
        return false;
    }

    @Override
    public boolean updateStatus(SysUser user) {
        if (StringUtils.isEmpty(user) || StringUtils.isEmpty(user.getId()) || StringUtils.isEmpty(user.getStatus())) {
            return false;
        }
        sysUserRepository.updateStatusById(user.getId(), user.getStatus());
        return true;
    }

    @Override
    public boolean delUser(Long[] ids) {
        for (Long userid : ids) {
            // 删除 用户与角色 关联表
            sysUserRoleRepository.deleteAllByUserId(userid);
            //删除用户
            sysUserRepository.deleteById(userid);
        }
        return true;
    }

    @Override
    public boolean updatePassWord(Long id, String newPassword, String salt, Integer pwdSecurityLevel) {
        SysUser sysUser = sysUserRepository.findById(id).get();
        sysUser.setPassword(newPassword);
        sysUser.setSalt(salt);
        sysUser.setPwdSecurityLevel(pwdSecurityLevel);
        sysUserRepository.save(sysUser);
        return true;
    }

    @Override
    public boolean updatePassWord(Long id, String newPassword, String salt) {
        SysUser sysUser = sysUserRepository.findById(id).get();
        sysUser.setPassword(newPassword);
        sysUser.setSalt(salt);
        sysUserRepository.save(sysUser);
        return true;
    }

    @Override
    public SysUser findUserByUserName(String username) {
        return sysUserRepository.findByUsername(username);
    }

}
