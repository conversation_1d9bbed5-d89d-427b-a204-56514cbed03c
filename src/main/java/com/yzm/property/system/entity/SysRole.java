package com.yzm.property.system.entity;

import com.yzm.framework.base.BaseEntity;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.validation.constraints.NotBlank;
import java.util.List;

@Entity
@Table(name="sys_role")
public class SysRole extends BaseEntity{
	private static final long serialVersionUID = -6916086123006432078L;

	/**
	 * 角色名称
	 */
	@NotBlank(message = "角色名称不能为空")
	private String name;
	
	@Transient
	private List<Long> menuIds;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public List<Long> getMenuIds() {
		return menuIds;
	}

	public void setMenuIds(List<Long> menuIds) {
		this.menuIds = menuIds;
	}

}
