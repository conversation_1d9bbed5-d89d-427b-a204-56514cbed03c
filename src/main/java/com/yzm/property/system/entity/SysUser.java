package com.yzm.property.system.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.yzm.framework.base.BaseEntity;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "sys_user")
@DynamicUpdate
public class SysUser extends BaseEntity {
	private static final long serialVersionUID = -5522780086369936734L;

	@Column(length = 100, nullable = false)
	private String username;

	@Column(length = 100, nullable = false)
	private String name;

	@Column(length = 255, nullable = false)
	@JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
	private String password;

	@Column(length = 100, nullable = false)
	private String salt;
	/**
	 * 密码安全级别（0初始 1很弱 2弱 3安全 4很安全）
	 */
	private Integer pwdSecurityLevel;

	/**
	 * 手机号
	 */
	private String mobile;

	/**
	 * 状态 1：禁用 0：正常
	 */
	@Column(columnDefinition = "int(1) default 1", nullable = false)
	private Integer status;

	/**
	 * 密码修改时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date pwdUpdateDate;

	/**
	 * 单点登录
	 */
	@Column(length = 1024)
	private String openId;
	/**
	 * 角色权限范围 1=所有数据权限,2=自定义数据权限,3=本部门数据权限,4=本部门及以下数据权限,5个人权限
	 */
	@Column(length = 1)
	private Integer dataScope;

	/**
	 * 角色IDs
	 */
	@Transient
	private List<Long> roleIdList;
	
	/**
	 * 自定义数据权限时的机构id
	 */
	private String organIds;  //部门1，部门2，部门3

	public Integer getPwdSecurityLevel() {
		return pwdSecurityLevel;
	}

	public void setPwdSecurityLevel(Integer pwdSecurityLevel) {
		this.pwdSecurityLevel = pwdSecurityLevel;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Date getPwdUpdateDate() {
		return pwdUpdateDate;
	}

	public void setPwdUpdateDate(Date pwdUpdateDate) {
		this.pwdUpdateDate = pwdUpdateDate;
	}

	public String getOpenId() {
		return openId;
	}

	public void setOpenId(String openId) {
		this.openId = openId;
	}

	public Integer getDataScope() {
		return dataScope;
	}

	public void setDataScope(Integer dataScope) {
		this.dataScope = dataScope;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getSalt() {
		return salt;
	}

	public void setSalt(String salt) {
		this.salt = salt;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getOrganIds() {
		return organIds;
	}

	public void setOrganIds(String organIds) {
		this.organIds = organIds;
	}

	public List<Long> getRoleIdList() {
		return roleIdList;
	}

	public void setRoleIdList(List<Long> roleIdList) {
		this.roleIdList = roleIdList;
	}

}
