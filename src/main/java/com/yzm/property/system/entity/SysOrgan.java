package com.yzm.property.system.entity;

import com.yzm.framework.base.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 组织机构
 * <AUTHOR>
 *2024年1月14日
 */
@Entity
@Table(name="sys_organ")
public class <PERSON>ys<PERSON>rgan extends BaseEntity{
	private static final long serialVersionUID = -4307243326241575950L;

	/**
	 * 机构名称
	 */
	@Column(length = 100)
	private String name;
	
	@Column(length = 20)
	private Long parentId;    //0 公司
	
	/**
	 * 机构全称
	 */
	private String fullName;
	
	private Integer orderNum;
	
	public Integer getOrderNum() {
		return orderNum;
	}

	public void setOrderNum(Integer orderNum) {
		this.orderNum = orderNum;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Long getParentId() {
		return parentId;
	}

	public void setParentId(Long parentId) {
		this.parentId = parentId;
	}

	public String getFullName() {
		return fullName;
	}

	public void setFullName(String fullName) {
		this.fullName = fullName;
	}
	
}
