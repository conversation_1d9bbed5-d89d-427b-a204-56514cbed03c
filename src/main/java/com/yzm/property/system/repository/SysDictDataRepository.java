package com.yzm.property.system.repository;

import com.yzm.property.system.entity.SysDictData;
import com.yzm.framework.base.BaseRepository;

import java.util.List;

public interface SysDictDataRepository extends BaseRepository<SysDictData, Long> {

	List<SysDictData> findAllByDictType(String dictType);

	SysDictData findByDictTypeAndDictValue(String dictType, String dictValue);

	List<SysDictData> findAllByDictTypeOrderByDictSortDesc(String dictType);

}
