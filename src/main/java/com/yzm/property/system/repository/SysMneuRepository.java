package com.yzm.property.system.repository;

import com.yzm.property.system.entity.SysMenu;
import com.yzm.framework.base.BaseRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Set;

public interface SysMneuRepository extends BaseRepository<SysMenu, Long> {

	List<SysMenu> findAllByTypeLessThan(int type);

	@Query(value="FROM SysMenu m " +
			"LEFT JOIN SysRoleMenu rm ON m.id = rm.menuId "+
			"LEFT JOIN SysUserRole ur ON rm.roleId = ur.roleId "+
			"WHERE ur.userId = ?1 ORDER BY m.orderNum asc")
	Set<SysMenu> findMenuByUserId(Long userId);

	SysMenu findByNameAndParentId(String name, Long parentId);

	List<SysMenu> findAllByParentId(Long menuId);

	List<SysMenu> findAllByTypeLessThanOrderByOrderNumAsc(int type);

}
