package com.yzm.property.system.repository;

import com.yzm.property.system.entity.SysOrgan;
import com.yzm.framework.base.BaseRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface SysOrganRepository extends BaseRepository<SysOrgan, Long>{

	@Query("FROM SysOrgan")
	List<SysOrgan> findOrganByUserId(Long userId);

	SysOrgan findByNameAndParentId(String name, Long parentId);

	List<SysOrgan> findAllByParentId(Long organId);

}
