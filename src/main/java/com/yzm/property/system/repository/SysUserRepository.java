package com.yzm.property.system.repository;

import com.yzm.property.system.entity.SysMenu;
import com.yzm.property.system.entity.SysUser;
import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.shiro.LoginUser;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import javax.transaction.Transactional;
import java.util.Set;

public interface SysUserRepository extends BaseRepository<SysUser, Long> {

	@Query(value = "SELECT new com.yzm.framework.shiro.LoginUser(u.username,u.password) FROM SysUser u WHERE u.username = ?1")
	LoginUser findUserByUsername(String username);

	SysUser findByUsername(String username);

	@Query(value="SELECT DISTINCT m.perms " +
			"FROM sys_menu m " +
			"LEFT JOIN sys_role_menu rm ON m.id = rm.menu_id " + 
			"LEFT JOIN sys_user_role ur ON rm.role_id = ur.role_id " + 
			"WHERE ur.user_id = ?1",nativeQuery = true)
	Set<String> findAllPermisByUserId(Long userid);

	@Query(value="FROM SysMenu m " +
			"LEFT JOIN SysRoleMenu rm ON m.id = rm.menuId "+
			"LEFT JOIN SysUserRole ur ON rm.roleId = ur.roleId "+
			"WHERE ur.userId = ?1 AND m.type < 2 ORDER BY m.orderNum ASC")
	Set<SysMenu> findMenuByUserId(Long id);

	SysUser findByMobile(String mobile);

	@Modifying
	@Transactional
	@Query(value="UPDATE SysUser u SET u.status = ?2 WHERE u.id = ?1")
	void updateStatusById(Long id, Integer status);

}
