package com.yzm.property.system.repository;

import com.yzm.property.system.entity.SysUserRole;
import com.yzm.framework.base.BaseRepository;

import javax.transaction.Transactional;
import java.util.List;

public interface SysUserRoleRepository extends BaseRepository<SysUserRole, Long> {

	/**
	 * @Transactional
	 * 解决 No EntityManager with actual transaction available for current thread -
	 * cannot reliably process 'remove' call
	 * 
	 * @param userid
	 */
	@Transactional
	void deleteAllByUserId(Long userid);

	List<SysUserRole> findAllByRoleIdIn(Long[] ids);

	List<SysUserRole> findAllByRoleId(Long roleId);

	List<SysUserRole> findAllByUserId(Long userId);

}
