package com.yzm.property.system.controller;

import cn.hutool.core.util.ArrayUtil;
import com.yzm.common.annotation.BussinessLog;
import com.yzm.common.config.ConfigConstant;
import com.yzm.common.enums.BusinessType;
import com.yzm.common.utils.ShiroUtils;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.framework.exception.RxcException;
import com.yzm.framework.freemark.Global;
import com.yzm.framework.shiro.LoginUser;
import com.yzm.property.system.criteria.SysUserCriteria;
import com.yzm.property.system.entity.SysUser;
import com.yzm.property.system.service.ISysRoleService;
import com.yzm.property.system.service.ISysUserRoleService;
import com.yzm.property.system.service.ISysUserService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Controller
@RequestMapping(value = "sys/user")
public class SysUserController extends BaseController {
    private String urlPrefix = "system/user";

    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private ISysRoleService sysRoleService;
    @Autowired
    private ISysUserRoleService sysUserRoleService;
//	@Autowired
//	private IWhWarehouseService whWarehouseService;


    @RequiresPermissions("sys:user:view")
    @GetMapping
    public String user(ModelMap mmap) {
        return urlPrefix + "/user";
    }

    @ResponseBody
    @PostMapping(value = "/list")
    public ResponseData page(PageBean pageBean, SysUserCriteria user) {
        Map<String, Object> datas = sysUserService.findAllByPage(user, pageBean.getPagable());
        return success(datas);
    }

    /**
     * 新增用户
     */
    @GetMapping("/add")
    public String add(ModelMap mmap) {
        LoginUser userInfo = ShiroUtils.getUserInfo();
        if (userInfo.getId().equals(1L)) {
            mmap.put("roles", sysRoleService.findAll());
//			mmap.put("warehouses", whWarehouseService.findAll());
        } else {
            mmap.put("roles", sysRoleService.findAllBy("whId", userInfo.getWarehouseId()));
//			mmap.put("warehouses", whWarehouseService.findAllBy("id", userInfo.getWarehouseId()));
        }
        return urlPrefix + "/add";
    }

    /**
     * 保存用户
     */
    @PostMapping("/add")
    @RequiresPermissions("sys:user:add")
    @ResponseBody
    @BussinessLog(businessType = BusinessType.INSERT, title = "添加用户")
    public ResponseData add(SysUser user) {
        if (StringUtils.isEmpty(user.getPassword())) {
            return error("密码不能为空");
        }
//		Long warehouseId = user.getWhId();
//		WhWarehouse warehouse = whWarehouseService.getById(warehouseId);
//		user.setWhName(warehouse.getWarehouseMDMName());
        return sysUserService.addUser(user) ? success() : error("新增失败!");
    }

    /**
     * 修改用户
     */
    @GetMapping("/edit/{userId}")
    public String edit(@PathVariable("userId") Long userId, ModelMap mmap) {
        if (ConfigConstant.SUPER_ADMIN.equals(userId)) {
            throw new RxcException("超级管理员不允许编辑");
        }
        SysUser user = sysUserService.getById(userId);
        mmap.put("roles", sysRoleService.findAll());
        List<Long> roleIds = sysUserRoleService.findRoldIdsByUser(userId);
        mmap.put("selectRoles", StringUtils.join(roleIds, ","));
//		mmap.put("warehouses", whWarehouseService.findAll());
        mmap.put("user", user);
        return urlPrefix + "/edit";
    }

    /**
     * 更新用户
     */
    @PostMapping("/edit")
    @RequiresPermissions("sys:user:edit")
    @ResponseBody
    @BussinessLog(businessType = BusinessType.UPDATE, title = "编辑用户")
    public ResponseData edit(SysUser user) {
        if (ConfigConstant.SUPER_ADMIN.equals(user.getId())) {
            return error("超级管理员不允许编辑");
        }
//		Long warehouseId = user.getWhId();
//		WhWarehouse warehouse = whWarehouseService.getById(warehouseId);
//		user.setWhName(warehouse.getWarehouseMDMName());
        return sysUserService.updateUser(user) ? success() : error("修改失败!");
    }

    /**
     * 用户状态修改
     */
    @RequiresPermissions("sys:user:edit")
    @PostMapping("/changeStatus")
    @ResponseBody
    @BussinessLog(businessType = BusinessType.UPDATE, title = "修改用户状态")
    public ResponseData changeStatus(SysUser user) {
        if (ConfigConstant.SUPER_ADMIN.equals(user.getId())) {
            return error("超级管理员不允许修改");
        }
        boolean changeStatus = sysUserService.updateStatus(user);
        return changeStatus ? success() : error("用户状态修改失败");
    }

    /**
     * 删除用户
     */
    @RequestMapping(value = "/del", method = RequestMethod.POST)
    @RequiresPermissions("sys:user:del")
    @ResponseBody
    @BussinessLog(businessType = BusinessType.DELETE, title = "删除用户")
    public ResponseData delete(Long[] ids) {
        if (ArrayUtil.contains(ids, ShiroUtils.getUserId())) {
            return error("不能删除自己!");
        }
        if (ArrayUtil.contains(ids, ConfigConstant.SUPER_ADMIN)) {
            return error("超级管理员不允许删除");
        }
        return sysUserService.delUser(ids) ? success() : error("删除失败!");
    }

    @RequiresPermissions("sys:user:resetPwd")
    @GetMapping("/resetPwd/{id}")
    public String resetPwd(@PathVariable("id") Long userId, ModelMap mmap) {
        mmap.put("user", sysUserService.getById(userId));
        return urlPrefix + "/resetPwd";
    }

    @RequiresPermissions("sys:user:resetPwd")
    @PostMapping("/resetPwd")
    @ResponseBody
    @BussinessLog(businessType = BusinessType.UPDATE, title = "重置密码")
    public ResponseData resetPwdSave(SysUser user) {
        LoginUser loginUser = ShiroUtils.getUserInfo();
        if (user.getPassword().equals(Global.getDbKey("sys.user.initPassword"))) {
            user.setPwdSecurityLevel(0);
        } else {
            user.setPwdSecurityLevel(1);
        }
        String salt = ShiroUtils.randomSalt();
        // 新密码
        String newPassword = ShiroUtils.sha256(user.getPassword(), salt);

        boolean flag = sysUserService.updatePassWord(user.getId(), newPassword, salt, user.getPwdSecurityLevel());
        if (!flag) {
            return error(StringUtils.message("sys.user.oldPasswordError"));
        }
        if (loginUser.getId().equals(user.getId())) {
            loginUser.setPwdSecurityLevel(user.getPwdSecurityLevel());
            loginUser.setPassword(newPassword);
            loginUser.setSalt(salt);
            ShiroUtils.reloadUser(loginUser);
        }
        return success();
    }

    @ResponseBody
    @PostMapping("/updatePass")
    @BussinessLog(businessType = BusinessType.UPDATE, title = "修改密码")
    public ResponseData updatePass(String oldPassword, String newPassword) {
        LoginUser loginUser = ShiroUtils.getUserInfo();
        if (StringUtils.isNotEmpty(oldPassword) && StringUtils.isNotEmpty(newPassword)) {
            oldPassword = ShiroUtils.sha256(oldPassword, loginUser.getSalt());
            if (oldPassword.equals(loginUser.getPassword())) {
                String salt = ShiroUtils.randomSalt();
                // 新密码
                newPassword = ShiroUtils.sha256(newPassword, salt);
                boolean flag = sysUserService.updatePassWord(loginUser.getId(), newPassword, salt);
                if (!flag) {
                    return error("原密码错误");
                } else {
                    // 更新Shiro
                    loginUser.setSalt(salt);
                    loginUser.setPassword(newPassword);
                    ShiroUtils.reloadUser(loginUser);
                    return success();
                }
            } else {
                return error("修改密码失败，旧密码错误");
            }
        } else {
            return error("修改密码失败!");
        }
    }

    /**
     * 校验用户名
     */
    @RequestMapping(value = "/checkUserNameUnique", method = RequestMethod.POST)
    @ResponseBody
    public ResponseData checkLoginNameUnique(String username) {
        if (sysUserService.checkUserNameUnique(username)) {
            return success();
        }
        return error("登录名已经存在!");
    }

    /**
     * 校验手机号码
     */
    @RequestMapping(value = "/checkMobileUnique", method = RequestMethod.POST)
    @ResponseBody
    public ResponseData checkMobileUnique(SysUser user) {
        if (sysUserService.checkMobileUnique(user)) {
            return success();
        }
        return error("手机号已经存在!");
    }
}
