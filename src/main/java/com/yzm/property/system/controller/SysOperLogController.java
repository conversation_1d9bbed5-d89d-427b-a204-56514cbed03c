package com.yzm.property.system.controller;

import com.yzm.property.system.criteria.SysRoleCriteria;
import com.yzm.property.system.service.ISysOperLogService;
import com.yzm.common.annotation.BussinessLog;
import com.yzm.common.annotation.RepeatSubmit;
import com.yzm.common.enums.BusinessType;
import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@Controller
@RequestMapping("sys/operLog")
public class SysOperLogController extends BaseController{
	private String urlPrefix = "system/log";

	@Autowired
	private ISysOperLogService sysOperLogService;

	@GetMapping()
	@RequiresPermissions("sys:log:view")
	public String operlog() {
		return urlPrefix + "/operlog";
	}

	/**
	 * 列表
	 */
	@ResponseBody
	@RequestMapping("/list")
	public ResponseData list(PageBean p, SysRoleCriteria criteria) {
		Map<String, Object> page = sysOperLogService.findAllByPage(criteria, p.getPagable(Sort.by(Direction.DESC, "id")));
		return success(page);
	}

	@RequiresPermissions("sys:log:detail")
	@GetMapping("/detail/{operId}")
	public String detail(@PathVariable("operId") Long operId, ModelMap mmap) {
		mmap.put("sysLog", sysOperLogService.getById(operId));
		return urlPrefix + "/detail";
	}
	
	@BussinessLog(title = "操作日志", businessType = BusinessType.CLEAN)
	@RequiresPermissions("sys:log:clean")
	@PostMapping("/clean")
	@ResponseBody
	@RepeatSubmit
	public ResponseData clean() {
		return sysOperLogService.cleanLog() ? success() : error("清空失败!");
	}
	
	@RequestMapping("/del")
	@BussinessLog(title = "操作日志", businessType = BusinessType.DELETE)
	@RequiresPermissions("sys:log:del")
	@ResponseBody
	public ResponseData del(Long[] ids) {
		return sysOperLogService.deleteBatchByIds(ids) ? success() : error("删除失败!");
	}

}
