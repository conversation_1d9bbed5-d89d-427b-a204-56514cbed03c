package com.yzm.property.system.controller;

import com.yzm.property.system.criteria.SysDictDataCriteria;
import com.yzm.property.system.entity.SysDictData;
import com.yzm.property.system.service.ISysDictDataService;
import com.yzm.common.annotation.BussinessLog;
import com.yzm.common.enums.BusinessType;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.ResponseData;
import com.yzm.framework.redis.SysConfigRedis;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/sys/dict/data")
public class SysDictDataController extends BaseController {
    private String urlPrefix = "system/dict/data";

    @Autowired
    private ISysDictDataService sysDictDataService;
    @Autowired
    private SysConfigRedis sysConfigRedis;

    @RequiresPermissions("sys:dict:view")
    @GetMapping()
    public String dictData() {
        return urlPrefix + "/data";
    }

    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @RequiresPermissions("sys:dict:list")
    @ResponseBody
    public ResponseData list(Integer __page, Integer __limit, SysDictDataCriteria criteria) {
        Map<String, Object> page = sysDictDataService.findAllByPage(criteria, PageRequest.of(__page - 1, __limit));
        return success(page);
    }

    /**
     * 新增字典类型
     */
    @GetMapping("/add/{dictType}")
    public String add(@PathVariable("dictType") String dictType, ModelMap mmap) {
        mmap.put("dictType", dictType);
        return urlPrefix + "/add";
    }

    /**
     * 新增保存字典类型
     */
    @RequiresPermissions("sys:dict:add")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @ResponseBody
    @BussinessLog(businessType = BusinessType.INSERT, title = "字典数据")
    public ResponseData addSave(@Validated SysDictData dict) {
        if (StringUtils.isNotEmpty(sysDictDataService.save(dict))) {
            List<SysDictData> list = sysDictDataService.findAllByDictType(dict);
            sysConfigRedis.saveOrUpdateDict(dict.getDictType(), list);
            return success();
        } else {
            return error("新增失败!");
        }
    }

    /**
     * 修改字典类型
     */
    @GetMapping("/edit/{dictCode}")
    public String edit(@PathVariable("dictCode") Long dictCode, ModelMap mmap) {
        mmap.put("dictHtml", sysDictDataService.getById(dictCode));
        return urlPrefix + "/edit";
    }

    /**
     * 修改保存字典类型
     */
    @RequiresPermissions("sys:dict:edit")
    @RequestMapping(value = "/edit", method = RequestMethod.POST)
    @ResponseBody
    @BussinessLog(businessType = BusinessType.UPDATE, title = "编辑字典数据")
    public ResponseData editSave(@Validated SysDictData dict) {
        if (StringUtils.isNotEmpty(sysDictDataService.update(dict))) {
            return success();
        } else {
            return error("修改失败!");
        }
    }

    @RequiresPermissions("sys:dict:del")
    @RequestMapping(value = "/del", method = RequestMethod.POST)
    @ResponseBody
    @BussinessLog(businessType = BusinessType.DELETE, title = "删除字典数据")
    public ResponseData del(Long[] ids) {
        sysDictDataService.deleteBatchByIds(ids);
        return success();
    }
}
