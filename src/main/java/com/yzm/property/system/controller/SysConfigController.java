package com.yzm.property.system.controller;

import com.yzm.property.system.criteria.SysConfigCriteria;
import com.yzm.property.system.entity.SysConfig;
import com.yzm.property.system.service.ISysConfigService;
import com.yzm.common.annotation.BussinessLog;
import com.yzm.common.enums.BusinessType;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@Controller
@RequestMapping("/sys/config")
public class SysConfigController extends BaseController {
	private String urlPrefix = "system/config";

	@Autowired
	private ISysConfigService sysConfigService;

	/**
	 * 系统参数
	 */
	@GetMapping
	@RequiresPermissions("sys:config:view")
	public String config() {
		return urlPrefix + "/config";
	}

	@ResponseBody
	@PostMapping(value = "/list")
	public ResponseData page(PageBean page,SysConfigCriteria criteria) {
		Map<String, Object> datas = sysConfigService.findAllByPage(criteria, page.getPagable());
		return success(datas);
	}

	/**
	 * 新增参数配置
	 */
	@GetMapping("/add")
	public String add() {
		return urlPrefix + "/add";
	}

	/**
	 * 保存配置
	 */
	@ResponseBody
	@RequestMapping("/add")
	@RequiresPermissions("sys:config:add")
	@BussinessLog(businessType = BusinessType.INSERT, title = "添加系统参数")
	public ResponseData save(SysConfig config) {
		if (!sysConfigService.checkConfigKeyUnique(config)) {
			return error("新增参数'" + config.getParamName() + "'失败，参数键名已存在");
		}
		return StringUtils.isNotEmpty(sysConfigService.save(config)) ? success() : error("新增失败!");
	}

	/**
	 * 修改参数配置
	 */
	@GetMapping("/edit/{id}")
	public String edit(@PathVariable("id") Long id, ModelMap mmap) {
		mmap.put("cfg", sysConfigService.getById(id));
		return urlPrefix + "/edit";
	}

	/**
	 * 修改配置
	 */
	@RequestMapping(value = "/edit", method = RequestMethod.POST)
	@RequiresPermissions("sys:config:edit")
	@ResponseBody
	@BussinessLog(businessType=BusinessType.UPDATE,title="修改系统参数")
	public ResponseData update(SysConfig config) {
		if (!sysConfigService.checkConfigKeyUnique(config)) {
			return error("修改参数'" + config.getParamName() + "'失败，参数键名已存在");
		}
		return StringUtils.isNotEmpty(sysConfigService.update(config)) ? success() : error("修改失败!");
	}

	/**
	 * 校验参数键名
	 */
	@RequestMapping(value = "/checkConfigKeyUnique", method = RequestMethod.POST)
	@ResponseBody
	public ResponseData checkConfigKeyUnique(SysConfig config) {
		return sysConfigService.checkConfigKeyUnique(config) ? success() : error("已经存在!");
	}

	/**
	 * 删除配置
	 */
	@RequestMapping(value = "/del", method = RequestMethod.POST)
	@RequiresPermissions("sys:config:del")
	@ResponseBody
	@BussinessLog(businessType=BusinessType.DELETE,title="删除系统参数")
	public ResponseData delete(Long[] ids) {
		return sysConfigService.deleteBatchByIds(ids) ? success() : error("删除失败!");
	}

	@RequestMapping(value = "/clearConfig", method = RequestMethod.GET)
	@RequiresPermissions("sys:config:clear")
	@ResponseBody
	@BussinessLog(businessType=BusinessType.CLEAN,title="系统配置缓存")
	public ResponseData clearConfig() {
		return sysConfigService.clearConfigRedis() ? success() : error("Redis没有开启无需清理!");
	}
}
