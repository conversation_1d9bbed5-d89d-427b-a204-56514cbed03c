package com.yzm.property.system.controller;

import com.yzm.common.config.ConfigConstant;
import com.yzm.common.utils.*;
import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.ResponseData;
import com.yzm.framework.exception.RxcException;
import com.yzm.framework.freemark.Global;
import com.wf.captcha.ArithmeticCaptcha;
import com.wf.captcha.GifCaptcha;
import com.wf.captcha.SpecCaptcha;
import com.wf.captcha.base.Captcha;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.apache.shiro.subject.Subject;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Controller
public class LoginController extends BaseController {

    /**
     * 生成验证码图片
     */
    @RequestMapping("captcha.gif")
    public void captcha(HttpServletResponse response) throws IOException {
        response.setHeader("Cache-Control", "no-store, no-cache");
        response.setContentType("image/gif");
        if (Global.getDbKey("SYS_LOGIN_CAPTACHA_TYPE", "0").equals("0")) {

            SpecCaptcha gifCaptcha = new SpecCaptcha(130, 48, 4);
            gifCaptcha.setCharType(Captcha.TYPE_ONLY_NUMBER);
            String result = gifCaptcha.text();
            ShiroUtils.setSessionAttribute(ConfigConstant.KAPTCHA_SESSION_KEY, result);
//            session.setAttribute(ConfigConstant.KAPTCHA_SESSION_KEY, result);
            gifCaptcha.out(response.getOutputStream());

//            GifCaptcha gifCaptcha = new GifCaptcha(130, 48, 4);
//            gifCaptcha.setCharType(Captcha.TYPE_DEFAULT);
//            String result = gifCaptcha.text();
//            ShiroUtils.setSessionAttribute(ConfigConstant.KAPTCHA_SESSION_KEY, result);
//            gifCaptcha.out(response.getOutputStream());
            return;
        } else if (Global.getDbKey("SYS_LOGIN_CAPTACHA_TYPE", "0").equals("1")) {
            ArithmeticCaptcha gifCaptcha = new ArithmeticCaptcha();
            // 几位数运算，默认是两位
            gifCaptcha.setLen(3);
            // 获取运算的公式：3+2=?
            gifCaptcha.getArithmeticString();
            // 获取运算的结果：5
            String result = gifCaptcha.text();
            ShiroUtils.setSessionAttribute(ConfigConstant.KAPTCHA_SESSION_KEY, result);
            gifCaptcha.out(response.getOutputStream());
            return;
        } else if (Global.getDbKey("SYS_LOGIN_CAPTACHA_TYPE", "0").equals("2")) {
            int rd = Math.random() > 0.5 ? 1 : 0;
            if (rd == 1) {
                GifCaptcha gifCaptcha = new GifCaptcha(130, 48, 4);
                gifCaptcha.setCharType(Captcha.TYPE_DEFAULT);
                String result = gifCaptcha.text();
                ShiroUtils.setSessionAttribute(ConfigConstant.KAPTCHA_SESSION_KEY, result);
                gifCaptcha.out(response.getOutputStream());
                return;
            } else {
                ArithmeticCaptcha gifCaptcha = new ArithmeticCaptcha();
                // 几位数运算，默认是两位
                gifCaptcha.setLen(3);
                // 获取运算的公式：3+2=?
                gifCaptcha.getArithmeticString();
                // 获取运算的结果：5
                String result = gifCaptcha.text();
                ShiroUtils.setSessionAttribute(ConfigConstant.KAPTCHA_SESSION_KEY, result);
                gifCaptcha.out(response.getOutputStream());
                return;
            }
        }
    }

    @GetMapping(value = "login")
    public String loginUi(ModelMap mmp) {

//        if (!AuthUtils.canDataChange()) {
//            mmp.put("machineCode", Machine.getMachieCode());
//            return "register";
//        }


        if (ShiroUtils.isLogin()) {
            ShiroUtils.logout();
        }
        return "login";
    }

    @ResponseBody
    @GetMapping("/getRegistData")
    public ResponseData getRegistData() {
        String machieCode = Machine.getMachieCode();
        System.out.println(machieCode);
        return success(machieCode);
    }
    @GetMapping("/registInfo")
    public String registInfo() {
        return "reg";
    }

    @ResponseBody
    @RequestMapping(value="/registerInfo", method=RequestMethod.POST)
    public ResponseData updateRegister(String key, String publickey) {
        if(key != null && publickey != null) {
            byte[] decodedData;
            try {
                decodedData = RSAUtils.decryptByPublicKey(Base64Utils.decode(key), publickey);
                String target = new String(decodedData);
                if(target.startsWith(Machine.getMachieCode())) {
                    PropertyConfig.writeProperties("machinekey", key);
                    PropertyConfig.writeProperties("publickey", publickey);
                    return ResponseData.success();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return ResponseData.error("授权失败");

    }

    @ResponseBody
    @RequestMapping(value = "/login", method = RequestMethod.POST)
    public ResponseData login(String username, String password, Boolean rememberMe) {
        rememberMe=true;

        Subject subject = null;
        try {
            // 账号密码登录
            UsernamePasswordToken token = new UsernamePasswordToken(username, password, rememberMe);
            subject = ShiroUtils.getSubject();
            subject.login(token);
        } catch (AuthenticationException e) {
            e.printStackTrace();
            RxcException ex = (RxcException) e.getCause();
            String msg = StringUtils.message("sys.login.failure");
            if (!StringUtils.isEmpty(e.getMessage())) {
                msg = e.getMessage();
            }
            if ("50004".equals(ex.getCode())) {
                return error(ex.getCode(), ex.getMessage());
            }
            return error(msg);
        }
        return success("登录成功!");
    }

    /**
     * 退出
     */
    @RequestMapping(value = "logout", method = RequestMethod.GET)
    public String logout() {
        ShiroUtils.getSession().stop();
        ShiroUtils.logout();
        return REDIRECT + "login";
    }
}
