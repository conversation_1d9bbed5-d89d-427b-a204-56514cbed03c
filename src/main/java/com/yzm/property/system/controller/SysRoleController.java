package com.yzm.property.system.controller;

import com.yzm.property.system.criteria.SysRoleCriteria;
import com.yzm.property.system.entity.SysRole;
import com.yzm.property.system.service.ISysRoleService;
import com.yzm.common.annotation.BussinessLog;
import com.yzm.common.enums.BusinessType;
import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.ResponseData;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 角色管理控制器
 */
@Controller
@RequestMapping("/sys/role")
public class SysRoleController extends BaseController {
	private String urlPrefix = "system/role";

	@Autowired
	private ISysRoleService sysRoleService;

	@GetMapping
	@RequiresPermissions("sys:role:view")
	public String role() {
		return urlPrefix + "/role";
	}
	
	@ResponseBody
	@PostMapping(value = "/list")
	public ResponseData page(Integer __page, Integer __limit, SysRoleCriteria criteria) {
		Map<String, Object> datas = sysRoleService.findAllByPage(criteria, PageRequest.of(__page - 1, __limit));
		return success(datas);
	}

	/**
	 * 新增角色
	 */
	@GetMapping("/add")
	public String add(ModelMap mmap) {
		return urlPrefix + "/add";
	}

	/**
	 * 保存角色
	 */
	@RequestMapping(value = "/add", method = RequestMethod.POST)
	@RequiresPermissions("sys:role:add")
	@ResponseBody
	@BussinessLog(businessType=BusinessType.INSERT,title="添加角色")
	public ResponseData add(SysRole role) {
		return sysRoleService.addRole(role)? success() : error("新增失败!");
	}

	/**
	 * 修改角色
	 */
	@GetMapping("/edit/{roleId}")
	public String edit(@PathVariable("roleId") Long roleId, ModelMap mmap) {
		mmap.put("role", sysRoleService.getById(roleId));
		return urlPrefix + "/edit";
	}

	/**
	 * 修改角色
	 */
	@RequestMapping(value = "/edit", method = RequestMethod.POST)
	@RequiresPermissions("sys:role:edit")
	@ResponseBody
	@BussinessLog(businessType=BusinessType.UPDATE,title="修改角色")
	public ResponseData update(SysRole role) {
		return sysRoleService.updateRole(role) ? success() : error("修改失败!");
	}

	/**
	 * 删除角色
	 */
	@RequestMapping(value = "/del", method = RequestMethod.POST)
	@RequiresPermissions("sys:role:del")
	@ResponseBody
	@BussinessLog(businessType=BusinessType.DELETE,title="删除角色")
	public ResponseData delete(Long[] ids) {
		return sysRoleService.deleteBatch(ids) ? success() : error("请先取消用户和角色的关联");
	}
	
	/**
	 * 校验角色名称
	 */
	@RequestMapping(value = "/checkRoleNameUnique", method = RequestMethod.POST)
	@ResponseBody
	public ResponseData checkRoleNameUnique(SysRole role) {
		if (sysRoleService.checkRoleNameUnique(role)) {
			return success();
		}
		return error("已经存在!");
	}
}
