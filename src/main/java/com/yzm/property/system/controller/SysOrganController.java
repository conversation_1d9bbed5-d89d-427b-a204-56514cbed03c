package com.yzm.property.system.controller;

import cn.hutool.core.util.StrUtil;
import com.yzm.property.system.entity.SysOrgan;
import com.yzm.property.system.service.ISysOrganService;
import com.yzm.common.annotation.BussinessLog;
import com.yzm.common.enums.BusinessType;
import com.yzm.common.utils.ShiroUtils;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.ResponseData;
import com.yzm.framework.bean.Ztree;
import com.yzm.framework.exception.RxcException;
import com.yzm.framework.shiro.LoginUser;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Controller
@RequestMapping("/sys/organ")
public class SysOrganController extends BaseController {
	private String urlPrefix = "system/organ";

	@Autowired
	private ISysOrganService sysOrganService;

	@GetMapping
	@RequiresPermissions("sys:organ:view")
	public String organView() {
		return urlPrefix + "/organ";
	}

	@ResponseBody
	@PostMapping(value = "/list")
	public ResponseData list(SysOrgan sysOrgan) {
		LoginUser userInfo = ShiroUtils.getUserInfo();
		List<SysOrgan> data = new ArrayList<>();
		if (StringUtils.isNotEmpty(sysOrgan.getName())) {
			data = sysOrganService.findOrganList(userInfo.getId()).stream()
					.filter(item -> item.getName().contains(sysOrgan.getName())).collect(Collectors.toList());
		} else {
			data = sysOrganService.findOrganList(userInfo.getId());
		}
		return success().put("list", data);
	}

	/**
	 * 新增
	 */
	@GetMapping("/add/{parentId}")
	public String add(@PathVariable("parentId") Long parentId, ModelMap mmap) {
		SysOrgan organ = null;
		if (0L != parentId) {
			organ = sysOrganService.getById(parentId);
		} else {
			organ = new SysOrgan();
			organ.setId(0L);
			organ.setName("总单位");
		}
		mmap.put("organ", organ);
		return urlPrefix + "/add";
	}

	/**
	 * 保存
	 */
	@RequestMapping(value = "/add", method = RequestMethod.POST)
	@RequiresPermissions("sys:organ:add")
	@ResponseBody
	@BussinessLog(businessType=BusinessType.INSERT,title="新增单位")
	public ResponseData save(SysOrgan organ) {
		// 数据校验
		verifyForm(organ);
		if (!sysOrganService.checkOrganNameUnique(organ)) {
			return error("新增单位'" + organ.getName() + "'失败，单位名称已存在");
		}
		return sysOrganService.addOrgan(organ) ? success() : error("新增失败!");
	}

	private void verifyForm(SysOrgan organ) {
		if (StrUtil.isBlank(organ.getName())) {
			throw new RxcException("单位名称不能为空");
		}
		Long parentId = organ.getParentId();
		if (parentId == null) {
			throw new RxcException("上级单位不能为空");
		}
	}

	/**
	 * 修改单位
	 */
	@GetMapping("/edit/{organId}")
	public String edit(@PathVariable("organId") Long organId, ModelMap mmap) {
		mmap.put("organ", sysOrganService.getById(organId));
		return urlPrefix + "/edit";
	}

	/**
	 * 修改
	 */
	@RequestMapping(value = "/edit", method = RequestMethod.POST)
	@RequiresPermissions("sys:organ:edit")
	@ResponseBody
	@BussinessLog(businessType=BusinessType.UPDATE,title="修改单位")
	public ResponseData edit(SysOrgan organ) {
		// 数据校验
		verifyForm(organ);
		if (!sysOrganService.checkOrganNameUnique(organ)) {
			return error("新增单位'" + organ.getName() + "'失败，单位名称已存在");
		}
		if (StringUtils.isNotEmpty(sysOrganService.save(organ))) {
			ShiroUtils.clearCachedAuthorizationInfo(); // 清理权限缓存
			return success();
		} else {
			return error("修改失败");
		}
	}

	/**
	 * 删除
	 */
	@RequestMapping(value = "/del/{organId}")
	@RequiresPermissions("sys:organ:del")
	@ResponseBody
	@BussinessLog(businessType=BusinessType.DELETE,title="删除单位")
	public ResponseData delete(@PathVariable("organId") Long organId) {
		List<SysOrgan> menuList = sysOrganService.findListParentId(organId);
		if (menuList.size() > 0) {
			return error("请先删除子单位");
		}
		ShiroUtils.clearCachedAuthorizationInfo(); // 清理权限缓存
		sysOrganService.deleteById(organId);
		return success();
	}

	/**
	 * 加载所有单位列表树
	 */
	@GetMapping("/organTreeData")
	@ResponseBody
	public List<Ztree> organTreeData() {
		List<Ztree> ztrees = sysOrganService.organTreeData(ShiroUtils.getUserInfo());
		return ztrees;
	}

	/**
	 * 加载所有单位列表树
	 */
	@ResponseBody
	@GetMapping("/userOrganTreeData/{userId}")
	public List<Ztree> userOrganTreeData(@PathVariable Long userId) {
		List<Ztree> ztrees = sysOrganService.userOrganTreeData(userId);
		return ztrees;
	}
}
