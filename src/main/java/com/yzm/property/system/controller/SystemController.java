package com.yzm.property.system.controller;

import com.yzm.property.system.entity.SysMenu;
import com.yzm.property.system.service.ISysUserService;
import com.yzm.common.utils.ShiroUtils;
import com.yzm.framework.shiro.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

@Controller
public class SystemController {

	@Autowired
	private ISysUserService sysUserService;

	@GetMapping(value = { "/", "/index" })
	public String index(ModelMap mmp) {
		LoginUser userInfo = ShiroUtils.getUserInfo();
		List<SysMenu> menuList = sysUserService.findMenuByUserId(userInfo.getId());
		mmp.put("user", userInfo);
		mmp.put("menuList", menuList);
		return "index";
	}

	// 切换主题
	@GetMapping("/sys/switchSkin")
	public String switchSkin(ModelMap mmap) {
		return "skin";
	}

	@RequestMapping("main")
	public String main() {
		return "main";
	}

}
