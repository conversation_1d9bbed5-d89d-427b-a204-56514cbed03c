package com.yzm.property.system.controller;

import cn.hutool.core.util.StrUtil;
import com.yzm.property.system.entity.SysMenu;
import com.yzm.property.system.entity.SysRole;
import com.yzm.property.system.service.ISysMenuService;
import com.yzm.common.annotation.BussinessLog;
import com.yzm.common.constant.Constant;
import com.yzm.common.enums.BusinessType;
import com.yzm.common.utils.ShiroUtils;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.ResponseData;
import com.yzm.framework.bean.Ztree;
import com.yzm.framework.exception.RxcException;
import com.yzm.framework.shiro.LoginUser;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Controller
@RequestMapping(value = "sys/menu")
public class SysMenuController extends BaseController {
	private String urlPrefix = "system/menu";

	@Autowired
	private ISysMenuService sysMenuService;

	@RequiresPermissions("sys:menu:view")
	@GetMapping
	public String user(ModelMap mmap) {
		return urlPrefix + "/menu";
	}

	@ResponseBody
	@PostMapping(value = "/list")
	public ResponseData list() {
		LoginUser userInfo = ShiroUtils.getUserInfo();
		List<SysMenu> datas = sysMenuService.findMenuList(userInfo.getId());
		return success().put("list", datas);
	}

	/**
	 * 新增
	 */
	@GetMapping("/add/{parentId}")
	public String add(@PathVariable("parentId") Long parentId, ModelMap mmap) {
		SysMenu menu = null;
		if (0L != parentId) {
			menu = sysMenuService.getById(parentId);
		} else {
			menu = new SysMenu();
			menu.setId(0L);
			menu.setName("主目录");
		}
		mmap.put("menu", menu);
		return urlPrefix + "/add";
	}

	/**
	 * 保存
	 */
	@RequestMapping(value = "/add", method = RequestMethod.POST)
	@RequiresPermissions("sys:menu:add")
	@ResponseBody
	@BussinessLog(businessType=BusinessType.INSERT,title="新增菜单")
	public ResponseData save(@Validated SysMenu menu) {
		// 数据校验
		verifyForm(menu);
		if (!sysMenuService.checkMenuNameUnique(menu)) {
			return error("新增菜单'" + menu.getName() + "'失败，菜单名称已存在");
		}
		ShiroUtils.clearCachedAuthorizationInfo(); // 清理权限缓存
		return StringUtils.isNotEmpty(sysMenuService.save(menu)) ? success() : error("新增失败!");
	}

	/**
	 * 修改菜单
	 */
	@GetMapping("/edit/{menuId}")
	public String edit(@PathVariable("menuId") Long menuId, ModelMap mmap) {
		mmap.put("menu", sysMenuService.getById(menuId));
		return urlPrefix + "/edit";
	}
	
	/**
	 * 修改
	 */
	@RequestMapping(value = "/edit", method = RequestMethod.POST)
	@RequiresPermissions("sys:menu:edit")
	@ResponseBody
	@BussinessLog(businessType=BusinessType.UPDATE,title="修改菜单")
	public ResponseData edit(SysMenu menu) {
		// 数据校验
		verifyForm(menu);
		if (!sysMenuService.checkMenuNameUnique(menu)){
			return error("新增菜单'" + menu.getName()+ "'失败，菜单名称已存在");
		}
		if(StringUtils.isNotEmpty(sysMenuService.save(menu))){
			ShiroUtils.clearCachedAuthorizationInfo(); //清理权限缓存
			return success();
		}else {
			return error("修改失败");
		}
	}
	
	/**
	 * 删除
	 */
	@RequestMapping(value = "/del/{menuId}")
	@RequiresPermissions("sys:menu:del")
	@ResponseBody
	@BussinessLog(businessType=BusinessType.DELETE,title="删除菜单")
	public ResponseData delete(@PathVariable("menuId") Long menuId) {
		List<SysMenu> menuList = sysMenuService.findListParentId(menuId);
		if (menuList.size() > 0) {
			return error("请先删除子菜单或按钮");
		}
		ShiroUtils.clearCachedAuthorizationInfo(); //清理权限缓存
		sysMenuService.deleteById(menuId);
		return success();
	}

	/**
	 * 清除菜单的redis缓存
	 * @return
	 */
	@RequestMapping(value = "/clearMenu", method = RequestMethod.GET)
	@RequiresPermissions("sys:menu:clear")
	@ResponseBody
	public ResponseData clearConfig(){
		return sysMenuService.clearMenuRedis()?success():error("清除失败!");
	}
	
	/**
	 * 加载所有菜单列表树
	 */
	@GetMapping("/menuTreeData")
	@ResponseBody
	public List<Ztree> menuTreeData(){
		List<Ztree> ztrees = sysMenuService.menuTreeData(ShiroUtils.getUserInfo());
		return ztrees;
	}
	
	/**
	 * 加载角色菜单列表树
	 */
	@GetMapping("/roleMenuTreeData")
	@ResponseBody
	public List<Ztree> roleModuleMenuTreeData(SysRole role){
		List<Ztree> ztrees = sysMenuService.roleMenuTreeData(role, ShiroUtils.getUserInfo());
		return ztrees;
	}

	/**
	 * 图标选择
	 */
	@GetMapping("/iconSelect")
	public String iconselect(@RequestParam(value = "value", required = true) String value, ModelMap mmap) {
		mmap.put("iconValue", value);
		return urlPrefix + "/icon";
	}

	/**
	 * 校验菜单名称
	 */
	@RequestMapping(value = "/checkMenuNameUnique", method = RequestMethod.POST)
	@ResponseBody
	public ResponseData checkMenuNameUnique(SysMenu menu) {
		return sysMenuService.checkMenuNameUnique(menu) ? success() : error("已经存在!");
	}

	/**
	 * 验证参数是否正确
	 */
	private void verifyForm(SysMenu menu) {
		if (StrUtil.isBlank(menu.getName())) {
			throw new RxcException("菜单名称不能为空");
		}

		if (menu.getParentId() == null) {
			throw new RxcException("上级菜单不能为空");
		}

		// 菜单
		if (menu.getType() == Constant.MenuType.MENU.getValue()) {
			if (StrUtil.isBlank(menu.getUrl())) {
				throw new RxcException("菜单URL不能为空");
			}
		}

		// 上级菜单类型
		int parentType = Constant.MenuType.CATALOG.getValue();
		if (menu.getParentId() != 0) {
			SysMenu parentMenu = sysMenuService.getById(menu.getParentId());
			parentType = parentMenu.getType();
		}

		// 目录、菜单
		if (menu.getType() == Constant.MenuType.CATALOG.getValue()
				|| menu.getType() == Constant.MenuType.MENU.getValue()) {
			if (parentType != Constant.MenuType.CATALOG.getValue()) {
				throw new RxcException("上级菜单只能为目录类型");
			}
			return;
		}

		// 按钮
		if (menu.getType() == Constant.MenuType.BUTTON.getValue()) {
			if (parentType != Constant.MenuType.MENU.getValue()) {
				throw new RxcException("上级菜单只能为菜单类型");
			}
			return;
		}
	}
}
