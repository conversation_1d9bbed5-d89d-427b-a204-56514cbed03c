package com.yzm.property.system.controller;

import com.yzm.property.system.criteria.SysDictDataCriteria;
import com.yzm.property.system.entity.SysDictData;
import com.yzm.property.system.entity.SysDictType;
import com.yzm.property.system.service.ISysDictDataService;
import com.yzm.property.system.service.ISysDictTypeService;
import com.yzm.common.annotation.BussinessLog;
import com.yzm.common.enums.BusinessType;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.ResponseData;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 字典管理
 */
@Controller
@RequestMapping("/sys/dict")
public class SysDictTypeController extends BaseController{
	private String urlPrefixBase = "system/dict/type";
	
	@Autowired
	private ISysDictTypeService sysDictTypeService;
	@Autowired
	private ISysDictDataService sysDictDataService;
	
	@RequiresPermissions("sys:dict:view")
	@GetMapping()
	public String dictType() {
		return urlPrefixBase + "/type";
	}

	@PostMapping("/list")
	@ResponseBody
	public ResponseData list(Integer __page, Integer __limit, SysDictDataCriteria user) {
		Map<String, Object> page = sysDictTypeService.findAllByPage(user, PageRequest.of(__page-1, __limit));
		return success(page);
	}
	
	/**
	 * 查询字典详细
	 */
	@RequiresPermissions("sys:dict:list")
	@GetMapping("/detail/{dictId}")
	public String detail(@PathVariable("dictId") Long dictId, ModelMap mmap) {
		mmap.put("dictHtml", sysDictTypeService.getById(dictId));
		mmap.put("dictList", sysDictTypeService.findAll());
		return "system/dict/data/data";
	}

	/**
	 * 新增字典类型
	 */
	@GetMapping("/add")
	public String add() {
		return urlPrefixBase + "/add";
	}
	
	/**
	 * 新增保存字典类型
	 */
	@RequiresPermissions("sys:dict:add")
	@RequestMapping(value = "/add", method = RequestMethod.POST)
	@ResponseBody
	@BussinessLog(businessType=BusinessType.INSERT,title="新增字段")
	public ResponseData addSave(@Validated SysDictType dict) {
		if (!sysDictTypeService.checkDictTypeUnique(dict)) {
			return error("新增字典'" + dict.getDictName() + "'失败，字典类型已存在");
		}
		if (StringUtils.isNotEmpty(sysDictTypeService.save(dict))) {
			return success();
		} else {
			return error("新增失败!");
		}
	}
	
	/**
	 * 修改字典类型
	 */
	@GetMapping("/edit/{dictId}")
	public String edit(@PathVariable("dictId") Long dictId, ModelMap mmap) {
		mmap.put("dictHtml", sysDictTypeService.getById(dictId));
		return urlPrefixBase + "/edit";
	}

	/**
	 * 修改保存字典类型
	 */
	@RequiresPermissions("sys:dict:edit")
	@RequestMapping(value = "/edit", method = RequestMethod.POST)
	@ResponseBody
	@BussinessLog(businessType=BusinessType.UPDATE,title="修改字段")
	public ResponseData editSave(@Validated SysDictType dict) {
		if (!sysDictTypeService.checkDictTypeUnique(dict)) {
			return error("修改字典'" + dict.getDictName() + "'失败，字典类型已存在");
		}
		return StringUtils.isNotEmpty(sysDictTypeService.update(dict)) ? success() : error("修改失败!");
	}

	@RequiresPermissions("sys:dict:del")
	@RequestMapping(value = "/del", method = RequestMethod.POST)
	@ResponseBody
	@BussinessLog(businessType=BusinessType.DELETE,title="删除字典")
	public ResponseData del(Long[] ids) {
		boolean flag = true;
		for (Long id : ids) {
			SysDictType dictType = sysDictTypeService.getById(id);
			List<SysDictData> dictDatas = sysDictDataService.selectDictDataByType(dictType.getDictType());
			if(StringUtils.isNotEmpty(dictDatas) && dictDatas.size() == 0) {
				flag = false;
				break;
			}
		}
		if(flag) {
			sysDictTypeService.deleteBatchByIds(ids);
			return success();
		}
		return error("请先删除字典列表数据");
	}
	
	@RequestMapping(value = "/clearDict", method = RequestMethod.GET)
	@RequiresPermissions("sys:dict:clear")
	@ResponseBody
	@BussinessLog(businessType=BusinessType.CLEAN,title="字典缓存")
	public ResponseData clearConfig() {
		return sysDictTypeService.clearDictRedis() ? success() : error("Redis没有开启无需清理!");
	}
}
