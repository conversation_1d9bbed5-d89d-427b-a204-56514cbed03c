package com.yzm.property.system.controller;

import com.yzm.property.system.criteria.SysRoleCriteria;
import com.yzm.property.system.service.ISysLoginInfoService;
import com.yzm.common.annotation.BussinessLog;
import com.yzm.common.annotation.RepeatSubmit;
import com.yzm.common.enums.BusinessType;
import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.ResponseData;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Map;

@Controller
@RequestMapping("sys/loginInfo")
public class SysLoginInfoController extends BaseController{
	private String urlPrefix = "system/log";

	@Autowired
	private ISysLoginInfoService sysLoginInfoService;

	@RequiresPermissions("sys:logininfo:view")
	@GetMapping()
	public String indfo() {
		return urlPrefix + "/logininfo";
	}
	
	/**
	 * 列表
	 */
	@RequestMapping("/list")
	@ResponseBody
	public ResponseData list(Integer __page, Integer __limit, SysRoleCriteria criteria) {
		Map<String, Object> page = sysLoginInfoService.findAllByPage(criteria, PageRequest.of(__page - 1, __limit));
		return success(page);
	}

	@RequestMapping("/del")
	@BussinessLog(title = "登陆日志", businessType = BusinessType.DELETE)
	@RequiresPermissions("sys:logininfo:del")
	@ResponseBody
	public ResponseData del(Long[] ids) {
		return sysLoginInfoService.deleteBatchByIds(ids) ? success() : error("删除失败!");
	}
	
	@BussinessLog(title = "登陆日志", businessType = BusinessType.CLEAN)
	@RequiresPermissions("sys:logininfo:clean")
	@PostMapping("/clean")
	@ResponseBody
	@RepeatSubmit
	public ResponseData clean() {
		return sysLoginInfoService.cleanLog() ? success() : error("清空失败!");
	}
}
