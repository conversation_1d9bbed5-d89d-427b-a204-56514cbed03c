package com.yzm.property.basis.utils;

import javax.xml.crypto.Data;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Package com.wisesoft.common.utils
 * @Description:
 * @date 2020/4/16 17:25
 */
public class BeanMapUtils {

    private static Pattern linePattern = Pattern.compile("_(\\w)");
    private static Pattern humpPattern = Pattern.compile("[A-Z]");
    /** 下划线转驼峰 */
    public static String lineToHump(String str) {
        str = str.toLowerCase();
        Matcher matcher = linePattern.matcher(str);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(sb, matcher.group(1).toUpperCase());
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    /** 驼峰转下划线 */
    public static String humpToLine(String str) {
        Matcher matcher = humpPattern.matcher(str);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(sb, "_" + matcher.group(0).toLowerCase());
        }
        matcher.appendTail(sb);
        return sb.toString();
    }
    /**
     * 将map集合中的数据转化为指定对象的同名属性中
     */
    public static <T> T mapToBean(Map<String, Object> map,Class<T> clazz) throws Exception {
        T bean = null;
        try {
            bean = clazz.newInstance();
            for (Field field : getAllFields(clazz)) {
                if (map.containsKey(humpToLine(field.getName()))) {
                    boolean flag = field.isAccessible();
                    field.setAccessible(true);
                    Object object = map.get(humpToLine(field.getName()));
                    if (object != null){
                        if(field.getType().isAssignableFrom(object.getClass())){
                            field.set(bean, object);
                        }else{
                            try {
                                if(field.getType().toString().contains("Long")){
                                    field.set(bean, Long.parseLong(object.toString()));
                                }
                                if(field.getType().toString().contains("BigDecimal")){
                                    field.set(bean, new BigDecimal(object.toString()));
                                }
                                if(field.getType().toString().contains("Date")){
                                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                                    field.set(bean, simpleDateFormat.parse(object.toString()));
                                }
                            }catch (Exception e){
                                System.out.println("aaaaaaa");
                            }
                            //判断属性类型 进行转换,map中存放的是Object对象需要转换 实体类中有多少类型就加多少类型,实体类属性用包装类;

                        }
                    }
                    field.setAccessible(flag);
                }
            }
            return bean;
        } catch (InstantiationException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        return bean;
    }
    /**
     * 获取自身和上一级父类属性
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T> Field[] getAllFields(Class<T> clazz){
        List<Field> fieldList = new ArrayList<>();
        fieldList.addAll(new ArrayList<>(Arrays.asList(clazz.getDeclaredFields())));
        if(clazz.getSuperclass()!=null){
            //写死的 取一层父类 TODO 没有好的解决方案
            fieldList.addAll(new ArrayList<>(Arrays.asList(clazz.getSuperclass().getDeclaredFields())));
        }
        Field[] fields = new Field[fieldList.size()];
        fieldList.toArray(fields);
        return fields;
    }
}
