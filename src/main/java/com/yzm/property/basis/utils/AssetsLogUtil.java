package com.yzm.property.basis.utils;

import com.yzm.property.basis.entity.AssetsInfo;
import com.yzm.property.basis.entity.AssetsLog;
import com.yzm.property.basis.repository.AssetsLogRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/1 9:04
 */
@Component
public class AssetsLogUtil {

    public static final String OPERATION_RECEIPT = "领用";
    public static final String OPERATION_CANCELLING_STOCKS = "退库";
    public static final String OPERATION_BORROW = "借用";
    public static final String OPERATION_RETURN = "借用归还";
    public static final String OPERATION_REPAIRS_ADD = "送修";
//    public static final String OPERATION_REPAIRS_UPDATE = "维修信息修改";
    public static final String OPERATION_REPAIRS_RETURN = "维修归还";
    public static final String OPERATION_SCRAP_ADD = "报废";
//    public static final String OPERATION_SCRAP_UPDATE = "报废信息修改";


    private static AssetsLogRepository assetsLogRepository;

    @Autowired
    public void init(AssetsLogRepository assetsLogRepository) {
        AssetsLogUtil.assetsLogRepository = assetsLogRepository;
    }

    public static void createAssetsLog(Long operationId,String operationOtherId, Long assetsId ,String assetsOtherId,String assetsName, String type){
        AssetsLog assetsLog = new AssetsLog(operationId,operationOtherId,type,assetsId,assetsOtherId,assetsName);
        AssetsLog save = assetsLogRepository.save(assetsLog);
        if (save == null){
            throw new RuntimeException("资产流水存储失败！");
        }
    }

    public static void createAssetsLog(Long operationId,String operationOtherId, AssetsInfo assetsInfo, String type){
        AssetsLog assetsLog = new AssetsLog(operationId,operationOtherId,type,assetsInfo.getId(),assetsInfo.getOtherId(),assetsInfo.getAssetsName());
        AssetsLog save = assetsLogRepository.save(assetsLog);
        if (save == null){
            throw new RuntimeException("资产流水存储失败！");
        }
    }
}
