package com.yzm.property.basis.criteria;

import com.yzm.common.annotation.Query;
import com.yzm.framework.base.CriteriaBean;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/9 11:38
 */
@Data
public class InventoryCriteria extends CriteriaBean {

    @Query(type = Query.Type.EQUAL)
    private String inventoryNumber;
    @Query(type = Query.Type.EQUAL)
    private String assetsNumber;
    @Query(type = Query.Type.EQUAL)
    private Long inventoryStatus;

}
