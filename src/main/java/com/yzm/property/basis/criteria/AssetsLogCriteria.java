package com.yzm.property.basis.criteria;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yzm.common.annotation.Query;
import com.yzm.framework.base.CriteriaBean;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 11:38
 */
@Data
public class AssetsLogCriteria extends CriteriaBean {

    @Query(type = Query.Type.INNER_LIKE)
    private String operationId;
    @Query(type = Query.Type.INNER_LIKE)
    private String operationOtherId;
    @Query(type = Query.Type.INNER_LIKE)
    private String operationName;
    @Query(type = Query.Type.INNER_LIKE)
    private String assetsOtherId;
    @Query(type = Query.Type.INNER_LIKE)
    private String assetsName;
    @Query(type = Query.Type.GREATER_THAN, propName = "createTime")
    @JsonFormat(shape= JsonFormat.Shape.STRING, pattern="yyyy-MM-dd")
    private Date startTime;
    @Query(type = Query.Type.LESS_THAN, propName = "createTime")
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd")
    private Date endTime;
}
