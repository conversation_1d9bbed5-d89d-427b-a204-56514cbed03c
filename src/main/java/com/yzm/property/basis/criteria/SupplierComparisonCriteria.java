package com.yzm.property.basis.criteria;

import com.yzm.common.annotation.Query;
import com.yzm.framework.base.CriteriaBean;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 11:38
 */
@Data
public class SupplierComparisonCriteria extends CriteriaBean {
    @Query(type = Query.Type.INNER_LIKE)
    private String supplierName;//供应商名称
    @Query(type = Query.Type.INNER_LIKE)
    private  String supplierNumber;//供应商代码
    @Query(type = Query.Type.INNER_LIKE)
    private String supplierMaterialsCode;//供应商材料代码
    @Query(type = Query.Type.INNER_LIKE)
    private  String internalMaterialsCode;//内部代码


}
