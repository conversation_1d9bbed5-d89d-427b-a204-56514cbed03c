package com.yzm.property.basis.criteria;

import com.yzm.common.annotation.Query;
import com.yzm.framework.base.CriteriaBean;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 11:38
 */
@Data
public class TypeCriteria extends CriteriaBean {

    @Query(type = Query.Type.INNER_LIKE)
    private String basisName;
    @Query(type = Query.Type.INNER_LIKE)
    private String typeRemark;

}
