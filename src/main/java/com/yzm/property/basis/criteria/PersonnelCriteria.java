package com.yzm.property.basis.criteria;

import com.yzm.common.annotation.Query;
import com.yzm.framework.base.CriteriaBean;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 11:38
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PersonnelCriteria extends CriteriaBean {

    @Query(type = Query.Type.INNER_LIKE)
    private String basisName;
    @Query(type = Query.Type.INNER_LIKE)
    private String personnelDepartment;
    @Query(type = Query.Type.EQUAL)
    private Integer deleteFlag;
}
