package com.yzm.property.basis.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yzm.common.utils.OrderUtils;
import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.basis.criteria.InventoryCriteria;
import com.yzm.property.basis.entity.*;
import com.yzm.property.basis.repository.*;
import com.yzm.property.basis.service.InventorySimpleItemService;
import com.yzm.property.basis.service.InventorySimpleService;
import com.yzm.property.basis.utils.BeanMapUtils;
import com.yzm.property.consumable.entity.ConsumableRepertory;
import com.yzm.property.materials.entity.MaterialsInfoBom;
import com.yzm.property.materials.entity.MaterialsRepertory;
import com.yzm.property.materials.repository.MaterialsRepertoryRepository;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/9 10:01
 */
@Service
public class InventorySimpleItemServiceImpl extends BaseService<InventorySimpleItem, Long> implements InventorySimpleItemService {

    @Autowired
    private InventorySimpleItemRepository inventorySimpleItemRepository;

    @Override
    public BaseRepository<InventorySimpleItem, Long> getRepository() {
        return inventorySimpleItemRepository;
    }

    @Override
    public Map<String, Object> findAllByPageGroup(InventoryCriteria bean, Pageable page) {
        List<Map<String, Object>> groupedResults = inventorySimpleItemRepository.findGroupedResults(bean.getInventoryNumber(), bean.getInventoryStatus(),bean.getAssetsNumber(), page);
        List<InventorySimpleItem> list = new ArrayList<>();
        for (Map<String, Object> map1 : groupedResults) {
            try {
                InventorySimpleItem inventorySimpleItem = BeanMapUtils.mapToBean(map1, InventorySimpleItem.class);
                list.add(inventorySimpleItem);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }


        int l = inventorySimpleItemRepository.countGroupedResults(bean.getInventoryNumber(), bean.getInventoryStatus(),bean.getAssetsNumber());
        Map<String, Object> map = new HashMap<>();
        map.put("list", list);
        map.put("totalCount", l);
//
//        Page<MaterialsInfoBom> datas = getRepository().findAll(new Specification<MaterialsInfoBom>() {
//            @Override
//            public Predicate toPredicate(Root<MaterialsInfoBom> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
//                query.groupBy(root.get("containerNumber")); // 在这里替换 "yourGroupByField" 为您要分组的字段名
//                return QueryHelp.getPredicate(root, bean, criteriaBuilder);
//            }
//        }, page);
        return map;
    }
}
