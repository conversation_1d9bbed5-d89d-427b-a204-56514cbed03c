package com.yzm.property.basis.service.impl;

import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.property.basis.entity.BasisArea;
import com.yzm.property.basis.repository.AreaRepository;
import com.yzm.property.basis.service.AreaService;
import com.yzm.property.basis.service.AssetsInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AreaServiceImpl extends BaseService<BasisArea, Long> implements AreaService {

    @Autowired
    private AreaRepository areaRepository;
    @Autowired
    private AssetsInfoService assetsInfoService;


    @Override
    public BaseRepository<BasisArea, Long> getRepository() {
        return areaRepository;
    }

    @Override
    public BasisArea findById(Integer assetsArea) {
        return areaRepository.findById(assetsArea.longValue()).get();
    }

    @Override
    public List<BasisArea> findByCkid(Long warehouseId) {
        return areaRepository.findByCkid(warehouseId);
    }

    @Override
    public List<BasisArea> findByCkIdAndNoInventory(String ckmc) {
        return areaRepository.findByCkIdAndNoInventory(ckmc);
    }

    @Override
    public boolean deleteBatchByIds(Long[] ids) {
        if (assetsInfoService.findByAssetsArea(ids)) {
            for (int i = 0; i < ids.length; i++) {
                areaRepository.deleteById(ids[i]);
            }
            return true;
        } else {
            return false;
        }
    }
}
