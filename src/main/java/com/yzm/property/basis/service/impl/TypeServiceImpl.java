package com.yzm.property.basis.service.impl;

import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.framework.bean.Ztree;
import com.yzm.framework.shiro.LoginUser;
import com.yzm.property.basis.entity.BasisType;
import com.yzm.property.basis.repository.TypeRepository;
import com.yzm.property.basis.service.AssetsInfoService;
import com.yzm.property.basis.service.TypeService;
import com.yzm.property.system.entity.SysRole;
import com.yzm.property.system.service.ISysRoleMenuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class TypeServiceImpl extends BaseService<BasisType, Long> implements TypeService {

    @Autowired
    private TypeRepository typeRepository;
    @Autowired
    private AssetsInfoService assetsInfoService;
    @Autowired
    private ISysRoleMenuService iSysRoleMenuService;
    @Override
    public BaseRepository<BasisType, Long> getRepository() {
        return typeRepository;
    }

    @Override
    public BasisType findById(Integer assetsType) {
        return typeRepository.findById(assetsType.longValue()).get();
    }

    @Override
    public BasisType findByDepartmentName(String departmentName) {
        return null;
    }

    @Override
    public List<BasisType> findMenuList(String keyword) {
        List<BasisType> datas = null;

        if (keyword == null || "".equals(keyword)) {
            datas = typeRepository.findAll();
        }else{
            datas =  typeRepository.findByBasisNameOrTypeNumber(keyword,keyword);
        }


        return datas;
    }

    @Override
    public List<BasisType> findMenuList(Long userId) {
        return typeRepository.findAll();
    }


    @Override
    public boolean deleteBatchByIds(Long[] ids){
        if(assetsInfoService.findByAssetsType(ids)){
            for (int i = 0; i < ids.length; i++) {
                typeRepository.deleteById(ids[i]);
            }
            return true;
        }else {
            return false;
        }
    }



    @Override
    public boolean checkMenuNameUnique(BasisType menu) {
        Long menuId = StringUtils.isEmpty(menu.getId()) ? -1L : menu.getId();
        BasisType info = typeRepository.findByBasisName(menu.getBasisName());
        if (!StringUtils.isEmpty(info) && info.getId().longValue() != menuId.longValue()) {
            return false;
        }
        return true;
    }

    @Override
    public List<BasisType> findListParentId(Long menuId) {
        return typeRepository.findByParentId(menuId);
    }

    @Override
    public boolean clearMenuRedis() {
        return false;
    }


    List<BasisType> childNode = new ArrayList<BasisType>();
    List<BasisType> lastChildNode = new ArrayList<BasisType>();

    public List<BasisType> treeMenuList(List<BasisType> treeNodes, int pid) {
//
        List<BasisType> tempTreeNode = new ArrayList<BasisType>();
        List<BasisType> tempTreeNode1 = new ArrayList<BasisType>();
        for (BasisType node : treeNodes) {
            if (node.getParentId() == pid) {
                //说明存在子节点

                tempTreeNode1 = treeMenuList(treeNodes, node.getId().intValue());
                if (tempTreeNode1.isEmpty()) {
                    //不存在子节点
                    lastChildNode.add(node);
                }
                childNode.add(node);
                //用于让上一级判断是否存在子节点
                //因为存在子节点则tempTreeNode不为空
                //函数结束后返回tempTreeNode给上一级以供判断
                tempTreeNode.add(node);
                System.out.println("当前节点存在子节点");
            }

        }
        return tempTreeNode;
    }


    @Override
    public List<Ztree> menuTreeData(LoginUser userInfo) {
        List<BasisType> menuList = findMenuList(userInfo.getId());
        List<Ztree> ztrees = initZtree(menuList, null, false);
        return ztrees;
    }

    /**
     * 对象转菜单树
     *
     * @param menuList     菜单列表
     * @param roleMenuList 角色已存在菜单列表
     * @param permsFlag    是否需要显示权限标识
     * @return 树结构列表
     */
    public List<Ztree> initZtree(List<BasisType> menuList, List<Long> roleMenuList, boolean permsFlag) {
        List<Ztree> ztrees = new ArrayList<Ztree>();
        boolean isCheck = !StringUtils.isEmpty(roleMenuList);
        for (BasisType menu : menuList) {
            Ztree ztree = new Ztree();
            ztree.setId(menu.getId());
            ztree.setpId(menu.getParentId());
            ztree.setName(menu.getBasisName());
            ztree.setTitle(menu.getBasisName());
            if (isCheck) {
                ztree.setChecked(roleMenuList.contains(menu.getId()));
            }
            ztrees.add(ztree);
        }
        return ztrees;
    }

    @Override
    public List<Ztree> roleMenuTreeData(SysRole role, LoginUser userInfo) {
        Long roleId = role.getId();
        List<Ztree> ztrees = new ArrayList<Ztree>();
        List<BasisType> menuList = findMenuList(userInfo.getId());
        if (!StringUtils.isEmpty(roleId)) {
            List<Long> roleMenuList = iSysRoleMenuService.selectRoleMenu(roleId);
            ztrees = initZtree(menuList, roleMenuList, true);
        } else {
            ztrees = initZtree(menuList, null, true);
        }
        return ztrees;
    }
}
