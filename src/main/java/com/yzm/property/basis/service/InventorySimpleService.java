package com.yzm.property.basis.service;

import com.yzm.framework.base.IBaseService;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.basis.entity.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/9 9:59
 */
public interface InventorySimpleService extends IBaseService<InventorySimple, Long> {

     Boolean saveSimpleAndInfo(InventorySimple inventorySimple);

    InventorySimple findAllById(Long id);

    Map<String,Object> findAssetsByTableAndType(Long id, PageBean pageBean);

    boolean checkStatus(Long id, Integer status);

    List<InventorySimple> findAllByPage(Integer page, Integer size);

    Page<InventorySimple> findAllByPage(Pageable pageable);

    String inventory(Long id, String barcode);

    List<InventoryResult> findAssetsByTableAndType(Long id);

    boolean addWarehouse(InventorySimple inventorySimple);

    ResponseData inventoryAssets2(Long id, BigDecimal resultAmount, String note);

    ResponseData takeStock(InventorySimple inventorySimple);
    ResponseData takeStockNew(InventorySimple inventorySimple);

    ResponseData updateConut(String data);

    boolean verify(Long id);

    InventorySimple findByInventoryNumber(String inventoryNumber);

    boolean over(Long id);

    ResponseData delInfo(Long id);

    ResponseData updateModeOfTrade(String json);

    boolean submitByIds(Long[] ids);

    void audit(Long id, Integer status);
}
