package com.yzm.property.basis.service;

import com.yzm.framework.base.IBaseService;
import com.yzm.property.basis.entity.BasisArea;
import com.yzm.property.basis.entity.SupplierComparison;

import java.util.List;

public interface SupplierComparisonService extends IBaseService<SupplierComparison, Long> {

    SupplierComparison findBySupplierNameAndSupplierMaterialsCodeAndInternalMaterialsCode(String supplierName, String supplierMaterialsCode, String internalMaterialsCode);
}
