package com.yzm.property.basis.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yzm.common.utils.OrderUtils;
import com.yzm.common.utils.ShiroUtils;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.framework.shiro.LoginUser;
import com.yzm.property.basis.entity.*;
import com.yzm.property.basis.repository.*;
import com.yzm.property.basis.service.InventorySimpleItemService;
import com.yzm.property.basis.service.InventorySimpleService;
import com.yzm.property.materials.entity.InStorageMaterials;
import com.yzm.property.materials.entity.MaterialsInfo;
import com.yzm.property.materials.entity.MaterialsListEntity;
import com.yzm.property.materials.entity.MaterialsRepertory;
import com.yzm.property.materials.repository.MaterialsListRepository;
import com.yzm.property.materials.repository.MaterialsRepertoryRepository;
import com.yzm.property.materials.service.MaterialsInfoService;
import com.yzm.property.materials.service.impl.IssueTempDtlImpl;
import com.yzm.property.materials.utils.MaterialsUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/9 10:01
 */
@Service
public class InventorySimpleServiceImpl extends BaseService<InventorySimple, Long> implements InventorySimpleService {

    @Autowired
    private InventorySimpleRepository inventorySimpleRepository;

    @Autowired
    private TypeRepository typeRepository;

    @Autowired
    private AreaRepository areaRepository;

    @Autowired
    private DepartmentRepository departmentRepository;

    @Autowired
    private PersonnelRepository personnelRepository;

    @Autowired
    private InventoryInfoRepository inventoryInfoRepository;

    @Autowired
    private InventorySimpleItemRepository inventorySimpleItemRepository;

    @Autowired
    private InventorySimpleItemService inventorySimpleItemService;

    @Autowired
    private AssetsInfoRepository assetsInfoRepository;

    @Autowired
    private MaterialsRepertoryRepository materialsRepertoryRepository;

    @Autowired
    private InventoryAssetsInfoRepository inventoryAssetsInfoRepository;

    @Autowired
    private MaterialsListRepository materialsListRepository;

    @Autowired
    private MaterialsInfoService materialsInfoService;

    @Override
    public BaseRepository<InventorySimple, Long> getRepository() {
        return inventorySimpleRepository;
    }

    @Override
    public Boolean saveSimpleAndInfo(InventorySimple inventorySimple) {
        inventorySimple.setInventoryStatus(0);
        inventorySimple.setInventoryNumber(OrderUtils.getInventoryCode());
        String table = inventorySimple.getInventoryTable();
        Long typeId = inventorySimple.getInventoryTypeId();
//        Integer count = 0;
        if (typeId == 0) {
            inventorySimple.setInventoryType("全部");
            inventorySimple = inventorySimpleRepository.save(inventorySimple);

            switch (table) {
                case "区域":
                    List<BasisArea> areas = areaRepository.findAll();
                    for (BasisArea area : areas) {
//                        count = assetsInfoRepository.countAllByAssetsArea(area.getId().intValue());
                        List<AssetsInfo> allByAssetsArea = assetsInfoRepository.findAllByAssetsAreaAndAssetsStatusIsNot(area.getId().intValue(), 8);
                        inventorySimple.setInventoryTypeId(area.getId());
                        this.addSimpleAndInfo(areaRepository, inventorySimple, allByAssetsArea);
                    }
                    break;
                case "类型":
                    List<BasisType> types = typeRepository.findAll();
                    for (BasisType type : types) {
                        List<AssetsInfo> allByAssetsType = assetsInfoRepository.findAllByAssetsTypeAndAssetsStatusIsNot(type.getId().intValue(), 8);
                        inventorySimple.setInventoryTypeId(type.getId());
                        this.addSimpleAndInfo(typeRepository, inventorySimple, allByAssetsType);
                    }
                    break;
                case "部门":
                    List<BasisDepartment> departments = departmentRepository.findAll();
                    for (BasisDepartment department : departments) {
                        List<AssetsInfo> allByDepartment = assetsInfoRepository.findAllByDepartmentAndAssetsStatusIsNot(department.getId().intValue(), 8);
                        inventorySimple.setInventoryTypeId(department.getId());
                        this.addSimpleAndInfo(departmentRepository, inventorySimple, allByDepartment);
                    }
                    break;
                case "人员":
                    List<BasisPersonnel> persons = personnelRepository.findAll();
                    for (BasisPersonnel person : persons) {
                        List<AssetsInfo> allByAssetsUser = assetsInfoRepository.findAllByAssetsUserAndAssetsStatusIsNot(person.getId().intValue(), 8);
                        inventorySimple.setInventoryTypeId(person.getId());
                        this.addSimpleAndInfo(personnelRepository, inventorySimple, allByAssetsUser);
                    }
                    break;
                default:
                    throw new RuntimeException("不符合要求的盘点方式");
            }
        } else {
            switch (table) {
                case "区域":
//                    count = assetsInfoRepository.countAllByAssetsArea(inventorySimple.getInventoryTypeId().intValue());
                    List<AssetsInfo> byAssetsArea = assetsInfoRepository.findAllByAssetsAreaAndAssetsStatusIsNot(inventorySimple.getInventoryTypeId().intValue(), 8);
                    this.addSimpleAndInfo(areaRepository, inventorySimple, byAssetsArea);
                    break;
                case "类型":
                    List<AssetsInfo> allByAssetsType = assetsInfoRepository.findAllByAssetsTypeAndAssetsStatusIsNot(inventorySimple.getInventoryTypeId().intValue(), 8);
                    this.addSimpleAndInfo(typeRepository, inventorySimple, allByAssetsType);
                    break;
                case "部门":
                    List<AssetsInfo> allByDepartment = assetsInfoRepository.findAllByDepartmentAndAssetsStatusIsNot(inventorySimple.getInventoryTypeId().intValue(), 8);
                    this.addSimpleAndInfo(departmentRepository, inventorySimple, allByDepartment);
                    break;
                case "人员":
                    List<AssetsInfo> allByAssetsUser = assetsInfoRepository.findAllByAssetsUserAndAssetsStatusIsNot(inventorySimple.getInventoryTypeId().intValue(), 8);
                    this.addSimpleAndInfo(personnelRepository, inventorySimple, allByAssetsUser);
                    break;
                default:
                    throw new RuntimeException("不符合要求的盘点方式");
            }
        }
        return true;
    }

    @Override
    public InventorySimple findAllById(Long id) {
        return inventorySimpleRepository.findById(id).get();
    }

    @Override
    public Map<String, Object> findAssetsByTableAndType(Long id, PageBean pageBean) {
        Map<String, Object> map = new HashMap<>();
        List<InventoryAssetsInfo> inventoryAssetsInfos = inventoryAssetsInfoRepository.findByInfoId(id);
        Integer max = pageBean.getLimit() * (pageBean.getPage());
        List<InventoryAssetsInfo> inventoryAssetsInfoList = inventoryAssetsInfos.subList((pageBean.getPage() - 1) * pageBean.getLimit(), max > inventoryAssetsInfos.size() ? inventoryAssetsInfos.size() : max);
        List<InventoryResult> results = new ArrayList<>();
        for (InventoryAssetsInfo inventoryAssetsInfo : inventoryAssetsInfoList) {
            results.add(new InventoryResult(assetsInfoRepository.findById(inventoryAssetsInfo.getAssetsId()).get(), inventoryAssetsInfo));
        }
        map.put("list", results);
        map.put("totalCount", inventoryAssetsInfos.size());
        return map;
    }

    @Override
    @Transactional
    public boolean checkStatus(Long id, Integer status) {
        InventoryAssetsInfo inventoryAssetsInfo = inventoryAssetsInfoRepository.findById(id).get();
        if (inventoryAssetsInfo.getInventoryStatus() != 0) {
            return false;
        }
        inventoryAssetsInfo.setInventoryStatus(status);
        inventoryAssetsInfoRepository.save(inventoryAssetsInfo);
        InventoryInfo inventoryInfo = inventoryInfoRepository.findById(inventoryAssetsInfo.getInfoId()).get();
        if (status == 2) {
            inventoryInfo.setResultAmount(inventoryInfo.getResultAmount() + 1);
        } else if (status == 1) {
            inventoryInfo.setProfitLoss(inventoryInfo.getProfitLoss() - 1);
            inventoryInfo.setLoss(inventoryInfo.getLoss() - 1);
            AssetsInfo assetsInfo = assetsInfoRepository.findById(inventoryAssetsInfo.getAssetsId()).get();
            //盘亏的直接报废
//            assetsInfo.setAssetsStatus(8);
//            assetsInfoRepository.save(assetsInfo);
        } else {
            inventoryInfo.setProfit(inventoryInfo.getProfit() + 1);
            inventoryInfo.setResultAmount(inventoryInfo.getResultAmount() + 1);
            inventoryInfo.setProfitLoss(inventoryInfo.getProfitLoss() + 1);
        }
        Integer infoCount = inventoryAssetsInfoRepository.countByInfoIdAndInventoryStatus(inventoryAssetsInfo.getInfoId(), 0);
        Long simpleId = inventoryInfo.getSimpleId();
        InventorySimple inventorySimple = inventorySimpleRepository.findById(simpleId).get();
        if (infoCount == 0) {
            inventoryInfo.setInventoryStatus(2);

            Integer simpleCount = inventoryInfoRepository.countBySimpleIdAndInventoryStatus(simpleId, 2);
            Integer simpleTotal = inventoryInfoRepository.countBySimpleId(simpleId);
            if (simpleCount >= simpleTotal) {
                inventorySimple.setInventoryStatus(2);
            }
        } else {
            inventoryInfo.setInventoryStatus(1);
            inventorySimple.setInventoryStatus(1);
        }
        inventorySimpleRepository.save(inventorySimple);
        inventoryInfoRepository.save(inventoryInfo);
        return true;
    }

    @Override
    public List<InventorySimple> findAllByPage(Integer page, Integer size) {
        return inventorySimpleRepository.findAllByPage((page - 1) * size, size);
    }

    @Override
    public Page<InventorySimple> findAllByPage(Pageable pageable) {
        return inventorySimpleRepository.findAllByInventoryStatusNot(2, pageable);
    }

    @Override
    @Transactional
    public String inventory(Long id, String barcode) {
        AssetsInfo assetsInfo = assetsInfoRepository.findByBarcode(barcode);
        if (assetsInfo == null) {
            return "资产不存在";
        }
        InventoryAssetsInfo inventoryAssetsInfo = inventoryAssetsInfoRepository.findByInfoIdAndAssetsId(id, assetsInfo.getId());
//        InventoryAssetsInfo inventoryAssetsInfo = inventoryAssetsInfoRepository.findById(id).get();
        if (inventoryAssetsInfo == null) {
            return "任务不存在";
        }
        if (inventoryAssetsInfo.getInventoryStatus() != 0) {
            return "资产已盘点";
        }
        inventoryAssetsInfo.setInventoryStatus(2);
        inventoryAssetsInfoRepository.save(inventoryAssetsInfo);
        InventoryInfo inventoryInfo = inventoryInfoRepository.findById(inventoryAssetsInfo.getInfoId()).get();
        inventoryInfo.setResultAmount(inventoryInfo.getResultAmount() + 1);
        Integer infoCount = inventoryAssetsInfoRepository.countByInfoIdAndInventoryStatus(inventoryAssetsInfo.getInfoId(), 0);
        Long simpleId = inventoryInfo.getSimpleId();
        InventorySimple inventorySimple = inventorySimpleRepository.findById(simpleId).get();
        if (infoCount == 0) {
            inventoryInfo.setInventoryStatus(2);

            Integer simpleCount = inventoryInfoRepository.countBySimpleIdAndInventoryStatus(simpleId, 2);
            Integer simpleTotal = inventoryInfoRepository.countBySimpleId(simpleId);
            if (simpleCount >= simpleTotal) {
                inventorySimple.setInventoryStatus(2);
            }
        } else {
            inventoryInfo.setInventoryStatus(1);
            inventorySimple.setInventoryStatus(1);
        }
        inventorySimpleRepository.save(inventorySimple);
        inventoryInfoRepository.save(inventoryInfo);
        return null;
    }

    @Override
    public List<InventoryResult> findAssetsByTableAndType(Long id) {
        List<InventoryResult> inventoryResults = new ArrayList<>();
        List<InventoryAssetsInfo> inventoryAssetsInfos = inventoryAssetsInfoRepository.findByInfoId(id);
        for (InventoryAssetsInfo inventoryAssetsInfo : inventoryAssetsInfos) {
            inventoryResults.add(new InventoryResult(assetsInfoRepository.findById(inventoryAssetsInfo.getAssetsId()).get(), inventoryAssetsInfo));
        }
        return inventoryResults;
    }

    class MyTh extends Thread {
        private InventorySimple inventorySimple;

        public MyTh(InventorySimple inventorySimple) {
            this.inventorySimple = inventorySimple;
        }

        @Override
        public void run() {
            addWarehouseInfo(inventorySimple);
        }
    }

    public boolean addWarehouseInfo(InventorySimple inventorySimple) {
        List<Map<String, Object>> map = materialsRepertoryRepository.findSumByDepartmentCode(inventorySimple.getInventoryType());

// 创建一个新的 Map 来存储转化后的数据
        Map<String, BigDecimal> resultMap = new HashMap<>();

// 遍历查询结果，将每个 Map 转换为你需要的键值对形式
        for (Map<String, Object> result : map) {
            String assetsNumber = (String) result.get("assets_number");
            BigDecimal nowRepertorySum = new BigDecimal(result.get("now_repertory_sum").toString());
            resultMap.put(assetsNumber, nowRepertorySum);
        }

        inventorySimple.setStatus(1);
        inventorySimple.setInventoryStatus(0);
        inventorySimple.setInventoryNumber(OrderUtils.getInventoryCode());
        List<MaterialsRepertory> byWarehouseInfo = materialsRepertoryRepository.findByDepartmentCode(inventorySimple.getInventoryType());
        for (MaterialsRepertory materialsRepertory : byWarehouseInfo) {
            InventorySimpleItem inventorySimpleItem = new InventorySimpleItem();
            BeanUtils.copyProperties(materialsRepertory, inventorySimpleItem);
            if (StringUtils.isEmpty(inventorySimpleItem.getModeOfTrade())) {
                inventorySimpleItem.setModeOfTrade(1L);
            }
            inventorySimpleItem.setInventoryStatus(0L);
            inventorySimpleItem.setId(null);
            inventorySimpleItem.setLoss(new BigDecimal(0));
            inventorySimpleItem.setProfit(new BigDecimal(0));
            inventorySimpleItem.setResultAmount(new BigDecimal(0));
            inventorySimpleItem.setProfitLoss(new BigDecimal(0));
            inventorySimpleItem.setInventoryNumber(inventorySimple.getInventoryNumber());
            BigDecimal aDouble = resultMap.get(inventorySimpleItem.getAssetsNumber());
            inventorySimpleItem.setNowRepertorySum(aDouble);
            inventorySimpleItemRepository.save(inventorySimpleItem);
        }
        inventorySimpleRepository.save(inventorySimple);
        return true;
    }

    @Override
    public boolean addWarehouse(InventorySimple inventorySimple) {
        Thread thread = new MyTh(inventorySimple);
        thread.start();
        return true;
    }


    @Override
    public ResponseData inventoryAssets2(Long id, BigDecimal resultAmount, String note) {
        InventorySimpleItem info = inventorySimpleItemRepository.findById(id).get();
        info.setResultAmount(resultAmount);
        info.setNote(note);
        info.setProfitLoss(resultAmount.subtract(info.getNowRepertory()));
        info.setInventoryStatus(2L);
        inventorySimpleItemRepository.saveAndFlush(info);

        List<InventorySimpleItem> list = inventorySimpleItemRepository.findByInventoryStatusAndInventoryNumber(0L, info.getInventoryNumber());
        InventorySimple simple = inventorySimpleRepository.findByInventoryNumber(info.getInventoryNumber());
        if (list.size() == 0) {
            simple.setInventoryStatus(2);
        } else {
            simple.setInventoryStatus(1);
        }
        inventorySimpleRepository.saveAndFlush(simple);
        return ResponseData.success("盘点成功");
    }

    //    @Override
//    public ResponseData takeStock(InventorySimple inventorySimple) {
//        InventorySimple one = inventorySimpleRepository.getOne(inventorySimple.getId());
//        List<InventorySimpleItem> list = JSONArray.parseArray(inventorySimple.getItemJson(), InventorySimpleItem.class);
//        for (InventorySimpleItem inventorySimpleItem : list) {
//
//            InventorySimpleItem byInventoryNumberAndAssetsCode = inventorySimpleItemRepository.findByInventoryNumberAndAssetsCode(one.getInventoryNumber(), inventorySimpleItem.getAssetsCode());
//            if (byInventoryNumberAndAssetsCode == null) {
//                inventorySimpleItem.setInventoryStatus(2L);
//                inventorySimpleItem.setId(null);
//                inventorySimpleItem.setInventoryNumber(one.getInventoryNumber());
//                inventorySimpleItem.setUpdateTime(new Date());
//
//                BigDecimal nowRepertory = inventorySimpleItem.getNowRepertory();//库存
//                BigDecimal resultAmount = inventorySimpleItem.getResultAmount();//已盘数量
//
//                BigDecimal resultAmountSum = inventorySimpleItemRepository.findBySumResultAmount(one.getInventoryNumber(), 2L, inventorySimpleItem.getAssetsNumber());
//                InventorySimpleItem bySumNowRepertoryData = inventorySimpleItemRepository.findBySumNowRepertoryData(one.getInventoryNumber(), inventorySimpleItem.getAssetsNumber());
//                BigDecimal nowRepertorySum = new BigDecimal(0);
//                BigDecimal add = resultAmountSum;
//                if (resultAmountSum == null) {
//                    add = inventorySimpleItem.getResultAmount();
//                    inventorySimpleItem.setNowRepertorySum(new BigDecimal(0));
//                } else {
//                    add = add.add(resultAmount);
//                }
//                if (bySumNowRepertoryData != null) {
//                    nowRepertorySum = bySumNowRepertoryData.getNowRepertorySum();//库存
//                    inventorySimpleItem.setNowRepertorySum(bySumNowRepertoryData.getNowRepertorySum());
//                }
//                int i2 = nowRepertorySum.compareTo(add);
//                if (i2 == -1) {
//                    inventorySimpleItem.setProfitSum(add.subtract(nowRepertorySum));
//                    inventorySimpleItem.setLossSum(new BigDecimal(0));
//                } else if (i2 == 1) {
//                    inventorySimpleItem.setProfitSum(new BigDecimal(0));
//                    inventorySimpleItem.setLossSum(add.subtract(nowRepertorySum));
//                }
//
//
//                int i = nowRepertory.compareTo(resultAmount);
//                if (i == -1) {
//                    inventorySimpleItem.setProfit(resultAmount.subtract(nowRepertory));
//                    inventorySimpleItem.setLoss(new BigDecimal(0));
//                } else if (i == 1) {
//                    inventorySimpleItem.setProfit(new BigDecimal(0));
//                    inventorySimpleItem.setLoss(resultAmount.subtract(nowRepertory));
//                } else {
//                    inventorySimpleItem.setProfit(new BigDecimal(0));
//                    inventorySimpleItem.setLoss(new BigDecimal(0));
//                }
//                inventorySimpleItemRepository.save(inventorySimpleItem);
//
//                List<InventorySimpleItem> byInventoryNumberAndInventoryStatusAndAssetsNumber = inventorySimpleItemRepository.findByInventoryNumberAndInventoryStatusAndAssetsNumber(inventorySimpleItem.getInventoryNumber(), 2L, inventorySimpleItem.getAssetsNumber());
//                for (InventorySimpleItem simpleItem : byInventoryNumberAndInventoryStatusAndAssetsNumber) {
//                    simpleItem.setLossSum(inventorySimpleItem.getLossSum());
//                    simpleItem.setProfitSum(inventorySimpleItem.getProfitSum());
//                    inventorySimpleItemRepository.saveAndFlush(simpleItem);
//                }
//
//
//            } else {
//
//                byInventoryNumberAndAssetsCode.setInventoryStatus(2L);
//                byInventoryNumberAndAssetsCode.setResultAmount(byInventoryNumberAndAssetsCode.getNowRepertory());
//                byInventoryNumberAndAssetsCode.setUpdateTime(new Date());
//
//
//                BigDecimal nowRepertory = byInventoryNumberAndAssetsCode.getNowRepertory();//库存
//                BigDecimal resultAmount = byInventoryNumberAndAssetsCode.getResultAmount();//已盘数量
//
//                BigDecimal resultAmountSum = inventorySimpleItemRepository.findBySumResultAmount(one.getInventoryNumber(), 2L, inventorySimpleItem.getAssetsNumber());
//                BigDecimal nowRepertorySum = byInventoryNumberAndAssetsCode.getNowRepertorySum();//库存
//                BigDecimal add = resultAmountSum;
//                int i2 = nowRepertorySum.compareTo(add);
//                if (i2 == -1) {
//                    byInventoryNumberAndAssetsCode.setProfitSum(add.subtract(nowRepertorySum));
//                    byInventoryNumberAndAssetsCode.setLossSum(new BigDecimal(0));
//                } else if (i2 == 1) {
//                    byInventoryNumberAndAssetsCode.setProfitSum(new BigDecimal(0));
//                    byInventoryNumberAndAssetsCode.setLossSum(add.subtract(nowRepertorySum));
//                }
//
//
//                int i = nowRepertory.compareTo(resultAmount);
//                if (i == -1) {
//                    byInventoryNumberAndAssetsCode.setProfit(resultAmount.subtract(nowRepertory));
//                    byInventoryNumberAndAssetsCode.setLoss(new BigDecimal(0));
//                } else if (i == 1) {
//                    byInventoryNumberAndAssetsCode.setProfit(new BigDecimal(0));
//                    byInventoryNumberAndAssetsCode.setLoss(resultAmount.subtract(nowRepertory));
//                }
//                inventorySimpleItemRepository.saveAndFlush(byInventoryNumberAndAssetsCode);
//
//
//                List<InventorySimpleItem> byInventoryNumberAndInventoryStatusAndAssetsNumber = inventorySimpleItemRepository.findByInventoryNumberAndInventoryStatusAndAssetsNumber(inventorySimpleItem.getInventoryNumber(), 2L, inventorySimpleItem.getAssetsNumber());
//                for (InventorySimpleItem simpleItem : byInventoryNumberAndInventoryStatusAndAssetsNumber) {
//                    simpleItem.setLossSum(byInventoryNumberAndAssetsCode.getLossSum());
//                    simpleItem.setProfitSum(byInventoryNumberAndAssetsCode.getProfitSum());
//                    inventorySimpleItemRepository.saveAndFlush(simpleItem);
//                }
//
//            }
//
//
//        }
//        List<InventorySimpleItem> byInventoryNumberAndInventoryStatus = inventorySimpleItemRepository.findByInventoryNumberAndInventoryStatus(one.getInventoryNumber(), 0L);
//        if (byInventoryNumberAndInventoryStatus.size() == 0) {
//            one.setInventoryStatus(2);
//        } else {
//            one.setInventoryStatus(1);
//        }
//        inventorySimpleRepository.saveAndFlush(one);
//        return ResponseData.success("盘点成功");
//    }
    private static final String REGEX = "^([^,]*,){7}[^,]*$";

    public static boolean isValidFormat(String text) {
        Pattern pattern = Pattern.compile(REGEX);
        Matcher matcher = pattern.matcher(text);
        return matcher.matches();
    }

    public static String[] parseFormat(String text) {
        // 使用下划线分隔字符串
        String[] parts = text.split(",");

        // 检查是否有3部分
        if (parts.length == 8) {
            return parts;
        } else {
            return null;
        }
    }

    @Override
    public ResponseData takeStock(InventorySimple inventorySimple) {
        InventorySimple one = inventorySimpleRepository.getOne(inventorySimple.getId());
        String itemJson = inventorySimple.getItemJson();
        List<InventorySimpleItem> updateList = new ArrayList<>();

        if (!isValidFormat(itemJson)) {
            return ResponseData.error("条码格式错误请重新扫描");
        }
        // 解析
        String[] parts = parseFormat(itemJson);

        if (parts != null) {
            System.out.println("编号: " + parts[0]);
            System.out.println("名称: " + parts[1]);
            System.out.println("批号: " + parts[2]);
            System.out.println("批次数量: " + parts[3]);
            System.out.println("总数量: " + parts[4]);
            System.out.println("来料日期: " + parts[5]);
            System.out.println("包装数量: " + parts[6]);
            System.out.println("流水号: " + parts[7]);
        } else {
            System.out.println(itemJson + "-格式无效");
        }
        if (StringUtils.isEmpty(parts)) {
            return ResponseData.error(itemJson + "-条码格式错误！");
        }
        String number = parts[0].trim();

        number = StringUtils.replaceFifthCharacter(number);

        MaterialsInfo materialsInfo = materialsInfoService.findByAssetsNumber(number);
        if (StringUtils.isEmpty(materialsInfo)) {
            materialsInfo = new MaterialsInfo();
            materialsInfo.setAssetsNumber(number);
            materialsInfo.setAssetsName(parts[1]);
//            return error("系统没有该材料！" + number);
        }
//        materialsInfo.setStorageArea("LG000000");
//
        List<String> materialsInfo1 = materialsInfoService.getMaterialsInfo(number, one.getInventoryType());
        if (materialsInfo1.size() > 0) {
            materialsInfo.setStorageArea(materialsInfo1.get(0));
        } else {
            return ResponseData.error("部门代码" + one.getInventoryType() + "中没有找到材料：" + number);
        }

        BigDecimal decimal = new BigDecimal(1);
        if (StringUtils.isNotEmpty(materialsInfo.getMinLot())) {
            decimal = materialsInfo.getMinLot();
        }


        MaterialsRepertory byAssetsCodeOrAssetsRfid = materialsRepertoryRepository.findByAssetsCodeOrAssetsRfid(itemJson, itemJson);
        if (byAssetsCodeOrAssetsRfid == null) {
            byAssetsCodeOrAssetsRfid = new MaterialsRepertory();
            BeanUtils.copyProperties(materialsInfo, byAssetsCodeOrAssetsRfid);
            if (!StringUtils.isEmpty(materialsInfo.getStorageArea())) {
                byAssetsCodeOrAssetsRfid.setAreaInfo(materialsInfo.getStorageArea());
            }
            BigDecimal count = new BigDecimal(parts[4]);
            byAssetsCodeOrAssetsRfid.setNowRepertory(count.multiply(decimal));
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
            try {
                byAssetsCodeOrAssetsRfid.setDateArrival(simpleDateFormat.parse(parts[5]));
            } catch (ParseException e) {
                e.printStackTrace();
            }
            byAssetsCodeOrAssetsRfid.setAssetsCode(itemJson);
            byAssetsCodeOrAssetsRfid.setBatchNumber(parts[2]);
            byAssetsCodeOrAssetsRfid.setWarehouseInfo(materialsInfo.getStorageWarehouse());
//            byAssetsCodeOrAssetsRfid.setAreaInfo(byAssetsCodeOrAssetsRfid.getAreaInfo());
            if (StringUtils.isEmpty(one.getInventoryType())) {
                byAssetsCodeOrAssetsRfid.setDepartmentCode("983C");
            } else {
                byAssetsCodeOrAssetsRfid.setDepartmentCode(one.getInventoryType());
            }
        }

        InventorySimpleItem inventorySimpleItem = new InventorySimpleItem();
        BeanUtils.copyProperties(byAssetsCodeOrAssetsRfid, inventorySimpleItem);


        InventorySimpleItem byInventoryNumberAndAssetsCode = inventorySimpleItemRepository.findByInventoryNumberAndAssetsCode(one.getInventoryNumber(), inventorySimpleItem.getAssetsCode());
        if (byInventoryNumberAndAssetsCode == null) {

            InventorySimpleItem firstByInventoryNumberAndAssetsNumber = inventorySimpleItemRepository.findFirstByInventoryNumberAndAssetsNumber(one.getInventoryNumber(), inventorySimpleItem.getAssetsNumber());
            if (firstByInventoryNumberAndAssetsNumber == null) {
                inventorySimpleItem.setModeOfTrade(1L);
            } else {
                if (StringUtils.isEmpty(firstByInventoryNumberAndAssetsNumber.getModeOfTrade())) {
                    inventorySimpleItem.setModeOfTrade(1L);
                } else {
                    inventorySimpleItem.setModeOfTrade(firstByInventoryNumberAndAssetsNumber.getModeOfTrade());
                }
            }
            inventorySimpleItem.setInventoryStatus(2L);
            inventorySimpleItem.setId(null);
            inventorySimpleItem.setInventoryNumber(one.getInventoryNumber());
            inventorySimpleItem.setUpdateTime(new Date());
            BigDecimal count = new BigDecimal(parts[4]);
            inventorySimpleItem.setResultAmount(count.multiply(decimal));
//            inventorySimpleItem.setResultAmount(inventorySimpleItem.getNowRepertory());


            BigDecimal nowRepertory = inventorySimpleItem.getNowRepertory();//库存

            BigDecimal resultAmount = inventorySimpleItem.getResultAmount();//已盘数量

            BigDecimal resultAmountSum = inventorySimpleItemRepository.findBySumResultAmount(one.getInventoryNumber(), 2L, inventorySimpleItem.getAssetsNumber());
            InventorySimpleItem bySumNowRepertoryData = inventorySimpleItemRepository.findBySumNowRepertoryData(one.getInventoryNumber(), inventorySimpleItem.getAssetsNumber());
            BigDecimal nowRepertorySum = new BigDecimal(0);
            BigDecimal add = resultAmountSum;
            if (resultAmountSum == null) {
                add = inventorySimpleItem.getResultAmount();
                inventorySimpleItem.setNowRepertorySum(new BigDecimal(0));
            } else {
                add = add.add(resultAmount);
            }
            if (bySumNowRepertoryData != null) {
                nowRepertorySum = bySumNowRepertoryData.getNowRepertorySum();//库存
                inventorySimpleItem.setNowRepertorySum(bySumNowRepertoryData.getNowRepertorySum());
            }
            int i2 = nowRepertorySum.compareTo(add);
            if (i2 == -1) {
                inventorySimpleItem.setProfitSum(add.subtract(nowRepertorySum));
                inventorySimpleItem.setLossSum(new BigDecimal(0));
            } else if (i2 == 1) {
                inventorySimpleItem.setProfitSum(new BigDecimal(0));
                inventorySimpleItem.setLossSum(add.subtract(nowRepertorySum));
            } else {
                inventorySimpleItem.setProfitSum(new BigDecimal(0));
                inventorySimpleItem.setLossSum(new BigDecimal(0));
            }


            int i = nowRepertory.compareTo(resultAmount);
            if (i == -1) {
                inventorySimpleItem.setProfit(resultAmount.subtract(nowRepertory));
                inventorySimpleItem.setLoss(new BigDecimal(0));
            } else if (i == 1) {
                inventorySimpleItem.setProfit(new BigDecimal(0));
                inventorySimpleItem.setLoss(resultAmount.subtract(nowRepertory));
            } else {
                inventorySimpleItem.setProfit(new BigDecimal(0));
                inventorySimpleItem.setLoss(new BigDecimal(0));
            }
            inventorySimpleItem.setCreateTime(new Date());
            if (!StringUtils.isEmpty(materialsInfo.getStorageArea())) {
                inventorySimpleItem.setAreaInfo(materialsInfo.getStorageArea());
            }
            inventorySimpleItem.setInventoryNumber(one.getInventoryNumber());
            inventorySimpleItemRepository.save(inventorySimpleItem);


//            if (!StringUtils.isEmpty(materialsInfo.getStorageArea())) {
//                inventorySimpleItemRepository.updateByInventoryNumberAndInventoryStatusAndAssetsNumberAndArea(inventorySimpleItem.getLossSum(), inventorySimpleItem.getProfitSum(), one.getInventoryNumber(), 2L, inventorySimpleItem.getAssetsNumber(),materialsInfo.getStorageArea());
//            }else{
//                inventorySimpleItemRepository.updateByInventoryNumberAndInventoryStatusAndAssetsNumber(inventorySimpleItem.getLossSum(), inventorySimpleItem.getProfitSum(), one.getInventoryNumber(), 2L, inventorySimpleItem.getAssetsNumber());
//
//            }

            List<InventorySimpleItem> byInventoryNumberAndInventoryStatusAndAssetsNumber = inventorySimpleItemRepository.findByInventoryNumberAndInventoryStatusAndAssetsNumber(inventorySimpleItem.getInventoryNumber(), 2L, inventorySimpleItem.getAssetsNumber());
            for (InventorySimpleItem simpleItem : byInventoryNumberAndInventoryStatusAndAssetsNumber) {
                simpleItem.setLossSum(inventorySimpleItem.getLossSum());
                simpleItem.setProfitSum(inventorySimpleItem.getProfitSum());
                if (!StringUtils.isEmpty(materialsInfo.getStorageArea())) {
                    simpleItem.setAreaInfo(materialsInfo.getStorageArea());
                }
//                updateList.add(simpleItem);
                inventorySimpleItemRepository.saveAndFlush(simpleItem);
            }


        } else {
            if (byInventoryNumberAndAssetsCode.getInventoryStatus() == 2) {
                return ResponseData.error("请勿重复添加!");
            }
            byInventoryNumberAndAssetsCode.setInventoryStatus(2L);
            byInventoryNumberAndAssetsCode.setResultAmount(byInventoryNumberAndAssetsCode.getNowRepertory());
            byInventoryNumberAndAssetsCode.setUpdateTime(new Date());
            BigDecimal count = new BigDecimal(parts[4]);
            byInventoryNumberAndAssetsCode.setResultAmount(count.multiply(decimal));
            BigDecimal nowRepertory = byInventoryNumberAndAssetsCode.getNowRepertory();//库存

            BigDecimal resultAmount = byInventoryNumberAndAssetsCode.getResultAmount();//已盘数量

            BigDecimal resultAmountSum = inventorySimpleItemRepository.findBySumResultAmount(one.getInventoryNumber(), 2L, inventorySimpleItem.getAssetsNumber());
            BigDecimal nowRepertorySum = byInventoryNumberAndAssetsCode.getNowRepertorySum();//库存
            BigDecimal add = resultAmountSum;
            int i2 = nowRepertorySum.compareTo(add);
            if (i2 == -1) {
                byInventoryNumberAndAssetsCode.setProfitSum(add.subtract(nowRepertorySum));
                byInventoryNumberAndAssetsCode.setLossSum(new BigDecimal(0));
            } else if (i2 == 1) {
                byInventoryNumberAndAssetsCode.setProfitSum(new BigDecimal(0));
                byInventoryNumberAndAssetsCode.setLossSum(add.subtract(nowRepertorySum));
            } else {
                byInventoryNumberAndAssetsCode.setProfitSum(new BigDecimal(0));
                byInventoryNumberAndAssetsCode.setLossSum(new BigDecimal(0));
            }


            int i = nowRepertory.compareTo(resultAmount);
            if (i == -1) {
                byInventoryNumberAndAssetsCode.setProfit(resultAmount.subtract(nowRepertory));
                byInventoryNumberAndAssetsCode.setLoss(new BigDecimal(0));
            } else if (i == 1) {
                byInventoryNumberAndAssetsCode.setProfit(new BigDecimal(0));
                byInventoryNumberAndAssetsCode.setLoss(resultAmount.subtract(nowRepertory));
            } else {
                byInventoryNumberAndAssetsCode.setProfit(new BigDecimal(0));
                byInventoryNumberAndAssetsCode.setLoss(new BigDecimal(0));
            }
            if (!StringUtils.isEmpty(materialsInfo.getStorageArea())) {
                byInventoryNumberAndAssetsCode.setAreaInfo(materialsInfo.getStorageArea());
            }
//            if(StringUtils.isEmpty(materialsInfo.getStorageArea())){
//                byInventoryNumberAndAssetsCode.setAreaInfo(materialsInfo.getStorageArea());
//            }
            inventorySimpleItemRepository.saveAndFlush(byInventoryNumberAndAssetsCode);

//            if (!StringUtils.isEmpty(materialsInfo.getStorageArea())) {
//                inventorySimpleItemRepository.updateByInventoryNumberAndInventoryStatusAndAssetsNumberAndArea(inventorySimpleItem.getLossSum(), inventorySimpleItem.getProfitSum(), one.getInventoryNumber(), 2L, inventorySimpleItem.getAssetsNumber(),materialsInfo.getStorageArea());
//            }else{
//                inventorySimpleItemRepository.updateByInventoryNumberAndInventoryStatusAndAssetsNumber(inventorySimpleItem.getLossSum(), inventorySimpleItem.getProfitSum(), one.getInventoryNumber(), 2L, inventorySimpleItem.getAssetsNumber());
//            }


            List<InventorySimpleItem> byInventoryNumberAndInventoryStatusAndAssetsNumber = inventorySimpleItemRepository.findByInventoryNumberAndInventoryStatusAndAssetsNumber(one.getInventoryNumber(), 2L, inventorySimpleItem.getAssetsNumber());
            for (InventorySimpleItem simpleItem : byInventoryNumberAndInventoryStatusAndAssetsNumber) {
                simpleItem.setLossSum(byInventoryNumberAndAssetsCode.getLossSum());
                simpleItem.setProfitSum(byInventoryNumberAndAssetsCode.getProfitSum());
                if (!StringUtils.isEmpty(materialsInfo.getStorageArea())) {
                    simpleItem.setAreaInfo(materialsInfo.getStorageArea());
                }
//                updateList.add(simpleItem);
                inventorySimpleItemRepository.saveAndFlush(simpleItem);
            }
        }
        List<InventorySimpleItem> list = inventorySimpleItemRepository.findByInventoryNumberAndAssetsNumber(one.getInventoryNumber(), inventorySimpleItem.getAssetsNumber());
        for (InventorySimpleItem simpleItem : list) {
            if (!StringUtils.isEmpty(materialsInfo.getStorageArea()) && StringUtils.isEmpty(simpleItem.getAreaInfo())) {
                simpleItem.setAreaInfo(materialsInfo.getStorageArea());
                inventorySimpleItemRepository.saveAndFlush(simpleItem);
            }
        }
//        Thread thread = new myThread(updateList,inventorySimpleItemService);
//        thread.start();
        List<InventorySimpleItem> byInventoryNumberAndInventoryStatus = inventorySimpleItemRepository.findByInventoryNumberAndInventoryStatus(one.getInventoryNumber(), 0L);
        if (byInventoryNumberAndInventoryStatus.size() == 0) {
            one.setInventoryStatus(2);
        } else {
            one.setInventoryStatus(1);
        }
        inventorySimpleRepository.saveAndFlush(one);
        return ResponseData.success("盘点成功");
    }

    @Override
    public ResponseData takeStockNew(InventorySimple inventorySimple) {
        InventorySimple one = inventorySimpleRepository.getOne(inventorySimple.getId());
        String itemJson = inventorySimple.getItemJson();

        if (!isValidFormat(itemJson)) {
            return ResponseData.error("条码格式错误请重新扫描");
        }
        // 解析
        String[] parts = parseFormat(itemJson);

        if (parts != null) {
            System.out.println("编号: " + parts[0]);
            System.out.println("名称: " + parts[1]);
            System.out.println("批号: " + parts[2]);
            System.out.println("批次数量: " + parts[3]);
            System.out.println("总数量: " + parts[4]);
            System.out.println("来料日期: " + parts[5]);
            System.out.println("包装数量: " + parts[6]);
            System.out.println("流水号: " + parts[7]);
        } else {
            System.out.println(itemJson + "-格式无效");
        }
        if (StringUtils.isEmpty(parts)) {
            return ResponseData.error(itemJson + "-条码格式错误！");
        }
        String number = parts[0].trim();

        number = StringUtils.replaceFifthCharacter(number);

        MaterialsInfo materialsInfo = materialsInfoService.findByAssetsNumber(number);
        if (StringUtils.isEmpty(materialsInfo)) {
            materialsInfo = new MaterialsInfo();
            materialsInfo.setAssetsNumber(number);
            materialsInfo.setAssetsName(parts[1]);
//            return error("系统没有该材料！" + number);
        }
//        materialsInfo.setStorageArea("LG000000");
//
        List<String> materialsInfo1 = materialsInfoService.getMaterialsInfo(number, one.getInventoryType());
        if (materialsInfo1.size() > 0) {
            materialsInfo.setStorageArea(materialsInfo1.get(0));
        } else {
            return ResponseData.error("部门代码" + one.getInventoryType() + "中没有找到材料：" + number);
        }

        BigDecimal decimal = new BigDecimal(1);
        if (StringUtils.isNotEmpty(materialsInfo.getMinLot())) {
            decimal = materialsInfo.getMinLot();
        }


        MaterialsRepertory byAssetsCodeOrAssetsRfid = materialsRepertoryRepository.findByAssetsCodeOrAssetsRfid(itemJson, itemJson);
        if (byAssetsCodeOrAssetsRfid == null) {
            byAssetsCodeOrAssetsRfid = new MaterialsRepertory();
            BeanUtils.copyProperties(materialsInfo, byAssetsCodeOrAssetsRfid);
            if (!StringUtils.isEmpty(materialsInfo.getStorageArea())) {
                byAssetsCodeOrAssetsRfid.setAreaInfo(materialsInfo.getStorageArea());
            }
            BigDecimal count = new BigDecimal(parts[4]);
            byAssetsCodeOrAssetsRfid.setNowRepertory(count.multiply(decimal));
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
            try {
                byAssetsCodeOrAssetsRfid.setDateArrival(simpleDateFormat.parse(parts[5]));
            } catch (ParseException e) {
                e.printStackTrace();
            }
            byAssetsCodeOrAssetsRfid.setAssetsCode(itemJson);
            byAssetsCodeOrAssetsRfid.setBatchNumber(parts[2]);
            byAssetsCodeOrAssetsRfid.setNowRepertory(new BigDecimal(0));
            byAssetsCodeOrAssetsRfid.setWarehouseInfo(materialsInfo.getStorageWarehouse());
//            byAssetsCodeOrAssetsRfid.setAreaInfo(byAssetsCodeOrAssetsRfid.getAreaInfo());
            if (StringUtils.isEmpty(one.getInventoryType())) {
                byAssetsCodeOrAssetsRfid.setDepartmentCode("983C");
            } else {
                byAssetsCodeOrAssetsRfid.setDepartmentCode(one.getInventoryType());
            }
        }

        InventorySimpleItem inventorySimpleItem = new InventorySimpleItem();
        BeanUtils.copyProperties(byAssetsCodeOrAssetsRfid, inventorySimpleItem);


        InventorySimpleItem byInventoryNumberAndAssetsCode = inventorySimpleItemRepository.findByInventoryNumberAndAssetsCode(one.getInventoryNumber(), inventorySimpleItem.getAssetsCode());
        if (byInventoryNumberAndAssetsCode == null) {

            InventorySimpleItem firstByInventoryNumberAndAssetsNumber = inventorySimpleItemRepository.findFirstByInventoryNumberAndAssetsNumber(one.getInventoryNumber(), inventorySimpleItem.getAssetsNumber());
            if (firstByInventoryNumberAndAssetsNumber == null) {
                inventorySimpleItem.setModeOfTrade(1L);
            } else {
                if (StringUtils.isEmpty(firstByInventoryNumberAndAssetsNumber.getModeOfTrade())) {
                    inventorySimpleItem.setModeOfTrade(1L);
                } else {
                    inventorySimpleItem.setModeOfTrade(firstByInventoryNumberAndAssetsNumber.getModeOfTrade());
                }
            }
            inventorySimpleItem.setInventoryStatus(2L);
            inventorySimpleItem.setId(null);
            inventorySimpleItem.setInventoryNumber(one.getInventoryNumber());
            inventorySimpleItem.setUpdateTime(new Date());
            BigDecimal count = new BigDecimal(parts[4]);
            inventorySimpleItem.setResultAmount(count.multiply(decimal));
//            inventorySimpleItem.setResultAmount(inventorySimpleItem.getNowRepertory());

            inventorySimpleItem.setCreateTime(new Date());
            if (!StringUtils.isEmpty(materialsInfo.getStorageArea())) {
                inventorySimpleItem.setAreaInfo(materialsInfo.getStorageArea());
            }
            inventorySimpleItem.setInventoryNumber(one.getInventoryNumber());
            inventorySimpleItemRepository.save(inventorySimpleItem);
        } else {
            if (byInventoryNumberAndAssetsCode.getInventoryStatus() == 2) {
                return ResponseData.error("请勿重复添加!");
            }
            byInventoryNumberAndAssetsCode.setInventoryStatus(2L);
            byInventoryNumberAndAssetsCode.setResultAmount(byInventoryNumberAndAssetsCode.getNowRepertory());
            byInventoryNumberAndAssetsCode.setUpdateTime(new Date());
            byInventoryNumberAndAssetsCode.setResultAmount(byInventoryNumberAndAssetsCode.getNowRepertory());
            if (!StringUtils.isEmpty(materialsInfo.getStorageArea())) {
                byInventoryNumberAndAssetsCode.setAreaInfo(materialsInfo.getStorageArea());
            }
            inventorySimpleItemRepository.save(byInventoryNumberAndAssetsCode);

        }
        // 使用count查询替代查询全部列表，提高性能
        int pendingCount = inventorySimpleItemRepository.countByInventoryNumberAndInventoryStatus(one.getInventoryNumber(), 0L);

        // 更新盘点状态
        one.setInventoryStatus(pendingCount == 0 ? 2 : 1);
        inventorySimpleRepository.save(one);


        return ResponseData.success("盘点成功");
    }

    class myThread extends Thread {
        List<InventorySimpleItem> updateList;
        InventorySimpleItemService service;

        public myThread(List<InventorySimpleItem> updateList, InventorySimpleItemService service) {
            this.updateList = updateList;
            this.service = service;
        }

        @Override
        public void run() {
            for (InventorySimpleItem inventorySimpleItem : updateList) {
                service.update(inventorySimpleItem);
            }
        }
    }

    @Override
    public ResponseData updateConut(String data) {
        InventorySimpleItem inventorySimpleItem = JSONObject.parseObject(data, InventorySimpleItem.class);
        InventorySimpleItem one = inventorySimpleItemRepository.getOne(inventorySimpleItem.getId());
        one.setResultAmount(inventorySimpleItem.getResultAmount());


//        BigDecimal nowRepertory = one.getNowRepertory();//库存
//        BigDecimal resultAmount = one.getResultAmount();//已盘数量

//
//        BigDecimal resultAmountSum = inventorySimpleItemRepository.findBySumResultAmount(one.getInventoryNumber(), 2L, inventorySimpleItem.getAssetsNumber());
//        BigDecimal nowRepertorySum = one.getNowRepertorySum();//库存
//        BigDecimal add = resultAmountSum;
//        int i2 = nowRepertorySum.compareTo(add);
//        if (i2 == -1) {
//            one.setProfitSum(add.subtract(nowRepertorySum));
//            one.setLossSum(new BigDecimal(0));
//        } else if (i2 == 1) {
//            one.setProfitSum(new BigDecimal(0));
//            one.setLossSum(add.subtract(nowRepertorySum));
//        } else {
//            one.setProfitSum(new BigDecimal(0));
//            one.setLossSum(new BigDecimal(0));
//        }
//
//        if (StringUtils.isEmpty(nowRepertory)) {
//            nowRepertory = new BigDecimal(0);
//        }
//        if (StringUtils.isEmpty(resultAmount)) {
//            resultAmount = new BigDecimal(0);
//        }
//        int i = nowRepertory.compareTo(resultAmount);
//        if (i == -1) {
//            one.setProfit(resultAmount.subtract(nowRepertory));
//            one.setLoss(new BigDecimal(0));
//        } else if (i == 1) {
//            one.setProfit(new BigDecimal(0));
//            one.setLoss(resultAmount.subtract(nowRepertory));
//        } else {
//            one.setProfit(new BigDecimal(0));
//            one.setLoss(new BigDecimal(0));
//        }
        inventorySimpleItemRepository.saveAndFlush(one);

//        inventorySimpleItemRepository.updateByInventoryNumberAndInventoryStatusAndAssetsNumber(one.getLossSum(), one.getProfitSum(), one.getInventoryNumber(), 2L, one.getAssetsNumber());
//        List<InventorySimpleItem> byInventoryNumberAndInventoryStatusAndAssetsNumber = inventorySimpleItemRepository.findByInventoryNumberAndInventoryStatusAndAssetsNumber(one.getInventoryNumber(), 2L, one.getAssetsNumber());
//        for (InventorySimpleItem simpleItem : byInventoryNumberAndInventoryStatusAndAssetsNumber) {
//            simpleItem.setLossSum();
//            simpleItem.setProfitSum();
//            inventorySimpleItemRepository.saveAndFlush(simpleItem);
//        }


//        MaterialsRepertory byAssetsCodeOrAssetsRfid = materialsRepertoryRepository.findByAssetsCodeOrAssetsRfid(inventorySimpleItem.getAssetsCode(), inventorySimpleItem.getAssetsCode());
//        byAssetsCodeOrAssetsRfid.setNowRepertory(inventorySimpleItem.getResultAmount());
//        materialsRepertoryRepository.saveAndFlush(byAssetsCodeOrAssetsRfid);

        return ResponseData.success("调整成功");
    }

    @Override
    @Transactional
    public boolean verify(Long id) {
        LoginUser userInfo = ShiroUtils.getUserInfo();
        InventorySimple one = inventorySimpleRepository.getOne(id);
        List<InventorySimpleItem> byInventoryNumber = inventorySimpleItemRepository.findByInventoryNumber(one.getInventoryNumber());
        Map<String, MaterialsRepertory> repertoryMap = materialsRepertoryRepository.findAll()
                .stream()
                .collect(Collectors.toMap(
                        MaterialsRepertory::getAssetsCode,
                        Function.identity(),
                        (existing, replacement) -> existing)); // 如果有重复的键，选择保留第一个
        List<MaterialsRepertory> toUpdate = new ArrayList<>();
        List<MaterialsRepertory> toInsert = new ArrayList<>();
        List<MaterialsListEntity> materialsListEntities = new ArrayList<>();

        for (InventorySimpleItem inventorySimpleItem : byInventoryNumber) {


            // 检查是否为空
            if (StringUtils.isEmpty(inventorySimpleItem.getAssetsCode())) {
                continue;
            }

            MaterialsRepertory repertory = repertoryMap.get(inventorySimpleItem.getAssetsCode());

            if (repertory == null) {
                MaterialsRepertory newRepertory = new MaterialsRepertory();
                BeanUtils.copyProperties(inventorySimpleItem, newRepertory);
                newRepertory.setNowRepertory(inventorySimpleItem.getResultAmount());
                newRepertory.setNowCount(new BigDecimal(1));
                newRepertory.setCreateTime(new Date());
                newRepertory.setId(null);
                newRepertory.setOperationNumber(one.getInventoryNumber());
                newRepertory.setPriority(1L);
                toInsert.add(newRepertory);
                repertoryMap.put(newRepertory.getAssetsCode(), newRepertory);
                MaterialsListEntity materialsListEntity = new MaterialsListEntity();
                BeanUtils.copyProperties(inventorySimpleItem, materialsListEntity);
                materialsListEntity.setId(null);
                materialsListEntity.setOperationName(MaterialsUtil.OPERATION_RUKU);
                materialsListEntity.setOperationOtherId(one.getInventoryNumber());
                materialsListEntity.setReason("5");
                materialsListEntity.setOperationId(one.getId());
                materialsListEntity.setAssetsRfid(inventorySimpleItem.getAssetsRfid());
                materialsListEntity.setCountInfo(inventorySimpleItem.getResultAmount());
                materialsListEntity.setNowRepertory(inventorySimpleItem.getResultAmount());
                materialsListEntity.setUserName(userInfo.getName());
                materialsListEntity.setOutDate(new Date());
                materialsListEntity.setCreateTime(new Date());
                materialsListEntity.setBatchNumber(inventorySimpleItem.getBatchNumber());
                materialsListEntities.add(materialsListEntity);

            } else {
                if (inventorySimpleItem.getInventoryStatus() != 2) {
                    repertory.setNowRepertory(new BigDecimal(0));
                    repertory.setModeOfTrade(inventorySimpleItem.getModeOfTrade());
                } else {
                    if (StringUtils.isEmpty(repertory.getAreaInfo())) {
                        repertory.setModeOfTrade(inventorySimpleItem.getModeOfTrade());
                        repertory.setAreaInfo(inventorySimpleItem.getAreaInfo());
                        repertory.setNowRepertory(inventorySimpleItem.getResultAmount());
                    } else if (inventorySimpleItem.getNowRepertory().compareTo(inventorySimpleItem.getResultAmount()) != 0) {
                        repertory.setModeOfTrade(inventorySimpleItem.getModeOfTrade());
                        repertory.setNowRepertory(inventorySimpleItem.getResultAmount());
                    }
                }
                toUpdate.add(repertory);
            }
        }
// 批量保存
        materialsRepertoryRepository.saveAll(toInsert);
        materialsRepertoryRepository.saveAll(toUpdate);
        materialsListRepository.saveAll(materialsListEntities);

        one.setInventoryStatus(3);
        inventorySimpleRepository.saveAndFlush(one);
//
//
//
//
//
//
//        for (InventorySimpleItem inventorySimpleItem : byInventoryNumber) {
//            if (StringUtils.isEmpty(inventorySimpleItem.getAssetsCode())) {
//                continue;
//            }
//            if (inventorySimpleItem.getInventoryStatus() != 2) {
//                MaterialsRepertory byAssetsCodeOrAssetsRfid = materialsRepertoryRepository.findByAssetsCodeOrAssetsRfid(inventorySimpleItem.getAssetsCode(), inventorySimpleItem.getAssetsCode());
//                if (byAssetsCodeOrAssetsRfid != null) {
//                    byAssetsCodeOrAssetsRfid.setNowRepertory(new BigDecimal(0));
//                    materialsRepertoryRepository.saveAndFlush(byAssetsCodeOrAssetsRfid);
////                    materialsRepertoryRepository.delete(byAssetsCodeOrAssetsRfid);
//                }
//                continue;
//            }
//            MaterialsRepertory byAssetsCodeOrAssetsRfid = materialsRepertoryRepository.findByAssetsCodeOrAssetsRfid(inventorySimpleItem.getAssetsCode(), inventorySimpleItem.getAssetsCode());
//            if (byAssetsCodeOrAssetsRfid == null) {
//                MaterialsRepertory consumableRepertory = new MaterialsRepertory();
//                BeanUtils.copyProperties(inventorySimpleItem, consumableRepertory);
//                consumableRepertory.setNowRepertory(inventorySimpleItem.getResultAmount());//当前库存
//                consumableRepertory.setNowCount(1L);
//                consumableRepertory.setCreateTime(new Date());
//                consumableRepertory.setId(null);
//                consumableRepertory.setOperationNumber(one.getInventoryNumber());
//                consumableRepertory.setPriority(1L);
//                //通过耗材查库存
//                materialsRepertoryRepository.save(consumableRepertory);
//
//                MaterialsListEntity materialsListEntity = new MaterialsListEntity();
//                BeanUtils.copyProperties(inventorySimpleItem, materialsListEntity);
//                materialsListEntity.setId(null);
//                materialsListEntity.setOperationName(MaterialsUtil.OPERATION_RUKU);
//                materialsListEntity.setOperationOtherId(one.getInventoryNumber());
//                materialsListEntity.setReason("5");
//                materialsListEntity.setOperationId(one.getId());
//                materialsListEntity.setAssetsRfid(inventorySimpleItem.getAssetsRfid());
//                materialsListEntity.setCountInfo(inventorySimpleItem.getResultAmount());
//                materialsListEntity.setNowRepertory(inventorySimpleItem.getResultAmount());
//                materialsListEntity.setUserName(userInfo.getName());
//                materialsListEntity.setOutDate(new Date());
//                materialsListEntity.setCreateTime(new Date());
//                materialsListEntity.setBatchNumber(inventorySimpleItem.getBatchNumber());
//                materialsListRepository.save(materialsListEntity);
//            } else {
//                if (StringUtils.isEmpty(byAssetsCodeOrAssetsRfid.getAreaInfo())) {
//                    byAssetsCodeOrAssetsRfid.setModeOfTrade(inventorySimpleItem.getModeOfTrade());
//                    byAssetsCodeOrAssetsRfid.setAreaInfo(inventorySimpleItem.getAreaInfo());
//                    byAssetsCodeOrAssetsRfid.setNowRepertory(inventorySimpleItem.getResultAmount());
//                    materialsRepertoryRepository.saveAndFlush(byAssetsCodeOrAssetsRfid);
//                } else {
//                    if (inventorySimpleItem.getNowRepertory().compareTo(inventorySimpleItem.getResultAmount()) == 0) {
//                        continue;
//                    }
//                    byAssetsCodeOrAssetsRfid.setModeOfTrade(inventorySimpleItem.getModeOfTrade());
//                    byAssetsCodeOrAssetsRfid.setNowRepertory(inventorySimpleItem.getResultAmount());
//                    materialsRepertoryRepository.saveAndFlush(byAssetsCodeOrAssetsRfid);
//                }
//            }
//
//
//        }
//        one.setInventoryStatus(3);
//        inventorySimpleRepository.saveAndFlush(one);
        return true;
    }

    @Override
    public InventorySimple findByInventoryNumber(String inventoryNumber) {
        return inventorySimpleRepository.findByInventoryNumber(inventoryNumber);
    }

    @Override
    public boolean over(Long id) {
        InventorySimple one = inventorySimpleRepository.getOne(id);
        one.setInventoryStatus(2);
        inventorySimpleRepository.saveAndFlush(one);
        return true;
    }

    @Override
    public ResponseData delInfo(Long id) {
        InventorySimpleItem one = inventorySimpleItemRepository.getOne(id);
        inventorySimpleItemRepository.delete(one);
//        BigDecimal resultAmountSum = inventorySimpleItemRepository.findBySumResultAmount(one.getInventoryNumber(), 2L, one.getAssetsNumber());
//        if (StringUtils.isEmpty(resultAmountSum)) {
//            resultAmountSum = new BigDecimal(0);
//        }
//
//
//        BigDecimal nowRepertorySum = one.getNowRepertorySum();//库存
//        BigDecimal add = resultAmountSum;
//        int i2 = nowRepertorySum.compareTo(add);
//        if (i2 == -1) {
//            one.setProfitSum(add.subtract(nowRepertorySum));
//            one.setLossSum(new BigDecimal(0));
//        } else if (i2 == 1) {
//            one.setProfitSum(new BigDecimal(0));
//            one.setLossSum(add.subtract(nowRepertorySum));
//        } else {
//            one.setProfitSum(new BigDecimal(0));
//            one.setLossSum(new BigDecimal(0));
//        }
//
//        List<InventorySimpleItem> byInventoryNumberAndInventoryStatusAndAssetsNumber = inventorySimpleItemRepository.findByInventoryNumberAndInventoryStatusAndAssetsNumber(one.getInventoryNumber(), 2L, one.getAssetsNumber());
//        for (InventorySimpleItem simpleItem : byInventoryNumberAndInventoryStatusAndAssetsNumber) {
//            simpleItem.setLossSum(one.getLossSum());
//            simpleItem.setProfitSum(one.getProfitSum());
//            inventorySimpleItemRepository.saveAndFlush(simpleItem);
//        }
        return ResponseData.success();

    }

    @Override
    public ResponseData updateModeOfTrade(String json) {
        InventorySimpleItem inventorySimpleItem = JSONObject.parseObject(json, InventorySimpleItem.class);
        List<InventorySimpleItem> byInventoryNumberAndInventoryStatusAndAssetsNumber = inventorySimpleItemRepository.findByInventoryNumberAndInventoryStatusAndAssetsNumber(inventorySimpleItem.getInventoryNumber(), 2L, inventorySimpleItem.getAssetsNumber());
        for (InventorySimpleItem simpleItem : byInventoryNumberAndInventoryStatusAndAssetsNumber) {
            simpleItem.setModeOfTrade(inventorySimpleItem.getModeOfTrade());
            inventorySimpleItemRepository.saveAndFlush(simpleItem);
        }
        return ResponseData.success("调整成功");
    }

    @Override
    public boolean submitByIds(Long[] ids) {
        Boolean code = true;
        for (Long id : ids) {
            InventorySimple inStorageMaterials = this.getById(id);
            inStorageMaterials.setStatus(2);//办理状态-审核中
            this.update(inStorageMaterials);
        }
        return code;
    }

    @Override
    public void audit(Long id, Integer status) {
        InventorySimple inStorageMaterials = this.getById(id);
        if (status == 3) {//审核通过
            inStorageMaterials.setStatus(3);
        } else {//不通过
            inStorageMaterials.setStatus(4);
        }
        inStorageMaterials.setAuditor(ShiroUtils.getUserInfo().getName());
        inStorageMaterials.setAuditorDate(new Date());
        this.update(inStorageMaterials);
    }

    @Transactional
    public <T> void addSimpleAndInfo(BaseRepository<T, Long> repository, InventorySimple inventorySimple, List<AssetsInfo> assetsInfos) {
        T t = repository.findById(inventorySimple.getInventoryTypeId()).get();
        BasisEntity basisEntity = (BasisEntity) t;
        inventorySimple.setInventoryType(basisEntity.getBasisName());
        inventorySimple = inventorySimpleRepository.save(inventorySimple);
        InventoryInfo info = inventoryInfoRepository.save(new InventoryInfo(inventorySimple.getId(), basisEntity.getId(), basisEntity.getBasisName(), basisEntity.getBasisStandBy(), assetsInfos.size(), 0, 0, 0, 0, assetsInfos.size() > 0 ? 0 : 2, null));
        for (AssetsInfo assetsInfo : assetsInfos) {
            inventoryAssetsInfoRepository.save(new InventoryAssetsInfo(assetsInfo.getId(), info.getId(), 0));
        }
    }
}
