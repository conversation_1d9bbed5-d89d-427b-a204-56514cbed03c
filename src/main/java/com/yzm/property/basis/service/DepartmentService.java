package com.yzm.property.basis.service;

import com.yzm.framework.base.IBaseService;
import com.yzm.framework.bean.Ztree;
import com.yzm.framework.shiro.LoginUser;
import com.yzm.property.basis.entity.BasisDepartment;
import com.yzm.property.system.entity.SysRole;

import java.util.List;

public interface DepartmentService extends IBaseService<BasisDepartment, Long> {


    BasisDepartment findByDepartmentName(String departmentName);

    BasisDepartment findById(Integer department);


    List<BasisDepartment> findMenuList(Long userId, String name);

    List<BasisDepartment> findMenuList(Long userId);

    boolean checkMenuNameUnique(BasisDepartment menu);

    List<BasisDepartment> findListParentId(Long menuId);

    boolean clearMenuRedis();

    List<Ztree> menuTreeData(LoginUser userInfo);

    List<Ztree> roleMenuTreeData(SysRole role, LoginUser userInfo);
}
