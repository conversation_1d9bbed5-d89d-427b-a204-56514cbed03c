package com.yzm.property.basis.service.impl;

import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.framework.bean.PageBean;
import com.yzm.property.basis.entity.AssetsLog;
import com.yzm.property.basis.repository.AssetsLogRepository;
import com.yzm.property.basis.service.AssetsLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/31 17:33
 */
@Service
public class AssetsLogServiceImpl extends BaseService<AssetsLog,Long> implements AssetsLogService {
    @Autowired
    private AssetsLogRepository assetsLogRepository;

    @Override
    public BaseRepository<AssetsLog, Long> getRepository() {
        return assetsLogRepository;
    }

    @Override
    public Map findAllByAssetsId(Long id, PageBean pageBean) {
        Map<String,Object> map = new HashMap<>();
        Page<AssetsLog> logs = assetsLogRepository.findByAssetsIdOrderByCreateTimeDesc(id, pageBean.getPagable());
        map.put("list", logs.getContent());
        map.put("totalCount", logs.getTotalElements());
        return map;
    }

    @Override
    public List<Map<String, Object>> assetsInfoUseFrequency() {
        return assetsLogRepository.assetsInfoUseFrequency();
    }

    @Override
    public List<Map<String, Object>> assetsInfoType() {
        return assetsLogRepository.assetsInfoType();
    }
}
