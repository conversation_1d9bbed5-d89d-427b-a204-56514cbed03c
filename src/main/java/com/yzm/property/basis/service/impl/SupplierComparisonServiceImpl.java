package com.yzm.property.basis.service.impl;

import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.property.basis.entity.BasisArea;
import com.yzm.property.basis.entity.SupplierComparison;
import com.yzm.property.basis.repository.AreaRepository;
import com.yzm.property.basis.repository.SupplierComparisonRepository;
import com.yzm.property.basis.service.AreaService;
import com.yzm.property.basis.service.AssetsInfoService;
import com.yzm.property.basis.service.SupplierComparisonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SupplierComparisonServiceImpl extends BaseService<SupplierComparison, Long> implements SupplierComparisonService {

    @Autowired
    private SupplierComparisonRepository supplierComparisonRepository;



    @Override
    public BaseRepository<SupplierComparison, Long> getRepository() {
        return supplierComparisonRepository;
    }

    @Override
    public SupplierComparison findBySupplierNameAndSupplierMaterialsCodeAndInternalMaterialsCode(String supplierName, String supplierMaterialsCode, String internalMaterialsCode) {
        return supplierComparisonRepository.findBySupplierNameAndSupplierMaterialsCodeAndInternalMaterialsCode(supplierName,supplierMaterialsCode,internalMaterialsCode);
    }
}
