package com.yzm.property.basis.service.impl;

import com.yzm.common.config.ConfigConstant;
import com.yzm.common.utils.RedisUtil;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.framework.bean.Ztree;
import com.yzm.framework.shiro.LoginUser;
import com.yzm.property.basis.entity.BasisDepartment;
import com.yzm.property.basis.repository.DepartmentRepository;
import com.yzm.property.basis.repository.PersonnelRepository;
import com.yzm.property.basis.service.DepartmentService;
import com.yzm.property.system.entity.SysMenu;
import com.yzm.property.system.entity.SysRole;
import com.yzm.property.system.service.ISysRoleMenuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class DepartmentServiceImpl extends BaseService<BasisDepartment, Long> implements DepartmentService {

    @Autowired
    private DepartmentRepository departmentRepository;
    @Autowired
    private PersonnelRepository personnelRepository;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private ISysRoleMenuService iSysRoleMenuService;


    @Override
    public BaseRepository<BasisDepartment, Long> getRepository() {
        return departmentRepository;
    }

    @Override
    public BasisDepartment findByDepartmentName(String departmentName) {
        return departmentRepository.findByBasisName(departmentName);
    }

    @Override
    public BasisDepartment findById(Integer department) {
        return departmentRepository.findById(department.longValue()).get();
    }

    @Override
    public List<BasisDepartment> findMenuList(Long userId,String name) {
        if(name==null){
            name="";
        }
        List<BasisDepartment> datas = null;
        if (ConfigConstant.SUPER_ADMIN.equals(userId)) {
            datas = departmentRepository.findByBasisNameLike(name);
        } else {
//            Set<SysMenu> menus = departmentRepository.findMenuByUserId(userId);
            datas = departmentRepository.findByBasisNameLike(name);
//            datas = departmentRepository.findByBasisNameLike(name,Sort.by(Sort.Direction.ASC, SysMenu.FIELD_ORDER_NUM));

//            datas = new ArrayList<>(menus);
        }
        return datas;
    }
    @Override
    public List<BasisDepartment> findMenuList(Long userId) {
        List<BasisDepartment> datas = null;
        if (ConfigConstant.SUPER_ADMIN.equals(userId)) {
            datas = departmentRepository.findAll();
        } else {
//            Set<SysMenu> menus = departmentRepository.findMenuByUserId(userId);
            datas = departmentRepository.findAll(Sort.by(Sort.Direction.ASC, SysMenu.FIELD_ORDER_NUM));

//            datas = new ArrayList<>(menus);
        }
        return datas;
    }

    @Override
    public boolean checkMenuNameUnique(BasisDepartment menu) {
        Long menuId = StringUtils.isEmpty(menu.getId()) ? -1L : menu.getId();
        BasisDepartment info = departmentRepository.findByBasisNameAndParentId(menu.getBasisName(), menu.getParentId());
        if (!StringUtils.isEmpty(info) && info.getId().longValue() != menuId.longValue()) {
            return false;
        }
        return true;
    }

    @Override
    public BasisDepartment save(BasisDepartment menu) {
        // 查询上级菜单
        if (menu.getParentId() != 0L) {
            BasisDepartment pMenu = super.getById(menu.getParentId());
            menu.setParentName(pMenu.getBasisName());
        }
        BasisDepartment sysmenu = super.save(menu);
        return sysmenu;
    }

    @Override
    public List<BasisDepartment> findListParentId(Long menuId) {
        return departmentRepository.findAllByParentId(menuId);
    }





    List<BasisDepartment> childNode = new ArrayList<BasisDepartment>();
    List<BasisDepartment> lastChildNode = new ArrayList<BasisDepartment>();

    public List<BasisDepartment> treeMenuList(List<BasisDepartment> treeNodes, int pid) {
//
        List<BasisDepartment> tempTreeNode = new ArrayList<BasisDepartment>();
        List<BasisDepartment> tempTreeNode1 = new ArrayList<BasisDepartment>();
        for (BasisDepartment node : treeNodes) {
            if (node.getParentId() == pid) {
                //说明存在子节点

                tempTreeNode1 = treeMenuList(treeNodes, node.getId().intValue());
                if (tempTreeNode1.isEmpty()) {
                    //不存在子节点
                    lastChildNode.add(node);
                }
                childNode.add(node);
                //用于让上一级判断是否存在子节点
                //因为存在子节点则tempTreeNode不为空
                //函数结束后返回tempTreeNode给上一级以供判断
                tempTreeNode.add(node);
                System.out.println("当前节点存在子节点");
            }

        }
        return tempTreeNode;
    }

    @Override
    public boolean clearMenuRedis() {
        // TODO Auto-generated method stub
        return true;
    }

    @Override
    public List<Ztree> menuTreeData(LoginUser userInfo) {
        List<BasisDepartment> menuList = findMenuList(userInfo.getId());
        List<Ztree> ztrees = initZtree(menuList, null, false);
        return ztrees;
    }

    /**
     * 对象转菜单树
     *
     * @param menuList     菜单列表
     * @param roleMenuList 角色已存在菜单列表
     * @param permsFlag    是否需要显示权限标识
     * @return 树结构列表
     */
    public List<Ztree> initZtree(List<BasisDepartment> menuList, List<Long> roleMenuList, boolean permsFlag) {
        List<Ztree> ztrees = new ArrayList<Ztree>();
        boolean isCheck = !StringUtils.isEmpty(roleMenuList);
        for (BasisDepartment menu : menuList) {
            Ztree ztree = new Ztree();
            ztree.setId(menu.getId());
            ztree.setpId(menu.getParentId());
            ztree.setName(menu.getBasisName());
            ztree.setTitle(menu.getBasisName());
            if (isCheck) {
                ztree.setChecked(roleMenuList.contains(menu.getId()));
            }
            ztrees.add(ztree);
        }
        return ztrees;
    }

    @Override
    public List<Ztree> roleMenuTreeData(SysRole role, LoginUser userInfo) {
        Long roleId = role.getId();
        List<Ztree> ztrees = new ArrayList<Ztree>();
        List<BasisDepartment> menuList = findMenuList(userInfo.getId());
        if (!StringUtils.isEmpty(roleId)) {
            List<Long> roleMenuList = iSysRoleMenuService.selectRoleMenu(roleId);
            ztrees = initZtree(menuList, roleMenuList, true);
        } else {
            ztrees = initZtree(menuList, null, true);
        }
        return ztrees;
    }

}
