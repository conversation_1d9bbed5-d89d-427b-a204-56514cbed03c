package com.yzm.property.basis.service.impl;

import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.basis.entity.InventoryInfo;
import com.yzm.property.basis.entity.InventorySimple;
import com.yzm.property.basis.entity.InventorySimpleItem;
import com.yzm.property.basis.repository.InventoryInfoRepository;
import com.yzm.property.basis.repository.InventorySimpleItemRepository;
import com.yzm.property.basis.repository.InventorySimpleRepository;
import com.yzm.property.basis.service.InventoryInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/9 10:01
 */
@Service
public class InventoryInfoImpl extends BaseService<InventoryInfo,Long> implements InventoryInfoService {

    @Autowired
    private InventoryInfoRepository inventoryInfoRepository;

    @Autowired
    private InventorySimpleRepository inventorySimpleRepository;
    @Autowired
    private InventorySimpleItemRepository inventorySimpleItemRepository;

    @Override
    public BaseRepository<InventoryInfo, Long> getRepository() {
        return inventoryInfoRepository;
    }

    @Override
    public Map<String,Object> findAllByInventSimpleId(Long id, PageBean pageBean) {
        Map<String, Object> map = new HashMap<>();
        Page page = inventoryInfoRepository.findBySimpleId(id, pageBean.getPagable());
        map.put("list",page.getContent());
        map.put("totalCount",page.getTotalElements());
        return map;
    }

    @Override
    @Transactional
    public InventoryInfo inventoryAssets(Long id, Integer resultAmount,String note) {
        InventoryInfo info = inventoryInfoRepository.findById(id).get();
        info.setResultAmount(resultAmount);
        info.setNote(note);
        info.setProfitLoss(resultAmount - info.getAssetsAmount());
        inventoryInfoRepository.save(info);
        Integer count = inventoryInfoRepository.countBySimpleIdAndResultAmountIsNull(info.getSimpleId());
        InventorySimple simple = inventorySimpleRepository.findById(info.getSimpleId()).get();
        if (count == 0 ){
            simple.setInventoryStatus(2);
        }else {
            simple.setInventoryStatus(1);
        }
        inventorySimpleRepository.save(simple);
        return info;
    }


    @Override
    public Page<InventoryInfo> findAllByInventSimpleIdAndStatus(Long id, Pageable pageable) {
        return inventoryInfoRepository.findAllBySimpleIdAndInventoryStatusNot(id,2,pageable);
    }

    @Override
    public Map<String, Object> showInfosW(Long id, PageBean pageBean) {
        Map<String, Object> map = new HashMap<>();
        InventorySimple inventorySimple = inventorySimpleRepository.findById(id).get();
        List<InventorySimpleItem> list =  inventorySimpleItemRepository.findByInventoryNumber(inventorySimple.getInventoryNumber());
        map.put("list",list);
        map.put("totalCount",list.size());
        return map;
    }

    @Override
    public ResponseData showInfosData(Long id) {
        InventorySimple inventorySimple = inventorySimpleRepository.findById(id).get();
        List<InventorySimpleItem> listNo =  inventorySimpleItemRepository.findByInventoryNumberAndInventoryStatus(inventorySimple.getInventoryNumber(),0L);
        List<InventorySimpleItem> listOK =  inventorySimpleItemRepository.findByInventoryNumberAndInventoryStatus(inventorySimple.getInventoryNumber(),2L);
        Map<String,Object> map = new HashMap<>();
        map.put("listNo",listNo);
        map.put("listOK",listOK);
        return ResponseData.success(map);
    }


}
