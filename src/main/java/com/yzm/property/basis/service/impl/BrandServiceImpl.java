package com.yzm.property.basis.service.impl;

import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.property.basis.entity.BasisBrand;
import com.yzm.property.basis.repository.BrandRepository;
import com.yzm.property.basis.service.BrandService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class BrandServiceImpl extends BaseService<BasisBrand, Long> implements BrandService {

    @Autowired
    private BrandRepository brandRepository;


    @Override
    public BaseRepository<BasisBrand, Long> getRepository() {
        return brandRepository;
    }

    @Override
    public BasisBrand findById(Long id) {
        return brandRepository.findById(id).get();
    }
}
