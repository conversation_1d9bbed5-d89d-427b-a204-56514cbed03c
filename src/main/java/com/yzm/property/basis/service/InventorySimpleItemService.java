package com.yzm.property.basis.service;

import com.yzm.framework.base.IBaseService;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.basis.criteria.InventoryCriteria;
import com.yzm.property.basis.entity.InventoryResult;
import com.yzm.property.basis.entity.InventorySimple;
import com.yzm.property.basis.entity.InventorySimpleItem;
import com.yzm.property.materials.criteria.MaterialsBomCriteria;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/9 9:59
 */
public interface InventorySimpleItemService extends IBaseService<InventorySimpleItem, Long> {
    Map<String, Object> findAllByPageGroup(InventoryCriteria bean, Pageable page);

}
