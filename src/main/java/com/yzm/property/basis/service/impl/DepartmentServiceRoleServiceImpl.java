package com.yzm.property.basis.service.impl;

import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.property.basis.entity.BasisDepartmentRole;
import com.yzm.property.basis.repository.DepartmentRoleRepository;
import com.yzm.property.basis.service.DepartmentServiceRole;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class DepartmentServiceRoleServiceImpl extends BaseService<BasisDepartmentRole, Long> implements DepartmentServiceRole {

    @Autowired
    private DepartmentRoleRepository basisDepartmentRole;

    @Override
    public BaseRepository<BasisDepartmentRole, Long> getRepository() {
        return basisDepartmentRole;
    }

    @Override
    public List<Long> selectRoleMenu(Long roleId) {
        List<BasisDepartmentRole> roleMenus = basisDepartmentRole.findAllByRoleId(roleId);
        return roleMenus.stream().map(m -> m.getMenuId()).collect(Collectors.toList());
    }

}
