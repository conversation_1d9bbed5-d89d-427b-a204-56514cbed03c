package com.yzm.property.basis.service;

import com.yzm.framework.base.IBaseService;
import com.yzm.framework.bean.Ztree;
import com.yzm.framework.shiro.LoginUser;
import com.yzm.property.basis.entity.BasisType;
import com.yzm.property.system.entity.SysRole;

import java.util.List;

public interface TypeService extends IBaseService<BasisType, Long> {


    BasisType findById(Integer assetsType);


    BasisType findByDepartmentName(String departmentName);



    List<BasisType> findMenuList(String keyword);

    List<BasisType> findMenuList(Long userId);

    boolean checkMenuNameUnique(BasisType menu);

    List<BasisType> findListParentId(Long menuId);

    boolean clearMenuRedis();

    List<Ztree> menuTreeData(LoginUser userInfo);

    List<Ztree> roleMenuTreeData(SysRole role, LoginUser userInfo);
}
