package com.yzm.property.basis.service.impl;

import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.property.basis.entity.BasisWarehouse;
import com.yzm.property.basis.repository.WarehouseRepository;
import com.yzm.property.basis.service.AssetsInfoService;
import com.yzm.property.basis.service.WarehouseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class WarehouseServiceImpl extends BaseService<BasisWarehouse, Long> implements WarehouseService {

    @Autowired
    private WarehouseRepository warehouseRepository;
    @Autowired
    private AssetsInfoService assetsInfoService;


    @Override
    public BaseRepository<BasisWarehouse, Long> getRepository() {
        return warehouseRepository;
    }

    @Override
    public BasisWarehouse findById(Integer assetsArea) {
        return warehouseRepository.findById(assetsArea.longValue()).get();
    }

    @Override
    public BasisWarehouse findByCkmc(String ckmc) {
        return warehouseRepository.findByCkmc(ckmc);
    }

}
