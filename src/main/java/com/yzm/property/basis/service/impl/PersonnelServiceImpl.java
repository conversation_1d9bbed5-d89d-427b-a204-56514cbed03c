package com.yzm.property.basis.service.impl;

import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.property.basis.entity.BasisDepartment;
import com.yzm.property.basis.entity.BasisPersonnel;
import com.yzm.property.basis.repository.DepartmentRepository;
import com.yzm.property.basis.repository.PersonnelRepository;
import com.yzm.property.basis.service.PersonnelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
public class PersonnelServiceImpl extends BaseService<BasisPersonnel, Long> implements PersonnelService {

    @Autowired
    private PersonnelRepository personnelRepository;
    @Autowired
    private DepartmentRepository departmentRepository;


    @Override
    public BaseRepository<BasisPersonnel, Long> getRepository() {
        return personnelRepository;
    }

    @Override
    public BasisPersonnel findById(Long id) {
        return personnelRepository.findById(id).get();
    }


    @Override
    public List<BasisPersonnel> findByDepartmentIdList(Long id) {
        List<BasisPersonnel> l = personnelRepository.findByPersonnelDepartmentId(id);
        List<BasisDepartment> allByParentId = departmentRepository.findAll();
        getMenuTreeList(allByParentId, id, l);
        return l;
    }

    @Override
    public List<BasisPersonnel> findByDeleteFlag(Integer status) {
        return personnelRepository.findByDeleteFlag(status);
    }

    @Override
    @Transactional
    public boolean deleteByIds(Long[] ids) {
        try {
            for (Long id : ids) {
                BasisPersonnel personnel = personnelRepository.findById(id).get();
                personnel.setDeleteFlag(1);
                personnelRepository.save(personnel);
            }
        } catch (Exception e) {
            return false;
        }
        return true;
    }


    /**
     * 递归
     */
    private List<BasisDepartment> getMenuTreeList(List<BasisDepartment> rootmenuList, Long parentId, List<BasisPersonnel> l) {
        List<BasisDepartment> menuList = new ArrayList<BasisDepartment>();
        for (BasisDepartment entity : rootmenuList) {
            if (entity.getParentId().equals(parentId)) {
                System.out.println(entity.getBasisName());
                List<BasisPersonnel> personnelList = personnelRepository.findByPersonnelDepartmentId(entity.getId());
                l.addAll(personnelList);
//                List<BasisDepartment> allByParentId = departmentRepository.findAllByParentId(entity.getId());
                entity.setChildren(getMenuTreeList(rootmenuList, entity.getId(), l));
                menuList.add(entity);
            }
        }
        return menuList;
    }

}
