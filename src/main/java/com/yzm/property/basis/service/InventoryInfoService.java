package com.yzm.property.basis.service;

import com.yzm.framework.base.IBaseService;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.basis.entity.InventoryInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/9 9:59
 */
public interface InventoryInfoService extends IBaseService<InventoryInfo, Long> {
    Map<String,Object> findAllByInventSimpleId(Long id, PageBean pageBean);

    InventoryInfo inventoryAssets(Long id, Integer resultAmount,String note);

    Page<InventoryInfo> findAllByInventSimpleIdAndStatus(Long id, Pageable pageable);

    Map<String,Object> showInfosW(Long id, PageBean pageBean);

    ResponseData showInfosData(Long id);
}
