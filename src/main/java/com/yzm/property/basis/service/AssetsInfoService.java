package com.yzm.property.basis.service;

import com.yzm.framework.base.IBaseService;
import com.yzm.framework.bean.PageBean;
import com.yzm.property.basis.entity.AssetsInfo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:47
 */
public interface AssetsInfoService extends IBaseService<AssetsInfo,Long> {

    AssetsInfo save(AssetsInfo assetsInfo);

    void delete(Long[] ids);

    Map showAllAssetsInfo(String keyword, PageBean pageBean);

    AssetsInfo findById(Long id);

    AssetsInfo findByOtherId(String otherId);

    boolean checkByOtherId(String otherId);

    List<AssetsInfo> findAll();

    List<AssetsInfo> showAssetsInfoByRepairs(Long repairsId);

    Map showAllAssetsInfoToRepairsOrScrap(String keyword, PageBean pageBean);


    List<AssetsInfo> showAssetsInfoByScrap(Long scrapId);

    List<AssetsInfo> showAssetsInfoByBorrowReturns(String  borrowReturnId);

    Map showAssetsInfoByStatus(Integer status,String keyword, PageBean pageBean);

    Map showAllAssetsInfoToReturn(String keyword, PageBean pageBean);
    //资产总金额
    int findByPurchasePriceSum();
    //到期维保数量
    int findByMaintenanceStatusCount();
    //本月新增资产数
    int findAssetsMonthCount();

    int findAssetsYearCount();

    Map showAllAssetsInfoToBorrow(String keyword, PageBean pageBean);

    Boolean findByUnit(Long[] ids);

    boolean findByAssetsArea(Long[] ids);

    boolean findByAssetsType(Long[] ids);
}
