package com.yzm.property.basis.service.impl;

import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.property.basis.entity.BasisUnit;
import com.yzm.property.basis.repository.UnitRepository;
import com.yzm.property.basis.service.AssetsInfoService;
import com.yzm.property.basis.service.UnitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class UnitServiceImpl extends BaseService<BasisUnit, Long> implements UnitService {

    @Autowired
    private UnitRepository unitRepository;

    @Autowired
    private AssetsInfoService assetsInfoService;

    @Override
    public BaseRepository<BasisUnit, Long> getRepository() {
        return unitRepository;
    }

    @Override
    public BasisUnit findById(Long id) {
        return unitRepository.findById(id).get();
    }

    @Override
    public boolean deleteBatchByIds(Long[] ids){
        if(assetsInfoService.findByUnit(ids)){
            for (int i = 0; i < ids.length; i++) {
                unitRepository.deleteById(ids[i]);
            }
            return true;
        }else {
            return false;
        }
    }
}
