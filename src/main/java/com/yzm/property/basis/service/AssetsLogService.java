package com.yzm.property.basis.service;

import com.yzm.framework.base.IBaseService;
import com.yzm.framework.bean.PageBean;
import com.yzm.property.basis.entity.AssetsLog;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/31 17:32
 */
public interface AssetsLogService extends IBaseService<AssetsLog, Long> {
    Map findAllByAssetsId(Long id, PageBean pageBean);

    List<Map<String,Object>> assetsInfoUseFrequency();

    List<Map<String,Object>> assetsInfoType();
}
