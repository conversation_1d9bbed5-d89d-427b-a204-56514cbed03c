package com.yzm.property.basis.service.impl;

import com.yzm.framework.base.BaseRepository;
import com.yzm.framework.base.BaseService;
import com.yzm.framework.bean.PageBean;
import com.yzm.property.basis.entity.AssetsInfo;
import com.yzm.property.basis.entity.InventoryAssetsInfo;
import com.yzm.property.basis.entity.InventoryInfo;
import com.yzm.property.basis.repository.AssetsInfoRepository;
import com.yzm.property.basis.repository.InventoryAssetsInfoRepository;
import com.yzm.property.basis.repository.InventoryInfoRepository;
import com.yzm.property.basis.repository.UnitRepository;
import com.yzm.property.basis.service.AssetsInfoService;
import com.yzm.property.basis.utils.AssetsLogUtil;
import com.yzm.property.business.entity.AssetsRepairsMiddle;
import com.yzm.property.business.entity.AssetsScrapMiddle;
import com.yzm.property.business.entity.BorrowReturnItem;
import com.yzm.property.business.repository.AssetsRepairsMiddleRepository;
import com.yzm.property.business.repository.AssetsScrapMiddleRepository;
import com.yzm.property.business.repository.BorrowReturnItemRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:47
 */
@Service
public class AssetsInfoServiceImpl extends BaseService<AssetsInfo, Long> implements AssetsInfoService {

    @Autowired
    private AssetsInfoRepository assetsInfoRepository;

    @Autowired
    private AssetsRepairsMiddleRepository assetsRepairsMiddleRepository;

    @Autowired
    private AssetsScrapMiddleRepository assetsScrapMiddleRepository;
    @Autowired
    private BorrowReturnItemRepository borrowReturnItemRepository;

    @Autowired
    private InventoryAssetsInfoRepository inventoryAssetsInfoRepository;

    @Autowired
    private InventoryInfoRepository inventoryInfoRepository;
    @Autowired
    private UnitRepository unitRepository;

    /**
     * 删除资产
     */
    @Override
    public void delete(Long[] ids) {
        for (int i = 0; i < ids.length; i++) {
            assetsInfoRepository.deleteById(ids[i]);
        }
    }

    @Override
    @Transactional
    public AssetsInfo save(AssetsInfo assetsInfo) {
        try {
            Long custom2 = Long.parseLong(assetsInfo.getCustom2());
            if (custom2 != 0) {
                assetsInfo.setCustom2(null);
                assetsInfo = assetsInfoRepository.save(assetsInfo);
                InventoryInfo inventoryInfo = inventoryInfoRepository.findById(custom2).get();
                inventoryInfo.setProfitLoss(inventoryInfo.getProfitLoss() + 1);
                inventoryInfo.setProfit(inventoryInfo.getProfit() + 1);
                inventoryInfo.setResultAmount(inventoryInfo.getResultAmount() + 1);
                inventoryInfoRepository.save(inventoryInfo);
                inventoryAssetsInfoRepository.save(new InventoryAssetsInfo(assetsInfo.getId(), custom2, 3));
                AssetsLogUtil.createAssetsLog(0L, assetsInfo.getOtherId(), assetsInfo.getId(), assetsInfo.getOtherId(), assetsInfo.getAssetsName(), assetsInfo.getAssetsSource());
                return assetsInfo;
            }
        } catch (Exception e) {
//            System.out.println("从盘点新增失败");
        }
        String msg = "";
        Long id = assetsInfo.getId();
        assetsInfo = assetsInfoRepository.save(assetsInfo);
        if (id == null) {
            msg = assetsInfo.getAssetsSource();
            AssetsLogUtil.createAssetsLog(0L, assetsInfo.getOtherId(), assetsInfo.getId(), assetsInfo.getOtherId(), assetsInfo.getAssetsName(), msg);
        }
        return assetsInfo;

    }

    /**
     * 条件查询及查询所有资产
     */
    @Override
    public Map showAllAssetsInfo(String keyword, PageBean pageBean) {
        Page<AssetsInfo> result;
        if (keyword == null || "".equals(keyword)) {
            result = assetsInfoRepository.findAll(pageBean.getPagable());
        } else {
            result = assetsInfoRepository.findAllByAssetsNameContainingOrOtherIdContaining(keyword, keyword, pageBean.getPagable());
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("list", result.getContent());
        map.put("totalCount", result.getTotalElements());
        return map;
    }

    /**
     * 根据ID查找资产
     *
     * @param id
     * @return
     */
    @Override
    public AssetsInfo findById(Long id) {
        return assetsInfoRepository.findById(id).get();
    }

    @Override
    public AssetsInfo findByOtherId(String otherId) {
        return assetsInfoRepository.findByOtherId(otherId);
    }

    /**
     * 根据编号查找资产
     *
     * @param otherId
     * @return
     */
    @Override
    public boolean checkByOtherId(String otherId) {
        return assetsInfoRepository.findByOtherId(otherId) == null;
    }

    @Override
    public BaseRepository<AssetsInfo, Long> getRepository() {
        return assetsInfoRepository;
    }

    /**
     * 查找所有资产
     *
     * @return
     */
    @Override
    public List<AssetsInfo> findAll() {
        return assetsInfoRepository.findAll();
    }

    /**
     * 根据报修id分页查找资产
     *
     * @param repairsId 保修表id
     * @return
     */
    @Override
    public List showAssetsInfoByRepairs(Long repairsId) {
        List<AssetsInfo> list = new ArrayList<>();
        List<AssetsRepairsMiddle> middles = assetsRepairsMiddleRepository.findAllByRepairsId(repairsId);
        if (middles.size() <= 0) {
            middles = assetsRepairsMiddleRepository.findAllByReturnId(repairsId);
        }
        for (AssetsRepairsMiddle middle : middles) {
            AssetsInfo assetsInfo = assetsInfoRepository.findById(middle.getAssetsId()).get();
            assetsInfo.setCustom1(middle.getReturnStatus() == 0 ? "未归还" : "已归还");
            list.add(assetsInfo);
        }
        return list;
    }

    /**
     * 根据报废id分页查询资产
     *
     * @param scrapId 报废表id
     * @return
     */
    @Override
    public List<AssetsInfo> showAssetsInfoByScrap(Long scrapId) {
        List<AssetsInfo> list = new ArrayList<>();
        List<AssetsScrapMiddle> middles = assetsScrapMiddleRepository.findAllByScrapId(scrapId);
        for (AssetsScrapMiddle middle : middles) {
            list.add(assetsInfoRepository.findById(middle.getAssetsId()).get());
        }
        return list;
    }

    @Override
    public List<AssetsInfo> showAssetsInfoByBorrowReturns(String borrowReturnId) {
        List<AssetsInfo> list = new ArrayList<>();
        List<BorrowReturnItem> middles = borrowReturnItemRepository.findAllByBorrowReturnId(borrowReturnId);
        for (BorrowReturnItem middle : middles) {
            list.add(assetsInfoRepository.findById(middle.getAssetsId()).get());
        }
        return list;
    }

    /**
     * 根据状态查找资产
     *
     * @param status
     * @param pageBean
     * @return
     */
    @Override
    public Map showAssetsInfoByStatus(Integer status,String keyword, PageBean pageBean) {
        Map<String, Object> map = new HashMap<>();
        Page<AssetsInfo> result = assetsInfoRepository.findAllByAssetsNameContainingOrOtherIdContainingAndAssetsStatus(keyword,keyword,status, pageBean.getPagable());
        map.put("list", result.getContent());
        map.put("totalCount", result.getTotalElements());
        return map;
    }

    @Override
    public Map showAllAssetsInfoToReturn(String keyword, PageBean pageBean) {
        Page<AssetsInfo> result;
        if (keyword == null || "".equals(keyword)) {
            result = assetsInfoRepository.findAllByAssetsStatus(6, pageBean.getPagable());
        } else {
            result = assetsInfoRepository.findAllByAssetsNameContainingOrOtherIdContainingAndAssetsStatus(keyword, keyword, 6, pageBean.getPagable());
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("list", result.getContent());
        map.put("totalCount", result.getTotalElements());
        return map;
    }

    @Override
    public int findByPurchasePriceSum() {
        return assetsInfoRepository.findByPurchasePriceSum();
    }

    @Override
    public int findByMaintenanceStatusCount() {
        return assetsInfoRepository.findByMaintenanceStatusCount();
    }

    @Override
    public int findAssetsMonthCount() {
        return assetsInfoRepository.findAssetsMonthCount();
    }

    @Override
    public int findAssetsYearCount() {
        return assetsInfoRepository.findAssetsYearCount();
    }

    @Override
    public Map showAllAssetsInfoToBorrow(String keyword, PageBean pageBean) {
        Page<AssetsInfo> result;
        if (keyword == null || "".equals(keyword)) {
            result = assetsInfoRepository.findAllByAssetsStatus(4, pageBean.getPagable());
        } else {
            result = assetsInfoRepository.findAllByAssetsStatus(keyword, keyword, 4, pageBean.getPagable());
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("list", result.getContent());
        map.put("totalCount", result.getTotalElements());
        return map;
    }

    @Override
    public Boolean  findByUnit(Long[] ids) {
        for (int i = 0; i < ids.length; i++) {
            List<AssetsInfo> byUnit = assetsInfoRepository.findByUnit(ids[i]);
            if (byUnit.size()>0){
                return false;
            }
        }
        return true;
    }

    @Override
    public boolean findByAssetsArea(Long[] ids) {
        for (int i = 0; i < ids.length; i++) {
            List<AssetsInfo> byAssetsArea = assetsInfoRepository.findByAssetsArea(ids[i].intValue());
            if (byAssetsArea.size()>0){
                return false;
            }
        }
        return true;
    }

    @Override
    public boolean findByAssetsType(Long[] ids) {
        for (int i = 0; i < ids.length; i++) {
            List<AssetsInfo> byAssetsType = assetsInfoRepository.findByAssetsType(ids[i].intValue());
            if (byAssetsType.size()>0){
                return false;
            }
        }
        return true;
    }

    /**
     * 根据关键字和分页查询所有闲置状态的资产
     *
     * @param keyword
     * @param pageBean
     * @return
     */
    @Override
    public Map showAllAssetsInfoToRepairsOrScrap(String keyword, PageBean pageBean) {
        Page<AssetsInfo> result;
        if (keyword == null || "".equals(keyword)) {
            result = assetsInfoRepository.findAllByAssetsStatus(0, pageBean.getPagable());
        } else {
            result = assetsInfoRepository.findAllByAssetsNameContainingOrOtherIdContainingAndAssetsStatus(keyword, keyword, 0, pageBean.getPagable());
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("list", result.getContent());
        map.put("totalCount", result.getTotalElements());
        return map;
    }

}
