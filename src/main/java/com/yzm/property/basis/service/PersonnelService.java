package com.yzm.property.basis.service;

import com.yzm.framework.base.IBaseService;
import com.yzm.property.basis.entity.BasisPersonnel;

import java.util.List;

public interface PersonnelService extends IBaseService<BasisPersonnel, Long> {


    BasisPersonnel findById(Long id);


    List<BasisPersonnel> findByDepartmentIdList(Long id);

    List<BasisPersonnel> findByDeleteFlag(Integer status);

    boolean deleteByIds(Long[] ids);
}
