package com.yzm.property.basis.entity;

import com.yzm.framework.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/17 9:35
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "inventory_assets_info")
public class InventoryAssetsInfo extends BaseEntity {

    @Column(name = "assets_id", columnDefinition = "int(100) COMMENT '资产id'")
    private Long assetsId; //盘点概述表id

    @Column(name = "info_id", columnDefinition = "int(100) COMMENT '盘点详情id'")
    private Long infoId; //盘点概述表id

    @Column(name = "inventory_status", columnDefinition = "int(10) COMMENT '盘点状态'")
    private Integer inventoryStatus; //盘点状态
}
