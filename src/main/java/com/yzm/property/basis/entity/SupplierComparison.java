package com.yzm.property.basis.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yzm.framework.base.BaseEntity;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;

@Data
@Entity
@Table(name = "supplier_comparison")
public class SupplierComparison extends BaseEntity {
    @ExcelProperty(index = 0)
    private String supplierName;//供应商名称
    @ExcelProperty(index = 1)
    private String supplierNumber;//供应商代码
    @ExcelProperty(index = 2)
    private String supplierMaterialsCode;//供应商材料代码
    @ExcelProperty(index = 3)
    private String internalMaterialsCode;//内部代码
}
