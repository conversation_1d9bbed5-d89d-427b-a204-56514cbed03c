package com.yzm.property.basis.entity;

import com.yzm.framework.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/9 10:00
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "inventory_info")
public class InventoryInfo extends BaseEntity {

    @Column(name = "simple_id", columnDefinition = "int(100) COMMENT '盘点概述表id'")
    private Long simpleId; //盘点概述表id

    @Column(name = "type_id", columnDefinition = "int(100) COMMENT '资产类型表id'")
    private Long typeId; //资产类型表id

    @Column(name = "type_name", columnDefinition = "varchar(100) COMMENT '资产类型'")
    private String typeName;//资产类型

    @Column(name = "type_standby", columnDefinition = "varchar(100) COMMENT '类型编号'")
    private String typeStandby;//类型编号

    @Column(name = "assets_amount", columnDefinition = "int(100) COMMENT '当前库存'")
    private Integer assetsAmount;//当前库存

    @Column(name = "result_amount", columnDefinition = "int(100) COMMENT '盘点数量'")
    private Integer resultAmount;//盘点数量

    @Column(name = "profit_loss", columnDefinition = "int(100) COMMENT '盈亏'")
    private Integer profitLoss;//盈亏

    @Column(name = "profit", columnDefinition = "int(100) COMMENT '盘盈'")
    private Integer profit;//盘盈

    @Column(name = "loss", columnDefinition = "int(100) COMMENT '盘亏'")
    private Integer loss;//盘亏

    @Column(name = "inventory_status", columnDefinition = "int(10) COMMENT '盘点状态'")
    private Integer inventoryStatus;//盘点状态

    @Column(name = "note", columnDefinition = "varchar(255) COMMENT '备注'")
    private String note;//备注

}
