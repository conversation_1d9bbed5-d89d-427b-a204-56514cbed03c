package com.yzm.property.basis.entity;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;

@Data
@Entity
@Table(name = "basis_personnel")
public class BasisPersonnel extends BasisEntity {
    //    private String personnelName;//人员名称
    private String personnelDepartment;//人员部门
    private Long personnelDepartmentId;//人员部门Id
    private Integer deleteFlag;
    private String personnelNumber;//人员工号
    private String personnelRemark;//人员备注
//    private  String personnelStandby;//人员备用字段

}
