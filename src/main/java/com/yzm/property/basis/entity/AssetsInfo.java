package com.yzm.property.basis.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yzm.framework.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:33
 */
@Entity
@Table(name = "assets_info")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AssetsInfo extends BaseEntity {
    @Column(name = "other_id", columnDefinition = "varchar(100) COMMENT '资产编码'")
    private String otherId; //资产编码
    @Column(name = "barcode", columnDefinition = "varchar(50) COMMENT '条形码'")
    private String barcode; //条形码
    @Column(name = "assets_status", columnDefinition = "int(2) COMMENT '资产状态'")
    private Integer assetsStatus; //资产状态
    @Column(name = "assets_type_name", columnDefinition = "varchar(255) COMMENT '材料类型'")
    private String assetsTypeName; //材料类型
    @Column(name = "assets_type", columnDefinition = "varchar(255) COMMENT '材料类型Id'")
    private Long assetsType; //材料类型
    @Column(name = "assets_name", columnDefinition = "varchar(100) COMMENT '资产名称'")
    private String assetsName; //资产名称
    @Column(name = "specifications", columnDefinition = "varchar(100) COMMENT '规格型号'")
    private String specifications; //规格型号
    @Column(name = "sequence", columnDefinition = "varchar(100) COMMENT '资产序号'")
    private String sequence; //资产序号
    @Column(name = "assets_source", columnDefinition = "varchar(20) COMMENT '入账方式'")
    private String assetsSource; //入账方式
    @Column(name = "supplier", columnDefinition = "varchar(50) COMMENT '供应商'")
    private String supplier; //供应商
    @Column(name = "brand_id", columnDefinition = "int(50) COMMENT '资产品牌'")
    private Long brandId; //资产品牌
    @Column(name = "brand", columnDefinition = "varchar(50) COMMENT '资产品牌'")
    private String brand; //资产品牌
    @Column(name = "unit", columnDefinition = "int(20) COMMENT '计量单位'")
    private Long unit; //计量单位
    @Column(name = "configuration_description", columnDefinition = "varchar(100) COMMENT '配置描述'")
    private String configurationDescription; //配置描述
    @Column(name = "company", columnDefinition = "varchar(100) COMMENT '所属公司'")
    private String company; //所属公司
    @Column(name = "department", columnDefinition = "int(20) COMMENT '所属部门'")
    private Integer department; //所属部门
    @Column(name = "assets_user", columnDefinition = "int(20) COMMENT '使用人'")
    private Integer assetsUser; //使用人
    @Column(name = "assets_area", columnDefinition = "int(20) COMMENT '所属区域'")
    private Integer assetsArea; //所属区域
    @Column(name = "attribution", columnDefinition = "varchar(100) COMMENT '资产归属'")
    private String attribution; //资产归属
    @Column(name = "assets_location", columnDefinition = "int(20) COMMENT '位置'")
    private Integer assetsLocation; //位置 int 50
    @Column(name = "durable_years", columnDefinition = "float(100,2) COMMENT '使用年限'")
    private Double durableYears; //使用年限
    @Column(name = "purchase_date", columnDefinition = "date COMMENT '采购日期'")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date purchaseDate; //采购日期
    @Column(name = "purchase_price", columnDefinition = "float(100,2) COMMENT '采购单价'")
    private Double purchasePrice; //采购单价
    @Column(name = "maintenance_status", columnDefinition = "varchar(20) COMMENT '维保状态'")
    private String maintenanceStatus; //维保状态
    @Column(name = "tuobao_date", columnDefinition = "date COMMENT '脱保日期'")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date tuobaoDate; //脱保日期
    @Column(name = "note", columnDefinition = "varchar(255) COMMENT '备注'")
    private String note; //备注

    @Column(name = "manufacturer", columnDefinition = "varchar(100) COMMENT '生产厂家'")
    private String manufacturer; //生产厂家
    @Column(name = "production_date", columnDefinition = "date COMMENT '生产日期'")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date productionDate; //生产日期
    @Column(name = "financial_code", columnDefinition = "varchar(100) COMMENT '财务编码'")
    private String financialCode; //财务编码
    @Column(name = "order_number", columnDefinition = "varchar(100) COMMENT '采购单号'")
    private String orderNumber; //采购单号
    @Column(name = "invoice_number", columnDefinition = "varchar(100) COMMENT '发票编号'")
    private String invoiceNumber; //发票编号
    @Column(name = "residuals_rate", columnDefinition = "float(50,2) COMMENT '净残值率'")
    private Double residualsRate; //净残值率
    @Column(name = "residuals_value", columnDefinition = "float(50,2) COMMENT '净残值额'")
    private Double residualsValue; //净残值额
    @Column(name = "estimated_life_of_depreciation", columnDefinition = "float(20,2) COMMENT '预计折旧年限'")
    private Double estimatedLifeOfDepreciation; //预计折旧年限
    @Column(name = "used_years", columnDefinition = "float(20,2) COMMENT '已用年限'")
    private Double usedYears; //已用年限
    @Column(name = "month_depreciation", columnDefinition = "float(50,2) COMMENT '月折旧额'")
    private Double monthDepreciation; //月折旧额
    @Column(name = "capital_source", columnDefinition = "varchar(100) COMMENT '资金来源'")
    private String capitalSource; //资金来源
    @Column(name = "project_number", columnDefinition = "varchar(100) COMMENT '项目编号'")
    private String projectNumber; //项目编号
    @Column(name = "use_to", columnDefinition = "varchar(100) COMMENT '使用方向'")
    private String useTo; //使用方向
    @Column(name = "file", columnDefinition = "varchar(255) COMMENT '文件'")
    private String file; //文件

}
