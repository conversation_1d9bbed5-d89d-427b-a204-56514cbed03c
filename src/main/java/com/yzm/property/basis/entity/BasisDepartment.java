package com.yzm.property.basis.entity;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.ArrayList;
import java.util.List;

@Data
@Entity
@Table(name = "basis_department")
public class BasisDepartment extends BasisEntity {

    public static final String FIELD_ORDER_NUM = "orderNum";

//    private  String departmentName;//部门名称
    /**
     * 父菜单ID，一级菜单为0
     */
    private Long parentId;
    /**
     * 父菜单名称
     */
    private String parentName;

    private String departmentAbbreviation;//部门缩写
    private String departmentRemark;//部门备注
//    private  String departmentStandby;//部门备用字段

    private String peopleNumber;//人员工号

    private Integer orderNum;//人员工号

    /**
     * 非数据库表
     */
    @Transient
    private List<BasisDepartment> children = new ArrayList<BasisDepartment>();

}
