package com.yzm.property.basis.entity;

import com.yzm.framework.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/31 16:49
 */

/**
 *
 */
@Entity
@Table(name = "assets_log")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AssetsLog extends BaseEntity {

    @Column

    private Long operationId;

    @Column
    private String operationOtherId;//操作编号

    @Column
    private String operationName;//操作方式

    @Column
    private Long assetsId;//资产编号

    @Column
    private String assetsOtherId;//资产编号

    @Column
    private String assetsName;//资产名称

}
