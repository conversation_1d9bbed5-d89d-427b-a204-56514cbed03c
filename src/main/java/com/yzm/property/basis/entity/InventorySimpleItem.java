package com.yzm.property.basis.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yzm.framework.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/9 10:00
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "inventory_simple_item")
public class InventorySimpleItem extends BaseEntity {

    @Column(name = "assets_number", columnDefinition = "varchar(100) COMMENT '材料编号'")
    private String assetsNumber; //材料编号
    @Column(name = "assets_name", columnDefinition = "varchar(100) COMMENT '材料名称'")
    private String assetsName; //材料名称
    @Column(name = "assets_specifications", columnDefinition = "varchar(100) COMMENT '规格型号'")
    private String assetsSpecifications;//规格型号
    @Column(name = "assets_unit", columnDefinition = "int(20) COMMENT '材料单位ID'")
    private Long assetsUnit; //材料单位ID
    @Column(name = "assets_unit_name", columnDefinition = "varchar(20) COMMENT '材料单位'")
    private String assetsUnitName; //材料单位
    @Column(name = "assets_code", columnDefinition = "varchar(255) COMMENT '材料条码'")
    private String assetsCode; //材料条码
    @Column(name = "assets_rfid", columnDefinition = "varchar(255) COMMENT '材料RFID'")
    private String assetsRfid; //材料RFID
    @Column(name = "assets_type_name", columnDefinition = "varchar(255) COMMENT '材料类型'")
    private String assetsTypeName; //材料类型
    @Column(name = "assets_type", columnDefinition = "varchar(255) COMMENT '材料类型Id'")
    private Long assetsType; //材料类型
    @Column(name = "assets_position", columnDefinition = "varchar(255) COMMENT '常用货位'")
    private String assetsPosition; //常用货位
    @Column(name = "now_repertory", columnDefinition = "int(20) COMMENT '当前库存'")
    private BigDecimal nowRepertory; //当前库存
    @Column(name = "warehouse_info", columnDefinition = "varchar(255) COMMENT '库存仓库'")
    private String warehouseInfo; //库存仓库
    @Column(name = "area_info", columnDefinition = "varchar(255) COMMENT '库区'")
    private String areaInfo; //库位
    @Column(name = "area_info_id", columnDefinition = "bigint(20) COMMENT '库位ID'")
    private Long areaInfoId; //库位id
    @Column(name = "location_info", columnDefinition = "varchar(255) COMMENT '库位'")
    private String locationInfo; //库位
    @Column(name = "operation_number", columnDefinition = "varchar(255) COMMENT '操作单号'")
    private String operationNumber; //操作单号

    private String note; //备注

    @Column(name = "result_amount", columnDefinition = "int(100) COMMENT '盘点数量'")
    private BigDecimal resultAmount;//盘点数量


    @Column(name = "profit_loss", columnDefinition = "int(100) COMMENT '盈亏'")
    private BigDecimal profitLoss;//盈亏

    @Column(name = "profit", columnDefinition = "int(100) COMMENT '盘盈'")
    private BigDecimal profit;//盘盈

    @Column(name = "loss", columnDefinition = "int(100) COMMENT '盘亏'")
    private BigDecimal loss;//盘亏

    @Column(name = "inventory_status", columnDefinition = "int(10) COMMENT '盘点状态'")
    private Long inventoryStatus;//盘点状态


    @Column(name = "inventory_number", columnDefinition = "varchar(100) COMMENT '盘点编码'")
    private String inventoryNumber; //盘点编码

    private BigDecimal profitSum;//盘盈

    private BigDecimal lossSum;//盘亏

    private BigDecimal nowRepertorySum; //库存总数

    @Transient
    private BigDecimal resultAmountSum;//盘点数量总数

    @Transient
    private BigDecimal nowRepertoryNo;//盘点数量总数



    @ExcelProperty(value = "贸易方式", index = 6)
    private Long modeOfTrade;//贸易方式

    @JsonFormat(pattern = "yyyyMMdd", timezone = "GMT+8")
    private Date dateArrival;//来料日期

    private String departmentCode;//部门代码
    @ExcelProperty(value = "材料批号", index = 8)
    private String batchNumber; //材料批号


    public InventorySimpleItem(String assetsNumber, String assetsName, String assetsSpecifications, Long assetsUnit, String assetsUnitName, String assetsCode, String assetsRfid, String assetsTypeName, Long assetsType, String assetsPosition, BigDecimal nowRepertory, String warehouseInfo, String areaInfo, Long areaInfoId, String locationInfo, String operationNumber, String note, BigDecimal resultAmount, BigDecimal profitLoss, BigDecimal profit, BigDecimal loss, Long inventoryStatus, String inventoryNumber, BigDecimal nowRepertorySum) {
        this.assetsNumber = assetsNumber;
        this.assetsName = assetsName;
        this.assetsSpecifications = assetsSpecifications;
        this.assetsUnit = assetsUnit;
        this.assetsUnitName = assetsUnitName;
        this.assetsCode = assetsCode;
        this.assetsRfid = assetsRfid;
        this.assetsTypeName = assetsTypeName;
        this.assetsType = assetsType;
        this.assetsPosition = assetsPosition;
        this.nowRepertory = nowRepertory;
        this.warehouseInfo = warehouseInfo;
        this.areaInfo = areaInfo;
        this.areaInfoId = areaInfoId;
        this.locationInfo = locationInfo;
        this.operationNumber = operationNumber;
        this.note = note;
        this.resultAmount = resultAmount;
        this.profitLoss = profitLoss;
        this.profit = profit;
        this.loss = loss;
        this.inventoryStatus = inventoryStatus;
        this.inventoryNumber = inventoryNumber;
        this.nowRepertorySum = nowRepertorySum;
    }
}
