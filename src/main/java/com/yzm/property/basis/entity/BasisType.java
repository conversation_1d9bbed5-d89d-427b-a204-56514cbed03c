package com.yzm.property.basis.entity;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.ArrayList;
import java.util.List;

@Data
@Entity
@Table(name = "basis_type")
public class BasisType extends BasisEntity {
//    private String typeName;//类别名称
    private  String typeRemark;//类别备注
    private  String typeNumber;//类别编号
//    private  String typeStandby;//类别备用字段
    /**
     * 父菜单ID，一级菜单为0
     */
    private Long parentId;

    private String parentName;


    /**
     * 非数据库表
     */
    @Transient
    private List<BasisDepartment> children = new ArrayList<BasisDepartment>();
}
