package com.yzm.property.basis.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yzm.framework.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/9 10:00
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "inventory_simple")
public class InventorySimple extends BaseEntity {

    @Column(name = "inventory_number", columnDefinition = "varchar(100) COMMENT '盘点编码'")
    private String inventoryNumber; //盘点编码

    @Column(name = "inventory_name", columnDefinition = "varchar(100) COMMENT '任务名称'")
    private String inventoryName; //任务名称

    @Column(name = "inventory_type_id", columnDefinition = "int(100) COMMENT '盘点类型的id'")
    private Long inventoryTypeId; //盘点类型的id

    @Column(name = "inventory_table", columnDefinition = "varchar(100) COMMENT '盘点依据'")
    private String inventoryTable; //盘点依据

    @Column(name = "inventory_type", columnDefinition = "varchar(100) COMMENT '盘点类型'")
    private String inventoryType; //盘点类型

    @Column(name = "start_date", columnDefinition = "date COMMENT '盘点范围开始时间'")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startDate; //盘点范围

    @Column(name = "end_date", columnDefinition = "date COMMENT '盘点范围截止时间'")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endDate; //盘点范围

    @Column(name = "inventory_status", columnDefinition = "int(10) COMMENT '盘点状态'")
    private Integer inventoryStatus; //盘点状态


    @Column(name = "status", columnDefinition = "int(10) COMMENT '状态'")
    private Integer status; //盘点状态
    private String auditor;//审核人
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date auditorDate;//审核时间

    @Column(name = "note", columnDefinition = "char(255) COMMENT '备注'")
    private String note; //备注


    @Transient
    private String itemJson;
}
