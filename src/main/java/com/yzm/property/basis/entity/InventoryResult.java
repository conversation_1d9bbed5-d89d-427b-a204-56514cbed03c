package com.yzm.property.basis.entity;

import com.yzm.framework.base.BaseEntity;
import lombok.Data;

import java.text.SimpleDateFormat;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/17 9:36
 */
@Data
public class InventoryResult extends BaseEntity {

    private Long assetsId;
    private Integer assetsStatus; //资产状态
    private String otherId; //资产编码
    private String assetsName; //资产名称
    private String specifications; //规格型号
    private String assetsSource; //来源
    private String purchaseDate; //采购日期
    private Double purchasePrice; //采购单价
    private String maintenanceStatus; //维保状态
    private Long inventoryAssetsInfoId;
    private Long inventoryInfoId;
    private Integer inventoryStatus; //盘点状态

    public InventoryResult(AssetsInfo assetsInfo, InventoryAssetsInfo inventoryAssetsInfo) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        this.assetsId = assetsInfo.getId();
        this.assetsStatus = assetsInfo.getAssetsStatus();
        this.otherId = assetsInfo.getOtherId();
        this.assetsName = assetsInfo.getAssetsName();
        this.specifications = assetsInfo.getSpecifications();
        this.assetsSource = assetsInfo.getAssetsSource();
        this.purchaseDate = format.format(assetsInfo.getPurchaseDate());
        this.purchasePrice = assetsInfo.getPurchasePrice();
        this.maintenanceStatus = assetsInfo.getMaintenanceStatus();
        this.inventoryAssetsInfoId = inventoryAssetsInfo.getId();
        this.inventoryInfoId = inventoryAssetsInfo.getInfoId();
        this.inventoryStatus = inventoryAssetsInfo.getInventoryStatus();
    }
}
