package com.yzm.property.basis.controller;

import com.yzm.common.annotation.BussinessLog;
import com.yzm.common.enums.BusinessType;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.basis.criteria.DepartmentCriteria;
import com.yzm.property.basis.entity.BasisDepartment;
import com.yzm.property.basis.service.DepartmentService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 部门
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 11:33
 */
@Controller
@RequestMapping(value = "basis/department")
public class DepartmentController extends BaseController {
    private String urlPrefix = "basis/department";

    @Autowired
    private DepartmentService departmentService;

    @RequiresPermissions("basis:department:view")
    @GetMapping
    public String client(ModelMap mmap) {
        return urlPrefix + "/department";
    }

    @ResponseBody
    @PostMapping(value = "/list")
    public ResponseData page(PageBean pageBean, DepartmentCriteria area) {
        Map<String, Object> datas = departmentService.findAllByPage(area, pageBean.getPagable());
        return success(datas);
    }

    /**
     * 新增参数配置
     */
    @GetMapping("/add")
    public String add() {
        return urlPrefix + "/add";
    }

    /**
     * 保存配置
     */
    @RequestMapping("/add")
    @RequiresPermissions("basis:department:add")
    @ResponseBody
	@BussinessLog(businessType=BusinessType.INSERT,title="新增区域信息")
    public ResponseData save(BasisDepartment basisDepartment) {
       // BasisDepartment departmentData = departmentService.findByDepartmentName(basisDepartment.getDepartmentName());
        //获取部门缩写
        //String departmentAbbreviation = basisDepartment.getDepartmentAbbreviation();
        //部门缩写+随机生成6位数
        //String result = departmentAbbreviation +  (int)(Math.random()*900)+100;
        //basisDepartment.setPeopleNumber(result);
        return StringUtils.isNotEmpty(departmentService.save(basisDepartment)) ? success() : error("新增失败!");
    }

    /**
     * 修改参数配置
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        mmap.put("department", departmentService.getById(id));
        return urlPrefix + "/edit";
    }

    /**
     * 修改配置
     */
    @RequestMapping(value = "/edit", method = RequestMethod.POST)
    @RequiresPermissions("basis:department:edit")
    @ResponseBody
	@BussinessLog(businessType=BusinessType.UPDATE,title="修改部门信息")
    public ResponseData update(BasisDepartment config) {
        return StringUtils.isNotEmpty(departmentService.update(config)) ? success() : error("修改失败!");
    }


    /**
     * 删除配置
     */
    @RequestMapping(value = "/del", method = RequestMethod.POST)
    @RequiresPermissions("basis:department:del")
    @ResponseBody
	@BussinessLog(businessType=BusinessType.DELETE,title="删除部门信息")
    public ResponseData delete(Long[] ids) {
        return departmentService.deleteBatchByIds(ids) ? success() : error("删除失败!");
    }
}
