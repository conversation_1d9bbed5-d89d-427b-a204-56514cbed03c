package com.yzm.property.basis.controller;

import cn.hutool.core.io.FileUtil;
import com.alibaba.excel.EasyExcel;
import com.yzm.common.annotation.BussinessLog;
import com.yzm.common.annotation.RepeatSubmit;
import com.yzm.common.enums.BusinessType;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.framework.freemark.DictUtil;
import com.yzm.framework.freemark.Global;
import com.yzm.property.basis.criteria.InventoryCriteria;
import com.yzm.property.basis.entity.*;
import com.yzm.property.basis.repository.InventorySimpleItemRepository;
import com.yzm.property.basis.service.*;
import com.yzm.property.basis.utils.BeanMapUtils;
import com.yzm.property.business.entity.HandleStatus;
import com.yzm.property.materials.entity.MaterialsRepertory;
import com.yzm.property.materials.pojo.InventorySimpleExcel;
import com.yzm.property.materials.service.MaterialsRepertoryService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.io.File;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/9 9:57
 */
@Controller
@RequestMapping("/inventory")
public class InventoryController extends BaseController {

    private String urlPrefix = "basis/inventory";

    @Autowired
    private InventorySimpleService inventorySimpleService;
    @Autowired
    private InventorySimpleItemRepository inventorySimpleItemRepository;

    @Autowired
    private InventorySimpleItemService inventorySimpleItemService;

    @Autowired
    private InventoryInfoService inventoryInfoService;

    @Autowired
    private TypeService typeService;

    @Autowired
    private AreaService areaService;

    @Autowired
    private WarehouseService warehouseService;

    @Autowired
    private DepartmentService departmentService;

    @Autowired
    private PersonnelService personnelService;

    @Autowired
    private MaterialsRepertoryService materialsRepertoryService;
    @Autowired
    private DictUtil dictUtil;
    private static List<AssetsAttribution> scopeAttrs = new ArrayList<>();
    private static List<AssetsAttribution> tableAttrs = new ArrayList<>();
    private List<HandleStatus> typeAttrs;

    static {
        scopeAttrs.add(new AssetsAttribution("月度盘点"));
        scopeAttrs.add(new AssetsAttribution("季度盘点"));
        scopeAttrs.add(new AssetsAttribution("年度盘点"));

        tableAttrs.add(new AssetsAttribution("区域"));
        tableAttrs.add(new AssetsAttribution("类型"));
        tableAttrs.add(new AssetsAttribution("部门"));
        tableAttrs.add(new AssetsAttribution("人员"));
    }

    private List<HandleStatus> getArea() {
        typeAttrs = new ArrayList<>();
        typeAttrs.add(new HandleStatus(0, "全部"));
        List<BasisArea> types = areaService.findAll();
        for (BasisArea type : types) {
            typeAttrs.add(new HandleStatus(type.getId().intValue(), type.getBasisName()));
        }
        return typeAttrs;
    }

    private List<HandleStatus> getDepartment() {
        typeAttrs = new ArrayList<>();
        typeAttrs.add(new HandleStatus(0, "全部"));
        List<BasisDepartment> types = departmentService.findAll();
        for (BasisDepartment type : types) {
            typeAttrs.add(new HandleStatus(type.getId().intValue(), type.getBasisName()));
        }
        return typeAttrs;
    }

    private List<HandleStatus> getPerson() {
        typeAttrs = new ArrayList<>();
        typeAttrs.add(new HandleStatus(0, "全部"));
        List<BasisPersonnel> types = personnelService.findAll();
        for (BasisPersonnel type : types) {
            typeAttrs.add(new HandleStatus(type.getId().intValue(), type.getBasisName()));
        }
        return typeAttrs;
    }

    private List<HandleStatus> getType() {
        typeAttrs = new ArrayList<>();
        typeAttrs.add(new HandleStatus(0, "全部"));
        List<BasisType> types = typeService.findAll();
        for (BasisType type : types) {
            typeAttrs.add(new HandleStatus(type.getId().intValue(), type.getBasisName()));
        }
        return typeAttrs;
    }

    /**
     * 跳转至list页面
     *
     * @param mmap
     * @return
     */
    @RequestMapping
    public String inventory(ModelMap mmap) {
        return urlPrefix + "/list";
    }

    /**
     * 根据关键字和分页查询盘点
     *
     * @param inventoryCriteria
     * @param pageBean
     * @return
     */
    @PostMapping("/list")
    @ResponseBody
    public ResponseData showInventoryByKeywordAndPage(
            InventoryCriteria inventoryCriteria,
            PageBean pageBean
    ) {
        return success(inventorySimpleService.findAllByPage(inventoryCriteria, pageBean.getPagable()));
    }

    /**
     * 跳转至新增页面
     *
     * @param mmap
     * @return
     */
    @GetMapping("/add")
    public String addInventory(ModelMap mmap) {
        List<MaterialsRepertory> groupByDepartmentCode = materialsRepertoryService.findGroupByDepartmentCode();
        Iterator<MaterialsRepertory> iterator = groupByDepartmentCode.iterator();
        while (iterator.hasNext()) {
            MaterialsRepertory materialsRepertory = iterator.next();
            // 如果 departmentCode 为空，移除该元素
            if (materialsRepertory == null || StringUtils.isEmpty(materialsRepertory.getDepartmentCode())) {
                iterator.remove(); // 使用 iterator 的 remove() 方法删除
            }
        }
        mmap.put("warehouseList", groupByDepartmentCode);
        mmap.put("scopeAttrs", scopeAttrs);
        mmap.put("tableAttrs", tableAttrs);
        typeAttrs = new ArrayList<>();
        mmap.put("typeAttrs", typeAttrs);
        return urlPrefix + "/add";
    }

    @GetMapping("/get")
    @ResponseBody
    public List getSimpleType(@RequestParam("table") String table) {
        switch (table) {
            case "区域":
                return getArea();
            case "类型":
                return getType();
            case "部门":
                return getDepartment();
            case "人员":
                return getPerson();
        }
        return null;
    }

    /**
     * 新增盘点
     *
     * @param inventorySimple
     * @return
     */
    @PostMapping("/addWarehouse")
    @ResponseBody
    public ResponseData addWarehouse(InventorySimple inventorySimple) {
        return inventorySimpleService.addWarehouse(inventorySimple) ? success() : error("新增盘点出错");
    }

    /**
     * 调整贸易方式
     *
     * @param json
     * @return
     */
    @PostMapping("/updateModeOfTrade")
    @ResponseBody
    public ResponseData updateModeOfTrade(String json) {
        return inventorySimpleService.updateModeOfTrade(json);
    }

    /**
     * 新增盘点
     *
     * @param inventorySimple
     * @return
     */
    @PostMapping("/add")
    @ResponseBody
    public ResponseData addInventory(InventorySimple inventorySimple) {
        return inventorySimpleService.saveSimpleAndInfo(inventorySimple) ? success() : error("新增盘点出错");
    }

    /**
     * 跳转详细
     */
    @GetMapping("/show/{id}")
    public String showInfo(@PathVariable("id") Long id, ModelMap mmap) {
        InventorySimple byId = inventorySimpleService.getById(id);
        mmap.put("id", id);
        mmap.put("inventoryNumber", byId.getInventoryNumber());
        return urlPrefix + "/show";
    }

    /**
     * 完成盘点
     */
    @RepeatSubmit
    @BussinessLog(businessType = BusinessType.RUN, title = "完成盘点操作")
    @RequestMapping(value = "/scanOk", method = RequestMethod.POST)
    @ResponseBody
    public ResponseData scanOk(Long id) {
        InventorySimpleItem one = inventorySimpleItemRepository.getOne(id);
        one.setInventoryStatus(2L);

        List<InventorySimpleItem> list = inventorySimpleItemRepository.findByInventoryStatusAndInventoryNumber(0L, one.getInventoryNumber());
        InventorySimple simple = inventorySimpleService.findByInventoryNumber(one.getInventoryNumber());
        if (list.size() == 0) {
            simple.setInventoryStatus(2);
        } else {
            simple.setInventoryStatus(1);
        }
        one.setResultAmount(one.getNowRepertory());
        one.setUpdateTime(new Date());
        InventorySimpleItem inventorySimpleItem = inventorySimpleItemRepository.saveAndFlush(one);

//        BigDecimal nowRepertory = one.getNowRepertory();//库存
//        BigDecimal resultAmount = one.getResultAmount();//已盘数量
//
//        BigDecimal resultAmountSum = inventorySimpleItemRepository.findBySumResultAmount(one.getInventoryNumber(), 2L, one.getAssetsNumber());
//        BigDecimal nowRepertorySum = one.getNowRepertorySum();//库存
//        BigDecimal add = resultAmountSum;
//        int i2 = nowRepertorySum.compareTo(add);
//        if (i2 == -1) {
//            one.setProfitSum(add.subtract(nowRepertorySum));
//            one.setLossSum(new BigDecimal(0));
//        } else if (i2 == 1) {
//            one.setProfitSum(new BigDecimal(0));
//            one.setLossSum(add.subtract(nowRepertorySum));
//        } else {
//            one.setProfitSum(new BigDecimal(0));
//            one.setLossSum(new BigDecimal(0));
//        }
//
//
//        int i = nowRepertory.compareTo(resultAmount);
//        if (i == -1) {
//            one.setProfit(resultAmount.subtract(nowRepertory));
//            one.setLoss(new BigDecimal(0));
//        } else if (i == 1) {
//            one.setProfit(new BigDecimal(0));
//            one.setLoss(resultAmount.subtract(nowRepertory));
//        } else {
//            one.setProfit(new BigDecimal(0));
//            one.setLoss(new BigDecimal(0));
//        }
//        inventorySimpleItemRepository.saveAndFlush(one);
//
//
////        inventorySimpleItemRepository.updateByInventoryNumberAndInventoryStatusAndAssetsNumber(one.getLossSum(), one.getProfitSum(), one.getInventoryNumber(), 2L, one.getAssetsNumber());
//
//
//        List<InventorySimpleItem> byInventoryNumberAndInventoryStatusAndAssetsNumber = inventorySimpleItemRepository.findByInventoryNumberAndInventoryStatusAndAssetsNumber(one.getInventoryNumber(), 2L, one.getAssetsNumber());
//        for (InventorySimpleItem simpleItem : byInventoryNumberAndInventoryStatusAndAssetsNumber) {
//            simpleItem.setLossSum(one.getLossSum());
//            simpleItem.setProfitSum(one.getProfitSum());
//            inventorySimpleItemRepository.saveAndFlush(simpleItem);
//        }
//
//
//        inventorySimpleService.update(simple);
//        if(StringUtils.isNotEmpty(inventorySimpleItem.getAssetsCode())){
//            MaterialsRepertory byAssetsCodeOrAssetsRfid = materialsRepertoryService.findByAssetsCodeOrAssetsRfid(inventorySimpleItem.getAssetsCode(), inventorySimpleItem.getAssetsCode());
//            materialsRepertoryService.delete(byAssetsCodeOrAssetsRfid);
//        }
        return ResponseData.success(inventorySimpleItem);
    }

    /**
     * 返回盘点详细信息
     */
    @RequestMapping("/showInfos/{id}")
    @ResponseBody
    public ResponseData showInfos(@PathVariable("id") Long id, ModelMap mmap, PageBean pageBean) {
        return success(inventoryInfoService.findAllByInventSimpleId(id, pageBean));
    }

    @ResponseBody
    @PostMapping(value = "/listOk")
    public ResponseData pageOk(PageBean pageBean, InventoryCriteria area) {

        Map<String, Object> datas = inventorySimpleItemService.findAllByPage(area, pageBean.getPagable());
        return success(datas);
    }

    @ResponseBody
    @PostMapping(value = "/listNoPage")
    public ResponseData listNoPage(PageBean pageBean, InventoryCriteria area) {
        area.setInventoryStatus(0L);
        Map<String, Object> datas = inventorySimpleItemService.findAllByPage(area, pageBean.getPagable());
        return success(datas);
    }

    @ResponseBody
    @PostMapping(value = "/listOkPage")
    public ResponseData listOkPage(PageBean pageBean, InventoryCriteria area) {
        area.setInventoryStatus(2L);
        Map<String, Object> datas = inventorySimpleItemService.findAllByPage(area, pageBean.getPagable());
        return success(datas);
    }

    @ResponseBody
    @PostMapping(value = "/listNo")
    public ResponseData pageNo(PageBean pageBean, InventoryCriteria area) {
        pageBean.setSidx(BeanMapUtils.humpToLine(pageBean.getSidx()));
        Map<String, Object> datas = inventorySimpleItemService.findAllByPageGroup(area, pageBean.getPagable());
        return success(datas);
    }

    @ResponseBody
    @PostMapping(value = "/listOK")
    public ResponseData listOK(PageBean pageBean, InventoryCriteria area) {
        Map<String, Object> datas = inventorySimpleItemService.findAllByPageGroup(area, pageBean.getPagable());
        return success(datas);
    }

    /**
     * 返回盘点详细信息
     */
    @RequestMapping("/showInfosW/{id}")
    @ResponseBody
    public ResponseData showInfosW(@PathVariable("id") Long id) {
        return inventoryInfoService.showInfosData(id);
    }

    /**
     * 查找info
     */
    @RequestMapping("/assetsByTable/{id}")
    @ResponseBody
    public ResponseData assetsByTable(@PathVariable("id") Long id, PageBean pageBean) {
        return success(inventorySimpleService.findAssetsByTableAndType(id, pageBean));
    }

    /**
     * 跳转至盘点页面
     */
    @RequestMapping("/toInventoryAssets/{id}")
    public String toInventory(@PathVariable("id") Long id, ModelMap mmap) {
        mmap.put("id", id);
        return urlPrefix + "/inventoryAmount";
    }

    /**
     * 跳转至当前盘点类目下的资产列表页面
     */
    @RequestMapping("/showAssets/{id}")
    public ModelAndView showAssets(@PathVariable("id") Long id, ModelMap mmap) {
        return new ModelAndView(urlPrefix + "/assets").addObject("id", id);
    }

    /**
     * 盘点
     */
    @PostMapping("/inventoryAssets")
    @ResponseBody
    public ResponseData inventory(
            @RequestParam("id") Long id,
            @RequestParam("resultAmount") Integer resultAmount,
            @RequestParam("note") String note
    ) {
        return inventoryInfoService.inventoryAssets(id, resultAmount, note) != null ? success() : error("盘点失败");
    }

    /**
     * 盘点2
     */
    @PostMapping("/inventoryAssets2")
    @ResponseBody
    public ResponseData inventory2(
            @RequestParam("id") Long id,
            @RequestParam("resultAmount") BigDecimal resultAmount,
            @RequestParam("note") String note
    ) {
        return inventorySimpleService.inventoryAssets2(id, resultAmount, note) != null ? success() : error("盘点失败");
    }

    /**
     *
     */
    @RequestMapping("/checkStatus")
    @ResponseBody
    public ResponseData checkStatus(
            @RequestParam("id") String id,
            @RequestParam("status") Integer status
    ) {
//        return null;
        return inventorySimpleService.checkStatus(Long.parseLong(id), status) ? success() : error("盘点失败");
    }

    /**
     * 盘点数据确认审核
     */
    @RequestMapping("/verify")
    @ResponseBody
    public ResponseData verify(@RequestParam("id") Long id) {
//        return null;
        return inventorySimpleService.verify(id) ? success() : error("盘点失败");
    }

    /**
     * 盘点数据确认审核
     */
    @RequestMapping("/over")
    @ResponseBody
    public ResponseData over(@RequestParam("id") Long id) {
//        return null;
        return inventorySimpleService.over(id) ? success() : error("盘点失败");
    }


    /**
     * 盘点
     *
     * @return
     */
    @PostMapping("/takeStock")
    @ResponseBody
    public ResponseData takeStock(InventorySimple inventorySimple) {

        return inventorySimpleService.takeStockNew(inventorySimple);
    }
    /**
     * 提交审核
     */
    @RepeatSubmit
    @BussinessLog(title = "提交审核盘点", businessType = BusinessType.RUN)
    @RequestMapping(value = "/submit", method = RequestMethod.POST)
    @ResponseBody
    public ResponseData submit(Long[] ids) {
        return inventorySimpleService.submitByIds(ids) ? success() : error("提交失败!");
    }

    /**
     * 送审、同意、拒绝、取消(借用)
     */
    @RequestMapping("/audit")
    @ResponseBody
    @BussinessLog(businessType = BusinessType.UPDATE, title = "盘点审核")
    public ResponseData audit(Long id,Integer status) {
        inventorySimpleService.audit(id, status);
        return ResponseData.success();
    }

    /**
     * 修改跳转页面
     */
    @GetMapping("/showInfo/{id}")
    public String handleData(@PathVariable("id") Long id, ModelMap mmap) {
        InventorySimpleItem one = inventorySimpleItemRepository.getOne(id);
        mmap.put("assetsNumber", one.getAssetsNumber());
        mmap.put("inventoryNumber", one.getInventoryNumber());
        return urlPrefix + "/showInfo";
    }

    /**
     * 修改跳转页面
     */
    @GetMapping("/showInfoOk/{id}")
    public String showInfoOk(@PathVariable("id") Long id, ModelMap mmap) {
        InventorySimpleItem one = inventorySimpleItemRepository.getOne(id);
        mmap.put("assetsNumber", one.getAssetsNumber());
        mmap.put("inventoryNumber", one.getInventoryNumber());
        return urlPrefix + "/showInfoOk";
    }

    /**
     * 修改库存
     *
     * @return
     */
    @PostMapping("/updateConut")
    @ResponseBody
    public ResponseData updateConut(String data) {

        return inventorySimpleService.updateConut(data);
    }

    /**
     * 修改库存
     *
     * @return
     */
    @PostMapping("/delInfo")
    @ResponseBody
    public ResponseData delInfo(Long id) {

        return inventorySimpleService.delInfo(id);
    }

    @BussinessLog(title = "盘点统计", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public ResponseData export(Long ids) throws Exception {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String fileName = "盘点信息";
        fileName = encodingExcelFilename(fileName);
        String folder = Global.getTempPath() + File.separator + "pio" + File.separator;
        FileUtil.touch(folder + fileName);
        InventorySimple listData = inventorySimpleService.getById(ids);
        List<InventorySimpleExcel> listExcels = new ArrayList<>();
        Map<String, InventorySimpleExcel> map = new HashMap<>();
        List<InventorySimpleItem> byInventoryNumber = inventorySimpleItemRepository.findByInventoryNumber(listData.getInventoryNumber());
        for (InventorySimpleItem inventorySimpleItem : byInventoryNumber) {
            InventorySimpleExcel inventorySimpleExcel = new InventorySimpleExcel();
            BeanUtils.copyProperties(inventorySimpleItem, inventorySimpleExcel);
            inventorySimpleExcel.setDepartmentCode(inventorySimpleItem.getDepartmentCode());
            if (inventorySimpleItem.getInventoryStatus() == 2L) {
                inventorySimpleExcel.setInventoryStatus("已盘");
            } else {
                inventorySimpleExcel.setInventoryStatus("未盘");
            }
            if (StringUtils.isNotEmpty(inventorySimpleItem.getModeOfTrade())) {
//                inventorySimpleExcel.setModeOfTrade(dictUtil.getLabel("mode_of_trade_type", inventorySimpleItem.getModeOfTrade().toString()));
            }

            String inventoryNumber = inventorySimpleExcel.getAssetsNumber();
            if (map.containsKey(inventoryNumber)) {
                // 如果存在，可以选择根据某些条件进行更新。这里直接覆盖：
                // map.put(inventoryNumber, inventorySimpleExcel);

                // 如果需要更复杂的合并逻辑，例如汇总已盘数量等，可以在此添加判断
                InventorySimpleExcel existingExcel = map.get(inventoryNumber);
                // 根据需求，合并数据，例如：
                // existingExcel.setProfit(existingExcel.getProfit().add(inventorySimpleExcel.getProfit()));
                // map.put(inventoryNumber, existingExcel);
                existingExcel.setResultAmountSum(existingExcel.getResultAmountSum().add(inventorySimpleItem.getResultAmount()));
                existingExcel.setNowRepertorySum(existingExcel.getNowRepertorySum().add(inventorySimpleItem.getNowRepertory()));
//                BigDecimal nowRepertory = existingExcel.getNowRepertorySum();//库存
//                BigDecimal resultAmount = existingExcel.getResultAmountSum();//已盘数量
//                int i = nowRepertory.compareTo(resultAmount);
//                if (i == -1) {
//                    inventorySimpleExcel.setProfitSum(resultAmount.subtract(nowRepertory));
//                } else if (i == 1) {
//                    inventorySimpleExcel.setLossSum(resultAmount.subtract(nowRepertory));
//                }
                map.put(inventoryNumber, existingExcel);
            } else {
                // 如果不存在，则直接插入
                inventorySimpleExcel.setResultAmountSum(inventorySimpleItem.getResultAmount());
                inventorySimpleExcel.setNowRepertorySum(inventorySimpleItem.getNowRepertory());
                map.put(inventoryNumber, inventorySimpleExcel);
            }
//            listExcels.add(inventorySimpleExcel);
        }

        for (Map.Entry<String, InventorySimpleExcel> stringInventorySimpleExcelEntry : map.entrySet()) {
            InventorySimpleExcel value = stringInventorySimpleExcelEntry.getValue();
            BigDecimal nowRepertory = value.getNowRepertorySum();//库存
            BigDecimal resultAmount = value.getResultAmountSum();//已盘数量
            int i = nowRepertory.compareTo(resultAmount);
            if (i == -1) {
                value.setProfitSum(resultAmount.subtract(nowRepertory));
                value.setLossSum(new BigDecimal(0));
            } else if (i == 1) {
                value.setLossSum(resultAmount.subtract(nowRepertory));
                value.setProfitSum(new BigDecimal(0));
            }else{
                value.setProfitSum(new BigDecimal(0));
                value.setLossSum(new BigDecimal(0));
            }
            if (value.getResultAmountSum().compareTo(new BigDecimal(0)) == 1) {
                value.setInventoryStatus("已盘");
            }

            if (StringUtils.isEmpty(value.getLossSum())) {
                value.setLossSum(new BigDecimal(0));
            }
            if (StringUtils.isEmpty(value.getProfitSum())) {
                value.setProfitSum(new BigDecimal(0));
            }
            listExcels.add(value);
        }
//        for (MaterialsListEntity materialsListEntity : listData) {
//            MaterialsListExcel materialsListExcel = new MaterialsListExcel();
//            materialsListExcel.setOperationNameType(dictUtil.getLabel("materials_list_type", materialsListEntity.getReason()));
//            if(StringUtils.isNotEmpty( materialsListEntity.getModeOfTrade())){
//                materialsListExcel.setModeOfTrade(dictUtil.getLabel("mode_of_trade_type", materialsListEntity.getModeOfTrade().toString()));
//            }
//            materialsListExcel.setInvoiceNumber(materialsListEntity.getInvoiceNumber());
//            materialsListExcel.setDepartmentCode(materialsListEntity.getDepartmentCode());
//            if(StringUtils.isNotEmpty( materialsListEntity.getDateArrival())){
//                materialsListExcel.setDateArrival(simpleDateFormat.format(materialsListEntity.getDateArrival()));
//            }
//
//
//
//            materialsListExcel.setOperationOtherId(materialsListEntity.getOperationOtherId());
//            materialsListExcel.setOperationName(materialsListEntity.getOperationName());
//            materialsListExcel.setWarehouseInfo(materialsListEntity.getWarehouseInfo());
//            materialsListExcel.setAreaInfo(materialsListEntity.getAreaInfo());
//            materialsListExcel.setAssetsUnitName(materialsListEntity.getAssetsUnitName());
//            materialsListExcel.setConsumableId(materialsListEntity.getAssetsNumber());
//            materialsListExcel.setConsumableName(materialsListEntity.getAssetsName());
//            materialsListExcel.setUserName(materialsListEntity.getUserName());
//            materialsListExcel.setNowRepertory(materialsListEntity.getNowRepertory());
//            materialsListExcel.setCreateTime(simpleDateFormat.format(materialsListEntity.getCreateTime()));
//            listExcels.add(materialsListExcel);
//        }

        EasyExcel.write(folder + fileName, InventorySimpleExcel.class).sheet("盘点明细").doWrite(listExcels);
        return success(fileName);
    }


    String encodingExcelFilename(String filename) {
        filename = UUID.randomUUID().toString() + "_" + filename + ".xlsx";
        return filename;
    }
}
