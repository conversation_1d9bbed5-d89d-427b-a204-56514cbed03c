package com.yzm.property.basis.controller;

import cn.hutool.core.util.StrUtil;
import com.yzm.common.annotation.BussinessLog;
import com.yzm.common.enums.BusinessType;
import com.yzm.common.utils.ShiroUtils;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.ResponseData;
import com.yzm.framework.bean.Ztree;
import com.yzm.framework.exception.RxcException;
import com.yzm.property.basis.entity.BasisType;
import com.yzm.property.basis.service.TypeService;
import com.yzm.property.system.entity.SysRole;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 类别
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 11:33
 */
@Controller
@RequestMapping(value = "basis/type")
public class TypeController extends BaseController {
    private String urlPrefix = "basis/type";

    @Autowired
    private TypeService typeService;

    @RequiresPermissions("basis:type:view")
    @GetMapping
    public String client(ModelMap mmap) {
        return urlPrefix + "/menu";
    }

//    @ResponseBody
//    @PostMapping(value = "/list")
//    public ResponseData page(PageBean pageBean, TypeCriteria type) {
//        Map<String, Object> datas = typeService.findAllByPage(type, pageBean.getPagable());
//        return success(datas);
//    }
//
//    /**
//     * 新增参数配置
//     */
//    @GetMapping("/add")
//    public String add() {
//        return urlPrefix + "/add";
//    }

//    /**
//     * 保存配置
//     */
//    @RequestMapping("/add")
//    @RequiresPermissions("basis:type:add")
//    @ResponseBody
//	@BussinessLog(businessType=BusinessType.INSERT,title="新增类别信息")
//    public ResponseData save(BasisType config) {
//        BasisType basisType = null;
//        try {
//            basisType = typeService.save(config);
//        } catch (Exception e) {
//            throw new RuntimeException("类别名称已存在，请重新输入");
//        }
//
//        return StringUtils.isNotEmpty(basisType) ? success() : error("新增失败!");
//    }
//
//    /**
//     * 修改参数配置
//     */
//    @GetMapping("/edit/{id}")
//    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
//        mmap.put("type", typeService.getById(id));
//        return urlPrefix + "/edit";
//    }

//    /**
//     * 修改配置
//     */
//    @RequestMapping(value = "/edit", method = RequestMethod.POST)
//    @RequiresPermissions("basis:type:edit")
//    @ResponseBody
//	@BussinessLog(businessType=BusinessType.UPDATE,title="修改类别信息")
//    public ResponseData update(BasisType config) {
//        return StringUtils.isNotEmpty(typeService.update(config)) ? success() : error("修改失败!");
//    }


//    /**
//     * 删除配置
//     */
//    @RequestMapping(value = "/del", method = RequestMethod.POST)
//    @RequiresPermissions("basis:type:del")
//    @ResponseBody
//	@BussinessLog(businessType=BusinessType.DELETE,title="删除类别信息")
//    public ResponseData delete(Long[] ids) {
//        return typeService.deleteBatchByIds(ids)?success():error("数据被占用");
//    }
//

//
//
//    @RequiresPermissions("basis:departmentMenu:view")
//    @GetMapping
//    public String user(ModelMap mmap) {
//        return urlPrefix + "/menu";
//    }

    @ResponseBody
    @PostMapping(value = "/list")
    public ResponseData list(String keyword) {
        List<BasisType> datas = typeService.findMenuList(keyword);
        return success().put("list", datas);
    }

    /**
     * 新增
     */
    @RequestMapping("/add/{parentId}")
    public String add(@PathVariable("parentId") Long parentId, ModelMap mmap) {
        BasisType menu = null;
        if (0L != parentId) {
            menu = typeService.getById(parentId);
        } else {
            menu = new BasisType();
            menu.setId(0L);
            menu.setBasisName("主目录");
        }
        mmap.put("menu", menu);
        return urlPrefix + "/add";
    }

    /**
     * 保存
     */
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @RequiresPermissions("basis:departmentMenu:add")
    @ResponseBody
    @BussinessLog(businessType = BusinessType.INSERT, title = "新增类别")
    public ResponseData save(@Validated BasisType menu) {
        // 数据校验
        verifyForm(menu);
        if (!typeService.checkMenuNameUnique(menu)) {
            return error("新增类别'" + menu.getBasisName() + "'失败，类别名称已存在");
        }
        // 查询上级菜单
        if (menu.getParentId() != 0L) {
            BasisType pMenu = typeService.getById(menu.getParentId());
            menu.setParentName(pMenu.getBasisName());
        }
        ShiroUtils.clearCachedAuthorizationInfo(); // 清理权限缓存
        return StringUtils.isNotEmpty(typeService.save(menu)) ? success() : error("新增失败!");
    }

    /**
     * 修改菜单
     */
    @GetMapping("/edit/{menuId}")
    public String edit(@PathVariable("menuId") Long menuId, ModelMap mmap) {
        BasisType byId = typeService.getById(menuId);
//        if (StringUtils.isEmpty(byId.getParentName())) {
//            byId.setParentName(null);
//        }
        mmap.put("menu", byId);
        return urlPrefix + "/edit";
    }

    /**
     * 修改
     */
    @RequestMapping(value = "/edit", method = RequestMethod.POST)
    @RequiresPermissions("basis:departmentMenu:edit")
    @ResponseBody
    @BussinessLog(businessType = BusinessType.UPDATE, title = "修改菜单")
    public ResponseData edit(BasisType menu) {
        // 数据校验
        verifyForm(menu);
        if (!typeService.checkMenuNameUnique(menu)) {
            return error("新增菜单'" + menu.getBasisName() + "'失败，菜单名称已存在");
        }
        if (StringUtils.isNotEmpty(typeService.save(menu))) {
            ShiroUtils.clearCachedAuthorizationInfo(); //清理权限缓存
            return success();
        } else {
            return error("修改失败");
        }
    }

    /**
     * 删除
     */
    @RequestMapping(value = "/del/{menuId}")
    @RequiresPermissions("basis:departmentMenu:del")
    @ResponseBody
    @BussinessLog(businessType = BusinessType.DELETE, title = "删除菜单")
    public ResponseData delete(@PathVariable("menuId") Long menuId) {
        List<BasisType> menuList = typeService.findListParentId(menuId);
        if (menuList.size() > 0) {
            return error("请先删除子菜单或按钮");
        }
        ShiroUtils.clearCachedAuthorizationInfo(); //清理权限缓存
        typeService.deleteById(menuId);
        return success();
    }

    /**
     * 清除菜单的redis缓存
     *
     * @return
     */
    @RequestMapping(value = "/clearMenu", method = RequestMethod.GET)
    @RequiresPermissions("basis:departmentMenu:clear")
    @ResponseBody
    public ResponseData clearConfig() {
        return typeService.clearMenuRedis() ? success() : error("清除失败!");
    }

    /**
     * 加载所有菜单列表树
     */
    @GetMapping("/menuTreeData")
    @ResponseBody
    public List<Ztree> menuTreeData() {
        List<Ztree> ztrees = typeService.menuTreeData(ShiroUtils.getUserInfo());
        return ztrees;
    }

    /**
     * 加载角色菜单列表树
     */
    @GetMapping("/roleMenuTreeData")
    @ResponseBody
    public List<Ztree> roleModuleMenuTreeData(SysRole role) {
        List<Ztree> ztrees = typeService.roleMenuTreeData(role, ShiroUtils.getUserInfo());
        return ztrees;
    }

    /**
     * 图标选择
     */
    @GetMapping("/iconSelect")
    public String iconselect(@RequestParam(value = "value", required = true) String value, ModelMap mmap) {
        mmap.put("iconValue", value);
        return urlPrefix + "/icon";
    }

    /**
     * 校验菜单名称
     */
    @RequestMapping(value = "/checkMenuNameUnique", method = RequestMethod.POST)
    @ResponseBody
    public ResponseData checkMenuNameUnique(BasisType menu) {
        return typeService.checkMenuNameUnique(menu) ? success() : error("已经存在!");
    }

    /**
     * 验证参数是否正确
     */
    private void verifyForm(BasisType menu) {
        if (StrUtil.isBlank(menu.getBasisName())) {
            throw new RxcException("菜单名称不能为空");
        }

        if (menu.getParentId() == null) {
            throw new RxcException("上级菜单不能为空");
        }

//        // 菜单
//        if (menu.getType() == Constant.MenuType.MENU.getValue()) {
//            if (StrUtil.isBlank(menu.getUrl())) {
//                throw new RxcException("菜单URL不能为空");
//            }
//        }

//        // 上级菜单类型
//        int parentType = Constant.MenuType.CATALOG.getValue();
//        if (menu.getParentId() != 0) {
//            BasisDepartment parentMenu = departmentService.getById(menu.getParentId());
//            parentType = parentMenu.getType();
//        }

//        // 目录、菜单
//        if (menu.getType() == Constant.MenuType.CATALOG.getValue()
//                || menu.getType() == Constant.MenuType.MENU.getValue()) {
//            if (parentType != Constant.MenuType.CATALOG.getValue()) {
//                throw new RxcException("上级菜单只能为目录类型");
//            }
//            return;
//        }

//        // 按钮
//        if (menu.getType() == Constant.MenuType.BUTTON.getValue()) {
//            if (parentType != Constant.MenuType.MENU.getValue()) {
//                throw new RxcException("上级菜单只能为菜单类型");
//            }
//            return;
//        }
    }








}
