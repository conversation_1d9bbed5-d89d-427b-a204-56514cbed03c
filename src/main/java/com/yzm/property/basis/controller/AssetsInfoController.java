package com.yzm.property.basis.controller;

import com.yzm.common.annotation.BussinessLog;
import com.yzm.common.enums.BusinessType;
import com.yzm.common.utils.OrderUtils;
import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.basis.entity.*;
import com.yzm.property.basis.service.*;
import com.yzm.property.business.entity.HandleStatus;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:46
 */
@Controller
@RequestMapping("/assets_info")
public class AssetsInfoController extends BaseController {

    private String urlPrefix = "basis/assets";

    private static List<AssetsAttribution> attributionAttrs = new ArrayList<>();
    private static List<AssetsAttribution> sourceAttrs = new ArrayList<>();
    private static List<HandleStatus> statusAttrs = new ArrayList<>();
    private static List<AssetsAttribution> capitalAttrs = new ArrayList<>();
    private static List<AssetsAttribution> useAttrs = new ArrayList<>();

    static {
        //资产所属
        attributionAttrs.add(new AssetsAttribution("企业资产"));
        attributionAttrs.add(new AssetsAttribution("个人资产"));
        //使用方向
        useAttrs.add(new AssetsAttribution("生产"));
        useAttrs.add(new AssetsAttribution("出租"));
        useAttrs.add(new AssetsAttribution("自用"));

        //入账方式
        sourceAttrs.add(new AssetsAttribution("购入"));
        sourceAttrs.add(new AssetsAttribution("自建"));
        sourceAttrs.add(new AssetsAttribution("租赁"));
        sourceAttrs.add(new AssetsAttribution("捐赠"));
        sourceAttrs.add(new AssetsAttribution("其他"));
        sourceAttrs.add(new AssetsAttribution("内部购入"));
        sourceAttrs.add(new AssetsAttribution("盘盈"));

        //资金来源
        capitalAttrs.add(new AssetsAttribution("内部资金"));
        capitalAttrs.add(new AssetsAttribution("捐赠"));

        //资产状态
        statusAttrs.add(new HandleStatus(0, "闲置"));
        statusAttrs.add(new HandleStatus(1, "领用审核中"));
        statusAttrs.add(new HandleStatus(2, "领用中"));
        statusAttrs.add(new HandleStatus(3, "借用审核中"));
        statusAttrs.add(new HandleStatus(4, "借用中"));
        statusAttrs.add(new HandleStatus(5, "维修审核中"));
        statusAttrs.add(new HandleStatus(6, "维修中"));
        statusAttrs.add(new HandleStatus(7, "报废审核中"));
        statusAttrs.add(new HandleStatus(8, "报废"));
        statusAttrs.add(new HandleStatus(9, "锁定中"));

    }


    @Autowired
    private AssetsInfoService assetsInfoService;

    @Autowired
    private TypeService typeService;//资产类型

    @Autowired
    private AreaService areaService;//资产区域

    @Autowired
    private UnitService unitService;//资产计量单位

    @Autowired
    private PersonnelService personnelService;//使用人

    @Autowired
    private DepartmentService departmentService;//部门

    @Autowired
    private BrandService brandService;//品牌

    @Autowired
    private AssetsLogService assetsLogService;

    /**
     * 跳转至资产流水页面
     *
     * @param mmap
     * @return
     */
    @RequiresPermissions("sys:assets:view")
    @RequestMapping("/log/{id}")
    public String assetsLog(@PathVariable("id") Long id, ModelMap mmap) {
        mmap.put("id", id);
        return urlPrefix + "/log";
    }

    /**
     * 根据关键字和分页查询可处理的资产
     *
     * @param id
     * @param pageBean
     * @return
     */
    @PostMapping("/logById/{id}")
    @ResponseBody
    public ResponseData showLogByAssetsId(
            @PathVariable("id") Long id,
            PageBean pageBean
    ) {
        return success(assetsLogService.findAllByAssetsId(id, pageBean));
    }

    /**
     * 跳转至list页面
     *
     * @param mmap
     * @return
     */
    @RequiresPermissions("sys:assets:view")
    @RequestMapping
    public String assets(ModelMap mmap) {
        return urlPrefix + "/list";
    }

    /**
     * 跳转至新增页面，携带资产类型以及资产分类数据
     *
     * @param mmap
     * @return
     */
    @GetMapping("/add")
    @RequiresPermissions("sys:assets:add")
    public String addAssetsInfo(@RequestParam(value = "infoId", required = false) Long infoId, ModelMap mmap) {
        mmap.put("sourceAttrs", sourceAttrs);
        mmap.put("attributionAttrs", attributionAttrs);
        mmap.put("capitalAttrs", capitalAttrs);
        mmap.put("useAttrs", useAttrs);
        mmap.put("unit", unitService.findAll());
        mmap.put("type", typeService.findAll());
        mmap.put("area", areaService.findAll());
        mmap.put("brand", brandService.findAll());
        mmap.put("person", personnelService.findAll());
        mmap.put("department", departmentService.findAll());
        if (infoId != null) {
            mmap.put("infoId", infoId);
        }
        return urlPrefix + "/add";
    }

    /**
     * 新增资产
     *
     * @param assetsInfo
     * @return
     */
    @PostMapping("/add")
    @ResponseBody
    @RequiresPermissions("sys:assets:add")
    @BussinessLog(businessType = BusinessType.INSERT, title = "新增资产")
    public ResponseData addAssetsInfo(AssetsInfo assetsInfo) {
        assetsInfo.setOtherId(OrderUtils.getAssetsCode());
        assetsInfo.setAssetsStatus(0);
        assetsInfo.setMaintenanceStatus("正常");
        assetsInfo.setUsedYears(0.0);
        return assetsInfoService.save(assetsInfo) != null ? success() : error("新增失败");
    }

    /**
     * 为修改获取资产对象以及资产类型和资产分类
     */
    @GetMapping("/update/{id}")
    @RequiresPermissions("sys:assets:update")
    public String updateAssetsInfo(@PathVariable("id") Long id, ModelMap mmap) {
        mmap.put("sourceAttrs", sourceAttrs);
        mmap.put("attributionAttrs", attributionAttrs);
        mmap.put("capitalAttrs", capitalAttrs);
        mmap.put("useAttrs", useAttrs);

        AssetsInfo assetsInfo = assetsInfoService.findById(id);
        mmap.put("assets_info", assetsInfo);

        List<HandleStatus> showStatus = new ArrayList<>();
        showStatus.add(statusAttrs.get(assetsInfo.getAssetsStatus()));
        mmap.put("statusAttrs", showStatus);
        mmap.put("unit", unitService.findAll());
        mmap.put("type", typeService.findAll());
        mmap.put("area", areaService.findAll());
        mmap.put("brand", brandService.findAll());
        List<BasisPersonnel> person = new ArrayList<>();
        try {
            person.add(personnelService.findById(assetsInfo.getAssetsUser().longValue()));
        } catch (Exception e) {
            System.out.println("当前资产无使用者");
        }
        mmap.put("person", person);
        List<BasisDepartment> department = new ArrayList<>();
        try {
            department.add(departmentService.findById(assetsInfo.getDepartment()));
        } catch (Exception e) {
            System.out.println("当前资产无指定部门");
        }
        mmap.put("department", department);
        return urlPrefix + "/edit";
    }

    /**
     * 修改资产
     */
    @PostMapping("/update")
    @ResponseBody
    @RequiresPermissions("sys:assets:update")
    @BussinessLog(businessType = BusinessType.UPDATE, title = "修改资产信息")
    public ResponseData updateAssetsInfo(AssetsInfo assetsInfo) {
        return assetsInfoService.save(assetsInfo) != null ? success() : error("修改失败");
    }

    /**
     * 根据id列表删除资产
     *
     * @param ids
     * @return
     */
    @PostMapping("/delete")
    @ResponseBody
    @RequiresPermissions("sys:assets:delete")
    @BussinessLog(businessType = BusinessType.DELETE, title = "删除资产")
    public ResponseData deleteAssetsInfo(Long[] ids) {
        assetsInfoService.delete(ids);
        return success();
    }

    /**
     * 根据关键字和分页查询资产
     *
     * @param keyword
     * @param pageBean
     * @return
     */
    @PostMapping("/list")
    @ResponseBody
    public ResponseData showAllAssetsInfo(
            @RequestParam(value = "keyword", required = false)
                    String keyword,
            PageBean pageBean
    ) {
        return success(assetsInfoService.showAllAssetsInfo(keyword, pageBean));
    }

    /**
     * 根据关键字和分页查询可处理的资产
     *
     * @param keyword
     * @param pageBean
     * @return
     */
    @PostMapping("/listToHandle")
    @ResponseBody
    public ResponseData showAllAssetsInfoToHandle(
            @RequestParam(value = "keyword", required = false)
                    String keyword,
            PageBean pageBean
    ) {
        return success(assetsInfoService.showAllAssetsInfoToRepairsOrScrap(keyword, pageBean));
    }

    /**
     * 根据关键字和分页查询待归还的资产
     *
     * @param keyword
     * @param pageBean
     * @return
     */
    @PostMapping("/listToReturn")
    @ResponseBody
    public ResponseData showAllAssetsInfoToReturn(
            @RequestParam(value = "keyword", required = false)
                    String keyword,
            PageBean pageBean
    ) {
        return success(assetsInfoService.showAllAssetsInfoToReturn(keyword, pageBean));
    }

    /**
     * 根据关键字和分页查询借用的资产
     *
     * @param keyword
     * @param pageBean
     * @return
     */
    @PostMapping("/listToBorrow")
    @ResponseBody
    public ResponseData showAllAssetsInfoToBorrow(
            @RequestParam(value = "keyword", required = false)
                    String keyword,
            PageBean pageBean
    ) {
        return success(assetsInfoService.showAllAssetsInfoToBorrow(keyword, pageBean));
    }

    /**
     * 根据维修表id查找对应的资产
     *
     * @param repairsId 维修表id
     * @return
     */
    @RequestMapping("/listOfRepairs/{id}")
    @ResponseBody
    public ResponseData showAssetsInfoByRepairs(
            @PathVariable("id") Long repairsId
    ) {
        return success().put("itemJson", assetsInfoService.showAssetsInfoByRepairs(repairsId));
    }

    /**
     * 根据状态查找对应的资产
     *
     * @param pageBean 分页
     * @return
     */
    @RequestMapping("/listOfStatus")
    @ResponseBody
    public ResponseData showAssetsInfoByStatus(
            @RequestParam("status") Integer status,
            @RequestParam("keyword") String keyword,
            PageBean pageBean
    ) {
        return success(assetsInfoService.showAssetsInfoByStatus(status, keyword, pageBean));
    }

    /**
     * 根据报废表id查找对应的资产
     *
     * @param scrapId 报废表id
     * @return
     */
    @RequestMapping("/listOfScrap/{id}")
    @ResponseBody
    public ResponseData showAssetsInfoByScrap(
            @PathVariable("id") Long scrapId
    ) {
        return success().put("itemJson", assetsInfoService.showAssetsInfoByScrap(scrapId));
    }

    /**
     * 根据id查找资产
     *
     * @param id 资产id
     * @return
     */
    @RequestMapping("/show/{id}")
    @RequiresPermissions("sys:assets:show")
    public ModelAndView showAssetsInfoById(@PathVariable("id") Long id) {
        AssetsInfo assetsInfo = assetsInfoService.findById(id);
        ModelAndView modelAndView = new ModelAndView(urlPrefix + "/show");
        modelAndView.addObject("assets_info", assetsInfo);
        modelAndView.addObject("unit", assetsInfo.getUnit() != null ? unitService.findById(assetsInfo.getUnit()) : new BasisUnit());
//        modelAndView.addObject("type", assetsInfo.getAssetsType() != null ? typeService.findById(assetsInfo.getAssetsType()) : new BasisType());
        modelAndView.addObject("area", assetsInfo.getAssetsArea() != null ? areaService.findById(assetsInfo.getAssetsArea()) : new BasisArea());
        modelAndView.addObject("person", assetsInfo.getAssetsUser() != null ? personnelService.findById(assetsInfo.getAssetsUser().longValue()) : new BasisPersonnel());
        modelAndView.addObject("department", assetsInfo.getDepartment() != null ? departmentService.findById(assetsInfo.getDepartment()) : new BasisDepartment());
        return modelAndView;
    }

    /**
     * 根据id查找资产
     *
     * @param id 资产id
     * @return
     */
    @RequestMapping("/showOtherId/{otherId}")
    @RequiresPermissions("sys:assets:show")
    public ModelAndView showAssetsInfoByOtherId(@PathVariable("otherId") String otherId) {
        AssetsInfo assetsInfo = assetsInfoService.findByOtherId(otherId);
        ModelAndView modelAndView = new ModelAndView(urlPrefix + "/show");
        modelAndView.addObject("assets_info", assetsInfo);
        modelAndView.addObject("unit", assetsInfo.getUnit() != null ? unitService.findById(assetsInfo.getUnit()) : new BasisUnit());
//        modelAndView.addObject("type", assetsInfo.getAssetsType() != null ? typeService.findById(assetsInfo.getAssetsType()) : new BasisType());
        modelAndView.addObject("area", assetsInfo.getAssetsArea() != null ? areaService.findById(assetsInfo.getAssetsArea()) : new BasisArea());
        modelAndView.addObject("person", assetsInfo.getAssetsUser() != null ? personnelService.findById(assetsInfo.getAssetsUser().longValue()) : new BasisPersonnel());
        modelAndView.addObject("department", assetsInfo.getDepartment() != null ? departmentService.findById(assetsInfo.getDepartment()) : new BasisDepartment());
        return modelAndView;
    }

    /**
     * 修改根据id查找资产
     */
    @RequestMapping("/listOfBorrowReturns")
    @ResponseBody
    public ResponseData getInfo(String borrowReturnId) {
        return success().put("itemJson", assetsInfoService.showAssetsInfoByBorrowReturns(borrowReturnId));
    }
}
