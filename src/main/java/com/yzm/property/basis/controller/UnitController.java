package com.yzm.property.basis.controller;

import com.yzm.common.annotation.BussinessLog;
import com.yzm.common.enums.BusinessType;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.basis.criteria.UnitCriteria;
import com.yzm.property.basis.entity.BasisUnit;
import com.yzm.property.basis.service.AssetsInfoService;
import com.yzm.property.basis.service.UnitService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 区域
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 11:33
 */
@Controller
@RequestMapping(value = "basis/unit")
public class UnitController extends BaseController {
    private String urlPrefix = "basis/unit";

    @Autowired
    private UnitService unitService;
    @Autowired
    private AssetsInfoService assetsInfoService;

    @RequiresPermissions("basis:unit:view")
    @GetMapping
    public String client(ModelMap mmap) {
        return urlPrefix + "/unit";
    }

    @ResponseBody
    @PostMapping(value = "/list")
    public ResponseData page(PageBean pageBean, UnitCriteria unit) {
        Map<String, Object> datas = unitService.findAllByPage(unit, pageBean.getPagable());
        return success(datas);
    }

    /**
     * 新增参数配置
     */
    @GetMapping("/add")
    public String add() {
        return urlPrefix + "/add";
    }

    /**
     * 保存配置
     */
    @RequestMapping("/add")
    @RequiresPermissions("basis:unit:add")
    @ResponseBody
	@BussinessLog(businessType=BusinessType.INSERT,title="新增单位信息")
    public ResponseData save(BasisUnit config) {
        BasisUnit basisUnit = null;
        try {
            basisUnit = unitService.save(config);
        } catch (Exception e) {
            throw new RuntimeException("单位名称已存在，请重新输入");
        }
        return StringUtils.isNotEmpty(basisUnit) ? success() : error("新增失败!");
    }

    /**
     * 修改参数配置
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        mmap.put("unit", unitService.getById(id));
        return urlPrefix + "/edit";
    }

    /**
     * 修改配置
     */
    @RequestMapping(value = "/edit", method = RequestMethod.POST)
    @RequiresPermissions("basis:unit:edit")
    @ResponseBody
	@BussinessLog(businessType=BusinessType.UPDATE,title="修改单位信息")
    public ResponseData update(BasisUnit config) {
        return StringUtils.isNotEmpty(unitService.update(config)) ? success() : error("修改失败!");
    }


    /**
     * 删除配置
     */
    @RequestMapping(value = "/del", method = RequestMethod.POST)
    @RequiresPermissions("basis:unit:del")
    @ResponseBody
	@BussinessLog(businessType=BusinessType.DELETE,title="删除单位信息")
    public ResponseData delete(Long[] ids) {
        return unitService.deleteBatchByIds(ids)?success():error("数据被占用");
    }
}
