package com.yzm.property.basis.controller;

import com.yzm.framework.base.BaseController;
import com.yzm.property.basis.service.AssetsInfoService;
import com.yzm.property.basis.service.AssetsLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 首页
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 11:33
 */
@Controller
@RequestMapping(value = "/mainPage")
public class MainPageController extends BaseController {

    @Autowired
    private AssetsInfoService assetsInfoService;
    @Autowired
    private AssetsLogService assetsLogService;



    /**
     * 首页查询数据显示
     */
    @PostMapping("/mainCount")
    @ResponseBody
    public Map<Object, Object> mainCount(){
        Map<Object, Object> map = new HashMap<Object, Object>();
        //总资产金额
        map.put("purchasePriceSum",assetsInfoService.findByPurchasePriceSum());
        //到期维保数量 维保状态为 脱保的总数
        map.put("maintenanceStatusCoun",assetsInfoService.findByMaintenanceStatusCount());
        //本月新增资产数
        map.put("assetsMonthCount",assetsInfoService.findAssetsMonthCount());
        //今年新增资产数
        map.put("assetsYearCount",assetsInfoService.findAssetsYearCount());
        return map;
    }

    /**
     * 首页资产使用频率
     */
    @RequestMapping("/assetsInfoUseFrequency")
    @ResponseBody
    public Map<Object, Object> assetsInfoUseFrequency() {
        Map<Object, Object> map = new HashMap<Object, Object>();
        //资产使用频率
        map.put("assetsInfoUseFrequency", assetsLogService.assetsInfoUseFrequency());
        return map;
    }

    /**
     * 首页资产类型分类
     */
    @RequestMapping("/assetsInfoType")
    @ResponseBody
    public Map<Object, Object> assetsInfoType() {
        Map<Object, Object> map = new HashMap<Object, Object>();
        //资产使用频率
        map.put("assetsInfoType", assetsLogService.assetsInfoType());
        return map;
    }
}
