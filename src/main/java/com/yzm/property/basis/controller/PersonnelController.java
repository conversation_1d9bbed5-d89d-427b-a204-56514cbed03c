package com.yzm.property.basis.controller;

import com.yzm.common.annotation.BussinessLog;
import com.yzm.common.enums.BusinessType;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.basis.criteria.PersonnelCriteria;
import com.yzm.property.basis.entity.BasisDepartment;
import com.yzm.property.basis.entity.BasisPersonnel;
import com.yzm.property.basis.service.DepartmentService;
import com.yzm.property.basis.service.PersonnelService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 区域
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 11:33
 */
@Controller
@RequestMapping(value = "basis/personnel")
public class PersonnelController extends BaseController {
    private String urlPrefix = "basis/personnel";

    @Autowired
    private PersonnelService personnelService;
    @Autowired
    private DepartmentService departmentService;

    @RequiresPermissions("basis:personnel:view")
    @GetMapping
    public String client(ModelMap mmap) {
        return urlPrefix + "/personnel";
    }

    @ResponseBody
    @PostMapping(value = "/list")
    public ResponseData page(PageBean pageBean, PersonnelCriteria personnel) {
        personnel.setDeleteFlag(0);
        Map<String, Object> datas = personnelService.findAllByPage(personnel, pageBean.getPagable());
        return success(datas);
    }

    /**
     * 新增参数配置
     */
    @GetMapping("/add")
    public String add(ModelMap mmap) {
        List<BasisDepartment> basisDepartmentData = departmentService.findAll();
      /*  for (BasisDepartment basisDepartmentDatum : basisDepartmentData) {
            BasisDepartment departmentData = departmentService.findByDepartmentName(basisDepartmentDatum.getDepartmentName());
            mmap.addAttribute("departmentPeopleNumber",departmentData.getPeopleNumber());
        }*/
        mmap.addAttribute("departmentList", basisDepartmentData);
        return urlPrefix + "/add";
    }

    /**
     * 保存配置
     */
    @RequestMapping("/add")
    @RequiresPermissions("basis:personnelService:add")
    @ResponseBody
    @BussinessLog(businessType = BusinessType.INSERT, title = "新增人员信息")
    public ResponseData save(BasisPersonnel config) {
        BasisDepartment byId = departmentService.findById(config.getPersonnelDepartmentId().intValue());
        config.setPersonnelDepartment(byId.getBasisName());
        config.setDeleteFlag(0);
//        return StringUtils.isNotEmpty(personnelService.save(config)) ? success() : error("新增失败!");
        BasisPersonnel basisPersonnel = null;
        try {
            basisPersonnel = personnelService.save(config);
        } catch (Exception e) {
            throw new RuntimeException("人员工号已存在，请重新输入");
        }
        return StringUtils.isNotEmpty(basisPersonnel) ? success() : error("新增失败!");
    }

    /**
     * 修改参数配置
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        mmap.put("personnel", personnelService.getById(id));
        return urlPrefix + "/edit";
    }

    /**
     * 修改配置
     */
    @RequestMapping(value = "/edit", method = RequestMethod.POST)
    @RequiresPermissions("basis:personnel:edit")
    @ResponseBody
    @BussinessLog(businessType = BusinessType.UPDATE, title = "修改区域信息")
    public ResponseData update(BasisPersonnel config) {
        return StringUtils.isNotEmpty(personnelService.update(config)) ? success() : error("修改失败!");
    }


    /**
     * 删除配置
     */
    @RequestMapping(value = "/del", method = RequestMethod.POST)
    @RequiresPermissions("basis:personnel:del")
    @ResponseBody
    @BussinessLog(businessType = BusinessType.DELETE, title = "删除区域信息")
    public ResponseData delete(Long[] ids) {
        return personnelService.deleteByIds(ids) ? success() : error("删除失败!");
    }

    @PostMapping("/showPeopleNumber")
    @ResponseBody
    public ResponseData showPeopleNumber(ModelMap mmap) {
        List<BasisDepartment> basisDepartmentData = departmentService.findAll();
        for (BasisDepartment basisDepartmentDatum : basisDepartmentData) {
            BasisDepartment departmentData = departmentService.findByDepartmentName(basisDepartmentDatum.getBasisName());
        }
        return null;
    }

    @GetMapping("/findByPersonnelDepartmentId")
    @ResponseBody
    public ResponseData findByPersonnelDepartmentId(Long id) {
        return new ResponseData("0", "成功", personnelService.findByDepartmentIdList(id));
    }
}
