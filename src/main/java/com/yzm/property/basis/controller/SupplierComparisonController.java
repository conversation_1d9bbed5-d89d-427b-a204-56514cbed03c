package com.yzm.property.basis.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.yzm.common.annotation.BussinessLog;
import com.yzm.common.enums.BusinessType;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.basis.criteria.SupplierComparisonCriteria;
import com.yzm.property.basis.entity.SupplierComparison;
import com.yzm.property.basis.service.SupplierComparisonService;
import com.yzm.property.business.entity.BorrowReturnItem;
import com.yzm.property.consumable.entity.ConsumableRepertory;
import com.yzm.property.consumable.entity.Rfid;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 区域
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 11:33
 */
@Controller
@RequestMapping(value = "basis/supplierComparison")
public class SupplierComparisonController extends BaseController {
    private String urlPrefix = "basis/supplierComparison";

    @Autowired
    private SupplierComparisonService supplierComparisonService;

    @GetMapping
    public String client(ModelMap mmap) {
        return urlPrefix + "/supplierComparison";
    }

    @ResponseBody
    @PostMapping(value = "/list")
    public ResponseData page(PageBean pageBean, SupplierComparisonCriteria supplierComparisonCriteria) {
        Map<String, Object> datas = supplierComparisonService.findAllByPage(supplierComparisonCriteria, pageBean.getPagable());
        return success(datas);
    }

    /**
     * 新增参数配置
     */
    @GetMapping("/add")
    public String add() {
        return urlPrefix + "/add";
    }

    /**
     * 保存配置
     */
    @RequestMapping("/add")
    @ResponseBody
    @BussinessLog(businessType = BusinessType.INSERT, title = "新增供应商对照信息")
    public ResponseData save(SupplierComparison config) {
        SupplierComparison supplierComparison = null;
        try {
            supplierComparison = supplierComparisonService.save(config);
        } catch (Exception e) {
            throw new RuntimeException("供应商对照名称已存在，请重新输入");
        }
        return StringUtils.isNotEmpty(supplierComparison) ? success() : error("新增失败!");
    }

    /**
     * 修改参数配置
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        mmap.put("supplierComparison", supplierComparisonService.getById(id));
        return urlPrefix + "/edit";
    }

    /**
     * 修改配置
     */
    @RequestMapping(value = "/edit", method = RequestMethod.POST)
    @ResponseBody
    @BussinessLog(businessType = BusinessType.UPDATE, title = "修改供应商对照信息")
    public ResponseData update(SupplierComparison supplierComparison) {
        return StringUtils.isNotEmpty(supplierComparisonService.update(supplierComparison)) ? success() : error("修改失败!");
    }


    /**
     * 删除配置
     */
    @RequestMapping(value = "/del", method = RequestMethod.POST)
    @ResponseBody
    @BussinessLog(businessType = BusinessType.DELETE, title = "删除供应商对照信息")
    public ResponseData delete(Long[] ids) {
        return supplierComparisonService.deleteBatchByIds(ids) ? success() : error("数据被占用");
    }

    /**
     * 导入物料
     *
     * @param excelFile 物料导入
     * @return
     */
    @RequestMapping("/ReadFileText")
    @ResponseBody
    public ResponseData readFileText2(@RequestParam(value = "file", required = false) MultipartFile excelFile) {
        //============================使用模板======================================================
        InputStream inputStream = null;
        try {
            // 使用模板导入——接收上传文件
            inputStream = excelFile.getInputStream();
            List<String> list = new ArrayList<>();
            EasyExcel.read(inputStream, SupplierComparison.class, new AnalysisEventListener<SupplierComparison>() {
                @Override
                public void invoke(SupplierComparison supplierComparison, AnalysisContext analysisContext) {

                    SupplierComparison supplierComparison1 = supplierComparisonService.findBySupplierNameAndSupplierMaterialsCodeAndInternalMaterialsCode(supplierComparison.getSupplierName(), supplierComparison.getSupplierMaterialsCode(), supplierComparison.getInternalMaterialsCode());
                    if (supplierComparison1 == null) {
                        supplierComparisonService.save(supplierComparison);
                    } else {
                        throw new ExcelAnalysisException("供应商:"+supplierComparison1.getSupplierName()+"-"+supplierComparison1.getSupplierMaterialsCode() + "与"+supplierComparison1.getSupplierMaterialsCode()+"重复");
                    }

                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                }
            }).sheet().doRead();
            return success(list);
        } catch (IOException e) {
            e.printStackTrace();
            return new ResponseData("500", "", null);
        } catch (ExcelAnalysisException e) {
            if (e.getCause() instanceof ExcelDataConvertException) {
                ExcelDataConvertException excelDataConvertException = (ExcelDataConvertException) e.getCause();
                return new ResponseData("500", "", null);
            } else {
                return new ResponseData("500", e.getMessage(), null);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return new ResponseData("500", "请检查导入模板！", null);
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

}
