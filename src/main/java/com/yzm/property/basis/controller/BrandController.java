package com.yzm.property.basis.controller;
import com.yzm.common.annotation.BussinessLog;
import com.yzm.common.enums.BusinessType;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.basis.criteria.BrandCriteria;
import com.yzm.property.basis.entity.BasisBrand;
import com.yzm.property.basis.entity.BasisUnit;
import com.yzm.property.basis.service.BrandService;
import com.yzm.property.basis.service.TypeService;
import com.yzm.property.basis.service.UnitService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 材料
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 11:33
 */
@Controller
@RequestMapping(value = "basis/brand")
public class BrandController extends BaseController{
    private String urlPrefix = "basis/brand";

    @Autowired
    private BrandService brandService;
    @Autowired
    private UnitService unitService;//资产计量单位
    @Autowired
    private TypeService typeService;//资产计量单位
    @RequiresPermissions("basis:brand:view")
    @GetMapping
    public String client(ModelMap map){
        return urlPrefix+"/brand";
    }

    @ResponseBody
    @PostMapping("/list")
    public ResponseData page(PageBean pageBean, BrandCriteria brand){
        Map<String,Object> datas=brandService.findAllByPage(brand,pageBean.getPagable());
        return  success(datas);
    }

    /**
     * 新增参数配置
     */
    @GetMapping("/add")
    public String add(ModelMap mmap){
        mmap.put("unit", unitService.findAll());
        return urlPrefix+"/add";
    }

    /**
     * 保存
     */
    @RequestMapping("/add")
    @RequiresPermissions("basis:brand:add")
    @ResponseBody
    @BussinessLog(businessType = BusinessType.INSERT,title="新增材料信息")
    public ResponseData save(BasisBrand config) {
        BasisBrand basisBrand=null;
        try{
            BasisUnit unit = unitService.findById(config.getAssetsUnit());
            config.setAssetsUnitName(unit.getUnitName());
            config.setAssetsTypeName(typeService.getById(config.getAssetsType()).getBasisName());
            basisBrand=brandService.save(config);
        }catch (Exception e){
            throw new RuntimeException("材料名称已存在，请重新输入");
        }
        return StringUtils.isNotEmpty(brandService.save(config)) ? success() : error("新增失败!");
    }

    /**
     * 修改
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        mmap.put("brand", brandService.getById(id));
        mmap.put("unit", unitService.findAll());
        return urlPrefix + "/edit";
    }

    /**
     * 修改配置
     */
    @RequestMapping(value = "/edit", method = RequestMethod.POST)
    @RequiresPermissions("basis:brand:edit")
    @ResponseBody
    @BussinessLog(businessType=BusinessType.UPDATE,title="修改材料信息")
    public ResponseData update(BasisBrand config) {
        BasisUnit unit = unitService.findById(config.getAssetsUnit());
        config.setAssetsUnitName(unit.getUnitName());
        config.setAssetsTypeName(typeService.getById(config.getAssetsType()).getBasisName());
        return StringUtils.isNotEmpty(brandService.update(config)) ? success() : error("修改失败!");
    }


    /**
     * 删除配置
     */
    @RequestMapping(value = "/del", method = RequestMethod.POST)
    @RequiresPermissions("basis:brand:del")
    @ResponseBody
    @BussinessLog(businessType=BusinessType.DELETE,title="删除材料信息")
    public ResponseData delete(Long[] ids) {
        return brandService.deleteBatchByIds(ids) ? success() : error("删除失败!");
    }
    /**
     * 根据id查询
     */
    @GetMapping("/findById")
    @ResponseBody
    public ResponseData findById(@RequestParam Long id) {
        BasisBrand basisBrand = brandService.getById(id);

        return success().put("basisBrand", basisBrand);
    }

}
