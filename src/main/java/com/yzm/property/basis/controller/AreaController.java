package com.yzm.property.basis.controller;

import com.yzm.common.annotation.BussinessLog;
import com.yzm.common.annotation.RepeatSubmit;
import com.yzm.common.enums.BusinessType;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.basis.criteria.AreaCriteria;
import com.yzm.property.basis.entity.BasisArea;
import com.yzm.property.basis.entity.BasisWarehouse;
import com.yzm.property.basis.service.AreaService;
import com.yzm.property.basis.service.WarehouseService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 库位
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 11:33
 */
@Controller
@RequestMapping(value = "basis/area")
public class AreaController extends BaseController {
    private String urlPrefix = "basis/area";
    @Autowired
    private WarehouseService warehouseService;
    @Autowired
    private AreaService areaService;

    @RequiresPermissions("basis:area:view")
    @GetMapping
    public String client(ModelMap mmap) {
        return urlPrefix + "/area";
    }

    @ResponseBody
    @PostMapping(value = "/list")
    public ResponseData page(PageBean pageBean, AreaCriteria area) {
        Map<String, Object> datas = areaService.findAllByPage(area, pageBean.getPagable());
        return success(datas);
    }

    /**
     * 新增参数配置
     */
    @GetMapping("/add")
    public String add(ModelMap mmap) {
        mmap.put("warehouseList", warehouseService.findAll());

        return urlPrefix + "/add";
    }

    /**
     * 保存配置
     */
    @RequestMapping("/add")
    @RequiresPermissions("basis:area:add")
    @ResponseBody
    @BussinessLog(businessType = BusinessType.INSERT, title = "新增库位信息")
    public ResponseData save(BasisArea config) {

        BasisArea basisArea = null;
        try {
            BasisWarehouse byId = warehouseService.getById(config.getCkid());
            config.setCkmc(byId.getCkmc());
            basisArea = areaService.save(config);
        } catch (Exception e) {
            throw new RuntimeException("库位名称已存在，请重新输入");
        }
        return StringUtils.isNotEmpty(basisArea) ? success() : error("新增失败!");
    }

    /**
     * 修改参数配置
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        mmap.put("area", areaService.getById(id));
        mmap.put("warehouseList", warehouseService.findAll());
        return urlPrefix + "/edit";
    }

    /**
     * 修改配置
     */
    @RequestMapping(value = "/edit", method = RequestMethod.POST)
    @RequiresPermissions("basis:area:edit")
    @ResponseBody
    @BussinessLog(businessType = BusinessType.UPDATE, title = "修改库位信息")
    public ResponseData update(BasisArea config) {
        BasisWarehouse byId = warehouseService.getById(config.getCkid());
        config.setCkmc(byId.getCkmc());
        return StringUtils.isNotEmpty(areaService.update(config)) ? success() : error("修改失败!");
    }


    /**
     * 删除配置
     */
    @RequestMapping(value = "/del", method = RequestMethod.POST)
    @RequiresPermissions("basis:area:del")
    @ResponseBody
    @BussinessLog(businessType = BusinessType.DELETE, title = "删除库位信息")
    public ResponseData delete(Long[] ids) {
        return areaService.deleteBatchByIds(ids) ? success() : error("数据被占用");
    }


    @RepeatSubmit
    @RequestMapping("/getAreaListByWarehouseId")
    @ResponseBody
    public ResponseData getAreaListByWarehouseId(String ckmc) {
        BasisWarehouse warehouse = warehouseService.findByCkmc(ckmc);
        List<BasisArea> list = areaService.findByCkid(warehouse.getId());
        return success().put("list", list);
    }
    @RepeatSubmit
    @RequestMapping("/getAreaListByWarehouseIdByStatus")
    @ResponseBody
    public ResponseData getAreaListByWarehouseIdByStatus(Long ckId) {
        List<BasisArea> list = areaService.findByCkid(ckId);
        for (BasisArea basisArea : list) {
            if (basisArea.getStatus()!=null&&basisArea.getStatus()==1){
                list.remove(basisArea);
            }
        }
        return success().put("list", list);
    }

    @RepeatSubmit
    @RequestMapping("/getAreaListByWarehouseIdByIsInventory")
    @ResponseBody
    public ResponseData getAreaListByWarehouseIdByIsInventory(String ckmc) {
        List<BasisArea> list = areaService.findByCkIdAndNoInventory(ckmc);

        return success().put("list", list);
    }
}
