package com.yzm.property.basis.controller;

import com.yzm.common.annotation.BussinessLog;
import com.yzm.common.enums.BusinessType;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.basis.criteria.WarehouseCriteria;
import com.yzm.property.basis.entity.BasisWarehouse;
import com.yzm.property.basis.service.WarehouseService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 区域
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 11:33
 */
@Controller
@RequestMapping(value = "basis/warehouse")
public class WarehouseController extends BaseController {
    private String urlPrefix = "basis/warehouse";

    @Autowired
    private WarehouseService warehouseService;

    @RequiresPermissions("basis:warehouse:view")
    @GetMapping
    public String client(ModelMap mmap) {
        return urlPrefix + "/warehouse";
    }

    @ResponseBody
    @PostMapping(value = "/list")
    public ResponseData page(PageBean pageBean, WarehouseCriteria area) {
        Map<String, Object> datas = warehouseService.findAllByPage(area, pageBean.getPagable());
        return success(datas);
    }

    /**
     * 新增参数配置
     */
    @GetMapping("/add")
    public String add() {
        return urlPrefix + "/add";
    }

    /**
     * 保存配置
     */
    @RequestMapping("/add")
    @RequiresPermissions("basis:warehouse:add")
    @ResponseBody
	@BussinessLog(businessType=BusinessType.INSERT,title="新增仓库信息")
    public ResponseData save(BasisWarehouse config) {
        BasisWarehouse byCkmc = warehouseService.findByCkmc(config.getCkmc());
        if(StringUtils.isNotEmpty(byCkmc)){
            return error("仓库名称已存在，请重新输入");
        }
        BasisWarehouse basisArea = warehouseService.save(config);

        return StringUtils.isNotEmpty(basisArea) ? success() : error("新增失败!");
    }

    /**
     * 修改参数配置
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        mmap.put("area", warehouseService.getById(id));
        return urlPrefix + "/edit";
    }

    /**
     * 修改配置
     */
    @RequestMapping(value = "/edit", method = RequestMethod.POST)
    @RequiresPermissions("basis:warehouse:edit")
    @ResponseBody
	@BussinessLog(businessType=BusinessType.UPDATE,title="修改仓库信息")
    public ResponseData update(BasisWarehouse config) {
        BasisWarehouse byCkmc = warehouseService.findByCkmc(config.getCkmc());
        if(StringUtils.isNotEmpty(byCkmc)){
            return error("仓库名称已存在，请重新输入");
        }
        return StringUtils.isNotEmpty(warehouseService.update(config)) ? success() : error("修改失败!");
    }


    /**
     * 删除配置
     */
    @RequestMapping(value = "/del", method = RequestMethod.POST)
    @RequiresPermissions("basis:warehouse:del")
    @ResponseBody
	@BussinessLog(businessType=BusinessType.DELETE,title="删除仓库信息")
    public ResponseData delete(Long[] ids) {
        return warehouseService.deleteBatchByIds(ids)?success():error("数据被占用");
    }
}
