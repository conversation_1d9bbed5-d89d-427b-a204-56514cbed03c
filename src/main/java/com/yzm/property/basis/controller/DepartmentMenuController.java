package com.yzm.property.basis.controller;

import cn.hutool.core.util.StrUtil;
import com.yzm.common.annotation.BussinessLog;
import com.yzm.common.enums.BusinessType;
import com.yzm.common.utils.ShiroUtils;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.ResponseData;
import com.yzm.framework.bean.Ztree;
import com.yzm.framework.exception.RxcException;
import com.yzm.framework.shiro.LoginUser;
import com.yzm.property.basis.entity.BasisDepartment;
import com.yzm.property.basis.service.DepartmentService;
import com.yzm.property.system.entity.SysRole;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Controller
@RequestMapping(value = "basis/departmentMenu")
public class DepartmentMenuController extends BaseController {
    private String urlPrefix = "basis/department";

    @Autowired
    private DepartmentService departmentService;

    @RequiresPermissions("basis:departmentMenu:view")
    @GetMapping
    public String user(ModelMap mmap) {
        return urlPrefix + "/menu";
    }

    @ResponseBody
    @PostMapping(value = "/list")
    public ResponseData list(String name) {
        LoginUser userInfo = ShiroUtils.getUserInfo();
        List<BasisDepartment> datas = departmentService.findMenuList(userInfo.getId(), name);
        return success().put("list", datas);
    }

    /**
     * 新增
     */
    @RequestMapping("/add/{parentId}")
    public String add(@PathVariable("parentId") Long parentId, ModelMap mmap) {
        BasisDepartment menu = null;
        if (0L != parentId) {
            menu = departmentService.getById(parentId);
        } else {
            menu = new BasisDepartment();
            menu.setId(0L);
            menu.setBasisName("主目录");
        }
        mmap.put("menu", menu);
        return urlPrefix + "/add";
    }

    /**
     * 保存
     */
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @RequiresPermissions("basis:departmentMenu:add")
    @ResponseBody
    @BussinessLog(businessType = BusinessType.INSERT, title = "新增菜单")
    public ResponseData save(@Validated BasisDepartment menu) {
        // 数据校验
        verifyForm(menu);
        if (!departmentService.checkMenuNameUnique(menu)) {
            return error("新增菜单'" + menu.getBasisName() + "'失败，菜单名称已存在");
        }
        ShiroUtils.clearCachedAuthorizationInfo(); // 清理权限缓存
        return StringUtils.isNotEmpty(departmentService.save(menu)) ? success() : error("新增失败!");
    }

    /**
     * 修改菜单
     */
    @GetMapping("/edit/{menuId}")
    public String edit(@PathVariable("menuId") Long menuId, ModelMap mmap) {
        BasisDepartment byId = departmentService.getById(menuId);
        if (StringUtils.isEmpty(byId.getParentName())) {
            byId.setParentName(null);
        }
        mmap.put("menu", byId);
        return urlPrefix + "/edit";
    }

    /**
     * 修改
     */
    @RequestMapping(value = "/edit", method = RequestMethod.POST)
    @RequiresPermissions("basis:departmentMenu:edit")
    @ResponseBody
    @BussinessLog(businessType = BusinessType.UPDATE, title = "修改菜单")
    public ResponseData edit(BasisDepartment menu) {
        // 数据校验
        verifyForm(menu);
        if (!departmentService.checkMenuNameUnique(menu)) {
            return error("新增菜单'" + menu.getBasisName() + "'失败，菜单名称已存在");
        }
        if (StringUtils.isNotEmpty(departmentService.save(menu))) {
            ShiroUtils.clearCachedAuthorizationInfo(); //清理权限缓存
            return success();
        } else {
            return error("修改失败");
        }
    }

    /**
     * 删除
     */
    @RequestMapping(value = "/del/{menuId}")
    @RequiresPermissions("basis:departmentMenu:del")
    @ResponseBody
    @BussinessLog(businessType = BusinessType.DELETE, title = "删除菜单")
    public ResponseData delete(@PathVariable("menuId") Long menuId) {
        List<BasisDepartment> menuList = departmentService.findListParentId(menuId);
        if (menuList.size() > 0) {
            return error("请先删除子菜单或按钮");
        }
        ShiroUtils.clearCachedAuthorizationInfo(); //清理权限缓存
        departmentService.deleteById(menuId);
        return success();
    }

    /**
     * 清除菜单的redis缓存
     *
     * @return
     */
    @RequestMapping(value = "/clearMenu", method = RequestMethod.GET)
    @RequiresPermissions("basis:departmentMenu:clear")
    @ResponseBody
    public ResponseData clearConfig() {
        return departmentService.clearMenuRedis() ? success() : error("清除失败!");
    }

    /**
     * 加载所有菜单列表树
     */
    @GetMapping("/menuTreeData")
    @ResponseBody
    public List<Ztree> menuTreeData() {
        List<Ztree> ztrees = departmentService.menuTreeData(ShiroUtils.getUserInfo());
        return ztrees;
    }

    /**
     * 加载角色菜单列表树
     */
    @GetMapping("/roleMenuTreeData")
    @ResponseBody
    public List<Ztree> roleModuleMenuTreeData(SysRole role) {
        List<Ztree> ztrees = departmentService.roleMenuTreeData(role, ShiroUtils.getUserInfo());
        return ztrees;
    }

    /**
     * 图标选择
     */
    @GetMapping("/iconSelect")
    public String iconselect(@RequestParam(value = "value", required = true) String value, ModelMap mmap) {
        mmap.put("iconValue", value);
        return urlPrefix + "/icon";
    }

    /**
     * 校验菜单名称
     */
    @RequestMapping(value = "/checkMenuNameUnique", method = RequestMethod.POST)
    @ResponseBody
    public ResponseData checkMenuNameUnique(BasisDepartment menu) {
        return departmentService.checkMenuNameUnique(menu) ? success() : error("已经存在!");
    }

    /**
     * 验证参数是否正确
     */
    private void verifyForm(BasisDepartment menu) {
        if (StrUtil.isBlank(menu.getBasisName())) {
            throw new RxcException("菜单名称不能为空");
        }

        if (menu.getParentId() == null) {
            throw new RxcException("上级菜单不能为空");
        }

//        // 菜单
//        if (menu.getType() == Constant.MenuType.MENU.getValue()) {
//            if (StrUtil.isBlank(menu.getUrl())) {
//                throw new RxcException("菜单URL不能为空");
//            }
//        }

//        // 上级菜单类型
//        int parentType = Constant.MenuType.CATALOG.getValue();
//        if (menu.getParentId() != 0) {
//            BasisDepartment parentMenu = departmentService.getById(menu.getParentId());
//            parentType = parentMenu.getType();
//        }

//        // 目录、菜单
//        if (menu.getType() == Constant.MenuType.CATALOG.getValue()
//                || menu.getType() == Constant.MenuType.MENU.getValue()) {
//            if (parentType != Constant.MenuType.CATALOG.getValue()) {
//                throw new RxcException("上级菜单只能为目录类型");
//            }
//            return;
//        }

//        // 按钮
//        if (menu.getType() == Constant.MenuType.BUTTON.getValue()) {
//            if (parentType != Constant.MenuType.MENU.getValue()) {
//                throw new RxcException("上级菜单只能为菜单类型");
//            }
//            return;
//        }
    }
}
