package com.yzm.property.basis.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.basis.entity.InventorySimple;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/9 10:03
 */
public interface InventorySimpleRepository extends BaseRepository<InventorySimple,Long> {
    @Query(nativeQuery=true,
            value = "select * from  inventory_simple order by id desc limit ?1,?2"
    )
    List<InventorySimple> findAllByPage(Integer page, Integer size);

    Page<InventorySimple> findAllByInventoryStatusNot(Integer status,Pageable pageable);

    InventorySimple findByInventoryNumber(String inventoryNumber);
}
