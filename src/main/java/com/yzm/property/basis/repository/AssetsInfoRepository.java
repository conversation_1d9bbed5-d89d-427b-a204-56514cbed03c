package com.yzm.property.basis.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.basis.entity.AssetsInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:49
 */
public interface AssetsInfoRepository  extends BaseRepository<AssetsInfo, Long> {
    Page<AssetsInfo> findAllByAssetsNameContainingOrOtherIdContaining(String keyword,String otherId, Pageable pageable);

    AssetsInfo findByOtherId(String otherId);

    @Query(nativeQuery=true,
            value = "select * from assets_info " +
            "where ( assets_name like '%' ?1 '%' or other_id like '%' ?2 '%' ) and assets_status = ?3",
            countQuery = "select count(*) from assets_info " +
                    "where ( assets_name like '%' ?1 '%' or other_id like '%' ?2 '%' ) and assets_status = ?3"
    )
    Page<AssetsInfo> findAllByAssetsNameContainingOrOtherIdContainingAndAssetsStatus(String keyword,String otherId,Integer status,Pageable pageable);

    Integer countAllByAssetsType(Integer assetsType);

    Integer countAllByAssetsArea(Integer type);

    Integer countAllByDepartment(Integer type);

    Integer countAllByAssetsUser(Integer type);
    //根据状态分页查询资产
    Page<AssetsInfo> findAllByAssetsStatus(Integer assetsStatus,Pageable pageable);
    //根据区域分页查询资产
    Page<AssetsInfo> findAllByAssetsArea(Integer assetsArea, Pageable pageable);
    //根据使用人分页查询资产
    Page<AssetsInfo> findAllByAssetsUser(Integer assetsUser, Pageable pageable);
    //根据部门分页查询资产
    Page<AssetsInfo> findAllByDepartment(Integer department, Pageable pageable);
    //根据类型分页查询资产
    Page<AssetsInfo> findAllByAssetsType(Integer assetsType, Pageable pageable);

    //根据状态查询资产
    List<AssetsInfo> findAllByAssetsStatus(Integer assetsStatus);
    //根据区域查询资产
    List<AssetsInfo> findAllByAssetsAreaAndAssetsStatusIsNot(Integer assetsArea,Integer status);
    //根据使用人查询资产
    List<AssetsInfo> findAllByAssetsUserAndAssetsStatusIsNot(Integer assetsUser,Integer status);
    //根据部门查询资产
    List<AssetsInfo> findAllByDepartmentAndAssetsStatusIsNot(Integer department,Integer status);
    //根据类型查询资产
    List<AssetsInfo> findAllByAssetsTypeAndAssetsStatusIsNot(Integer assetsType,Integer status);

//    Page<AssetsInfo> findAllByAssetsNameContainingAndMaintenanceStatusNotContainsAndMaintenanceStatusNotContains(String keyword, String repairs,String scrap,Pageable pageable);


    @Query(value="select COALESCE(SUM(purchase_price),0) from assets_info", nativeQuery = true)
    int findByPurchasePriceSum();
    @Query(value="select count(*) from assets_info where maintenance_status = '已脱保'", nativeQuery = true)
    int findByMaintenanceStatusCount();
    @Query(value="SELECT count(*) AS sum FROM assets_info WHERE MONTH(create_time) = MONTH( NOW( ) )", nativeQuery = true)
    int findAssetsMonthCount();
    @Query(value="SELECT count(*) AS sum FROM assets_info WHERE YEAR(create_time) = YEAR( NOW( ) )", nativeQuery = true)
    int findAssetsYearCount();

    @Query(nativeQuery=true,
            value = "select * from assets_info " +
                    "where ( assets_name like '%' ?1 '%' or other_id like '%' ?2 '%' ) and assets_status = ?3",
            countQuery = "select count(*) from assets_info " +
                    "where ( assets_name like '%' ?1 '%' or other_id like '%' ?2 '%' ) and assets_status = ?3"
    )
    Page<AssetsInfo> findAllByAssetsStatus(String keyword,String otherId,Integer status, Pageable pagable);

    @Modifying
    @Query(nativeQuery = true,
            value = "update assets_info set assets_status = ?2,assets_user = null where id = ?1"
    )
    void saveAssetsInfo(Long id,Integer status);

    List<AssetsInfo>  findByUnit(Long ids);

    List<AssetsInfo> findByAssetsArea(int ids);

    List<AssetsInfo> findByAssetsType(int ids);

    AssetsInfo findByBarcode(String barcode);

}
