package com.yzm.property.basis.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.basis.entity.BasisArea;
import com.yzm.property.basis.entity.InventorySimpleItem;
import com.yzm.property.materials.entity.MaterialsInfoBom;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface InventorySimpleItemRepository extends BaseRepository<InventorySimpleItem, Long> {


    List<InventorySimpleItem> findByInventoryNumber(String inventoryNumber);

    List<InventorySimpleItem> findByInventoryStatusAndInventoryNumber(long l, String inventoryNumber);

    InventorySimpleItem findByInventoryNumberAndAssetsCode(String inventoryNumber, String code);

    List<InventorySimpleItem> findByInventoryNumberAndInventoryStatus(String inventoryNumber, Long status);

    List<InventorySimpleItem> findByInventoryNumberAndInventoryStatusAndAssetsNumber(String inventoryNumber, Long status, String number);

    @Modifying
    @Query(value = "UPDATE inventory_simple_item SET loss_sum = ?1, profit_sum = ?2 WHERE inventory_number = ?3 AND inventory_status = ?4 AND assets_number = ?5", nativeQuery = true)
    void updateByInventoryNumberAndInventoryStatusAndAssetsNumber(BigDecimal loss_sum, BigDecimal profit_sum, String inventoryNumber, Long status, String number);

    @Modifying
    @Query(value = "UPDATE inventory_simple_item SET loss_sum = ?1, profit_sum = ?2,area_info=?6 WHERE inventory_number = ?3 AND inventory_status = ?4 AND assets_number = ?5", nativeQuery = true)
    void updateByInventoryNumberAndInventoryStatusAndAssetsNumberAndArea(BigDecimal loss_sum, BigDecimal profit_sum, String inventoryNumber, Long status, String number, String area);


    @Query(value = "select *,sum(result_amount) as result_amount_sum,sum(now_repertory) as now_repertory_no,    MAX(update_time) AS update_time_new  from inventory_simple_item where assets_number   like '%' ?3 '%' and  inventory_number = ?1  and inventory_status  =  ?2   group by assets_number", nativeQuery = true)
    List<Map<String, Object>> findGroupedResults(String inventoryNumber, Long inventory_status, String assets_number, Pageable page);

    @Query(value = "SELECT COUNT(*) FROM ( " +
            "SELECT assets_number FROM inventory_simple_item " +
            "WHERE assets_number   like '%' ?3 '%' and  inventory_number = ?1  and inventory_status  =  ?2 " +
            "GROUP BY assets_number) AS subquery", nativeQuery = true)
    int countGroupedResults(String inventoryNumber, Long inventory_status, String assets_number);

    @Query(value = "select sum(result_amount) from inventory_simple_item where inventory_number = ?1  and inventory_status  =  ?2  and assets_number = ?3", nativeQuery = true)
    BigDecimal findBySumResultAmount(String inventoryNumber, Long inventory_status, String assets_number);

    @Query(value = "select * from inventory_simple_item where inventory_number = ?1   and assets_number = ?2 limit 0,1", nativeQuery = true)
    InventorySimpleItem findBySumNowRepertoryData(String inventoryNumber, String assetsNumber);

    void deleteByInventoryStatus(int i);

    List<InventorySimpleItem> findByInventoryNumberAndAssetsNumber(String inventoryNumber, String assetsNumber);

    InventorySimpleItem findFirstByInventoryNumberAndAssetsNumber(String inventoryNumber, String assetsNumber);

    int countByInventoryNumberAndInventoryStatus(String inventoryNumber, long l);
}
