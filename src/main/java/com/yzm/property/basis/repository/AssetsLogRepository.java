package com.yzm.property.basis.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.basis.entity.AssetsLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/26 9:49
 */
public interface AssetsLogRepository extends BaseRepository<AssetsLog, Long> {

    Page<AssetsLog> findByAssetsIdOrderByCreateTimeDesc(Long id, Pageable pageable);
    @Query(value="select  assets_name ,count(assets_name) allCount  from assets_log where assets_name <>''   group by assets_name ORDER BY allCount DESC limit 5",
            nativeQuery = true)
    List<Map<String, Object>> assetsInfoUseFrequency();
    @Query(value = "SELECT count(assets_status) AS typeSum ,dict_label as dictLabel ,a.assets_status as assetsStatus FROM assets_info as a ,sys_dict_data as d where a.assets_status = d.dict_value and d.dict_type=\"assets_status\" \n" +
            " GROUP BY a.assets_status ",nativeQuery = true)
    List<Map<String, Object>> assetsInfoType();
}
