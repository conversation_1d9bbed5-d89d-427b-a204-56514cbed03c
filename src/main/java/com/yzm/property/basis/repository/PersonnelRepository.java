package com.yzm.property.basis.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.basis.entity.BasisPersonnel;

import java.util.List;

public interface PersonnelRepository extends BaseRepository<BasisPersonnel, Long> {

    BasisPersonnel findByBasisName(String brPeople);

    List<BasisPersonnel> findByPersonnelDepartmentId(Long id);

    List<BasisPersonnel> findByDeleteFlag(Integer status);
}
