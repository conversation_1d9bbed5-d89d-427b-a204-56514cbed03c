package com.yzm.property.basis.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.basis.entity.BasisArea;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface AreaRepository extends BaseRepository<BasisArea, Long> {

    List<BasisArea> findByCkid(Long warehouseId);


    @Query(value = "select a.* from basis_area a,materials_repertory b where a.id != b.area_info_id and a.ckmc = ?1",nativeQuery = true)
    List<BasisArea> findByCkIdAndNoInventory(String ckmc);

    BasisArea findByCkidAndBasisName(Long storageWarehouseId, String storageWarehouse);
}
