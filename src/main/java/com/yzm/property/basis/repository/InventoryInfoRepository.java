package com.yzm.property.basis.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.basis.entity.InventoryInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/9 10:03
 */
public interface InventoryInfoRepository extends BaseRepository<InventoryInfo,Long> {
    Page findBySimpleId(Long simpleId, Pageable pageable);

    Integer countBySimpleIdAndResultAmountIsNull(Long simpleId);
    
    Integer countBySimpleIdAndInventoryStatus(Long id , Integer status);

    Integer countBySimpleId(Long simpleId);

    Page<InventoryInfo> findAllBySimpleIdAndInventoryStatusNot(Long id, Integer status, Pageable pageable);
}
