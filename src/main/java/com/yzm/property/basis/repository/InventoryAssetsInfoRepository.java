package com.yzm.property.basis.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.basis.entity.InventoryAssetsInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/9 10:03
 */
public interface InventoryAssetsInfoRepository extends BaseRepository<InventoryAssetsInfo,Long> {

    List<InventoryAssetsInfo> findByInfoId(Long id);

    Integer countByInfoIdAndInventoryStatus(Long infoId,Integer status);

    InventoryAssetsInfo findByInfoIdAndAssetsId(Long id, Long id1);
}
