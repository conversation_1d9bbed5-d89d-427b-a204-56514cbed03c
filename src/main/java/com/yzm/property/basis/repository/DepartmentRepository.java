package com.yzm.property.basis.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.basis.entity.BasisDepartment;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface DepartmentRepository extends BaseRepository<BasisDepartment, Long> {

    BasisDepartment findByBasisName(String departmentName);


//    List<BasisDepartment> findAllByTypeLessThan(int type);

//    @Query(value = "FROM BasisDepartment m " +
//            "LEFT JOIN SysRoleMenu rm ON m.id = rm.menuId " +
//            "LEFT JOIN SysUserRole ur ON rm.roleId = ur.roleId " +
//            "WHERE ur.userId = ?1 ORDER BY m.orderNum asc")
//    Set<SysMenu> findMenuByUserId(Long userId);

    BasisDepartment findByBasisNameAndParentId(String name, Long parentId);

    List<BasisDepartment> findAllByParentId(Long menuId);
    @Query(value = "select * from basis_department  where basis_name like '%' ?1 '%' order by order_num ",nativeQuery = true)
    List<BasisDepartment> findByBasisNameLike(@Param("name")String name);

//    List<BasisDepartment> findAllByTypeLessThanOrderByOrderNumAsc(int type);
}
