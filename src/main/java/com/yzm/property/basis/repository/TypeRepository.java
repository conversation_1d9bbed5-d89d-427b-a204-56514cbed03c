package com.yzm.property.basis.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.basis.entity.BasisType;

import java.util.List;

public interface TypeRepository extends BaseRepository<BasisType, Long> {

    BasisType findByBasisName(String basisName);

    List<BasisType> findByParentId(Long menuId);

    List<BasisType> findByBasisNameOrTypeNumber(String keyword, String keyword1);

    BasisType findByBasisNameAndParentName(String assetsUnitName, String assetsTypeNameSuperior);
}
