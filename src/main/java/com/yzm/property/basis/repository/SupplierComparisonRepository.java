package com.yzm.property.basis.repository;

import com.yzm.framework.base.BaseRepository;
import com.yzm.property.basis.entity.BasisArea;
import com.yzm.property.basis.entity.SupplierComparison;

import java.util.List;

public interface SupplierComparisonRepository extends BaseRepository<SupplierComparison, Long> {

    SupplierComparison findBySupplierNameAndSupplierMaterialsCodeAndInternalMaterialsCode(String supplierName, String supplierMaterialsCode, String internalMaterialsCode);
}
