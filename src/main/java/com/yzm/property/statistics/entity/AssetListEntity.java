package com.yzm.property.statistics.entity;

import com.yzm.framework.base.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;

@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AssetListEntity extends BaseEntity {
    private String operationNumber;//操作编号
    private String operatingMode;//操作方式
    private String assetName;//资产名称
    private String assetNumber;//资产编号
    private String operator;//操作者
    //private Date createTime;//操作时间

}
