package com.yzm.property.statistics.service.impl;

import com.yzm.property.statistics.entity.AssetListEntity;
import com.yzm.property.statistics.repository.AssetListRepository;
import com.yzm.property.statistics.service.AssetListService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AssetListImpl implements AssetListService {
    @Autowired
    AssetListRepository assetListRepository;
    public List<AssetListEntity> getAssetList(){
        List<AssetListEntity> assetListEntity=assetListRepository.findByAssetsInfoAsAssetsLogOrderByCreateTimeDesc();
        return assetListEntity;
    }
}
