package com.yzm.property.statistics.contorller;

import cn.hutool.core.io.FileUtil;
import com.alibaba.excel.EasyExcel;
import com.yzm.common.annotation.BussinessLog;
import com.yzm.common.enums.BusinessType;
import com.yzm.framework.base.BaseController;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.framework.freemark.Global;
import com.yzm.property.basis.criteria.AssetsLogCriteria;
import com.yzm.property.basis.entity.AssetsLog;
import com.yzm.property.basis.service.AssetsLogService;
import com.yzm.property.consumable.pojo.AssetsListExcel;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;

@Controller
@RequestMapping("statistics/assetList")
public class AssetListController extends BaseController {
    private String urlPrefix = "assetList";

    @Autowired
    private AssetsLogService assetsLogService;

    @RequiresPermissions("basis:assets:view")
    @GetMapping
    public String client(ModelMap map){
        return urlPrefix+"/assetsList";
    }

    /**
     * 资产流水
     * @param assetsLogCriteria
     * @param pageBean
     * @return
     */
    @ResponseBody
    @RequestMapping("/list")
    public ResponseData getAssetsLog(AssetsLogCriteria assetsLogCriteria, PageBean pageBean) {
        if(assetsLogCriteria.getStartTime()!=null){
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date endTime=assetsLogCriteria.getEndTime();
            Calendar c=Calendar.getInstance();
            c.setTime(endTime);
            c.add(Calendar.DATE,1);
            assetsLogCriteria.setEndTime(c.getTime());
            System.out.println(assetsLogCriteria);
        }
        Map<String,Object> datas=assetsLogService.findAllByPage(assetsLogCriteria,pageBean.getPagable(Sort.by(Direction.DESC, "id")));
        return success(datas);
    }

    @BussinessLog(title = "申领单", businessType = BusinessType.EXPORT)
    @PostMapping("/toExport")
    @ResponseBody
    public ResponseData export(AssetsLogCriteria assetsLogCriteria) throws Exception {
        String fileName = "申领单";
        fileName = encodingExcelFilename(fileName);
        String folder = Global.getTempPath() + File.separator + "pio" + File.separator;
        FileUtil.touch(folder + fileName);
        List<AssetsLog> listData = assetsLogService.findAll(assetsLogCriteria);
        List<AssetsListExcel> listExcels=new ArrayList<>();
        AssetsListExcel assetsListExcel=new AssetsListExcel();
        for (AssetsLog assetsLog:listData){
            assetsListExcel.setOperationName(assetsLog.getOperationName());
            assetsListExcel.setAssetsOtherId(assetsLog.getAssetsOtherId());
            assetsListExcel.setAssetsName(assetsLog.getAssetsName());
            assetsListExcel.setCreateBy(assetsLog.getCreateBy());
            assetsListExcel.setCreateTime(assetsLog.getCreateTime());
            listExcels.add(assetsListExcel);
        }
//        List<ConsumableListItemEntity> outPlanItems = new ArrayList<>();
//        listData.forEach(ConsumableListEntity -> {
//			/*outPlan.setOrderTypeName(sysDictDataRepository.findByDictTypeAndDictValue("plan_out_type",outPlan.getOrderType().toString()).getDictLabel());
//			outPlan.setStatusName(sysDictDataRepository.findByDictTypeAndDictValue("planIn_storage_status",outPlan.getStatus().toString()).getDictLabel());
//*/
//            outPlanItems.addAll(consumableListRepository.getByOutPlanId(ConsumableListEntity.getId()));
//        });

        EasyExcel.write(folder + fileName, AssetsListExcel.class).sheet("模板").doWrite(listExcels);
        return success(fileName);
    }

    String encodingExcelFilename(String filename) {
        filename = UUID.randomUUID().toString() + "_" + filename + ".xlsx";
        return filename;
    }
}


