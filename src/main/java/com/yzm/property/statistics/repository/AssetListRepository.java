package com.yzm.property.statistics.repository;


import com.yzm.framework.base.BaseRepository;
import com.yzm.property.statistics.entity.AssetListEntity;
import org.springframework.data.jpa.repository.Query;

import java.util.List;


public interface AssetListRepository extends BaseRepository<AssetListEntity,Long> {
    @Query(nativeQuery=true,value = "select " +
            "assets_other_id,a_i.assets_source,a_i.assets_name,a_i.other_id,a_i.create_by,a_i.create_time " +
            "from " +
            "assets_info " +
            "AS a_i " +
            "join " +
            "assets_log AS a_l " +
            "on (a_i.other_id=a_l.assets_other_id)" +
            "ORDER BY create_time DESC;")
    List<AssetListEntity> findByAssetsInfoAsAssetsLogOrderByCreateTimeDesc();
}
