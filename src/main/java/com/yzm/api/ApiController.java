package com.yzm.api;

import com.alibaba.fastjson.JSONObject;
import com.yzm.common.utils.StringUtils;
import com.yzm.framework.bean.PageBean;
import com.yzm.framework.bean.ResponseData;
import com.yzm.property.materials.entity.BindingCar;
import com.yzm.property.materials.service.BindingCarService;
import com.yzm.property.system.entity.SysDictData;
import com.yzm.property.system.entity.SysDictType;
import com.yzm.property.system.service.ISysDictDataService;
import com.yzm.property.system.service.ISysDictTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName ApiController * @Description TODO
 * <AUTHOR>
 * @Date 13:45 2024/12/13
 * @Version 1.0
 **/
@RestController
@RequestMapping("/api")
public class ApiController {

    @Autowired
    private BindingCarService bindingCarService;

    @Autowired
    private ISysDictDataService sysDictDataService;
    @Autowired
    private ISysDictTypeService sysDictTypeService;

//    /**
//     * 根据关键字和分页查询盘点
//     */
//    @PostMapping("/getFloorNoUpdate")
//    @ResponseBody
//    public ResponseData getFloorNoUpdate(@RequestBody String rfid) {
//        JSONObject jsonObject = JSONObject.parseObject(rfid);
//
//        String[] rfidList = jsonObject.getString("rfid").split(",");
//// 遍历 rfidList
//        for (String rfidData : rfidList) {
//            List<String> rfidString = new ArrayList<>();
//
//            // 查找 bindingCar 对象
//            BindingCar bindingCar = bindingCarService.findByCar1AndStatus(rfidData, 0L);
//            if (StringUtils.isEmpty(bindingCar)) {
//                continue;
//            } else {
//                rfidString.add(bindingCar.getCar1());
//                rfidString.add(bindingCar.getCar2());
//                rfidString.add(bindingCar.getCar3());
//                rfidString.add(bindingCar.getCar4());
//            }
//
//            if (rfidString.size() != 0) {
//                return ResponseData.success("成功！", rfidString);
//            }
//        }
//        return ResponseData.error("没有楼层信息" + rfid);
////        // 最终判断结果
////        if (areAllMatlPrcsCd1Same) {
////            if (StringUtils.isEmpty(firstMatlPrcsCd1)) {
////                return ResponseData.error("没有楼层信息" + rfid);
////            }
////            return ResponseData.success("成功！", firstMatlPrcsCd1);
////        } else {
////            return ResponseData.error("存在不同的楼层信息" + rfid);
////        }
//    }
//
//
//    /**
//     * 根据关键字和分页查询盘点
//     */
//    @PostMapping("/getFloor")
//    @ResponseBody
//    public ResponseData getFloor(@RequestBody String rfid) {
//        JSONObject jsonObject = JSONObject.parseObject(rfid);
//
//        String[] rfidList = jsonObject.getString("rfid").split(",");
////        String floor = null;
////        for (String rfidData : rfidList) {
////            BindingCar bindingCar = bindingCarService.findByCar1AndStatus(rfidData, 0L);
////            bindingCar.getMatlPrcsCd1();
////        }
//        String firstMatlPrcsCd1 = null; // 用于存储第一次查到的 MatlPrcsCd1
//        boolean areAllMatlPrcsCd1Same = true; // 标记是否所有 MatlPrcsCd1 相同
//
//// 遍历 rfidList
//        for (String rfidData : rfidList) {
//            // 查找 bindingCar 对象
//            BindingCar bindingCar = bindingCarService.findByCar1AndStatus(rfidData, 0L);
//
//            // 获取 MatlPrcsCd1
//            String currentMatlPrcsCd1 = bindingCar != null ? bindingCar.getMatlPrcsCd1() : null;
//
//            // 第一次获取 MatlPrcsCd1 时，赋值给 firstMatlPrcsCd1
//            if (firstMatlPrcsCd1 == null) {
//                firstMatlPrcsCd1 = currentMatlPrcsCd1;
//            } else {
//                // 如果不是第一次且当前的 MatlPrcsCd1 与第一次不同，则设置标记为 false
//                if (firstMatlPrcsCd1 == null || !firstMatlPrcsCd1.equals(currentMatlPrcsCd1)) {
//                    areAllMatlPrcsCd1Same = false;
//                    break; // 找到不相同的值，直接跳出循环
//                }
//            }
//        }
//// 最终判断结果
//        if (areAllMatlPrcsCd1Same) {
//            if (StringUtils.isEmpty(firstMatlPrcsCd1)) {
//                return ResponseData.error("没有楼层信息" + rfid);
//            }
//            for (String rfidData : rfidList) {
//                BindingCar bindingCar = bindingCarService.findByCar1AndStatus(rfidData, 0L);
//                if(StringUtils.isNotEmpty(bindingCar)){
//                    bindingCar.setStatus(1L);
//                    bindingCarService.update(bindingCar);
//                }
//            }
//            return ResponseData.success("成功！", firstMatlPrcsCd1);
//        } else {
//            return ResponseData.error("存在不同的楼层信息" + rfid);
//        }
//    }

    /**
     * 根据关键字和分页查询盘点
     */
    @PostMapping("/getFloorNoUpdate")
    @ResponseBody
    public ResponseData getFloorNoUpdate(@RequestBody String rfid) {
        JSONObject jsonObject = JSONObject.parseObject(rfid);

        String[] rfidList = jsonObject.getString("rfid").split(",");
//        String floor = null;
//        for (String rfidData : rfidList) {
//            BindingCar bindingCar = bindingCarService.findByCar1AndStatus(rfidData, 0L);
//            bindingCar.getMatlPrcsCd1();
//        }
        String firstMatlPrcsCd1 = null; // 用于存储第一次查到的 MatlPrcsCd1
        boolean areAllMatlPrcsCd1Same = true; // 标记是否所有 MatlPrcsCd1 相同

// 遍历 rfidList
        for (String rfidData : rfidList) {
            // 查找 bindingCar 对象
            BindingCar bindingCar = bindingCarService.findByCar1AndStatus(rfidData, 0L);
            if (StringUtils.isEmpty(bindingCar)) {
                continue;
            }
            // 获取 MatlPrcsCd1
            String currentMatlPrcsCd1 = bindingCar != null ? bindingCar.getMatlPrcsCd1() : null;

            // 第一次获取 MatlPrcsCd1 时，赋值给 firstMatlPrcsCd1
            if (firstMatlPrcsCd1 == null) {
                firstMatlPrcsCd1 = currentMatlPrcsCd1;
            } else {
                // 如果不是第一次且当前的 MatlPrcsCd1 与第一次不同，则设置标记为 false
                if (firstMatlPrcsCd1 == null || !firstMatlPrcsCd1.equals(currentMatlPrcsCd1)) {
                    areAllMatlPrcsCd1Same = false;
                    break; // 找到不相同的值，直接跳出循环
                }
            }
        }
// 最终判断结果
        if (areAllMatlPrcsCd1Same) {
            if (StringUtils.isEmpty(firstMatlPrcsCd1)) {
                return ResponseData.error("没有楼层信息" + rfid);
            }
            return ResponseData.success("成功！", firstMatlPrcsCd1);
        } else {
            return ResponseData.error("存在不同的楼层信息" + rfid);
        }
    }

    /**
     * 根据关键字和分页查询盘点
     */
    @PostMapping("/getFloor")
    @ResponseBody
    public ResponseData getFloor(@RequestBody String rfid) {
        JSONObject jsonObject = JSONObject.parseObject(rfid);
        try {
            String[] rfidList = jsonObject.getString("rfid").split(",");
//        String floor = null;
//        for (String rfidData : rfidList) {
//            BindingCar bindingCar = bindingCarService.findByCar1AndStatus(rfidData, 0L);
//            bindingCar.getMatlPrcsCd1();
//        }
            String firstMatlPrcsCd1 = null; // 用于存储第一次查到的 MatlPrcsCd1
            boolean areAllMatlPrcsCd1Same = true; // 标记是否所有 MatlPrcsCd1 相同

// 遍历 rfidList
            for (String rfidData : rfidList) {
                // 查找 bindingCar 对象
                BindingCar bindingCar = bindingCarService.findByCar1AndStatus(rfidData, 0L);

                // 获取 MatlPrcsCd1
                String currentMatlPrcsCd1 = bindingCar != null ? bindingCar.getMatlPrcsCd1() : null;

                // 第一次获取 MatlPrcsCd1 时，赋值给 firstMatlPrcsCd1
                if (firstMatlPrcsCd1 == null) {
                    firstMatlPrcsCd1 = currentMatlPrcsCd1;
                } else {
                    // 如果不是第一次且当前的 MatlPrcsCd1 与第一次不同，则设置标记为 false
                    if (firstMatlPrcsCd1 == null || !firstMatlPrcsCd1.equals(currentMatlPrcsCd1)) {
                        areAllMatlPrcsCd1Same = false;
                        break; // 找到不相同的值，直接跳出循环
                    }
                }
            }
// 最终判断结果
            if (areAllMatlPrcsCd1Same) {
                if (StringUtils.isEmpty(firstMatlPrcsCd1)) {
                    return ResponseData.error("没有楼层信息" + rfid);
                }
                for (String rfidData : rfidList) {
                    BindingCar bindingCar = bindingCarService.findByCar1AndStatus(rfidData, 0L);
                    bindingCar.setStatus(1L);
                    bindingCarService.update(bindingCar);
                }
                return ResponseData.success("成功！", firstMatlPrcsCd1);
            } else {
                return ResponseData.error("存在不同的楼层信息" + rfid);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error("楼层信息获取错误：" + rfid);
        }

    }
}
