package com.yzm.reader;




import java.util.ArrayList;
import java.util.List;

public class ReaderUtil {
    private static final String IP = "*************";
    private static final int PORT = 6000;

    public static byte[] comAddr = new byte[1];

    public static int[] PortHandle = new int[1];

    public static int openLink() {
        byte[] comAddr = new byte[1];
        comAddr[0] = (byte) 255;
        List<String> list = new ArrayList<>();
        HFReader9 reader = HFReader9.INSTANCE;
        // 打开网口
        int result = reader.OpenNetPort(PORT, IP, comAddr, PortHandle);
        return result;
    }

    public static int openLinkCom(int com) {
        byte[] comAddr = new byte[1];
        comAddr[0] = (byte) 255;
        List<String> list = new ArrayList<>();
        HFReader9 reader = HFReader9.INSTANCE;
        // 打开网口
        int result = reader.OpenComPort(com, comAddr, (byte) 0, PortHandle);
        return result;
    }

    public static int closeLink() {

        HFReader9 reader = HFReader9.INSTANCE;
        // 打开网口
        int result = reader.CloseNetPort(PortHandle[0]);
        return result;
    }

    public static int closeComLink() {

        HFReader9 reader = HFReader9.INSTANCE;
        // 打开网口
        int result = reader.CloseSpecComPort(PortHandle[0]);
        return result;
    }



    public static void main(String[] args) {
        int i = openLinkCom(4);
        System.out.println(i);
//        String ad8A4483002302E0 = readMultipleBlock("AD8A4483002302E0");
//        System.out.println(ad8A4483002302E0);
//        AD8A4483002302   E9847CED500104
    }

    public static String addZeroForNum(String str, int strLength) {
        int strLen = str.length();
        if (strLen < strLength) {
            while (strLen < strLength) {
                StringBuffer sb = new StringBuffer();
//                sb.append(":").append(str);// left+0
                sb.append(str).append(":");//right+0
                str = sb.toString();
                strLen = str.length();
            }
        }
        return str;
    }


}
