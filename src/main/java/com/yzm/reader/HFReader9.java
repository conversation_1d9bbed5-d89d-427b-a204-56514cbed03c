package com.yzm.reader;

import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.Platform;

public interface HFReader9 extends Library {

    // DLL文件默认路径为项目根目录，若DLL文件存放在项目外，请使用绝对路径。（此处：(Platform.isWindows()?"msvcrt":"c")指本地动态库msvcrt.dll）
    HFReader9 INSTANCE = (HFReader9) Native.loadLibrary((Platform.isWindows() ? "Reader_DLLV1" : "c"), HFReader9.class);

    //    HFReader9 INSTANCE = (HFReader9) Native.loadLibrary(("/Users/<USER>/Desktop/HFReader9.dll"), HFReader9.class);
    int ReadMultipleBlock(byte[] ComAddr, byte[] state, byte[] UID, byte BlockNum, byte BlockCount, byte[] BlockSecStatus, byte[] Data, byte[] ErrorCode, int FrmHandle);

    int CloseCom();

    int ConnectServer(String ipAddress, int nPort, String strException);
    /**
     * @param args
     */
    int OpenComPort(int port, byte[] comAddr, byte baud, int[] PortHandle);

    int CloseSpecComPort(int PortHandle);

    //巡查标签
    int Inventory(byte[] ComAddr, byte[] tate, byte[] AFI, byte[] pOUcharUIDList, int[] pOUcharTagNum, int FrmHandle);

    //打开网口
    int OpenNetPort(int port, String IPaddr, byte[] ComAdr, int[] FrmHandle);

    //    int OpenComPort(int port,  byte[] ComAdr, byte baud, int[] FrmHandle) ;
    int WriteSingleBlock(byte[] ComAddr, byte[] state, byte[] UID, byte BlockNum, byte[] Data, byte[] ErrorCode, int FrmHandle);

    int GetSystemInformation(byte[] ComAddr, byte[] state, byte[] UIDI, byte[] InformationFlag, byte[] UIDO, byte[] DSFID, byte[] AFI, byte[] MemorySize, byte[] ICReference, byte[] ErrorCode, int FrmHandle);

    //关闭网口
    int CloseNetPort(int PortHandle);

    int GetReaderInformation(byte[] comAddr, byte[] versionInfo, byte[] readerType, byte[] trType, byte[] dmaxfre,
                             byte[] dminfre, byte[] powerdBm, byte[] InventoryScanTime, byte[] Ant, byte[] BeepEn,
                             byte[] OutputRep, byte[] CheckAnt, int PortHandle);

    int Inventory_G2(byte[] comAddr, byte QValue, byte Session, byte MaskMem, byte[] MaskAdr, byte MaskLen,
                     byte[] MaskData, byte MaskFlag, byte AdrTID, byte LenTID, byte TIDFlag, byte Target,
                     byte InAnt, byte Scantime, byte FastFlag, byte[] pEPCList, byte[] Ant, int[] Totallen,
                     int[] CardNum, int PortHandle);

    int InventoryMix_G2(byte[] comAddr, byte QValue, byte Session, byte MaskMem, byte[] MaskAdr, byte MaskLen,
                        byte[] MaskData, byte MaskFlag, byte ReadMem, byte[] ReadAdr, byte ReadLen, byte[] Psd,
                        byte Target, byte InAnt, byte Scantime, byte FastFlag, byte[] pEPCList, byte[] Ant,
                        int[] Totallen, int[] CardNum, int PortHandle);

    int ReadData_G2(byte[] comAddr, byte[] EPC, byte ENum, byte Mem, byte WordPtr, byte Num, byte[] Password,
                    byte MaskMem, byte[] MaskAdr, byte MaskLen, byte[] MaskData, byte[] Data, int[] Errorcode,
                    int PortHandle);

    int WriteData_G2(byte[] comAddr, byte[] EPC, byte WNum, byte ENum, byte Mem, byte WordPtr, byte[] Wdt,
                     byte[] Password, byte MaskMem, byte[] MaskAdr, byte MaskLen, byte[] MaskData, int[] Errorcode,
                     int PortHandle);

    int WriteEPC_G2(byte[] comAddr, byte[] Password, byte[] EPC, byte ENum, int[] Errorcode, int PortHandle);

    int KillTag_G2(byte[] comAddr, byte[] EPC, byte ENum, byte[] Killpwd, byte MaskMem, byte[] MaskAdr, byte MaskLen,
                   byte[] MaskData, int[] Errorcode, int PortHandle);

    int Lock_G2(byte[] comAddr, byte[] EPC, byte ENum, byte selectid, byte setprotect, byte[] Password, byte MaskMem,
                byte[] MaskAdr, byte MaskLen, byte[] MaskData, int[] Errorcode, int PortHandle);

    int BlockErase_G2(byte[] comAddr, byte[] EPC, byte ENum, byte Mem, byte WordPtr, byte Num, byte[] Password,
                      byte MaskMem, byte[] MaskAdr, byte MaskLen, byte[] MaskData, int[] Errorcode, int PortHandle);

    int SetRegion(byte[] comAddr, byte dmaxfre, byte dminfre, int PortHandle);

    int SetAddress(byte[] comAddr, byte ComAdrData, int PortHandle);

    int SetInventoryScanTime(byte[] comAddr, byte ScanTime, int PortHandle);

    int SetBaudRate(byte[] comAddr, byte baud, int PortHandle);

    int SetRfPower(byte[] comAddr, byte PowerDbm, int PortHandle);

    int SetWorkMode(byte[] comAddr, byte Read_mode, int PortHandle);

    int GetSystemParameter(byte[] comAddr, byte[] Read_mode, byte[] Accuracy, byte[] RepCondition, byte[] RepPauseTime,
                           byte[] ReadPauseTim, byte[] TagProtocol, byte[] MaskMem, byte[] MaskAdr, byte[] MaskLen,
                           byte[] MaskData, byte[] TriggerTime, byte[] AdrTID, byte[] LenTID, int PortHandle);

    int SetAntennaMultiplexing(byte[] comAddr, byte Ant, int PortHandle);

    int SetAntennaA12(byte[] comAddr, byte SetOnce, byte AntCfg1, byte AntCfg2, int PortHandle);

    int WriteRfPower(byte[] comAddr, byte PowerDbm, int PortHandle);

    int ReadRfPower(byte[] comAddr, byte[] PowerDbm, int PortHandle);

    int RetryTimes(byte[] comAddr, byte[] Times, int PortHandle);

    int SetReadMode(byte[] comAddr, byte ReadMode, int PortHandle);

    int SetCheckAnt(byte[] comAddr, byte CheckAnt, int PortHandle);

    int GetSeriaNo(byte[] comAddr, byte[] SeriaNo, int PortHandle);

    int SetBeepNotification(byte[] comAddr, byte BeepEn, int PortHandle);

    int SetReal_timeClock(byte[] comAddr, byte[] paramer, int PortHandle);

    int GetTime(byte[] comAddr, byte[] paramer, int PortHandle);

    int SetDRM(byte[] comAddr, byte DRM, int PortHandle);

    int GetDRM(byte[] comAddr, byte[] DRM, int PortHandle);

    int GetReaderTemperature(byte[] comAddr, byte[] PlusMinus, byte[] Temperature, int PortHandle);

    int MeasureReturnLoss(byte[] comAddr, byte[] TestFreq, byte Ant, byte[] ReturnLoss, int PortHandle);

    int GetGPIOStatus(byte[] ComAdr, byte[] OutputPin, int frmComPortindex);

    int SetGPIO(byte[] ComAdr, byte OutputPin, int frmComPortindex);
}
